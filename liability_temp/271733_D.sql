UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012119800') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014297592') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014719544') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017293176') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018634616') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011500500') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011854420') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013523540') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014888468') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014920340') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014762408') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016774484') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017051412') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016200552') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016352104') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018827028') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017394553') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017821753') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017896121') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015198249') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016413289') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018510249') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018602601') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011608597') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011629461') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012064853') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012928213') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014927701') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017632533') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019202837') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011800954') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014506362') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016728954') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017399098') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018200442') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018242938') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019324986') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011445930') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011712490') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013291306') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014884714') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015935786') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016883498') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018777514') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011996566') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013241366') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016190614') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018457110') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012390779') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014876539') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015947067') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016155643') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016278139') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017043515') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017734203') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
INSERT INTO gv_report_temp_liability_d_2409(issuer_code, merchant_code, outlet_code, cpg_code, voucher_status, denomination, effective_date, voucher_code_page_index, voucher_codes) VALUE ('', '', '', 'dcc4d827b5a34dbc97274ed3120c75cf', '5', '50000.00', '2024-09-21 23:59:59', '0', '5020000013342635'); 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018374443') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012232151') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012303383') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012963735') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015679255') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015992279') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017817559') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018616599') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018676311') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011519420') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011976124') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013192764') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013704700') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015157180') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016785980') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018718908') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011033744') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012046480') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013442896') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015884688') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018384464') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018747984') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019034256') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011971308') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016806764') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018569004') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012681277') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013209341') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013462909') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017194685') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014931345') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015737169') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016180177') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016305681') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017240977') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017571793') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019992977') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011019245') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014432382') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015255166') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015366206') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015741374') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017352446') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019177406') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018969581') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015145874') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015639634') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015709394') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015904850') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015929746') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017008594') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011346495') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012816575') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012938495') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015273407') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011486995') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013012179') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013744595') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013752467') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014669075') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015952915') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017999123') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018570643') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018916307') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011192052') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011977460') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013878708') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015211316') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015545140') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019241524') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011601774') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013692398') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015555822') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016163886') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019990190') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014374812') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014930012') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015023516') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017234140') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019772188') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011667829') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011826357') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013597045') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017575989') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018553653') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019432181') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011312175') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017207087') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018237807') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019010735') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012075254') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013515190') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011911517') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013783069') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014347229') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015537885') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015702557') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016797469') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016941277') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017179165') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017704221') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018180573') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018917981') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012135780') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012439716') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013875812') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015413796') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016046692') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016951396') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017299044') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018386020') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018636196') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019527076') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015599223') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016632311') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018146423') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019012023') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011284382') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014766878') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015599454') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019399070') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015518629') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016195045') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019405733') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013925104') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017845680') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018190832') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012059487') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013545631') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018287711') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019243295') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011556273') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012222257') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014211569') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017509809') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018003313') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015431654') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018891750') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011007832') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012980376') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012989144') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014265816') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014316184') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015381400') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015979032') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017512984') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019844632') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014138994') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015431218') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015820658') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016369010') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016915250') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016931378') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012351385') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012749721') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013246169') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014740889') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014848665') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012748711') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017972761') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018285977') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013523879') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015320935') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017600807') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019541735') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014899955') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015791411') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016124147') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017094899') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019759091') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019958195') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011570010') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012322330') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015984602') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017328090') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012847776') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014012320') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017087584') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017723168') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011353928') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011648456') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017643080') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019492488') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013970459') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013613281') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014120801') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011768162') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012534690') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013538722') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014432674') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015103522') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016868834') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018208098') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019628898') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012049097') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013710281') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013860553') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016962057') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017568457') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014905059') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016334563') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017072291') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019352995') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019406819') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011466058') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011550666') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012373258') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014386058') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014741578') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016043850') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013155908') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016064516') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017027204') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017760580') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011971340') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012793740') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013726284') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015966988') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017965644') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019444620') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011665291') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011840907') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012699787') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013067019') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014390987') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014406539') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018775307') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019657995') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011165061') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011498565') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011631557') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012872005') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013606341') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015863045') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017682501') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019018629') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011429056') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012679104') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014724352') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011885453') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012021645') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016916237') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014146510') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014406734') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014732750') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015249998') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017185294') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011782598') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012177094') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012527238') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012960774') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013714118') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014888838') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015000902') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017986822') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019600262') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011701889') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013358657') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016212161') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018565825') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012778895') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014866447') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015883471') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016377039') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000017932559') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018957903') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019670735') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013180871') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014832583') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016047559') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016388807') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018885511') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013940802') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000013995010') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014910658') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000019809538') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011898435') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000011958275') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000012748547') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014166915') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014495427') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000014784323') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000015903683') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000016036099') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018574723') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000018974083') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = 'dcc4d827b5a34dbc97274ed3120c75cf' AND voucher_status = '5' AND denomination = '50000.00' AND effective_date = '2024-09-21 23:59:59' ; 
INSERT INTO gv_report_temp_liability_d_2409(issuer_code, merchant_code, outlet_code, cpg_code, voucher_status, denomination, effective_date, voucher_code_page_index, voucher_codes) VALUE ('', '', '', '522526f16fae412fa3366b92c40eff64', '5', '100000.00', '2024-09-03 23:59:59', '0', '5020000022202872'); 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000024863672') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000022422442') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000026807082') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000022538007') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000022644050') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000021077614') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000023142703') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000027034991') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000022844676') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000026609861') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = CONCAT(voucher_codes, ',', '5020000025882511') , voucher_code_page_index = voucher_code_page_index + 1  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '522526f16fae412fa3366b92c40eff64' AND voucher_status = '5' AND denomination = '100000.00' AND effective_date = '2024-09-03 23:59:59' ; 


-- -------------------------------------------------------------------- 

UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241251456', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241681152', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243991488', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245403328', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247582592', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241052033', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246616961', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248394625', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249296641', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249482625', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245801474', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246615106', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248121474', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248416002', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241387331', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241665603', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244376067', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246763971', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249596547', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241862340', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243476996', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246868676', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248319556', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241279429', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243962245', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247036549', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248433157', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246621830', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249634630', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244301832', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247974664', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248971336', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241566089', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241622153', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241728905', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244107401', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244838153', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246973193', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246076746', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246305674', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243067276', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244379084', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246834572', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247397388', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247474892', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241137869', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242597581', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242694349', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242728781', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244611149', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245826317', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245833101', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242806478', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246251726', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246729742', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246805518', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247112142', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247173326', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245231823', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246309775', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247181903', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248492111', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242838672', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243525776', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247278480', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249266704', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242063441', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244639121', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245363473', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246206353', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247475409', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247982545', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249715793', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243227154', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243322514', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243554002', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246473682', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247904530', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241019859', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241022611', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247029971', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247146067', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247339667', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249782547', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244369044', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247183444', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248927892', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241306901', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242496789', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244624917', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246829781', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241748118', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247489174', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247758678', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248094550', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249915414', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243405271', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244567447', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246624471', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246951383', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241845400', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242302680', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247043160', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247637336', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248787096', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249654040', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241322393', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245008921', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248596313', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241385946', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243471386', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245469082', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245747418', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246792154', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243572699', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244143323', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244507227', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244646811', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244907483', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245859355', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246744987', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247514587', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242545116', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247577436', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249333660', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241314077', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242413981', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245756574', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248858206', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249861086', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241234975', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241778079', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244134048', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245053984', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246245536', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247582112', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249161568', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241792161', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243888162', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244255266', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245033698', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247859682', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248109986', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249574050', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242723811', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245469475', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248611875', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246452708', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249314276', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249581476', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242683365', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242717925', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243496293', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245018214', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249036454', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249474342', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241843047', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242262375', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244267303', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242628328', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247478696', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248484392', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248941224', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242127145', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247968361', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249136553', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249938729', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242149354', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243861162', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245812074', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246184362', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246624234', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248377642', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241537963', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248733547', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249698475', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242946988', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246691372', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241703085', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242528621', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244686829', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246359149', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246989037', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247513133', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243695470', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246202926', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246417582', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247161966', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249235374', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243203823', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246474287', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249491823', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242284592', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243776368', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244395185', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247716209', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248132145', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247662898', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242153075', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242303859', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245332531', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246692787', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241201588', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246874100', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247448500', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248239796', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244019765', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245227509', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246484661', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249126965', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245635702', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242776887', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247115383', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247428407', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246813816', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247362104', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248309624', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241208761', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241676217', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242165433', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242424313', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242764793', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244159033', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244244178873', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246339769', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247078073', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248259513', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241848762', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242065274', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242287482', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242038523', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243932923', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246391163', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244247529915', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243173116', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248916860', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249083324', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249287164', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249523580', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242691005', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244245224765', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244249406973', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241975806', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244242039486', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246801854', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248876798', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244241078271', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244243345343', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244246159359', '') WHERE id = '4760';
UPDATE gv_report_temp_liability_d_2409 SET voucher_codes = REPLACE(voucher_codes, '1304244248717439', '') WHERE id = '4760';
