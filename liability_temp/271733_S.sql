 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268;
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 50000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 268;
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409 SET activated_amount = activated_amount + 0, purchased_amount = purchased_amount + 100000.00, deactivated_amount = deactivated_amount + 0, expired_amount = expired_amount + 0, recently_expired_amount = recently_expired_amount + 0 WHERE id = 426; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3'; 
UPDATE gv_report_temp_liability_s_2409  SET activated_amount = activated_amount - 0, purchased_amount = purchased_amount - 0, deactivated_amount = deactivated_amount - 1000000,  expired_amount = expired_amount - 0, recently_expired_amount = recently_expired_amount - 0  WHERE issuer_code = '' AND merchant_code = '' AND outlet_code = '' AND cpg_code = '0fd542a809434928845d08c00b07e2d3';
