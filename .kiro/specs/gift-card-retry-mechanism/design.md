# Design Document: Gift Card Retry Mechanism

## Overview

The Gift Card Retry Mechanism is designed to provide idempotency for GiftCardApi operations. When a client includes an optional `retryKey` parameter in their request, the system will store the result of the operation. Subsequent requests with the same `retryKey` will return the stored result instead of re-executing the operation. This mechanism helps prevent duplicate operations and ensures consistent responses for retried requests, which is particularly important for financial transactions like gift card activations, redemptions, and issuances.

## Architecture

The retry mechanism will be implemented as a cross-cutting concern that can be applied to all relevant GiftCardApi endpoints. The architecture consists of the following components:

1. **Request Interceptor**: Checks if a request contains a retryKey and if a cached response exists for that key.
2. **Response Cache**: Stores API responses indexed by retryKey.
3. **Cache Management**: Handles expiration and cleanup of cached responses.

The solution will use Redis as the caching mechanism since it's already being used in the application and provides the necessary features for distributed caching with expiration.

## Components and Interfaces

### 1. RetryKeyAwareRequest Interface

This interface will be implemented by all request classes that support the retry mechanism:

```java
public interface RetryKeyAwareRequest {
    String getRetryKey();
    void setRetryKey(String retryKey);
}
```

### 2. RetryKeyAwareResponse Interface

This interface will be implemented by all response classes that support the retry mechanism:

```java
public interface RetryKeyAwareResponse {
    String getRetryKey();
    void setRetryKey(String retryKey);
}
```

### 3. GiftCardRetryService

This service will handle the caching and retrieval of responses:

```java
@Service
public class GiftCardRetryService {
    private final RedisTemplate<String, Object> redisTemplate;
    private final long cacheExpirationTimeInSeconds;
    
    // Methods for checking, storing, and retrieving cached responses
    public boolean hasResponseForRetryKey(String retryKey);
    public <T> T getCachedResponse(String retryKey, Class<T> responseType);
    public void cacheResponse(String retryKey, Object response);
}
```

### 4. RetryAspect

An aspect that will intercept GiftCardApi method calls to implement the retry mechanism. The aspect can be selectively applied to specific methods using custom annotations or pointcut expressions, allowing for flexible application of the retry mechanism to any API endpoint:

```java
@Aspect
@Component
public class RetryAspect {
    private final GiftCardRetryService retryService;
    
    // This pointcut can be customized to target specific methods
    @Around("@annotation(com.gtech.gvcore.giftcard.annotation.RetryableOperation) || " +
            "execution(* com.gtech.gvcore.giftcard.application.api.GiftCardApiService.*(..))")
    public Object handleRetry(ProceedingJoinPoint joinPoint) throws Throwable {
        // Extract retryKey from request if present
        // Check cache for existing response
        // Either return cached response or proceed with method execution and cache result
    }
}
```

## Data Models

### Redis Cache Entry

The cache entries will be stored in Redis with the following structure:

- **Key**: `GV:API_RETRY:{retryKey}`
- **Value**: Serialized API response object
- **Expiration**: Configurable TTL (Time-To-Live)

### Configuration Properties

```properties
# Retry mechanism configuration
gvcore.retry.cache-expiration-seconds=86400  # Default: 24 hours
gvcore.retry.max-entries=10000               # Maximum number of cached responses
```

## Error Handling

1. **Cache Access Errors**: If there are issues accessing the cache, the system will log the error and proceed with normal request processing.
2. **Serialization/Deserialization Errors**: If there are issues serializing or deserializing cached responses, the system will log the error and proceed with normal request processing.
3. **Cache Capacity Exceeded**: If the cache reaches its capacity limit, the oldest entries will be evicted first.

## Testing Strategy

### Unit Tests

1. Test the `GiftCardRetryService` methods for caching and retrieving responses.
2. Test the `RetryAspect` with mock requests and responses.

### Integration Tests

1. Test the retry mechanism with actual API calls.
2. Verify that identical requests with the same retryKey return the same response.
3. Verify that the cache expiration works as expected.
4. Test concurrent requests with the same retryKey.

### Performance Tests

1. Measure the overhead of the retry mechanism on API response times.
2. Test the system under load to ensure the cache performs well.

## Implementation Approach

The implementation will follow these steps:

1. Define the `RetryKeyAwareRequest` and `RetryKeyAwareResponse` interfaces.
2. Update all relevant request and response classes to implement these interfaces.
3. Implement the `GiftCardRetryService` for caching and retrieving responses.
4. Implement the `RetryAspect` to intercept API calls and apply the retry logic.
5. Add configuration properties for the retry mechanism.
6. Update the GiftCardApiService to handle retryKey in all relevant methods.

## Design Decisions and Rationales

### Why Redis?

Redis is chosen as the caching mechanism because:
1. It's already being used in the application.
2. It provides built-in expiration functionality.
3. It's distributed, allowing the retry mechanism to work across multiple instances.

### Why Aspect-Oriented Programming?

AOP is used to implement the retry mechanism because:
1. It allows the retry logic to be applied consistently across all API endpoints.
2. It separates the retry logic from the business logic, making the code more maintainable.
3. It can be easily enabled or disabled through configuration.

### Cache Expiration Strategy

Cached responses will expire after a configurable period (default: 24 hours) to:
1. Prevent indefinite storage of responses.
2. Balance between providing a reasonable window for retries and efficient resource usage.
3. Comply with data retention policies.

## Security Considerations

1. **RetryKey Generation**: Clients should use secure random values for retryKeys to prevent guessing.
2. **Cache Isolation**: Each tenant's data should be isolated in the cache to prevent cross-tenant data access.
3. **Sensitive Data**: Care should be taken to ensure that cached responses don't contain sensitive data that shouldn't be persisted.