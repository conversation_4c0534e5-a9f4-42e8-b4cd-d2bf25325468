spring.profiles.active = @profile.active@
spring.application.name = gvcore
spring.main.allow-bean-definition-overriding=true
apollo.bootstrap.enabled=true
# multipart settings
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=64KB
spring.servlet.multipart.max-request-size=64KB

server.port= 8081
server.servlet.context-path= /gvcore
server.connection-timeout= 10000

server.tomcat.accept-count = 1000
server.tomcat.max-connections = 20000
server.tomcat.threads.max= 1000
server.tomcat.threads.min-spare= 100

eureka.client.register-with-eureka=true
eureka.instance.preferIpAddress=true
eureka.instance.instance-id=${spring.cloud.client.ip-address}:${server.port}

app.version=@project.version@
info.build.version=@project.version@

demo.info.project.artifactId= @project.artifactId@
demo.info.name= @project.name@
demo.info.basedir=@project.basedir@
demo.info.outputDirectory=@project.build.outputDirectory@

spring.shardingsphere.sharding.master-slave-rules.ds.master-data-source-name=master
spring.shardingsphere.sharding.master-slave-rules.ds.slave-data-source-names=read
#\u5206\u8868\u89C4\u5219--\u6682\u65F6\u4E0D\u7528
#spring.shardingsphere.sharding.tables.gv_voucher.actual-data-nodes=ds.gv_voucher_$->{0..31}
#spring.shardingsphere.sharding.tables.gv_voucher.table-strategy.standard.sharding-column=voucher_code
#spring.shardingsphere.sharding.tables.gv_voucher.table-strategy.standard.precise-algorithm-class-name=com.gtech.gvcore.sharding.PreciseShardingAlgorithmImpl


# Whether to open Swagger access
gtech.common.security.enableSwagger = true

# mybatis configuration
mybatis.configuration.map-underscore-to-camel-case = true
mybatis.type-aliases-package=com.gtech.gvcore.dao.model
mybatis.mapper-locations=classpath*:/mapper/*.xml
mapper.not-empty=false
mapper.identity=MYSQL
# ribbon.ReadTimeout=3000

spring.cloud.loadbalancer.cache.ttl=10s

# Spring Boot Actuator health check
management.endpoint.shutdown.enabled=true
management.health.db.enabled=true

spring.main.allow-circular-references=true
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

# Logger configuration
logging.file.name=logs/gvcore.log
logging.config = classpath:logback-spring.xml

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone = GMT+7
#spring.jackson.default-property-inclusion=NON_NULL


titan.masterdata.context-path = /masterdata
titan.store.context-path = /store
titan.idm.context-path = /idm
titan.message.context-path = /message

titan.filecloud.api.embedded = true

# 统一监控系统配置
gv.monitor.stats.log.enabled=true
gv.monitor.stats.reset.enabled=false

# 基于注解的对象追踪配置
gv.monitor.annotation.enabled=true
gv.monitor.bytecode.enabled=false