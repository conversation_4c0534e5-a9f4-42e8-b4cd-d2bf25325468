0 = Transaction successful.			
99 = Batch Closed. Totals mismatch.			
1022 = Card/Voucher reload successful.			
1056 = Cancel Redeem successful.			
10001 = Card/Voucher expired.			
10002 = Card/Voucher is deactivated.			
10003 = Card/Voucher Program not applicable.			
10004 = Either Card Number or Card Pin is Incorrect.			
10005 = Card/Voucher redeem successful.			
10006 = Card/Voucher expiry invalid format.			
10007 = Expiry date has not been set.			
10008 = Authorization failed.			
10009 = Security check failed.			
10010 = Balance is insufficient.			
10011 = Redemption failed.			
10012 = Transaction failed.			
10013 = Balance Enquiry successful.			
10014 = Purchase Activate successful.			
10015 = Card/Voucher already active.			
10016 = Card/Voucher reload successful.			
10017 = Card/Voucher already purchased.			
10018 = Transaction failed.			
10019 = Transaction Id check failed.			
10020 = Card/Voucher already deactivated.			
10021 = Card/Voucher deactivated.			
10022 = Amount more than max reload limit.			
10023 = No cards/vouchers available.			
10024 = Card/Voucher issued.			
10025 = Card/Voucher exists for this customer.			
10026 = Card/Voucher Program not supported.			
10027 = Card/Voucher is deactivated.			
10028 = Customer check failed.			
10029 = Card/Voucher not activated.			
10030 = Card/Voucher deactivated. Zero balance.			
10031 = Card/Voucher not issued.			
10032 = Card/Voucher not purchased.			
10033 = Load transaction successful.			
10034 = Amount less than min redeem limit.			
10035 = Amount less than min reload limit.			
10036 = Amount less than min load limit.			
10037 = Amount more than max redeem limit.			
10038 = Amount more than max load limit.			
10040 = Could not find Corporate.			
10041 = Balance is less than first redemption limit.			
10042 = Insufficient balance.			
10043 = Add on cards/vouchers limit reached.			
10044 = Primary card/voucher deactivated.			
10045 = Primary card/voucher deactivated.			
10046 = Primary card/voucher expired.			
10047 = Primary card/voucher deactivated.			
10048 = Primary card/voucher not activated.			
10049 = Primary card/voucher not found.			
10050 = Primary cant be Addon card/voucher.			
10051 = Temporary card/voucher expired.			
10052 = Cannot Cancel. Input mismatch.			
10053 = Cancel Redeem successful.			
10054 = Cancel Reload successful.			
10055 = Cancel Load successful.			
10056 = Cancel Redeem successful.			
10057 = Cannot reload Fixed Card/Voucher.			
10058 = Card/Voucher program group inactive.			
10059 = Amount incorrect.			
10060 = Either Card Number or Card Pin is Incorrect.			
10061 = Card/Voucher Number check failed.			
10062 = Card/Voucher is already deactivated.			
10063 = Could not find addon card/voucher.			
10064 = Invalid batch.			
10065 = Card/Voucher format is incorrect.			
10066 = Could not read card/voucher.			
10067 = Amount not multiple of reload unit.			
10068 = Validation Failed. Last Transaction is not same as you are trying to cancel.			
10069 = Cannot Reissue Card/Voucher.			
10070 = Validation failed. Transaction not in the same batch.			
10071 = The replacement card/voucher has been deactivated.			
10072 = Reissuing card/voucher expired.			
10073 = Could not find the reissuing card/voucher.			
10074 = Reissuing card/voucher is already active.			
10075 = Card/Voucher already reissued.			
10076 = Validation failed. Transaction not from same outlet.			
10077 = Cannot issue.Primary card/voucher is individual.			
10078 = Cannot issue.Primary card/voucher is corporate.			
10079 = Issue of add-on cards/vouchers not allowed.			
10080 = Parent card/voucher deactivated. It was lost or stolen. Please pick up the card/voucher.			
10081 = Batch already closed.			
10082 = Card/Voucher deactivated. It was lost or stolen. Please pick up the card/voucher.			
10083 = Parent card/voucher is stolen. Please pick up the card/voucher.			
10084 = Transaction is already cancelled.			
10085 = Card/Voucher Purchased successful.			
10086 = Either card/voucher number or card/voucher pin is incorrect.			
10087 = Amount validation failed.			
10088 = Add-on card/voucher deactivated.			
10089 = Add-on card/voucher already been issued.			
10090 = Amount not a multiple of redeem unit.			
10091 = Your Date of Birth is not being stored in our database. Please contact merchant.			
10092 = Date of Birth validation failed.			
10093 = Name validation failed.			
10094 = Validation failed. Amount is not part of initial values.			
10095 = Purchaser Info. is Mandatory.			
10096 = Merchant not authorized to accept this card/voucher.			
10097 = Insufficent balance in transfer card/voucher.			
10098 = Transfer Card/Voucher not activated.			
10099 = Transfer Card/Voucher is deactivated.			
10100 = Transfer Card/Voucher expired.			
10101 = Card/Voucher Program Group can accept only physical card/vouchers.			
10102 = Card/Voucher Program Group does not support the auto generation of card/voucher numbers.			
10103 = Card/Voucher Program Group does not exists.			
10104 = BIN/IIN Code or Merchant prefix not configured in card/voucher program group.			
10105 = Amount being cancelled is exceeding the original transaction amount.			
10106 = BIN/IIN Code does not exists.			
10107 = Customer mobile number does not exists.			
10108 = Customer not holding any card/voucher.			
10109 = Card/Voucher Program Group is inactive.			
10110 = Card/Voucher Program Group not supported.			
10111 = Customer is inactive.			
10112 = Settlement already completed.			
10113 = Settlement Completed. Totals mismatch.			
10114 = Card/Voucher velocity limit reached.			
10115 = Entered amount is greater than the configured decimal places.			
10116 = Only integer amount is allowed.			
10117 = Conversion rate is not configured for this currency.			
10118 = Cannot cancel.Cancellation across merchants is not allowed.			
10119 = Your card has been temporarily deactivated for the day due to multiple attempts with invalid PIN. Please retry tomorrow.			
10120 = Inactive pos.			
10121 = Invalid TerminalID.			
10122 = POS does not exist.			
10123 = Invalid username or password.			
10124 = Invalid ForwardingEntityId or ForwardingEntityPwd.			
10125 = POSType Auth failed.			
10126 = Merchant Outlet Auth failed.			
10127 = Issuer could not be found.			
10128 = Access denied for performing the transaction.			
10129 = Merchant auth failed.			
10130 = Username not provided.			
10131 = User does not exist.			
10132 = Your Email Id is not provided. Please contact administrator.			
10133 = New password has been sent to your email.			
10134 = Merchant Group Auth failed.			
10135 = POS Auth failed.			
10136 = The user has been disabled.			
10137 = User authorization for merchant failed.			
10138 = User not authorized to login to this outlet.			
10139 = Customer is not validated. Cannot redeem.			
10140 = Parent card/voucher customer is not validated. Cannot redeem.			
10141 = Invoice Number format is incorrect.			
10142 = Cannot process transaction. Same request has already been processed.			
10143 = Card/Voucher Pin is mandatory.			
10144 = Notify message gateway details is not configured.			
10145 = Cannot process reverse. Card/Voucher last transaction validation failed.			
10146 = Balance is less than min redemption limit.			
10147 = Cannot process Redeem transaction. Same Invoice Number has already been processed.			
10148 = No transactions on card/voucher.			
10149 = Merchant does not exist.			
10150 = Merchant Outlet does not exist.			
10151 = Multiple card/voucher holders exists for the input data provided. Provide additional search criteria to narrow down customer.			
10152 = Customer is holding multiple cards/vouchers.			
10153 = Customer does not exist.			
10154 = Upgrade card/voucher program group does not exist.			
10155 = Upgrade card/voucher program group is inactive.			
10156 = Customer already exists with the same mobile number.			
10157 = City does not exist.			
10158 = State does not exist.			
10159 = Country does not exist.			
10160 = Customer record not associated with card/voucher.Cannot redeem.			
10161 = Customer record not associated with parent card/voucher.Cannot redeem.			
10162 = Either card/vouchernumber or self service pin is incorrect.			
10163 = Invoice number validation failed.			
10164 = Stored procedure returned response message.			
10165 = Actual merchant outlet does not exist.			
10166 = Invoice Number out of sequence.			
10167 = Invoice Number has been previously used.			
10168 = Inactive merchant outlet.			
10169 = Inactive actual merchant outlet.			
10170 = Transaction Date cannot be earlier than Card/Voucher Activation Date.			
10171 = TransactionTypeId is not provided.			
10172 = TransactionTypeId is incorrect.			
10173 = TransactionId is not provided.			
10174 = TransactionId is incorrect.			
10175 = ForwardingInstitutionType is incorrect.			
10176 = POSTypeId is not provided.			
10177 = POSTypeId is incorrect.			
10178 = UserId is not provided.			
10179 = Password is not provided.			
10180 = TerminalId is not provided.			
10181 = You cannot use future date as Transaction Date.			
10182 = DateAtClient is not provided.			
10183 = DateAtClient is incorrect.			
10184 = Invalid POS.			
10185 = AcquirerId is not provided.			
10186 = MerchantOutletName is not provided.			
10187 = SettlementDate is not provided.			
10188 = SettlementDate is incorrect.			
10189 = InvoiceNumber is not provided.			
10190 = Card/VoucherNumber is not provided.			
10191 = Access denied for this terminal to perform bulk transactions.			
10192 = Card/Voucher Number Entry not allowed.			
10193 = Card/VoucherNumber does not match with TrackData Card/VoucherNumber.			
10194 = Invalid Transaction Type.			
10195 = OriginalInvoiceNumber is not provided.			
10196 = Amount is not provided.			
10197 = Amount is incorrect.			
10198 = AddonCard/VoucherNumber is not provided.			
10199 = AddonCard/VoucherNumber does not match with AddonCard/VoucherTrackData Card/VoucherNumber.			
10200 = Card/VoucherProgramID is not provided.			
10201 = You can not initiate a transaction as Offline and Proxy at same time.			
10202 = ApprovalCode is not provided.			
10203 = MerchantOutletName and ActualMerchantOutletName can not be same.			
10204 = ActualMerchantOutletName is not provided.			
10205 = Not supporting Offline or Proxy transactions.			
10206 = Card/VoucherPIN is not provided.			
10207 = ForwardingEntityID is not provided.			
10208 = ForwardingEntityPwd is not provided.			
10209 = ReloadCount is not provided.			
10210 = ReloadCount is incorrect.			
10211 = ReloadAmount is not provided.			
10212 = ReloadAmount is incorrect.			
10213 = ActivationCount is not provided.			
10214 = ActivationCount is incorrect.			
10215 = ActivationAmount is not provided.			
10216 = ActivationAmount is incorrect.			
10217 = RedemptionCount is not provided.			
10218 = RedemptionCount is incorrect.			
10219 = RedemptionAmount is not provided.			
10220 = RedemptionAmount is incorrect.			
10221 = CancelLoadCount is incorrect.			
10222 = CancelLoadAmount is incorrect.			
10223 = CancelRedeemCount is incorrect.			
10224 = CancelRedeemAmount is incorrect.			
10225 = CancelActivationCount is incorrect.			
10226 = CancelActivationAmount is incorrect.			
10227 = POSEntryMode is not provided.			
10228 = POSEntryMode is incorrect.			
10229 = BINORIINCODE is not provided.			
10230 = OrgName is not provided.			
10231 = CurrentBatchNumber is not provided.			
10232 = CurrentBatchNumber is incorrect.			
10233 = BulkOperationID is not provided.			
10234 = BulkOperationID is incorrect.			
10235 = OriginalTransactionId is not provided.			
10236 = OriginalTransactionId is incorrect.			
10237 = OriginalApprovalCode is not provided.			
10238 = OriginalApprovalCode is incorrect.			
10239 = OriginalBatchNumber is not provided.			
10240 = OriginalBatchNumber is incorrect.			
10241 = Email is incorrect.			
10242 = DOB is incorrect.			
10243 = Anniversary is incorrect.			
10244 = CorporateName is not provided.			
10245 = Given invoice number outlet code is not same as merchant outlet code.			
10246 = Cancel Transaction Date cannot be earlier than Original Transaction Date.			
10247 = Online Transactions for Previous Financial Year cannot be processed.			
10248 = Cannot process offline or proxy transactions for previous year invoice number during current financial year.			
10249 = Card/Voucher not allocated to the current outlet. Cannot be activated / issued.			
10250 = Failed to get recent transactions.			
10251 = Voucher is already redeemed.			
10252 = Card/Voucher Expiry date incorrect format.			
10253 = POS already initialized. Please contact QwikCilver Support team for assistance.			
10254 = Cannot reload. Card/Voucher balance exceeding maximum card/voucher balance.			
10255 = Cannot activate. Card/Voucher balance exceeding maximum card/voucher balance.			
10256 = Execution of custom procedure for {0} transaction failed.			
10257 = Redemption amount is greater than total bill amount.			
10258 = Bill amount is incorrect.			
10259 = Invoice Number cannot be blank.			
10260 = Cancel all other transactions before cancelling the Issue/Activate transaction.			
10261 = Could not retrieve transaction log data..			
10262 = For self service balance enquiry either Card/Voucher Number or Mobile Number is required.			
10263 = Multiple customer entries found for the same mobile number.			
10264 = Multiple card/voucher exists for the same mobile number(customer).			
10265 = Cannot redeem. Cumulative amount spent is less than the minimum cumulative amount spent limit.			
10266 = Cumulative amount spent cannot be retrieved.			
10267 = Requested cancel activation amount does not match the present card/voucher balance	 kindly cancel all other transactions.		
10268 = Original card/voucher number not provided.			
10269 = Proxy transaction not enabled for this pos.			
10270 = Card/Voucher balance exceeding maximum card/voucher balance.			
10271 = Merchant outletname does not match the outlet of the POS.			
10272 = Not a valid customer information	 either First Name	 Last Name	 Mobile is missing.
10273 = Not a valid First Name	 should consist only alphabets.		
10274 = Not a valid Last Name	 should consist only alphabets.		
10275 = Not a valid mobile/phone number.			
10276 = Not a valid Email.			
10277 = Not a valid employee ID	 Only alphanumeric values are allowed.		
10278 = Not a valid area	 Only alphanumeric values are allowed.		
10279 = Not a valid pincode	 Only numeric values are allowed.		
10280 = Not a valid gender.			
10281 = Merchant does not allow multiple cards/vouchers for the same customer.			
10282 = Merchant does not allow same mobile numbers to be enrolled for different card/vouchers.			
10283 = Anniversary date should be greater than the Date of Birth.			
10284 = Not a valid Marital Status.			
10285 = Generating new card/voucher failed. Contact helpdesk.			
10286 = Failed to execute the procedure.			
10287 = Purchase order completed	 cannot activate more card/voucher(s) for the same PO.		
10288 = Activation amount exceeds total Net Activation Amount for this purchase order	 try with a lesser activation value.		
10289 = Original card/voucher already been purchased.			
10290 = Card/Voucher not reissued.			
10291 = Original card/voucher does not exist.			
10292 = Original card/voucher is already active.			
10293 = Original card/voucher not purchased.			
10294 = Original card/voucher has expired.			
10295 = Failed to fulfill Bulk Order	 Merchant Outlet or Invoice Number does not match with the original Bulk Order request.		
10296 = Total Bill Amount should be passed to proceed with the redeem transaction.			
10297 = Total Bill Amount is not in the accepted range.			
10298 = Not able to generate the Claim Code	 Please Contact helpdesk.		
10299 = Could not find the Claim code. Please enter valid Claim code.			
10300 = Duplicate Claim Code exists. Please enter a different Claim code.			
10301 = No cards/vouchers found for the given booklet number.			
10302 = One or More cards/vouchers are already activated in the booklet.			
10303 = Card/Voucher Count for Booklet Activation exceeds configured Max Booklet Card/Voucher Count.			
10304 = Total Number of Cards/Vouchers exceed the Maximum Transactions Limit.			
10305 = Expiry date not provided.			
10306 = Activation of expired card/voucher not allowed.			
10307 = Database connect failed. Contact Admin.			
10308 = Card/Voucher number can contain only alphabets and numbers.			
10309 = Card/Voucher number length exceeds expected length.			
10310 = No matching records found.			
10311 = Begin Date or Invoice Number or Card/Voucher Number Mandatory.			
10312 = Invalid Idempotent Request - Parameters Mismatch or Validity Period Passed.			
10313 = Invalid Idempotency Key.			
10314 = Either fixed denomination value is invalid or not set.			
10315 = Cannot genearate activation url.			
10316 = Cannot genearate unique indentification code.			
10317 = Card/Voucher activation not allowed.			
10318 = Cross currency reissue not allowed.			
10319 = Failed to update customer information.			
10320 = Redemption not allowed out of specified time range.			
10321 = Validation Failed. Could not find corresponding value transfer redeem transaction.			
10322 = Invalid fund type.			
10323 = Cannot associate customer with card/voucher. Customer search criteria not specified.			
10324 = Could not find svc limit card/voucher.			
10326 = Insufficient customer fund.			
10327 = Insufficient merchant fund.			
10328 = BookletNumber is not provided.			
10329 = Could not find booklet. Please enter valid booklet number.			
10330 = Transaction already completed.			
10331 = Missing arguments on rule.			
10332 = Failed to apply rule.			
10333 = Discount amount is incorrect.			
10334 = Discount percentage is incorrect.			
10335 = Discount typeid is incorrect.			
10336 = Payment modeid is incorrect.			
10337 = Customer Already Associated With Another Card.			
10338 = Password reset email will be sent to your registered email Id. Please contact your system administrator if you do not receive email.			
10339 = Amount more than Annual Reloadable limit.			
10340 = Encountered duplicate request. Please retry after sometime.			
10341 = Cannot reload. Card/Voucher balance exceeding maximum card/voucher balance or Amount more than reload limit.			
10342 = Universal Product Code does not exists.			
10343 = Invalid card/voucher status.			
10344 = Invalid PreAuth Code.			
10345 = PreAuth already completed.			
10346 = PreAuth already cancelled.			
10347 = Cannot process transaction	 input mismatch.		
10348 = Card/Voucher is reserved.			
10349 = Bulk Order already Submitted.			
10350 = Bulk Order failed or Cancelled.			
10351 = Total Bill Amount should be passed to proceed with the preauth transaction.			
10352 = Card/Voucher cannot be generated. Reached maximum range.			
10353 = Cannot process reverse transaction.			
10354 = Transaction Partially Successful.			
10355 = Cancel Redemption not allowed out of specified time range.			
10356 = Card PIN Expired.			
10357 = Atleast one primary filter must be provided to get Customer details.			
10358 = Expiry Date cannot be a past date.			
10359 = Invalid preAuth type.			
10360 = Cancel all other transactions before cancelling the reissue transaction.			
10361 = Invalid currency code.			
10362 = This currency is not supported by the issuer.			
10363 = This currency is not supported by the merchant.			
10364 = Currency should be the same as the original transaction.			
10365 = Card is already registered.			
10367 = Card does not belong to same original transaction.			
10368 = Currency conversion not allowed.			
10369 = Organisation not found.			
10370 = Merchant has No Outlets.			
10371 = Merchant has No Active Outlets.			
10372 = Card already exists.			
10373 = Special characters not allowed in card number.			
10374 = No search filter provided.			
10375 = Imported Cards are not supported for this merchant.			
10401 = Transaction is in progress.			
10414 = Third Party Service Request Validation Failed.			
10415 = Third Party Merchant Setting Not Found.			
10416 = Third Party Api Internal  Error.			
10417 = External Merchant Mapping Not Found.			
10418 = Third Party Service Failed.			
10419 = External Card Not Found.			
10420 = Card Not Processed.			
10421 = Third Party Cancellation Not Allowed.			
10422 = Third Party Transaction Not Supported.			
10521 = Wallet Creation Failed.			
10522 = Wallet Program Group Does Not Exist.			
10525 = Wallet - Card/Voucher Addition Failed.			
10527 = Wallet - Card/Voucher Removal Failed.			
10528 = Wallet - Card/Voucher Already Added.			
10529 = Wallet - No Redeemable Cards Available.			
10530 = Wallet - Zero Balance.			
10531 = Wallet - Invalid Input Arguments.			
10532 = Wallet Not Found.			
10533 = Wallet Fund Type Does Not Exist.			
10534 = No Card/Voucher Program Group Mapped to this Wallet Bucket Type.			
10535 = Generating new wallet failed. Contact helpdesk.			
10536 = Wallet Number Not Found.			
10537 = Customer Already Associated With Existing Wallet.			
10538 = InCorrect Wallet Pin.			
10539 = Invalid Wallet Bucket.			
10540 = Invalid Wallet Pin Attempts Limit Reached.			
10541 = Failure to Reset Invalid Wallet Pin Attempts.			
10542 = Wallet - Customer Email Not Provided.			
10543 = Wallet - Customer Phone/Mobile Not Provided.			
10544 = Wallet - External Wallet Id Not Provided.			
10545 = Wallet already exists with same External WalletID.			
10546 = Wallet - Customer already exists with same Phone Number.			
10547 = Wallet - Customer already exists with same EmailID.			
10548 = Card/VoucherProgramGroup Not Associated with this Wallet Program.			
10549 = BIN/IIN Code or Merchant prefix not configured in Wallet Program.			
10550 = Card/Voucher Already Associated With Another Wallet. Card/Voucher Cannot be Shared.			
10551 = Wallet is Deactivated!.			
10552 = Failed To Update Wallet Status!.			
10553 = Wallet is Already Deactivated!.			
10554 = Wallet is Already Activated!.			
10555 = Wallet Customer FirstName or LastName Mandatory.			
10556 = Card/Voucher Not Available In Wallet. Add the Card/Voucher to Wallet First.			
10557 = Excluded BucketNames not found.			
10558 = Wallet Bucket Name Not Provided.			
10559 = Merchant not authorized to accept this wallet.			
10560 = WalletNumber is not provided.			
10561 = Given Include Transactions value is either invalid or not supported by the service.			
10562 = Given Include Card Transactions value is either invalid or not supported by the service.			
10563 = Wallet redemption failed.			
10564 = Number of fund types exceed maximum limit.			
10600 = Third party balance enquiry failed.			
10601 = Third party card/voucher creation failed.			
10602 = Third party card/voucher cancel activate failed.			
10603 = Required data not provided.			
10604 = Failed to update transaction log.			
10605 = Failed to grant external card/vouchernumber.			
10606 = Failed to revoke external card/vouchernumber.			
10607 = No vouchers are available for this specific requested Denomination.			
10608 = Proxy transaction is not allowed for this Merchant.			
10609 = Booklet number entry not allowed.			
10610 = Booklet number does not match with track.			
10611 = Booklet has no leaflets.			
10612 = Booklet leaflet and request amount does not match.			
10613 = Booklet not redeemable.			
10614 = Booklet has zero or nil balance.			
10615 = Booklet is lost or stolen.			
10616 = Booklet is deactivated.			
10617 = Booklet is in created state.			
10618 = Booklet has expired.			
10619 = Booklet is in purchased state.			
10620 = Booklet Inactive.			
10621 = Updation of Booklet status has failed.			
10622 = Invalid Booklet Pin Attempts Limit Reached.			
10623 = Invalid Booklet PIN.			
10624 = Booklet leaflet amount does not match.			
10625 = Booklet already activated.			
10626 = Cannot activate booklet.			
10627 = Number of booklet leaflets are less than actual leaflet count.			
10628 = Number of booklet leaflets are more than actual leaflet count.			
10629 = One or more leaflet(s) do not belong to given booklet.			
10630 = Cannot allow booklet activation across merchant outlets.			
10631 = Booklet is partially activated.			
10632 = Booklet activation is not allowed.			
10633 = Failed to parse json data.			
10634 = Failed to update booklet.			
10635 = Duplicate card/voucher entries are not allowed.			
10636 = Booklet Pin is mandatory.			
10637 = Failed to get booklet details.			
10638 = Program parameters not available.			
10639 = FromDate is incorrect.			
10640 = ToDate is incorrect.			
10641 = TransactionTypeIdToFilter is incorrect.			
10642 = NoOfTransactions is incorrect.			
10643 = PosType not supported.			
10644 = NoOfTransactions not provided.			
10645 = StartIndex should be greater than 0.			
10646 = Date range exceeds the limit.			
10647 = NoOftransactions exceeds the limit.			
10648 = Could not update card/voucher.			
10650 = Card expiry date cannot be earlier than card activation date.			
10651 = Card Pin entry is not allowed.			
10700 = User ID / password is invalid.			
10701 = Please check input ItemId is Invalid.			
10702 = Please check input Denomination should be greater than 0.			
10703 = Error while creating Giftcards/vouchers.			
10704 = Json Data is not valid	 Please try again with valid data.		
10705 = Denomination is not valid	 Please try again with valid denomination.		
10706 = Quantity is not valid	 Please try again with valid Quantity.		
10707 = Invalid Item Id.			
10708 = Transaction has been refunded/ marked for refund.			
10709 = Payment details are not available for given transaction Id.			
10710 = Invalid Response from Integration Partner.			
10711 = Product not available.			
10712 = Reference no is already exists.			
10713 = Price is not available.			
10714 = Requested Theme is not matching.			
10715 = Internal error occured.			
10716 = Requested Product is not available at the moment.			
10717 = Product Price is not available or not a valid amount.			
10718 = Product quantity is not available.			
10719 = Requested Product quantity is not valid.			
10720 = Requested Product id is not valid.			
10721 = Requested Price is lower than available price.			
10722 = Requested Price is higher than available price.			
10723 = Product details are required to create order.			
10724 = Amount to redeem is not a valid amount.			
10725 = PO Number is not available.			
10726 = Invalid Payment Method.			
10727 = Payment amount is not matching.			
10728 = Invalid Payment Method.			
10729 = This payment exceeds limitations.			
10730 = Unable to create order.			
10731 = Order can be placed as customer only.			
10732 = Requested Product is not available for this store.			
10733 = Handling charge option is not enabled for this product.			
10734 = Resend not allowed for this order. Order has been cancelled.			
10735 = Your order is being processed. Please try again later for card/voucher details.			
10736 = Order is not available.			
10737 = Please enter a valid reference number.			
10738 = This action is not allowed.			
10739 = This action is not allowed for guest.			
10740 = Not possible to cancel this order. Please contact Helpdesk for assistance.			
10741 = This operation is not supported. Please contact Helpdesk for assistance.			
10742 = Cannot perform Transaction. Maximum Wallet Balance limit would be exceeded.			
10743 = Invalid Authorization Token.			
10744 = Authorization Token Expired.			
10745 = Invalid Authorization Type.			
10746 = Invalid Validation Token.			
10747 = Validation Token Expired.			
10748 = idempotencyKey exceeds max length.			
10749 = invoiceNumber exceeds max length.			
10750 = notes exceeds max length.			
10751 = actualMerchantOutletName exceeds max length.			
10752 = messageId exceeds max length.			
10753 = sourceId exceeds max length.			
10754 = orderNumber exceeds max length.			
10755 = corporateName exceeds max length.			
10756 = salutation exceeds max length.			
10757 = firstName exceeds max length.			
10758 = lastName exceeds max length.			
10759 = mobile exceeds max length.			
10760 = address1 exceeds max length.			
10761 = address2 exceeds max length.			
10762 = address3 exceeds max length.			
10763 = email exceeds max length.			
10764 = city exceeds max length.			
10765 = state exceeds max length.			
10766 = country exceeds max length.			
10767 = empid exceeds max length.			
10768 = lineItemNo exceeds max length.			
10769 = card/voucherNumber exceeds max length.			
10770 = card/voucherProgramGroupName exceeds max length.			
10771 = card/voucherPin exceeds max length.			
10772 = productCode  exceeds max length.			
10773 = designCode exceeds max length.			
10774 = trackData exceeds max length.			
10775 = paymentDetails exceeds max length.			
10800 = Unable to Generate Sequence Number.			
10801 = Sequence Number Generation Flags are Not Enabled.			
10802 = Unable to Update Card/Voucher Sequence Number.			
10803 = Customer Validation Status is not provided.			
10804 = Invalid Customer Validation Status.			
10805 = Customer Information not found.			
10806 = Reload is not allowed for this customer status type.			
10807 = Monthly redemption limit reached.			
10808 = Integration Partner Timed out.			
10809 = Transaction Failed at Integration Partner end.			
10810 = Transaction Type is not provided.			
10811 = Idempotency Key is not provided.			
10812 = Order items is not provided.			
10813 = Input type is not provided.			
10814 = Product Code is not provided.			
10815 = StartSequenceNumber is not provided.			
10816 = EndSequenceNumber is not provided.			
10817 = Card/VoucherNumbers is not provided.			
10818 = AmountPerCard/Voucher is not provided.			
10819 = NumberOfCards/Vouchers is not provided.			
10820 = Reference Number is not provided.			
10821 = Bulk Start and End Sequence Id not consistent with the Number of Cards/Vouchers.			
10822 = Bulk Sequence Validation Failed.			
10823 = Duplicate Store Id.			
10824 = Merchant not active.			
10825 = Duplicate Seller Order id.			
10826 = Signature creation error.			
10827 = Invalid Input Parameter from Merchant.			
10828 = Third Party Service Unavailable.			
10829 = Third Party Service gateway error.			
10830 = actionType not Provided.			
10831 = orderType not Provided.			
10832 = startCard/VoucherInfo not Provided.			
10833 = endCard/VoucherInfo  not Provided.			
10834 = card/voucherInfo not Provided.			
10835 = transactionModeId not Provided.			
10836 = Exceeded Max Cards/Vouchers per LineItem for BULK API Request.			
10837 = Exceeded Max Cards/Vouchers per BULK API Request.			
10838 = Validation Failed.			
10839 = Invalid actionType .			
10840 = Invalid transactionModeId .			
10841 = Invalid transactionTypeId.			
10842 = Invalid orderType .			
10843 = lineItemNo not provided.			
10844 = Invalid inputType .			
10845 = paymentDetails not provided.			
10846 = paymentModeId not provided.			
10847 = purchaserInfo not provided.			
10848 = Invalid Input for isSync .			
10849 = Invalid Input for sendOnlyFailedCards/Vouchers .			
10850 = card/voucherholderInfo  not provided.			
10851 = Invalid invoiceDate Format.			
10852 = invoiceamount is incorrect.			
10853 = firstName not provided.			
10854 = lastName not provided.			
10855 = Invalid number of cards/vouchers per lineitem.			
10856 = Activation amount lesser than total Net Activation Amount for this purchase order	 try with a higher activation value.		
10857 = ScanCode Expired.			
10858 = This card cannot be used by other than card holder.			
10859 = Not a valid beneficiary	 either firstname	lastname	mobile is missing.
10860 = Duplicate Lineitem No.			
10861 = Unable to send forgot password link. Please contact your system administrator.			
10862 = Invalid amountPerCard/Voucher in lineItems.			
10863 = Invalid request.			
10864 = Validation successful.			
10865 = Invalid range. Starting and Ending card/voucher number should belong to the same card/voucher program group.			
10866 = Transaction failed. Unknown error.			
10867 = Transaction failed. Response validation failed.			
10868 = Transaction completed with system errors.			
10869 = None of the beneficiaries is allowed to use this card.			
10870 = Transaction Failed.			
10871 = Transaction Reversed.			
10872 = Transaction Skipped.			
10873 = Third Party Service gateway timeout.			
10874 = Invalid Barcode.			
10875 = Barcode Expired.			
10876 = Redeem Start Date must be greater than or equal to current date.			
10877 = Expiry date must be greater than or equal to Redeem start date.			
10878 = Redemption start date format is incorrect.			
10879 = Generated card is invalid. Please check BIN/IIN Code and Merchant Prefix.			
10880 = CardPin generation failed.			
10881 = One beneficiary should be set to primary.			
10882 = Multiple beneficiaries are not allowed.			
10883 = Your account is temporarily locked.			
10884 = Your account has expired.			
10885 = Cannot pass CardNumber and SequenceNumber together in request.			
10886 = Not a valid SequenceNumber	 Only numeric values are allowed.		
10887 = Duplicate sequence number entries are not allowed.			
10888 = General Ledger does not exist.			
10889 = Cost Centre does not exist.			
10990 = One Time Barcode Not Supported for this CPG.			
10991 = Barcode Generation Failed.			
10993 = The Merchant is not allowed to perform the transaction on this fund type.			
10994 = Either Universal Product Code/GTIN or Design Code is mandatory.			
10995 = Invalid Universal Product Code/GTIN or Design Code.			
10996 = Publish to queue failed.			
11000 = Invalid Data.Entered Data size is more than expected size.			
11001 = No Stored Values Available for this card/voucher.			
11002 = Wallet cannot be generated. Reached maximum range.			
11003 = This transaction is not allowed on the card.			
11004 = Duplicate slab info.			
11005 = Sequence Number deallocation failed.			
11006 = Exceeded allocation limit for the sequence.			
11007 = Card count mismatch for the slab allocated.			
20000 = Card/Voucher can be redeemed after next day of Purchase or Recharge.			
20001 = Cannot Process Redeem Transaction during End Of Season Sale.			
20002 = Enrolling members into Gold Tier is not supported at stores.			
20003 = Redemption is blocked till 14th September 2010.			
20004 = Invalid reference number.			
20005 = Load operation is not supported for Referral program cards/vouchers.			
20006 = Old Voucher/Card Balance is 0			
20007 = Old Voucher/Card denomination does not match with new Voucher/Card			
30001 = Outlet Name is mandatory.			
30002 = Outlet already exists.			
30003 = AddressLine1 is mandatory.			
30004 = Contact First Name is mandatory.			
30005 = Contact last Name is mandatory.			
30006 = Phone Number is mandatory.			
30007 = City is mandatory.			
30008 = State is mandatory.			
30009 = Country is mandatory.			
30010 = PinCode is mandatory.			
30011 = Area is mandatory.			
30012 = InvoiceNumberPrefix is mandatory.			
30013 = Timezone does not exists.			
30014 = MerchantOutletType does not exists.			
30015 = Merchant is mandatory.			
30016 = MerchantOutletCode is mandatory.			
30017 = MerchantOutletCode already exists.			
30018 = Reference Number is mandatory.			
30019 = Invalid Merchant.			
30020 = Invalid Outlet Status.			
30050 = Invalid OutletName.			
30051 = Invalid Outlet Group.			
30052 = Invalid MID.			
35000 = Parameter is mandatory.			
35001 = Parameter is invalid.			
35002 = Parameter value is not in range.			
50001 = Xnp message moved to error queue.			
50002 = Bulkxnp message moved to error queue.			
