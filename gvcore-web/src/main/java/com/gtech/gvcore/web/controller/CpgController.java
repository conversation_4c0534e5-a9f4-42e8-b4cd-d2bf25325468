package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.cpg.*;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgByOutletCodeResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgVoucherInventoryResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgsByPageResponse;
import com.gtech.gvcore.service.CpgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年2月22日
 */
@RestController
@RequestMapping(value = "/gv/cpg")
@Api(value = "Cpg maintenance.", tags = {"GV Cpg Api"})
public class CpgController {

    @Autowired
    private CpgService cpgService;

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年2月22日
     */
    @ApiOperation(value = "Create cpg", notes = "Create cpg")
    @PostMapping(value = "/createCpg")
    public Result<Long> createCpg(@RequestBody @Validated CreateCpgRequest request) {

        return cpgService.createCpg(request);
    }

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年2月22日
     */
    @ApiOperation(value = "Update cpg", notes = "Update cpg")
    @PostMapping(value = "/updateCpg")
    public Result<String> updateCpg(@RequestBody @Validated UpdateCpgRequest request) {

        return cpgService.updateCpg(request);
    }

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年2月22日
     */
    @ApiOperation(value = "Update cpg status", notes = "Create cpg status")
    @PostMapping(value = "/updateCpgStatus")
    public Result<String> updateCpgStatus(@RequestBody @Validated UpdateCpgStatusRequest request) {

        return cpgService.updateCpgStatus(request);
    }

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年2月22日
     */
    @ApiOperation(value = "Query Cpgs By Page", notes = "Query Cpgs By Page")
    @PostMapping(value = "/queryCpgsByPage")
    public PageResult<QueryCpgsByPageResponse> queryCpgsByPage(@RequestBody @Validated QueryCpgsByPageRequest request) {

        return cpgService.queryCpgsByPage(request);
    }

    /**
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年3月7日
     */
    @ApiOperation(value = "get Cpg", notes = "get Cpg")
    @PostMapping(value = "/getCpg")
    public Result<GetCpgResponse> getCpg(@RequestBody @Validated GetCpgRequest request) {

        return cpgService.getCpg(request);
    }

    /**
     * <AUTHOR>
     * Get VPG denomination through outlets
     */
    @ApiOperation(value = "Query denomination by outlet code")
    @PostMapping(value = "/queryOutletDenomination")
    public Result<List<QueryCpgByOutletCodeResponse>> queryOutletDenomination(@RequestBody @Validated QueryOutletDenominationRequest queryOutletDenominationRequest) {
        return cpgService.queryOutletDenomination(queryOutletDenominationRequest.getOutletCode());
    }


    @ApiOperation(value = "Query cpg voucher inventory")
    @PostMapping(value = "/queryCpgVoucherInventory")
    public Result<QueryCpgVoucherInventoryResponse> queryCpgVoucherInventory(@RequestBody @Validated QueryCpgVoucherInventory queryCpgVoucherInventory) {
        return cpgService.queryCpgVoucherInventory(queryCpgVoucherInventory);
    }

}
