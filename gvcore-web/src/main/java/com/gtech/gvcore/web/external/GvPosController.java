package com.gtech.gvcore.web.external;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.annotation.NotSysLogger;
import com.gtech.gvcore.monitor.annotation.TrackObjects;
import com.gtech.gvcore.common.enums.GvPosActionTypeEnum;
import com.gtech.gvcore.common.enums.GvPosCommonResponseCodesEnum;
import com.gtech.gvcore.common.enums.GvPosTransactionTypesEnum;
import com.gtech.gvcore.common.exception.GvcoreUnknownException;
import com.gtech.gvcore.common.externalmapping.request.BulkCancelRedeemRequest;
import com.gtech.gvcore.common.request.authorize.AuthorizePayload;
import com.gtech.gvcore.common.request.authorize.AuthorizeRequest;
import com.gtech.gvcore.common.request.transaction.*;
import com.gtech.gvcore.common.request.voucher.GetVoucherInformationRequest;
import com.gtech.gvcore.common.response.authorize.AuthorizeResponse;
import com.gtech.gvcore.common.response.cancelredeem.APIBulkCancelRedeemResponse;
import com.gtech.gvcore.common.response.transaction.*;
import com.gtech.gvcore.common.response.voucher.GetVoucherInformationResponse;
import com.gtech.gvcore.external.GvPosService;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.context.TransactionContext;
import com.gtech.gvcore.web.config.Idempotent;
import com.gtech.gvcore.web.config.Idempotent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/gv/external")
@TrackObjects(description = "API")
@Api(value = "External API.", tags = { "External API" })
public class GvPosController {

	@Lazy
	@Autowired
	GvPosService gvPosService;


	@Value("${gv.sourceid:}")
	protected String sourceid;


	@Value("${gv.messageid:}")
	protected String messageid;

	@Value("${gv.pos.special.terminal:UV01_01}")
	private String specialTerminalId;

	@Autowired
	private TransactionDataService transactionDataService;

	/**
	 * This API is used to Initialize POS & get the authorization (JWT) token.
	 * 
	 * @param
	 * @return
	 */
	@ApiOperation(value = "Authorize")
	@PostMapping(value = "/authorize")
	@NotSysLogger(disableLogger = true)
	public ResponseEntity<AuthorizeResponse> authorize(@RequestBody @Validated Map<String, Object> map) {
		log.info("External API -authorize,Param={}", JSON.toJSONString(map));
		AuthorizeRequest param = JSON.parseObject(JSON.toJSONString(map), AuthorizeRequest.class);
		AuthorizeResponse authorizeResponse = new AuthorizeResponse();
		try {
			authorizeResponse = gvPosService.authorize(param);

			log.info("External API -authorize,Response={}", JSON.toJSONString(authorizeResponse));
		} catch (GvcoreUnknownException e) {
			log.error("External API -authorize,Error={0}", e);
			authorizeResponse.setResponseCode(Integer.valueOf(e.getCode()));
			authorizeResponse.setResponseMessage(e.getMessage());
			log.info("External API -authorize,Response={}", JSON.toJSONString(authorizeResponse));
			return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(authorizeResponse);
		}

		if (authorizeResponse.getResponseCode()!=0){
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(authorizeResponse);
		}

		return ResponseEntity.ok(authorizeResponse);

	}

	/**
	 * This API is used for synchronous bulk processing of Activate, Redeem and
	 * Validate.
	 *
	 * @param map 入参
	 * @return ResponseEntity<TransactionResponse> 出参
	 */
	@ApiOperation(value = "Transaction")
	@PostMapping(value = "/gc/bulk/transaction")
	@Idempotent
	@NotSysLogger(disableLogger = true)
	public ResponseEntity<TransactionResponse> transaction(@RequestBody @Validated Map<String, Object> map,
			@RequestHeader("Authorization") String authHeader) {
		TransactionRequest param = JSON.parseObject(JSON.toJSONString(map), TransactionRequest.class);
		TransactionResponse transactionResponse = new TransactionResponse();
		AuthorizePayload authorizePayload = new AuthorizePayload();
		try {
			authorizePayload = getAuthorizePayloadByAuthHeader(authHeader);// posCode

			TransactionContext.init(param,authorizePayload.getTerminalId(),authorizePayload.getCurrentBatchNumber());

			if (GvPosTransactionTypesEnum.BULK_ACTIVATE.getCode() == param.getTransactionTypeId()
					&& GvPosActionTypeEnum.VALIDATE.getCode().equals(param.getActionType())) {// 验证-激活
				transactionResponse = gvPosService.transactionValidateActivate(param, authorizePayload);

			} else if (GvPosTransactionTypesEnum.BULK_ACTIVATE.getCode() == param.getTransactionTypeId()
					&& GvPosActionTypeEnum.EXECUTE.getCode().equals(param.getActionType())) {// 激活
				transactionResponse = gvPosService.transactionExecuteActivate(param, authorizePayload);
			} else if (GvPosTransactionTypesEnum.BULK_REDEEM.getCode() == param.getTransactionTypeId()
					&& GvPosActionTypeEnum.VALIDATE.getCode().equals(param.getActionType())) {// 验证-使用
				transactionResponse = gvPosService.transactionValidateRedeem(param, authorizePayload);

			} else if (GvPosTransactionTypesEnum.BULK_REDEEM.getCode() == param.getTransactionTypeId()
					&& GvPosActionTypeEnum.EXECUTE.getCode().equals(param.getActionType())) {// 使用
				transactionResponse = gvPosService.transactionExecuteRedeem(param, authorizePayload);
			}

			transactionResponse.setSourceId(sourceid);
			transactionResponse.setMessageId(messageid);
			if (StringUtils.isEmpty(param.getOrderNumber())){
				transactionResponse.setOrdernumber(String.valueOf(RandomUtils.nextInt(0, 999999)));
			}

		} catch (GvcoreUnknownException e) {
			transactionResponse.setResponseCode(Integer.valueOf(e.getCode()));
			transactionResponse.setResponseMessage(e.getMessage());
			transactionResponse.setSourceId(sourceid);
			transactionResponse.setMessageId(messageid);
			transactionResponse.setTransactionId(param.getTransactionId());
			transactionResponse.setTransactionTypeId(param.getTransactionTypeId());
			transactionResponse.setActionType(Integer.valueOf(param.getActionType()));
			transactionResponse.setBatchId(0);
			transactionResponse.setTotalSuccessCardCount(0);
			transactionResponse.setTotalCardCount(0);
			transactionResponse.setTransactionAmount(BigDecimal.ZERO);
			transactionResponse.setValidationToken(null);
			transactionResponse.setApprovalCode(null);
			transactionResponse.setReferenceNumber(null);
			transactionResponse.setOrdernumber(param.getOrderNumber());
			transactionResponse.setLineItems(null);
			transactionResponse.setInvalidLineItems(null);

			log.debug("External API -transaction,Error={0}", e);
			if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.parseInt(e.getCode())) {
				log.debug("External API -transaction,Response={}", JSON.toJSONString(transactionResponse));
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(transactionResponse);
			}
			//transactionDataService.requestLog(param, authorizePayload.getTerminalId(), ResponseEntity.status(HttpStatus.BAD_REQUEST).body(transactionResponse),authorizePayload.getCurrentBatchNumber());

			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(transactionResponse);
		} catch (GTechBaseException e) {
			transactionResponse.setTransactionId(param.getTransactionId());
			transactionResponse.setResponseCode(Integer.valueOf(e.getCode()));
			transactionResponse.setResponseMessage(e.getMessage());
			//transactionDataService.requestLog(param, authorizePayload.getTerminalId(), ResponseEntity.status(HttpStatus.BAD_REQUEST).body(transactionResponse),authorizePayload.getCurrentBatchNumber());

			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(transactionResponse);
		}
		log.debug("External API -transaction,Response={}", JSON.toJSONString(transactionResponse));

		if (transactionResponse.getResponseCode()!=0){
			//transactionDataService.requestLog(param, authorizePayload.getTerminalId(), ResponseEntity.status(HttpStatus.BAD_REQUEST).body(transactionResponse),authorizePayload.getCurrentBatchNumber());

			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(transactionResponse);
		}

		//transactionDataService.requestLog(param, authorizePayload.getTerminalId(), ResponseEntity.ok(transactionResponse),authorizePayload.getCurrentBatchNumber());

		return ResponseEntity.ok(transactionResponse);
	}

	/**
	 * This API is used to cancel redemption of single card/voucher.
	 * 
	 * @param
	 * @return
	 */
	@ApiOperation(value = "Cancelredeem")
	@PostMapping(value = "/gc/cancelredeem")
	@NotSysLogger(disableLogger = true)
	public ResponseEntity<CancelredeemResponse> cancelredeem(@RequestBody @Validated Map<String, Object> map,
			@RequestHeader("Authorization") String authHeader) {
		log.info("External API -cancelredeem,Param={},authHeader={}", JSON.toJSONString(map), authHeader);
		CancelredeemRequest param = JSON.parseObject(JSON.toJSONString(map), CancelredeemRequest.class);
		CancelredeemResponse cancelredeemResponse = new CancelredeemResponse();
		try {
			String terminalId = getAuthorizePayloadByAuthHeader(authHeader).getTerminalId();// posCode
			String batchId = getAuthorizePayloadByAuthHeader(authHeader).getCurrentBatchNumber();// batchId
			cancelredeemResponse = gvPosService.cancelredeem(param, terminalId,batchId );
			cancelredeemResponse.setResponseCode(0);
		} catch (GvcoreUnknownException e) {
			cancelredeemResponse.setResponseCode(Integer.valueOf(e.getCode()));
			cancelredeemResponse.setResponseMessage(e.getMessage());
			log.error("External API -cancelredeem,Error={0}", e);
			if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.parseInt(e.getCode())) {
				log.info("External API -cancelredeem,Response={}", JSON.toJSONString(cancelredeemResponse));
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(cancelredeemResponse);
			}
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cancelredeemResponse);
		} catch (GTechBaseException e) {
			cancelredeemResponse.setResponseCode(Integer.valueOf(e.getCode()));
			cancelredeemResponse.setResponseMessage(e.getMessage());
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cancelredeemResponse);
		}
		log.info("External API -cancelredeem,Response={}", JSON.toJSONString(cancelredeemResponse));
		if (cancelredeemResponse.getResponseCode()!=0){
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cancelredeemResponse);
		}
		return ResponseEntity.ok(cancelredeemResponse);
	}

	/**
	 * This API is used to close the current batch, optionally validate count &
	 * amounts and generate a new batchid
	 * 
	 * @param
	 * @return
	 */
	@ApiOperation(value = "Batchclose")
	@PostMapping(value = "/batchclose")
	@NotSysLogger(disableLogger = true)
	public ResponseEntity<BatchcloseResponse> batchclose(@RequestBody @Validated Map<String, Object> map,
			@RequestHeader("Authorization") String authHeader) {
		log.info("External API -batchclose,Param={},authHeader={}", JSON.toJSONString(map), authHeader);
		BatchcloseRequest param = JSON.parseObject(JSON.toJSONString(map), BatchcloseRequest.class);
		BatchcloseResponse batchcloseResponse = new BatchcloseResponse();

		try {
			String terminalId = getAuthorizePayloadByAuthHeader(authHeader).getTerminalId();// posCode
			batchcloseResponse = gvPosService.batchclose(param, terminalId,authHeader );
			batchcloseResponse.setResponseCode(0);
			batchcloseResponse.setResponseMessage("Transaction successful.");
		} catch (GvcoreUnknownException e) {
			batchcloseResponse.setResponseCode(Integer.valueOf(e.getCode()));
			batchcloseResponse.setResponseMessage(e.getMessage());
			log.error("External API -batchclose,Error={0}", e);
			if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.parseInt(e.getCode())) {
				log.info("External API -batchclose,Response={}", JSON.toJSONString(batchcloseResponse));
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(batchcloseResponse);
			}
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(batchcloseResponse);
		} catch (GTechBaseException e) {
			batchcloseResponse.setResponseCode(Integer.valueOf(e.getCode()));
			batchcloseResponse.setResponseMessage(e.getMessage());
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(batchcloseResponse);
		}
		log.info("External API -batchclose,Response={}", JSON.toJSONString(batchcloseResponse));

		if (batchcloseResponse.getResponseCode()!=0){
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(batchcloseResponse);
		}
		return ResponseEntity.ok(batchcloseResponse);
	}

	/**
	 * This API is used to create a new gift card and activate it. Customer info can
	 * be passed as part of request to map customer to the card.
	 * 
	 * @param
	 * @return
	 */
	@ApiOperation(value = "Createandissue")
	@PostMapping(value = "/gc/createandissue")
	@NotSysLogger(disableLogger = true)
	public ResponseEntity<CreateandissueResponse> createandissue(@RequestBody @Validated Map<String, Object> map,
			@RequestHeader("Authorization") String authHeader) {
		log.info("External API -createandissue,Param={},authHeader={}", JSON.toJSONString(map), authHeader);
		CreateandissueRequest param = JSON.parseObject(JSON.toJSONString(map), CreateandissueRequest.class);
		CreateandissueResponse createandissueResponse = new CreateandissueResponse();
		try {

			// posCode
			String terminalId = getAuthorizePayloadByAuthHeader(authHeader).getTerminalId();
			String batchNumber = getAuthorizePayloadByAuthHeader(authHeader).getCurrentBatchNumber();
			createandissueResponse = gvPosService.createandissue(param, terminalId,batchNumber);

		} catch (GvcoreUnknownException e) {
			createandissueResponse.setResponseCode(Integer.valueOf(e.getCode()));
			createandissueResponse.setResponseMessage(e.getMessage());
			log.error("External API -createandissue,Error={}", JSON.toJSONString(e.getMessage()));
			if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.parseInt(e.getCode())) {
				log.info("External API -createandissue,Response={}", JSON.toJSONString(createandissueResponse));
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createandissueResponse);
			}
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(createandissueResponse);
		} catch (GTechBaseException e) {
			createandissueResponse.setResponseCode(Integer.valueOf(e.getCode()));
			createandissueResponse.setResponseMessage(e.getMessage());
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(createandissueResponse);
		}
		log.info("External API -createandissue,Response={0}", JSON.toJSONString(createandissueResponse));

		if (createandissueResponse.getResponseCode()!=0){
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(createandissueResponse);
		}
		return ResponseEntity.ok(createandissueResponse);
	}





	@ApiOperation(value = "CancelCreateandissue")
	@PostMapping(value = "/gc/cancelcreateandissue")
	@NotSysLogger(disableLogger = true)
	public ResponseEntity<CancelCreateandissueResponse> cancelcreateandissue(@RequestBody @Validated Map<String, Object> map,
																 @RequestHeader("Authorization") String authHeader) {
		log.info("External API -createandissue,Param={},authHeader={}", JSON.toJSONString(map), authHeader);
		CancelCreateandissueRequest param = JSON.parseObject(JSON.toJSONString(map), CancelCreateandissueRequest.class);
		CancelCreateandissueResponse createandissueResponse = new CancelCreateandissueResponse();
		try {
			String terminalId = getAuthorizePayloadByAuthHeader(authHeader).getTerminalId();// posCode
			createandissueResponse = gvPosService.cancelcreateandissue(param, terminalId);
			createandissueResponse.setResponseCode(0);
			createandissueResponse.setResponseMessage("Transaction Successful");
		} catch (GvcoreUnknownException e) {
			createandissueResponse.setResponseCode(Integer.valueOf(e.getCode()));
			createandissueResponse.setResponseMessage(e.getMessage());
			log.error("External API -createandissue,Error={0}", e);
			if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.valueOf(e.getCode())) {
				log.info("External API -createandissue,Response={0}", JSON.toJSONString(createandissueResponse));
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createandissueResponse);
			}
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(createandissueResponse);
		} catch (GTechBaseException e) {
			createandissueResponse.setResponseCode(Integer.valueOf(e.getCode()));
			createandissueResponse.setResponseMessage(e.getMessage());
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(createandissueResponse);
		}
		log.info("External API -createandissue,Response={0}", JSON.toJSONString(createandissueResponse));

		if (createandissueResponse.getResponseCode()!=0){
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(createandissueResponse);
		}
		return ResponseEntity.ok(createandissueResponse);
	}



	/**
	 * This API is used to cancel the activation of a gift card.
	 * 
	 * @param
	 * @return
	 */
	@ApiOperation(value = "Cancelactivate")
	@PostMapping(value = "/gc/cancelactivate")
	@NotSysLogger(disableLogger = true)
	public ResponseEntity<CancelactivateResponse> cancelactivate(@RequestBody @Validated Map<String, Object> map,
			@RequestHeader("Authorization") String authHeader) {
		log.info("External API -cancelactivate,Param={},authHeader={}", JSON.toJSONString(map), authHeader);
		CancelactivateRequest param = JSON.parseObject(JSON.toJSONString(map), CancelactivateRequest.class);
		CancelactivateResponse cancelactivateResponse = new CancelactivateResponse();
		try {
			String terminalId = getAuthorizePayloadByAuthHeader(authHeader).getTerminalId();// posCode
			String batchNumber = getAuthorizePayloadByAuthHeader(authHeader).getCurrentBatchNumber();// posCode
			cancelactivateResponse = gvPosService.cancelactivate(param, terminalId, batchNumber);
		} catch (GvcoreUnknownException e) {
			cancelactivateResponse.setResponseCode(Integer.valueOf(e.getCode()));
			cancelactivateResponse.setResponseMessage(e.getMessage());
			log.error("External API -cancelactivate,Error={}", e);
			if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.valueOf(e.getCode())) {
				log.info("External API -cancelactivate,Response={}", JSON.toJSONString(cancelactivateResponse));
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(cancelactivateResponse);
			}
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cancelactivateResponse);
		} catch (GTechBaseException e) {
			cancelactivateResponse.setResponseCode(Integer.valueOf(e.getCode()));
			cancelactivateResponse.setResponseMessage(e.getMessage());
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cancelactivateResponse);
		}
		log.info("External API -cancelactivate,Response={}", JSON.toJSONString(cancelactivateResponse));

		if (cancelactivateResponse.getResponseCode()!=0){
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cancelactivateResponse);
		}
		return ResponseEntity.ok(cancelactivateResponse);
	}


	/*public void doTest(HttpServletRequest request ) {
		String header = request.getHeader("content-type");
		String[] split = header.split(";");//multipart/form-data 需要分割，对其他请求无影响
		switch (split[0]) {
			case "application/json":
				StringBuffer buffer = new StringBuffer();
				String line = null;
				try {
					BufferedReader reader = request.getReader();
					while ((line = reader.readLine()) != null) {
						buffer.append(line);
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
				//buffer就是获取到的json，这里解析json即可
				break;
			case "application/x-www-form-urlencoded":
				String[] pages = request.getParameterValues("page");
				String[] pageSizes = request.getParameterValues("page_size");
				break;
			case "multipart/form-data":
				String[] pages1 = request.getParameterValues("page");
				String[] pageSizes1 = request.getParameterValues("page_size");
				break;
			default:
				System.out.println("未知请求:" + split[0]);
				break;
		}
	}*/

	/**
	 * This api will activate a gift card from PURCHASED state to ACTIVATED state.
	 * 
	 * @param
	 * @return
	 */
	@ApiOperation(value = "Activateonly")
	@PostMapping(value = "/gc/activateonly")
	@NotSysLogger(disableLogger = true)
	public ResponseEntity<ActivateonlyResponse> activateonly(@RequestBody @Validated Map<String, Object> map,
			@RequestHeader("Authorization") String authHeader) {
		log.info("External API -activateonly,Param={},authHeader={}", JSON.toJSONString(map), authHeader);

		Object customer = map.get("Customer");
		CustomerInfo jsonObject = JSONObject.parseObject((String) customer,CustomerInfo.class);
		map.put("Customer",jsonObject);


		ActivateonlyRequest param = JSON.parseObject(JSON.toJSONString(map), ActivateonlyRequest.class);
		ActivateonlyResponse activateonlyResponse = new ActivateonlyResponse();
		try {
			String terminalId = getAuthorizePayloadByAuthHeader(authHeader).getTerminalId();// posCode
			String batchNumber = getAuthorizePayloadByAuthHeader(authHeader).getCurrentBatchNumber();// posCode
			activateonlyResponse = gvPosService.activateonly(param, terminalId, batchNumber);
		} catch (GvcoreUnknownException e) {
			activateonlyResponse.setResponseCode(Integer.valueOf(e.getCode()));
			activateonlyResponse.setResponseMessage(e.getMessage());
			log.error("External API -activateonly,Error={}", e);
			if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.valueOf(e.getCode())) {
				log.info("External API -activateonly,Response={}", JSON.toJSONString(activateonlyResponse));
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(activateonlyResponse);
			}
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(activateonlyResponse);
		} catch (GTechBaseException e) {
			activateonlyResponse.setResponseCode(Integer.valueOf(e.getCode()));
			activateonlyResponse.setResponseMessage(e.getMessage());
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(activateonlyResponse);
		}
		log.info("External API -activateonly,Response={}", JSON.toJSONString(activateonlyResponse));
		if (activateonlyResponse.getResponseCode()!=0){
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(activateonlyResponse);
		}
		return ResponseEntity.ok(activateonlyResponse);
	}

	/**
	 * This API is used to obtain the transaction history on a card (Currently upto
	 * 30 transactions)
	 * 
	 * @param
	 * @return
	 */
	@ApiOperation(value = "Cardtransactionhistory")
	@PostMapping(value = "/gc/cardtransactionhistory")
	@NotSysLogger(disableLogger = true)
	public ResponseEntity<CardtransactionhistoryResponse> cardtransactionhistory(
			@RequestBody @Validated Map<String, Object> map, @RequestHeader("Authorization") String authHeader) {
		log.info("External API -cardtransactionhistory,Param={},authHeader={}", JSON.toJSONString(map), authHeader);
		CardtransactionhistoryRequest param = JSON.parseObject(JSON.toJSONString(map),
				CardtransactionhistoryRequest.class);
		CardtransactionhistoryResponse cardtransactionhistoryResponse = new CardtransactionhistoryResponse();
		try {
			String terminalId = getAuthorizePayloadByAuthHeader(authHeader).getTerminalId();// posCode
			cardtransactionhistoryResponse = gvPosService.cardtransactionhistory(param, terminalId);
		} catch (GvcoreUnknownException e) {
			cardtransactionhistoryResponse.setResponseCode(Integer.valueOf(e.getCode()));
			cardtransactionhistoryResponse.setResponseMessage(e.getMessage());
			log.error("External API -cardtransactionhistory,Error={}", e);
			if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.valueOf(e.getCode())) {
				log.info("External API -cardtransactionhistory,Response={}",
						JSON.toJSONString(cardtransactionhistoryResponse));
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(cardtransactionhistoryResponse);
			}
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cardtransactionhistoryResponse);
		} catch (GTechBaseException e) {
			cardtransactionhistoryResponse.setResponseCode(Integer.valueOf(e.getCode()));
			cardtransactionhistoryResponse.setResponseMessage(e.getMessage());
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cardtransactionhistoryResponse);
		}
		log.info("External API -cardtransactionhistory,Response={}", JSON.toJSONString(cardtransactionhistoryResponse));

		if (cardtransactionhistoryResponse.getResponseCode()!=0){
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cardtransactionhistoryResponse);
		}
		return ResponseEntity.ok(cardtransactionhistoryResponse);
	}

	/**
	 * This api is used to generate one time barcode with a limited expiry period
	 * for a virtual card number
	 * 
	 * @param
	 * @return
	 */
	@ApiOperation(value = "Onetimebarcode")
	@PostMapping(value = "/gc/onetimebarcode")
	@NotSysLogger(disableLogger = true)
	public ResponseEntity<OnetimebarcodeResponse> onetimebarcode(@RequestBody @Validated Map<String, Object> map,
			@RequestHeader("Authorization") String authHeader) {
		log.info("External API -onetimeBarcode,Param={},authHeader={}", JSON.toJSONString(map), authHeader);
		OnetimebarcodeRequest param = JSON.parseObject(JSON.toJSONString(map), OnetimebarcodeRequest.class);
		OnetimebarcodeResponse onetimebarcodeResponse = new OnetimebarcodeResponse();
		try {
			String terminalId = getAuthorizePayloadByAuthHeader(authHeader).getTerminalId();// posCode
			String batchNumber = getAuthorizePayloadByAuthHeader(authHeader).getCurrentBatchNumber();// posCode
			onetimebarcodeResponse = gvPosService.onetimebarcode(param, terminalId,batchNumber);
		} catch (GvcoreUnknownException e) {
			onetimebarcodeResponse.setResponseCode(Integer.valueOf(e.getCode()));
			onetimebarcodeResponse.setResponseMessage(e.getMessage());
			log.error("External API -onetimeBarcode,Error={0}", e);
			if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.valueOf(e.getCode())) {
				log.info("External API -onetimeBarcode,Response={}", JSON.toJSONString(onetimebarcodeResponse));
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(onetimebarcodeResponse);
			}
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(onetimebarcodeResponse);
		} catch (GTechBaseException e) {
			onetimebarcodeResponse.setResponseCode(Integer.valueOf(e.getCode()));
			onetimebarcodeResponse.setResponseMessage(e.getMessage());
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(onetimebarcodeResponse);
		}
		log.info("External API -onetimebarcode,Response={}", JSON.toJSONString(onetimebarcodeResponse));

		if (onetimebarcodeResponse.getResponseCode()!=0){
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(onetimebarcodeResponse);
		}
		return ResponseEntity.ok(onetimebarcodeResponse);
	}

	private AuthorizePayload getAuthorizePayloadByAuthHeader(String authHeader) {
		if (StringUtils.isBlank(authHeader) || !authHeader.startsWith("Bearer ")) {
			throw new GvcoreUnknownException(
					String.valueOf(GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode()),
					GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseMessage());
		}

		// 获取token
		String authToken = authHeader.substring(7);
		AuthorizePayload authorizePayload = gvPosService.getAuthorizePayloadByToken(authToken);
		authorizePayload.setAuthToken(authToken);
		log.info("External API -getAuthorizePayloadByAuthHeader,AuthorizePayload={}",
				JSON.toJSONString(authorizePayload));
		if (StringUtils.isBlank(authorizePayload.getTerminalId())) {
			throw new GvcoreUnknownException(
					String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()),
					GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
		}

		ServletRequestAttributes requestAttributes =
				(ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		requestAttributes.getRequest().getRequestURI();
		if (authorizePayload.getTerminalId().equals(specialTerminalId) && !requestAttributes.getRequest().getServletPath().equals("/gv/external/getvoucherinfo")){
			throw new GvcoreUnknownException(
					String.valueOf(GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED.getResponseCode()),
					GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED.getResponseMessage());
		}
		return authorizePayload;
	}


	@ApiOperation(value = "Get basic voucher information.",notes = "Get basic voucher information.")
	@PostMapping(value = "/getvoucherinfo")
	@NotSysLogger(disableLogger = true)
	public Result<GetVoucherInformationResponse> getVoucherInformation(@RequestBody @Validated GetVoucherInformationRequest request,@RequestHeader("Authorization") String authHeader) {
		log.info("External API -getVoucherInfo,Param={},authHeader={}", JSON.toJSONString(request),authHeader);
		AuthorizePayload authorizePayloadByAuthHeader = getAuthorizePayloadByAuthHeader(authHeader);
		return gvPosService.getVoucherInformation(request);
	}


	public ResponseEntity<BulkCancelRedeemResponseV2> bulkcancelredeem(BulkCancelRedeemRequestV2 request, String authHeader) {

		BulkCancelRedeemResponseV2 cancelredeemResponse = new BulkCancelRedeemResponseV2();
		try {
			String terminalId = getAuthorizePayloadByAuthHeader(authHeader).getTerminalId();// posCode
			String batchId = getAuthorizePayloadByAuthHeader(authHeader).getCurrentBatchNumber();// batchId
			cancelredeemResponse = gvPosService.bulkCancelRedeem(request, terminalId,batchId);
		} catch (GvcoreUnknownException e) {
			cancelredeemResponse.setResponseCode(Integer.valueOf(e.getCode()));
			cancelredeemResponse.setResponseMessage(e.getMessage());
			log.error("External API -cancelredeem,Error={0}", e);
			if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.parseInt(e.getCode())) {
				log.info("External API -cancelredeem,Response={}", JSON.toJSONString(cancelredeemResponse));
				return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(cancelredeemResponse);
			}
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cancelredeemResponse);
		} catch (GTechBaseException e) {
			cancelredeemResponse.setResponseCode(Integer.valueOf(e.getCode()));
			cancelredeemResponse.setResponseMessage(e.getMessage());
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cancelredeemResponse);
		}
		log.info("External API -cancelredeem,Response={}", JSON.toJSONString(cancelredeemResponse));
		if (cancelredeemResponse.getResponseCode()!=0){
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(cancelredeemResponse);
		}
		return ResponseEntity.ok(cancelredeemResponse);
	}
}
