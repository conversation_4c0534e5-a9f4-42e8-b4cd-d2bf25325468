package com.gtech.gvcore.web.controller.distribution;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.distribution.*;
import com.gtech.gvcore.common.response.distribution.DistributionEmailTemplateResponse;
import com.gtech.gvcore.service.distribution.DistributionEmailTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName GvDistributionEmailTemplateController
 * @Description 分发系统邮件模板
 * <AUTHOR>
 * @Date 2022/7/5 14:27
 * @Version V1.0
 **/
@RestController
@RequestMapping(value = "/gv/distribution/email")
@Api(value = "Distribution Email Template", tags = {"GV Distribution Email Template Api"})
public class DistributionEmailTemplateController {

    @Autowired
    private DistributionEmailTemplateService distributionEmailTemplateService;

    @PostMapping("/create")
    @ApiOperation(value = "创建分发邮件模板 ", notes = "响应内容为有邮件唯一标识符 templateCode")
    public Result<String> createEmailTemplate(@RequestBody @Validated CreateDistributionEmailTemplateRequest request) {

        final String templateCode = this.distributionEmailTemplateService.createEmailTemplate(request);

        return Result.ok(templateCode);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新分发邮件模板 ", notes = "响应内容为有邮件唯一标识符 templateCode")
    public Result<String> updateEmailTemplate(@RequestBody @Validated UpdateDistributionEmailTemplateRequest request) {

        final String templateCode = this.distributionEmailTemplateService.updateEmailTemplate(request);

        return Result.ok(templateCode);
    }

    @PostMapping("/status")
    @ApiOperation(value = "更新邮件模板的有效/无效状态 ", notes = "响应内容为有邮件唯一标识符 templateCode")
    public Result<String> changeStatus(@RequestBody @Validated ChangeEmailTemplateStatusRequest request) {

        final String templateCode = this.distributionEmailTemplateService.changeStatus(request);

        return Result.ok(templateCode);
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询全部分发邮件模板", notes = "不分页")
    public Result<List<DistributionEmailTemplateResponse>> queryEmailTemplates(@RequestBody @Validated QueryEmailTemplateRequest request) {

        final List<DistributionEmailTemplateResponse> templateList = this.distributionEmailTemplateService.queryEmailTemplates(request);

        return Result.ok(templateList);
    }

    @PostMapping("/preview/send")
    @ApiOperation(value = "发送预览邮件", notes = "保持和save一致的校验规则")
    public Result<Void> previewSend(@RequestBody @Validated PreviewSendEmailTemplateRequest request) {

        this.distributionEmailTemplateService.previewSend(request);

        return Result.ok();
    }

}
