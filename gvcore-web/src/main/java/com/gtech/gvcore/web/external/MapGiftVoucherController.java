package com.gtech.gvcore.web.external;

import com.alibaba.fastjson.JSON;
import com.gtech.gvcore.common.enums.GvPosCommonResponseCodesEnum;
import com.gtech.gvcore.common.exception.GvcoreUnknownException;
import com.gtech.gvcore.common.request.authorize.AuthorizePayload;
import com.gtech.gvcore.common.request.authorize.AuthorizeRequest;
import com.gtech.gvcore.common.request.v3.TransactionsRequest;
import com.gtech.gvcore.common.response.authorize.AuthorizeResponseV3;
import com.gtech.gvcore.common.response.v3.CreateAndIssueResponse;
import com.gtech.gvcore.external.GvPosService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2022/8/2 13:53
 */
@Slf4j
@RestController
@RequestMapping(value = "/gv/external/v3")
@Api(value = "External API.", tags = { "External API" })
public class MapGiftVoucherController {

    @Lazy
    @Autowired
    GvPosService gvPosService;



    @ApiOperation(value = "Authorize")
    @PostMapping(value = "/authorize")
    public ResponseEntity<AuthorizeResponseV3> authorize(@RequestBody  AuthorizeRequest param,
                                                         @RequestHeader("DateAtClient") String dateAtClient ) {
        log.debug("External API -authorize,Param={},DateAtClient={}", JSON.toJSONString(param));

        AuthorizeResponseV3 authorizeResponse = new AuthorizeResponseV3();
        try {
            authorizeResponse = gvPosService.authorizeV3(param);


            log.debug("External API -authorize,Response={}", JSON.toJSONString(authorizeResponse));
            return ResponseEntity.ok(authorizeResponse);
        } catch (GvcoreUnknownException e) {
            log.error("External API -authorize,Error={}", e);
            authorizeResponse.setResponseCode(Integer.valueOf(e.getCode()));
            authorizeResponse.setResponseMessage(e.getMessage());
            log.debug("External API -authorize,Response={}", JSON.toJSONString(authorizeResponse));
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(authorizeResponse);
        }
    }



    @ApiOperation(value = "V3Transactions")
    @PostMapping(value = "/gc/transactions")
    public ResponseEntity<CreateAndIssueResponse> transactions(@RequestBody @Validated TransactionsRequest  param,
                                                                 @RequestHeader("Authorization") String authHeader,
                                                               @RequestHeader("TransactionId") String transactionId) {
        CreateAndIssueResponse createandissueResponse = new CreateAndIssueResponse();
        try {
            String terminalId = getAuthorizePayloadByAuthHeader(authHeader).getTerminalId();// posCode
            String batchId = getAuthorizePayloadByAuthHeader(authHeader).getCurrentBatchNumber();// posCode
            param.setTransactionId(transactionId);
            param.setBatchId(batchId);
            createandissueResponse = gvPosService.transactionsCreateAndIssue(param, terminalId);
            createandissueResponse.setResponseCode(0);
            createandissueResponse.setResponseMessage("Transaction Successful");
        } catch (GvcoreUnknownException e) {
            createandissueResponse.setResponseCode(Integer.valueOf(e.getCode()));
            createandissueResponse.setResponseMessage(e.getMessage());
            log.error("External API -createandissue,Error={}", e);
            if (GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode() == Integer.valueOf(e.getCode())) {
                log.debug("External API -createandissue,Response={}", JSON.toJSONString(createandissueResponse));
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(createandissueResponse);
            }
        }
        log.debug("External API -createandissue,Response={}", JSON.toJSONString(createandissueResponse));
        return ResponseEntity.ok(createandissueResponse);
    }



    private AuthorizePayload getAuthorizePayloadByAuthHeader(String authHeader) {
        if (StringUtils.isBlank(authHeader) || !authHeader.startsWith("Bearer ")) {
            throw new GvcoreUnknownException(
                    String.valueOf(GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseCode()),
                    GvPosCommonResponseCodesEnum.AUTHORIZATION_FAILED_10744.getResponseMessage());
        }
        // 获取token
        String authToken = authHeader.substring(7);
        AuthorizePayload authorizePayload = gvPosService.getAuthorizePayloadByToken(authToken);
        authorizePayload.setAuthToken(authToken);
        log.debug("External API -getAuthorizePayloadByAuthHeader,AuthorizePayload={}",
                JSON.toJSONString(authorizePayload));
        if (StringUtils.isBlank(authorizePayload.getTerminalId())) {
            throw new GvcoreUnknownException(
                    String.valueOf(GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode()),
                    GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage());
        }
        return authorizePayload;
    }



}
