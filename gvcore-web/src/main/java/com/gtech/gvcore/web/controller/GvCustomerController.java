package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.customer.*;
import com.gtech.gvcore.common.response.customer.CustomerResponse;
import com.gtech.gvcore.service.CustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/16 17:36
 */
@RestController
@RequestMapping(value = "/gv/customer")
@Api(value = "Customer data dictionary.", tags = {"GV customer Api"})
public class GvCustomerController {


    @Autowired
    private CustomerService customerService;

    @ApiOperation(value = "Create customer ", notes = "Create customer information.")
    @PostMapping(value = "/createCustomer")
    public Result<String> createCustomer(@RequestBody @Validated CreateCustomerRequest param) {

        // Return result object
        return customerService.createCustomer(param);
    }


    @ApiOperation(value = "Query customer company name.", notes = "Query customer company name.")
    @PostMapping(value = "/queryCustomerCompanyName")
    public PageResult<String> queryCustomerCompanyName(@RequestBody @Validated QueryCustomerCompanyNameRequest param) {

        // Return result object
        return customerService.queryCustomerCompanyName(param);
    }

    @ApiOperation(value = "Update customer ", notes = "Update customer information.")
    @PostMapping(value = "/updateCustomer")
    public Result<Void> updateCustomer(@RequestBody @Validated UpdateCustomerRequest param) {

        // Return result object
        return customerService.updateCustomer(param);
    }


    @ApiOperation(value = "Update customer status ", notes = "Update customer status .")
    @PostMapping(value = "/updateCustomerStatus")
    public Result<Void> updateCustomerStatus(@RequestBody @Validated UpdateCustomerStatusRequest param) {

        // Return result object
        return customerService.updateCustomerStatus(param);
    }


    @ApiOperation(value = "Delete customer ", notes = "Delete customer information.")
    @PostMapping(value = "/deleteCustomer")
    public Result<Void> deleteCustomer(@RequestBody @Validated DeleteCustomerRequest param) {


        customerService.deleteCustomer(param);

        // Return result object
        return Result.ok();
    }


    @ApiOperation(value = "Query customer  list", notes = "Query customer information list.")
    @PostMapping(value = "/queryCustomerList")
    public PageResult<CustomerResponse> queryCustomerList(@RequestBody @Validated QueryCustomerRequest param) {

        // Return result object
        return customerService.queryCustomerList(param);
    }


    @ApiOperation(value = "Get customer ", notes = "Get customer information.")
    @PostMapping(value = "/getCustomer")
    public Result<CustomerResponse> getCustomer(@RequestBody @Validated GetCustomerRequest param) {


        CustomerResponse customer = customerService.getCustomer(param);

        // Return result object
        return Result.ok(customer);
    }



    @PostMapping("/queryCustomerListByUserCode")
    @ApiModelProperty(value = "Query customerList by userCode")
    public Result<List<CustomerResponse>> queryCustomerListByUserCode(@RequestBody @Validated QueryCustomerByUserCodeRequest email) {
        return customerService.queryCustomerListByUserCode(email);
    }



    /***************************************************************************************************************
     *
     * 1.Check whether the email exists and send the verification code
     * 2.Verify the mailbox verification code
     * 3.Create incomplete Customer information
     * 4.Check whether the user information is complete
     * author Dragon
     *
     */

    @PostMapping("/getEmailValidateCode")
    @ApiOperation(value = "Validate email exist，if exist get email validate code")
    public Result<Boolean> getEmailValidateCode(@RequestBody @Validated GetEmailValidateCodeRequest email) {
        return customerService.getEmailValidateCode(email.getEmail(),email.getIssuerCode());
    }

    @PostMapping(value = "/validateEmail")
    @ApiOperation(value = "Validate email")
    public Result<Boolean> validatedEmail(@RequestBody @Validated ValidateEmailRequest validateEmailRequest) {
        return customerService.validateEmail(validateEmailRequest);
    }

    @ApiOperation(value = "Create incomplete customer")
    @PostMapping(value = "/createIncompleteCustomer")
    public Result<String> createIncompleteCustomer(@RequestBody @Validated CreateIncompleteCustomerRequest createIncompleteCustomerRequest) {
        return customerService.createIncompleteCustomer(createIncompleteCustomerRequest);
    }

    @PostMapping("/checkCustomerInfo")
    @ApiOperation("Check customer info")
    public Result<Boolean> checkCustomerInfo(@RequestBody @Validated CheckCustomerInfoRequest checkCustomerInfo) {
        return customerService.checkCustomerInfo(checkCustomerInfo);
    }

    @PostMapping("/queryCustomerByEmail")
    @ApiModelProperty(value = "Query customers by email")
    public Result<List<CustomerResponse>> queryCustomerByEmail(@RequestBody @Validated GetEmailValidateCodeRequest email) {
        return customerService.queryCustomerByEmail(email);
    }
    //**************************************************************************************************************
}
