package com.gtech.gvcore.web.external.gc;

import com.gtech.gvcore.common.request.customerorder.SapSalesPostingXmlRequest;
import com.gtech.gvcore.external.GcPostingXmlService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping(value = "/gc/external/gcxml")
@Api(value = "GC Posting XML API.", tags = { "GC Posting XML API" })
public class GcPostingXmlController {

    @Autowired
    private GcPostingXmlService gcPostingXmlService;

    @PostMapping(value = "/allXml")
    public void allXml(@RequestBody SapSalesPostingXmlRequest request) throws ExecutionException, InterruptedException {
        Date queryTime = request.getQueryDate();

        CompletableFuture.runAsync(() -> {
            log.info("Executing GC XML generation task: WPUBON_C");
            gcPostingXmlService.generateWpubonC(queryTime);
        }).get();

        CompletableFuture.runAsync(() -> {
            log.info("Executing GC XML generation task: WPUBON");
            gcPostingXmlService.generateWpubon(queryTime);
        }).get();

        CompletableFuture.runAsync(() -> {
            log.info("Executing GC XML generation task: WPUUMS");
            gcPostingXmlService.generateWpuums(queryTime);
        }).get();
    }
} 