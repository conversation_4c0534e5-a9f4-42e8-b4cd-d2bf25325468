package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.monitor.annotation.TrackObjects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 监控测试控制器
 * 用于测试基于注解的对象监控功能
 */
@Slf4j
@RestController
@RequestMapping("/test/monitor")
public class MonitorTestController {

    /**
     * 测试基本的对象创建监控
     */
    @TrackObjects(description = "基本对象创建测试", threshold = 5)
    @GetMapping("/basic")
    public Map<String, Object> testBasicObjectCreation(@RequestParam(defaultValue = "10") int count) {
        log.info("开始基本对象创建测试，创建{}个对象", count);
        
        // 创建一些对象来测试监控
        List<String> list = new ArrayList<>();
        Map<String, Object> result = new HashMap<>();
        
        for (int i = 0; i < count; i++) {
            list.add("item" + i);
            result.put("key" + i, "value" + i);
            
            // 每5个创建一个临时对象
            if (i % 5 == 0) {
                List<Integer> tempList = new ArrayList<>();
                tempList.add(i);
            }
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("处理完成，共创建").append(count).append("个项目");
        
        result.put("message", sb.toString());
        result.put("items", list);
        result.put("count", count);
        
        log.info("基本对象创建测试完成");
        return result;
    }

    /**
     * 测试大量对象创建
     */
    @TrackObjects(description = "大量对象创建测试", threshold = 50)
    @GetMapping("/heavy")
    public Map<String, Object> testHeavyObjectCreation(@RequestParam(defaultValue = "100") int count) {
        log.info("开始大量对象创建测试，创建{}个对象", count);
        
        List<Map<String, Object>> results = new ArrayList<>();
        Map<String, List<String>> categories = new HashMap<>();
        
        for (int i = 0; i < count; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", i);
            item.put("name", "item" + i);
            item.put("category", "category" + (i % 10));
            
            List<String> tags = new ArrayList<>();
            tags.add("tag1");
            tags.add("tag2");
            item.put("tags", tags);
            
            results.add(item);
            
            // 分类统计
            String category = "category" + (i % 10);
            categories.computeIfAbsent(category, k -> new ArrayList<>()).add("item" + i);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("results", results);
        response.put("categories", categories);
        response.put("totalCount", count);
        
        log.info("大量对象创建测试完成");
        return response;
    }

    /**
     * 测试无监控的方法（没有注解）
     */
    @GetMapping("/no-monitor")
    public Map<String, Object> testNoMonitoring(@RequestParam(defaultValue = "20") int count) {
        log.info("开始无监控测试，创建{}个对象", count);
        
        // 这个方法没有@TrackObjects注解，不会被监控
        List<String> list = new ArrayList<>();
        Map<String, Object> result = new HashMap<>();
        
        for (int i = 0; i < count; i++) {
            list.add("item" + i);
            result.put("key" + i, "value" + i);
        }
        
        result.put("message", "无监控测试完成");
        result.put("items", list);
        
        log.info("无监控测试完成");
        return result;
    }

    /**
     * 测试禁用监控
     */
    @TrackObjects(enabled = false, description = "禁用监控测试")
    @GetMapping("/disabled")
    public Map<String, Object> testDisabledMonitoring(@RequestParam(defaultValue = "15") int count) {
        log.info("开始禁用监控测试，创建{}个对象", count);
        
        // 虽然有@TrackObjects注解，但enabled=false，所以不会被监控
        List<String> list = new ArrayList<>();
        Map<String, Object> result = new HashMap<>();
        
        for (int i = 0; i < count; i++) {
            list.add("item" + i);
            result.put("key" + i, "value" + i);
        }
        
        result.put("message", "禁用监控测试完成");
        result.put("items", list);
        
        log.info("禁用监控测试完成");
        return result;
    }

    /**
     * 测试特定对象类型监控
     */
    @TrackObjects(
        description = "特定对象类型测试", 
        objectTypes = {"ArrayList", "HashMap"},
        threshold = 3
    )
    @GetMapping("/specific-types")
    public Map<String, Object> testSpecificObjectTypes(@RequestParam(defaultValue = "8") int count) {
        log.info("开始特定对象类型测试，创建{}个对象", count);
        
        List<String> list = new ArrayList<>();  // 会被监控
        Map<String, Object> map = new HashMap<>();  // 会被监控
        StringBuilder sb = new StringBuilder();  // 不会被监控（不在objectTypes中）
        
        for (int i = 0; i < count; i++) {
            list.add("item" + i);
            map.put("key" + i, "value" + i);
            sb.append("item").append(i).append(",");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "特定对象类型测试完成");
        result.put("items", list);
        result.put("data", map);
        result.put("summary", sb.toString());
        
        log.info("特定对象类型测试完成");
        return result;
    }
}
