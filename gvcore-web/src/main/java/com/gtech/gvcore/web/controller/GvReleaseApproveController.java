package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.releaseapprove.GetNextAmountRequest;
import com.gtech.gvcore.common.request.releaseapprove.QueryLogByBusinessCodeRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAmountListRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAmountRequest;
import com.gtech.gvcore.common.response.releaseapprove.ApproveNodeRecordResponse;
import com.gtech.gvcore.common.response.releaseapprove.GetNextAmountResponse;
import com.gtech.gvcore.common.response.releaseapprove.ReleaseApproveAmountResponse;
import com.gtech.gvcore.service.ReleaseApproveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 14:56
 */

@RestController
@RequestMapping("/gv/releaseApprove")
@Slf4j
@Api(tags = "Release approve")
public class GvReleaseApproveController {

    @Autowired
    private ReleaseApproveService releaseApproveService;

    @PostMapping("/settingReleaseApproveConfig")
    @ApiOperation(value = "Setting release approve config")
    public Result<List<String>> settingApproveConfig(@RequestBody @Validated ReleaseApproveAmountListRequest releaseApproveAmountRequests) {
        log.info("releaseApproveAmountRequests:{}", JSON.toJSONString(releaseApproveAmountRequests));
        List<ReleaseApproveAmountRequest> releaseApproveAmountRequests1 = releaseApproveAmountRequests.getReleaseApproveAmountRequests();
		releaseApproveAmountRequests1.forEach(vo -> {
			vo.setCreateUser(releaseApproveAmountRequests.getCreateUser());
		});
		return releaseApproveService.settingApproveConfig(releaseApproveAmountRequests1);
    }

    @PostMapping("/queryReleaseApproveConfig")
    @ApiOperation(value = "Query release approve config")
	public Result<List<ReleaseApproveAmountResponse>> queryApproveConfig(@RequestParam(name = "issuerCode", required = true) String issuerCode) {
		return releaseApproveService.queryApproveConfig(issuerCode);
    }

    @PostMapping("/getNextAmount")
    @ApiOperation(value = "Gets the starting Amount and rangeName of the next Amount")
    public Result<GetNextAmountResponse> getNextAmount(@RequestBody @Validated GetNextAmountRequest getNextAmountRequest) {
        return releaseApproveService.getNextAmount(getNextAmountRequest);
    }

    @PostMapping("/queryLogByBusinessCode")
    @ApiOperation(value = "Get a approveType of audit record for businessCode")
    public Result<List<ApproveNodeRecordResponse>> queryLogByBusinessCode(@RequestBody @Validated QueryLogByBusinessCodeRequest queryLogByBusinessCode) {
        log.info("queryLogByBusinessCode:{}", queryLogByBusinessCode);
        return releaseApproveService.queryLogByBusinessCode(queryLogByBusinessCode.getBusinessCode(), null);
    }

}
