package com.gtech.gvcore.web.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtech.gvcore.monitor.JvmObjectMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * JVM监控定时任务
 * 定期打印JVM内存和对象统计信息
 */
@Slf4j
@Component
public class JvmMonitorTask {

    /**
     * 每5分钟打印一次简化的JVM统计信息（结构化输出）
     */
    @Scheduled(fixedRate = 5 * 60 * 1000) // 5分钟
    public void printSimpleJvmStats() {
        try {
            String statsJson = JvmObjectMonitor.getSimpleObjectStatistics();
            JSONObject stats = JSON.parseObject(statsJson);

            // 结构化输出
            JSONObject output = new JSONObject();
            output.put("type", "JVM_SIMPLE_STATS");
            output.put("timestamp", System.currentTimeMillis());
            output.put("data", stats);

            log.info("=== JVM简化统计 ===\n{}", JSON.toJSONString(output, true));
        } catch (Exception e) {
            log.error("打印JVM简化统计失败", e);
        }
    }

    /**
     * 每15分钟打印一次详细的对象实例信息（结构化输出）
     */
    @Scheduled(fixedRate = 15 * 60 * 1000) // 15分钟
    public void printDetailedObjectInstances() {
        try {
            String instances = JvmObjectMonitor.getDetailedObjectInstances();

            JSONObject output = new JSONObject();
            output.put("type", "JVM_OBJECT_INSTANCES");
            output.put("timestamp", System.currentTimeMillis());

            if (!instances.isEmpty()) {
                try {
                    JSONObject instancesJson = JSON.parseObject(instances);
                    output.put("data", instancesJson);
                } catch (Exception e) {
                    output.put("data", instances);
                }
            } else {
                output.put("data", "暂无跟踪的对象实例");
            }

            log.info("=== JVM详细对象实例 ===\n{}", JSON.toJSONString(output, true));
        } catch (Exception e) {
            log.error("打印JVM详细对象实例失败", e);
        }
    }

    /**
     * 每30分钟进行一次内存泄漏风险评估（结构化输出）
     */
    @Scheduled(fixedRate = 30 * 60 * 1000) // 30分钟
    public void assessMemoryLeakRisk() {
        try {
            JvmObjectMonitor.MemoryLeakRisk risk = JvmObjectMonitor.assessMemoryLeakRisk();

            JSONObject output = new JSONObject();
            output.put("type", "MEMORY_LEAK_RISK");
            output.put("timestamp", System.currentTimeMillis());

            JSONObject riskData = new JSONObject();
            riskData.put("level", risk.getLevel().toString());
            riskData.put("description", risk.getDescription());
            output.put("data", riskData);

            if (risk.getLevel() == JvmObjectMonitor.RiskLevel.HIGH) {
                log.warn("=== 内存泄漏风险评估 === 高风险！\n{}", JSON.toJSONString(output, true));
            } else if (risk.getLevel() == JvmObjectMonitor.RiskLevel.MEDIUM) {
                log.warn("=== 内存泄漏风险评估 === 中等风险\n{}", JSON.toJSONString(output, true));
            } else {
                log.info("=== 内存泄漏风险评估 === 低风险\n{}", JSON.toJSONString(output, true));
            }
        } catch (Exception e) {
            log.error("内存泄漏风险评估失败", e);
        }
    }

    /**
     * 每小时打印一次完整的JVM统计信息（结构化输出）
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时
    public void printDetailedJvmStats() {
        try {
            String detailedStatsJson = JvmObjectMonitor.getDetailedObjectStatistics();

            JSONObject output = new JSONObject();
            output.put("type", "JVM_DETAILED_STATS");
            output.put("timestamp", System.currentTimeMillis());

            try {
                JSONObject detailedStats = JSON.parseObject(detailedStatsJson);
                output.put("data", detailedStats);
            } catch (Exception e) {
                output.put("data", detailedStatsJson);
            }

            log.info("=== JVM完整统计 ===\n{}", JSON.toJSONString(output, true));
        } catch (Exception e) {
            log.error("打印JVM完整统计失败", e);
        }
    }

    /**
     * 每天凌晨2点执行一次垃圾回收并记录效果
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
    public void dailyGarbageCollection() {
        try {
            // 记录GC前的内存状态
            String beforeStats = JvmObjectMonitor.getSimpleObjectStatistics();
            log.info("=== 定时GC === GC前内存状态: {}", beforeStats);
            
            // 执行GC
            System.gc();
            
            // 等待GC完成
            Thread.sleep(2000);
            
            // 记录GC后的内存状态
            String afterStats = JvmObjectMonitor.getSimpleObjectStatistics();
            log.info("=== 定时GC === GC后内存状态: {}", afterStats);
            
            // 计算GC效果
            logGcEffect(beforeStats, afterStats);
            
        } catch (Exception e) {
            log.error("定时GC执行失败", e);
        }
    }

    /**
     * 分析并记录GC效果
     */
    private void logGcEffect(String beforeStats, String afterStats) {
        try {
            // 简单解析内存使用情况
            long beforeHeap = extractHeapUsage(beforeStats);
            long afterHeap = extractHeapUsage(afterStats);
            
            if (beforeHeap > 0 && afterHeap > 0) {
                long freedMemory = beforeHeap - afterHeap;
                double freedPercent = (double) freedMemory / beforeHeap * 100;
                
                if (freedMemory > 0) {
                    log.info("=== GC效果 === 释放内存: {}MB ({:.1f}%)", freedMemory, freedPercent);
                } else {
                    log.warn("=== GC效果 === 内存未减少，可能存在内存泄漏");
                }
            }
        } catch (Exception e) {
            log.debug("分析GC效果失败: {}", e.getMessage());
        }
    }

    /**
     * 从统计字符串中提取堆内存使用量
     */
    private long extractHeapUsage(String stats) {
        try {
            // 解析 "Heap:824/7282MB" 格式
            if (stats.contains("Heap:")) {
                String heapPart = stats.substring(stats.indexOf("Heap:") + 5);
                String usedPart = heapPart.substring(0, heapPart.indexOf("/"));
                return Long.parseLong(usedPart);
            }
        } catch (Exception e) {
            log.debug("解析堆内存使用量失败: {}", e.getMessage());
        }
        return 0;
    }
}
