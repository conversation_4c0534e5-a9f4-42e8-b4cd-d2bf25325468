package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.productcategory.*;
import com.gtech.gvcore.common.response.productcategory.*;
import com.gtech.gvcore.service.ProductCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
@RestController
@RequestMapping(value = "/gv/productCategory")
@Api(value = "Product Category maintenance.", tags = { "GV Product Category Api" })
public class ProductCategoryController {

    @Autowired
    private ProductCategoryService productCategoryService;

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    @ApiOperation(value = "Create Product Category", notes = "Create Product Category")
    @PostMapping(value = "/createProductCategory")
    public Result<CreateProductCategoryResponse> createProductCategory(
            @RequestBody @Validated CreateProductCategoryRequest request) {

        return productCategoryService.createProductCategory(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    @ApiOperation(value = "Update Product Category", notes = "Update Product Category")
    @PostMapping(value = "/updateProductCategory")
    public Result<Void> updateProductCategory(@RequestBody @Validated UpdateProductCategoryRequest request) {

        return productCategoryService.updateProductCategory(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    @ApiOperation(value = "Update Product Category Status", notes = "Update Product Category Status")
    @PostMapping(value = "/updateProductCategoryStatus")
    public Result<Void> updateProductCategoryStatus(
            @RequestBody @Validated UpdateProductCategoryStatusRequest request) {

        return productCategoryService.updateProductCategoryStatus(request);

    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月23日
     */
    @ApiOperation(value = "Query Product Category By Page", notes = "Query Product Category By Page")
    @PostMapping(value = "/queryProductCategoryByPage")
    public PageResult<QueryProductCategoryByPageResponse> queryProductCategoryByPage(
            @RequestBody @Validated QueryProductCategoryByPageRequest request) {

        return productCategoryService.queryProductCategoryByPage(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月24日
     */
    @ApiOperation(value = "Query Product Category CPG", notes = "Query Product Category CPG")
    @PostMapping(value = "/queryProductCategoryCpg")
    public Result<QueryProductCategoryCpgResponse> queryProductCategoryCpg(
            @RequestBody @Validated QueryProductCategoryCpgRequest request) {
        return productCategoryService.queryProductCategoryCpg(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月24日
     */
    @ApiOperation(value = "Create Or Update Product Category CPG", notes = "Create Or Update Product Category CPG")
    @PostMapping(value = "/createOrUpdateProductCategoryCpg")
    public Result<Void> createOrUpdateProductCategoryCpg(
            @RequestBody @Validated CreateOrUpdateProductCategoryCpgRequest request) {
        return productCategoryService.createOrUpdateProductCategoryCpg(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月25日
     */
    @ApiOperation(value = "Query Product Category Disscount", notes = "Query Product Category Disscount")
    @PostMapping(value = "/queryProductCategoryDisscount")
    public Result<QueryProductCategoryDisscountResponse> queryProductCategoryDisscount(
            @RequestBody @Validated QueryProductCategoryDisscountRequest request) {
        return productCategoryService.queryProductCategoryDisscount(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月25日
     */
    @ApiOperation(value = "Create Or Update Product Category Disscount", notes = "Create Or Update Product Category Disscount")
    @PostMapping(value = "/createOrUpdateProductCategoryDisscount")
    public Result<Void> createOrUpdateProductCategoryDisscount(
            @RequestBody @Validated CreateOrUpdateProductCategoryDisscountRequest request) {
        return productCategoryService.createOrUpdateProductCategoryDisscount(request);
    }

	@ApiOperation(value = "Calculate the discount amount")
	@PostMapping(value = "/calculateDiscountAmount")
	public Result<DiscountInfoResponse> calculateDiscountAmount(@RequestBody @Validated CalculateDiscountAmountRequest request) {
		DiscountInfoResponse discountInfoResponse = productCategoryService.calculateDiscountAmount(request);
		return Result.ok(discountInfoResponse);
    }
}
