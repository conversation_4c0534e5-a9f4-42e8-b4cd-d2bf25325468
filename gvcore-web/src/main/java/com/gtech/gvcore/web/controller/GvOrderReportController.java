package com.gtech.gvcore.web.controller;

import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.annotation.NotSysLogger;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.request.orderreport.GetReportSystemLoggerRequest;
import com.gtech.gvcore.common.request.orderreport.QueryOrderReportDataRequest;
import com.gtech.gvcore.common.request.orderreport.QueryOrderReportRequest;
import com.gtech.gvcore.common.response.orderreport.QueryReportReqResponse;
import com.gtech.gvcore.common.response.orderreport.QuerySupportReportResponse;
import com.gtech.gvcore.dao.model.SysLogger;
import com.gtech.gvcore.service.SystemLoggerService;
import com.gtech.gvcore.service.report.ReportRequestService;
import com.gtech.gvcore.service.report.base.BuilderReportHelper;
import com.gtech.gvcore.service.report.export.snapshoot.QueryOrderReportDataResponse;
import com.gtech.gvcore.service.report.export.snapshoot.ReportDataHelper;
import com.gtech.gvcore.service.report.extend.ReportFactory;
import com.gtech.gvcore.service.report.impl.param.AuditTrailReportQueryData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/6 22:05
 */

@RestController
@RequestMapping("/gv/orderReport")
@Api(tags = "Order report")
@Slf4j
public class GvOrderReportController {

    @Autowired
    private ReportDataHelper reportDataHelper;

    @Autowired
    private ReportRequestService reportRequestService;

    @Autowired
    private BuilderReportHelper builderReportHelper;

    @PostMapping("/reBuilderReport/{reportCode}")
    @ApiOperation(value = "re builder order report", hidden = true)
    public Result<String> reBuilderReport(@PathVariable("reportCode") String reportCode) {

        return Result.ok(builderReportHelper.reBuilder(reportCode));
    }

    @PostMapping("/createOrderReport")
    @ApiOperation("Create order report")
    public Result<String> createOrderReport(@RequestBody @Validated CreateReportRequest createReportRequest) {

        log.info("createOrderReportRequest:{}", createReportRequest);

        return Result.ok(reportRequestService.createReport(createReportRequest, false));
    }

    @NotSysLogger(notResponseLogger = true)
    @PostMapping("/createOrderReportLocal")
    @ApiOperation("Create order report Local")
    public Result<String> createOrderReportLocal(@RequestBody @Validated CreateReportRequest createReportRequest) {

        log.info("createOrderReportRequest:{}", createReportRequest);

        return Result.ok(reportRequestService.createReport(createReportRequest, true));
    }

    @PostMapping("/queryOrderReportByType")
    @ApiOperation("Query order report list by type")
    public PageResult<QueryReportReqResponse> queryOrderReportByType(@RequestBody @Validated QueryOrderReportRequest queryOrderReportRequest) {

        log.info("queryOrderReportByType:{}", queryOrderReportRequest);

        PageData<QueryReportReqResponse> responsePageData = reportRequestService.queryReport(queryOrderReportRequest);

        return PageResult.ok(responsePageData.getList(), responsePageData.getTotal());
    }

    @NotSysLogger(notResponseLogger = true)
    @PostMapping("/queryOrderReportData")
    @ApiOperation("Query order report data.")
    public Result<QueryOrderReportDataResponse> queryOrderReportData (@RequestBody @Validated QueryOrderReportDataRequest request) {

        log.info("queryOrderReportByType:{}", request);

        final int pageNum = request.getPageNum();
        final int pageSize = request.getPageSize();
        final String reportCode = request.getOrderReportCode();
        final Integer reportType = request.getReportType();

        return Result.ok(reportDataHelper.selectReportData(reportCode, reportType, pageSize, pageNum));
    }

    @NotSysLogger(notResponseLogger = true)
    @PostMapping("/querySupportReportList")
    @ApiOperation("Query support report list.")
    public Result<List<QuerySupportReportResponse>> querySupportReportList () {

        Set<ReportExportTypeEnum> allSupportEnum = ReportFactory.getAllSupportEnum();

        if (CollectionUtils.isEmpty(allSupportEnum)) return Result.ok(Collections.emptyList());

        return Result.ok(allSupportEnum.stream().map(e -> new QuerySupportReportResponse()
                .setReportCode(e.getExportType()).setReportName(e.getExportName())).collect(Collectors.toList()));
    }


    // ------ other

    @Autowired
    private SystemLoggerService loggerService;

    @NotSysLogger(disableLogger = true)
    @PostMapping("/getSystemLogger")
    @ApiOperation("get system logger by id")
    public Result<SysLogger> getSystemLoggerById (@RequestBody @Validated GetReportSystemLoggerRequest request) {

        log.info("getSystemLoggerById:{}", request.getId());

		return Result.ok(loggerService.getLoggerById(request.getId(), request.getOperationTime()));
    }

	@PostMapping("/querySysLogger")
	@ApiOperation(value = "querySysLogger", hidden = true)
	public Result<List<SysLogger>> querySysLogger(@RequestBody @Validated AuditTrailReportQueryData queryData) {

		return Result.ok(loggerService.queryLoggerReport(queryData));
	}

    // ----- find mv01

    @Value("${gv.outlet.warehouse.MV01}")
    private String mv01Code;

    @PostMapping("/getMv01Code")
    @ApiOperation("get mv01 code")
    public Result<String> getSystemLoggerById () {

        return Result.ok(mv01Code);
    }
}
