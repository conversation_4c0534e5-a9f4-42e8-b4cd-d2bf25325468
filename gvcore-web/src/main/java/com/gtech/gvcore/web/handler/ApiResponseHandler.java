package com.gtech.gvcore.web.handler;

import com.gtech.gvcore.common.enums.GvPosCommonResponseCodesEnum;
import com.gtech.gvcore.common.enums.GiftCardResponseCodesEnum;
import com.gtech.gvcore.common.exception.GvBusinessException;
import com.gtech.gvcore.common.exception.GvcoreUnknownException;
import com.gtech.gvcore.common.response.gc.BaseApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.function.Supplier;

public class ApiResponseHandler {


    /**
     * Handles GvBusinessException and its subtypes.
     */
    public static <T extends BaseApiResponse> ResponseEntity<T> handleGvBusinessException(
            GvBusinessException e, Supplier<T> responseSupplier) {
        
        T responseBody = responseSupplier.get();
        responseBody.setResponseCode(e.getResponseCodeInt());
        responseBody.setResponseMessage(e.getCustomMessage());

        HttpStatus httpStatus = HttpStatus.BAD_REQUEST;
        GvPosCommonResponseCodesEnum errorCodeEnum = e.getMatchingErrorCodeEnum();
        GiftCardResponseCodesEnum giftCardErrorCodeEnum = e.getMatchingGiftCardErrorCodeEnum();

        if (errorCodeEnum != null) {
             httpStatus = getHttpStatusForBusinessError(errorCodeEnum);
        } else if (giftCardErrorCodeEnum != null) {
             httpStatus = getHttpStatusForGiftCardError(giftCardErrorCodeEnum);
        } else {
        }
        
        return new ResponseEntity<>(responseBody, httpStatus);
    }

    private static HttpStatus getHttpStatusForBusinessError(GvPosCommonResponseCodesEnum errorCodeEnum) {
        switch (errorCodeEnum) {
            case AUTHORIZATION_FAILED_10744:
            case INVALID_USERNAME_OR_PASSWORD:
            case USER_DOES_NOT_EXIST:
            case THE_USER_HAS_BEEN_DISABLED:
                return HttpStatus.UNAUTHORIZED;
            case INVALID_TERMINALID:
                return HttpStatus.FORBIDDEN;
            default:
                return HttpStatus.BAD_REQUEST;

        }
    }

    private static HttpStatus getHttpStatusForGiftCardError(GiftCardResponseCodesEnum errorCodeEnum) {
        switch (errorCodeEnum) {
            case TOKEN_EXPIRED:
            case TOKEN_IS_NULL:
                return HttpStatus.UNAUTHORIZED;
            case POS_CPG_AUTHORIZATION_FAILED:
            case OUTLET_CPG_AUTHORIZATION_FAILED:
                return HttpStatus.FORBIDDEN;
            case GIFT_CARD_NUMBER_DOES_NOT_EXIST:
            case GIFT_CARD_NUMBER_IN_GIFT_CARDS_DOES_NOT_EXIST:
            case GCPG_DOES_NOT_EXIST:
            case CUSTOMER_ID_DOES_NOT_EXIST:
                return HttpStatus.NOT_FOUND;
            default:
                return HttpStatus.BAD_REQUEST;
        }
    }

    /**
     * Builds a successful response or handles a response DTO that might already contain an error code.
     */
    public static <T extends BaseApiResponse> ResponseEntity<T> buildResponse(T serviceResponse) {
        HttpStatus httpStatus = HttpStatus.OK;

        if (serviceResponse.getResponseCode() != GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode()
            && serviceResponse.getResponseCode() != GiftCardResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode()) {

            GvPosCommonResponseCodesEnum knownError = null;
            GiftCardResponseCodesEnum giftCardKnownError = null;

            // Check GvPosCommonResponseCodesEnum first
            for (GvPosCommonResponseCodesEnum ec : GvPosCommonResponseCodesEnum.values()) {
                if (ec.getResponseCode() == serviceResponse.getResponseCode()) {
                    knownError = ec;
                    break;
                }
            }

            // If not found in common codes, check GiftCard codes
            if (knownError == null) {
                for (GiftCardResponseCodesEnum ec : GiftCardResponseCodesEnum.values()) {
                    if (ec.getResponseCode() == serviceResponse.getResponseCode()) {
                        giftCardKnownError = ec;
                        break;
                    }
                }
            }

            if (knownError != null) {
                 httpStatus = getHttpStatusForBusinessError(knownError);
            } else if (giftCardKnownError != null) {
                 httpStatus = getHttpStatusForGiftCardError(giftCardKnownError);
            } else {
                 httpStatus = HttpStatus.BAD_REQUEST;
            }
        }
        return new ResponseEntity<>(serviceResponse, httpStatus);
    }
} 