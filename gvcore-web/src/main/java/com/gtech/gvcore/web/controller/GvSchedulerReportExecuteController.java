package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.service.report.SchedulerReportTaskService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@ApiIgnore
@ConditionalOnExpression(
        //dev 环境
        "#{'dev'.equals(environment.getProperty('spring.profiles.active')) " +
        //test 环境
        "|| 'test'.equals(environment.getProperty('spring.profiles.active')) " +
        //junit 环境
        "|| 'junit'.equals(environment.getProperty('spring.profiles.active'))}")
@RequestMapping("/gv/schedulerReport")
public class GvSchedulerReportExecuteController {

    @Autowired
    private SchedulerReportTaskService schedulerReportService;


    @ApiIgnore
    @PostMapping("/enforce/{code}")
    @ApiOperation(value = "enforce", hidden = true)
    public Result<String> execute(@PathVariable(value = "code") String code) {

        return Result.ok(schedulerReportService.enforce(code));
    }


}
