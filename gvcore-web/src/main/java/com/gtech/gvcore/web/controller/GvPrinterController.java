package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.printer.*;
import com.gtech.gvcore.common.response.productcategory.printer.GetPrinterResponse;
import com.gtech.gvcore.common.response.productcategory.printer.QueryPrinterByPageResponse;
import com.gtech.gvcore.service.PrinterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-21 17:49
 **/
@Slf4j
@RestController
@RequestMapping(value = "/gv/printer")
@Api(value = "Printer", tags = {"Printer Api"})
public class GvPrinterController {

    @Autowired
    private PrinterService printerService;

    @ApiOperation(value = "create printer", notes = "create printer")
    @PostMapping(value = "/createPrinter")
    public Result<Object> createPrinter(@Validated @RequestBody CreatePrinterRequest requestParam) {
        log.info("CreatePrinterRequest:{}", JSON.toJSONString(requestParam));
        return printerService.createPrinter(requestParam);
    }

    @ApiOperation(value = "Query Printer Data", notes = "Query Printer Data")
    @PostMapping(value = "/queryPrinterDataByPage")
    public PageResult<QueryPrinterByPageResponse> queryPrinterDataByPage(@RequestBody @Validated QueryPrinterByPageRequest request) {

        return printerService.queryPrinterDataByPage(request);
    }

    @ApiOperation(value = "update Printer Status", notes = "update Printer Status")
    @PostMapping(value = "/updatePrinterStatus")
    public Result<Void> updatePrinterStatus(@RequestBody @Validated UpdatePrinterStatusRequest request) {
        log.info("UpdatePrinterStatusRequest:{}", JSON.toJSONString(request));
        return printerService.updatePrinterStatus(request);
    }

    @ApiOperation(value = "update Printer", notes = "update Printer")
    @PostMapping(value = "/updatePrinter")
    public Result<Object> updatePrinter(@RequestBody @Validated UpdatePrinterRequest requestParam) {
        log.info("UpdatePrinterRequest:{}", JSON.toJSONString(requestParam));

        return printerService.updatePrinter(requestParam);
    }

    @ApiOperation(value = "get Printer", notes = "get Printer")
    @PostMapping(value = "/getPrinter")
    public Result<GetPrinterResponse> getPrinter(@RequestBody @Validated GetPrinterRequest request) {
        log.info("UpdatePrinterRequest:{}", JSON.toJSONString(request));

        return printerService.getPrinter(request);
    }

    @ApiIgnore
    @ApiOperation(value = "delete Printer", notes = "delete Printer")
    @PostMapping(value = "/deletePrinter/{printerCode}")
    public Result<Object> deletePrinter(@PathVariable String printerCode) {
        return printerService.deletePrinterById(printerCode);
    }


}
