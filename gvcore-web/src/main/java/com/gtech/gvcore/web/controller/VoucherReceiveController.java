package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.receive.GetReceiveByCustomerOrderRequest;
import com.gtech.gvcore.common.request.receive.GetVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.QueryVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.ReceiveVoucherRequest;
import com.gtech.gvcore.common.response.voucherreceive.VoucherReceiveResponse;
import com.gtech.gvcore.common.utils.ValidList;
import com.gtech.gvcore.service.VoucherReceiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping(value = "/gv/receive")
@Api(value = "Vourcher receive.", tags = { "Vourcher Receive Api" })
public class VoucherReceiveController {

	private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(8, 20, 100, TimeUnit.SECONDS, new LinkedBlockingDeque<>(300));
	
	@Autowired
	private VoucherReceiveService voucherReceiveService;

	@ApiOperation(value = "Query voucher receive page", notes = "Query voucher receive page")
	@PostMapping(value = "/queryVoucherReceivePage")
	public PageResult<VoucherReceiveResponse> queryVoucherReceivePage(@RequestBody @Validated QueryVoucherReceiveRequest request) {

		return voucherReceiveService.queryVoucherReceivePage(request);
    }

	@ApiOperation(value = "Get voucher receive", notes = "Get voucher receive")
	@PostMapping(value = "/getVoucherReceive")
	public Result<VoucherReceiveResponse> getVoucherReceive(@RequestBody @Validated GetVoucherReceiveRequest request) {
		VoucherReceiveResponse voucherReceiveResponse = voucherReceiveService.getVoucherReceive(request);
		return Result.ok(voucherReceiveResponse);
	}

	@ApiOperation(value = "Get voucher receive by customer order", notes = "Get voucher receive by customer order")
	@PostMapping(value = "/getReceiveByCustomerOrder")
	public Result<VoucherReceiveResponse> getReceiveByCustomerOrder(@RequestBody @Validated GetReceiveByCustomerOrderRequest request) {
		VoucherReceiveResponse voucherReceiveResponse = voucherReceiveService.getVoucherReceiveByCustomerOrder(request);
		return Result.ok(voucherReceiveResponse);
	}

	
	@ApiOperation(value = "Receive voucher", notes = "Receive voucher")
	@PostMapping(value = "/receive")
	public Result<Void> receive(@RequestBody @Validated ReceiveVoucherRequest receiveVoucherRequest) {
		executor.execute(() ->{
			voucherReceiveService.receive(receiveVoucherRequest);
		});
		return Result.ok();
	}

	@ApiOperation(value = "Receive voucher batch", notes = "Receive voucher batch")
	@PostMapping(value = "/receiveBatch")
	public Result<List<String>> receiveBatch(@RequestBody @Validated ValidList<ReceiveVoucherRequest> receiveVoucherList) {

		CountDownLatch downlatch = new CountDownLatch(receiveVoucherList.size());
		List<String> errorMsgList = new CopyOnWriteArrayList<>();
		for (ReceiveVoucherRequest receiveVoucherRequest : receiveVoucherList) {
			executor.execute(() ->{
				try {
					voucherReceiveService.receive(receiveVoucherRequest);
				} catch (Exception e) {
					errorMsgList.add("[" + receiveVoucherRequest.getVoucherReceiveCode() + "]" + e.getMessage());
				} finally {
					downlatch.countDown();
				}
			});
		}
		return Result.ok(errorMsgList);
	}
}
