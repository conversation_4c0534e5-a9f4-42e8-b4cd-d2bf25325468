package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.dashboard.*;
import com.gtech.gvcore.common.response.dashboard.*;
import com.gtech.gvcore.service.DashboardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/8/30 15:13
 */
@RestController
@RequestMapping(value = "/gv/dashboard")
@Api(value = "Dashboard.", tags = {"GV Dashboard Api"})
public class DashboardController {


    @Value("${dashboard.open:false}")
    private Boolean open;

    @Autowired
    private DashboardService dashboardService;



    @ApiOperation(value = "DataRecord", notes = "DataRecord")
    @PostMapping(value = "/dataRecord")
    public Result dataRecord(@RequestBody DataRecordRequest request) {

        //dashboardService.dataRecord(request);
        return Result.ok();
    }

    @ApiOperation(value = "Histogram", notes = "Histogram")
    @PostMapping(value = "/histogram")
    public Result<List<HistogramResponse>> histogram(@RequestBody HistogramRequest request) {
        /*List<HistogramResponse> histogram = dashboardService.histogram(request);
        return Result.ok(histogram);*/
        return Result.ok();
    }

    @ApiOperation(value = "LastUpdateTime", notes = "LastUpdateTime")
    @PostMapping(value = "/lastUpdateTime")
    public Result<String> lastUpdateTime() {
//        return Result.ok(dashboardService.lastUpdateTime());
        return Result.ok();
    }


    @ApiOperation(value = "SalesBarChart", notes = "SalesBarChart")
    @PostMapping(value = "/salesBarChart")
    public Result<List<SalesBarChartResponse>> salesBarChart(@RequestBody SalesBarChartRequest request) {
        /*List<SalesBarChartResponse> salesBarChart = dashboardService.salesBarChart(request);
        return Result.ok(salesBarChart);*/
        return Result.ok();
    }


    @ApiOperation(value = "PieChart", notes = "PieChart")
    @PostMapping(value = "/pieChart")
    public Result<List<PieChartResponse>> pieChart(@RequestBody PieChartRequest request) {
        /*List<PieChartResponse> pieChart = dashboardService.pieChart(request);
        return Result.ok(pieChart);*/
        return Result.ok();
    }


    @ApiOperation(value = "TopTen", notes = "TopTen")
    @PostMapping(value = "/topTen")
    public Result<List<TopTenResponse>> topTen(@RequestBody TopTenRequest request) {
        /*List<TopTenResponse> topTen = dashboardService.topTen(request);
        return Result.ok(topTen);*/
        return Result.ok();
    }



    @ApiOperation(value = "TotalSales", notes = "TotalSales")
    @PostMapping(value = "/totalSales")
    public Result<List<TotalSalesResponse>> totalSales(@RequestBody TotalSalesRequest request) {
        /*List<TotalSalesResponse> totalSales = dashboardService.totalSales(request);
        return Result.ok(totalSales);*/
        return Result.ok();
    }


    @ApiOperation(value = "TotalRedemption", notes = "TotalRedemption")
    @PostMapping(value = "/totalRedemption")
    public Result<BigDecimal> totalRedemption(@RequestBody TotalRedemptionRequest request) {
        /*BigDecimal amount =  dashboardService.totalRedemption(request);
        return Result.ok(amount);*/
        return Result.ok();
    }


    @ApiOperation(value = "RedemptionProportion", notes = "RedemptionProportion")
    @PostMapping(value = "/redemptionProportion")
    public Result<List<RedemptionProportionResponse>> redemptionProportion(@RequestBody RedemptionProportionRequest request) {
        /*List<RedemptionProportionResponse> redemptionProportion = dashboardService.redemptionProportion(request);
        return Result.ok(redemptionProportion);*/
        return Result.ok();
    }





    @ApiOperation(value = "VcrInStock", notes = "VcrInStock")
    @PostMapping(value = "/vcrInStock")
    public Result<List<Map<String, String>>> vcrInStock(@RequestBody VcrInStockRequest request) {
        /*List<Map<String, String>> vcrInStock = dashboardService.vcrInStock(request);
        return Result.ok(vcrInStock);*/
        return Result.ok();
    }



    @ApiOperation(value = "RedemptionOtherSum", notes = "RedemptionOtherSum")
    @PostMapping(value = "/redemptionOtherSum")
    public Result<RedemptionOtherSumResponse> redemptionOtherSum(@RequestBody RedemptionOtherSumRequest request) {
        /*RedemptionOtherSumResponse response = dashboardService.redemptionOtherSum(request);
        return Result.ok(response);*/
        return Result.ok();
    }




}
