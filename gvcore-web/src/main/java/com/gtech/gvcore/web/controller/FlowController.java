package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.flow.CreateFlowRequest;
import com.gtech.gvcore.common.request.flow.FlowNodeRequest;
import com.gtech.gvcore.common.request.flow.QueryFlowRequest;
import com.gtech.gvcore.common.request.flow.UpdateFlowRequest;
import com.gtech.gvcore.common.response.flow.FlowResponse;
import com.gtech.gvcore.service.FlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/gv/flow")
@Api(value = "Flow.", tags = { "GV Flow Api" })
public class FlowController {

	@Autowired
	private FlowService flowService;

	@ApiOperation(value = "Create flow ", notes = "Create flow.")
	@PostMapping(value = "/createFlow")
	public Result<String> createFlow(@RequestBody @Validated CreateFlowRequest request) {

		return Result.ok(flowService.createFlow(request));
	}

	@ApiOperation(value = "Update flow ", notes = "Update flow.")
	@PostMapping(value = "/updateFlow")
	public Result<String> updateFlow(@RequestBody @Validated UpdateFlowRequest request) {

		return Result.ok(flowService.updateFlow(request));
	}
	
	@ApiOperation(value = "Query flow list", notes = "Query flow list.")
	@PostMapping(value = "/queryFlowList")
	public PageResult<FlowResponse> queryFlowList(@RequestBody @Validated QueryFlowRequest request) {

		return flowService.queryFlowList(request);
	}
	
	@ApiOperation(value = "Save flow node ", notes = "Save flow node.")
	@PostMapping(value = "/saveFlowNode")
	public Result<String> saveFlowNode(@RequestBody @Validated FlowNodeRequest request) {

		return Result.ok(flowService.saveFlowNode(request));
	}
}
