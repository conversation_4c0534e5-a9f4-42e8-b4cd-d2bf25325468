package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.customerproductcategory.*;
import com.gtech.gvcore.common.response.customerproductcategory.CustomerProductCategoryResponse;
import com.gtech.gvcore.service.CustomerProductCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/2/16 17:36
 */
@RestController
@RequestMapping(value = "/gv/customerProductCategory")
@Api(value = "CustomerProductCategory data dictionary.", tags = { "GV customerProductCategory Api" })
public class GvCustomerProductCategoryController {


    @Autowired
    private CustomerProductCategoryService customerProductCategoryService;
    
    @ApiOperation(value = "Create customer product category ",notes = "Create customer product category information.")
    @PostMapping(value = "/createCustomerProductCategory")
    public Result<Void> createCustomerProductCategory(@RequestBody @Validated CreateCustomerProductCategoryRequest param) {




        // Return result object
        return customerProductCategoryService.createCustomerProductCategory(param);
    }

    @ApiOperation(value = "Update customer product category ",notes = "Update customer product category information.")
    @PostMapping(value = "/updateCustomerProductCategory")
    public Result<Void> updateCustomerProductCategory(@RequestBody @Validated UpdateCustomerProductCategoryRequest param) {




        // Return result object
        return customerProductCategoryService.updateCustomerProductCategory(param);
    }

    @ApiOperation(value = "Delete customer product category ",notes = "Delete customer product category information.")
    @PostMapping(value = "/deleteCustomerProductCategory")
    public Result<Void> deleteCustomerProductCategory(@RequestBody @Validated DeleteCustomerProductCategoryRequest param) {


        customerProductCategoryService.deleteCustomerProductCategory(param);

        // Return result object
        return Result.ok();
    }


    @ApiOperation(value = "Query customer product category  list",notes = "Query customer product category information list.")
    @PostMapping(value = "/queryCustomerProductCategoryList")
    public PageResult<CustomerProductCategoryResponse> queryCustomerProductCategoryList(@RequestBody @Validated QueryCustomerProductCategoryRequest param) {

        // Return result object
        return customerProductCategoryService.queryCustomerProductCategoryList(param);
    }


    @ApiOperation(value = "Get customer product category ",notes = "Get customer product category information.")
    @PostMapping(value = "/getCustomerProductCategory")
    public Result<CustomerProductCategoryResponse> getCustomerProductCategory(@RequestBody @Validated GetCustomerProductCategoryRequest param) {


        CustomerProductCategoryResponse customerProductCategory = customerProductCategoryService.getCustomerProductCategory(param);

        // Return result object
        return Result.ok(customerProductCategory);
    }
    
    
    
}
