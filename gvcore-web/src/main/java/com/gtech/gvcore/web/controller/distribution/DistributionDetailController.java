package com.gtech.gvcore.web.controller.distribution;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.distribution.ChangeResendVoucherEmailRequest;
import com.gtech.gvcore.common.request.distribution.QueryDistributionDetailRequest;
import com.gtech.gvcore.common.request.distribution.ResendVoucherEmailRequest;
import com.gtech.gvcore.common.response.distribution.QueryDistributionDetailResult;
import com.gtech.gvcore.service.distribution.DistributionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName CustomerDistributionController
 * @Description TODO_Silwings 注意添加swagger文档
 * <AUTHOR>
 * @Date 2022/8/8 15:13
 * @Version V1.0
 **/
@Slf4j
@RestController
@Api(value = "distribution detail controller", tags = {"distribution detail controller"})
@RequestMapping("/gv/distribution/detail")
public class DistributionDetailController {

    @Autowired
    private DistributionService distributionService;

    @PostMapping("/list")
    @ApiOperation(value = "query distribution detail list", notes = "query distribution detail list")
    public PageResult<QueryDistributionDetailResult> list(@RequestBody @Validated QueryDistributionDetailRequest request) {

        return this.distributionService.queryDistributionDetailList(request);
    }

    @PostMapping("/download")
    @ApiOperation(value = "download distribution detail excel", notes = "download distribution detail excel")
    public Result<String> downloadDistributionDetail(@RequestBody @Validated QueryDistributionDetailRequest request) {

        return Result.ok(this.distributionService.downloadDistributionDetail(request));
    }

    @PostMapping("/resendVoucherEmail")
    @ApiOperation(value = "resend voucher email", notes = "resend voucher email")
    public Result<String> resendVoucherEmail(@RequestBody @Validated ResendVoucherEmailRequest request) {

        this.distributionService.resendVoucherEmail(request.getDistributionItemCode(), request.getVoucherCode());

        return Result.ok();
    }

    @PostMapping("/changeResendVoucherEmail")
    @ApiOperation(value = "change resend voucher email", notes = "change resend voucher email")
    public Result<Void> changeResendVoucherEmail(@RequestBody @Validated ChangeResendVoucherEmailRequest request) {

        this.distributionService.changeResendVoucherEmail(request.getDistributionItemCode(), request.getVoucherCode(), request.getEmailAddress());

        return Result.ok();
    }

}
