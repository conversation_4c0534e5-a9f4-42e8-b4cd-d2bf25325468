package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.customerpaymentmethod.*;
import com.gtech.gvcore.common.response.customerpaymentmethod.CustomerPaymentMethodResponse;
import com.gtech.gvcore.service.CustomerPaymentMethodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/2/16 17:36
 */
@RestController
@RequestMapping(value = "/gv/customerPaymentMethod")
@Api(value = "Customer payment method data dictionary.", tags = { "GV customer payment method Api" })
public class GvCustomerPaymentMethodController {


    @Autowired
    private CustomerPaymentMethodService customerPaymentMethodService;
    
    @ApiOperation(value = "Create customer payment method ",notes = "Create customer payment method information.")
    @PostMapping(value = "/createCustomerPaymentMethod")
    public Result<Void> createCustomerPaymentMethod(@RequestBody @Validated CreateCustomerPaymentMethodRequest param) {

        // Return result object
        return customerPaymentMethodService.createCustomerPaymentMethod(param);
    }

    @ApiOperation(value = "Update customer payment method ",notes = "Update customer payment method information.")
    @PostMapping(value = "/updateCustomerPaymentMethod")
    public Result<Void> updateCustomerPaymentMethod(@RequestBody @Validated UpdateCustomerPaymentMethodRequest param) {




        // Return result object
        return customerPaymentMethodService.updateCustomerPaymentMethod(param);
    }

    @ApiOperation(value = "Delete customer payment method ",notes = "Delete customer payment method information.")
    @PostMapping(value = "/deleteCustomerPaymentMethod")
    public Result<Void> deleteCustomerPaymentMethod(@RequestBody @Validated DeleteCustomerPaymentMethodRequest param) {


        customerPaymentMethodService.deleteCustomerPaymentMethod(param);

        // Return result object
        return Result.ok();
    }


    @ApiOperation(value = "Query customer payment method  list",notes = "Query customer payment method information list.")
    @PostMapping(value = "/queryCustomerPaymentMethodList")
    public PageResult<CustomerPaymentMethodResponse> queryCustomerPaymentMethodList(@RequestBody @Validated QueryCustomerPaymentMethodRequest param) {

        // Return result object
        return customerPaymentMethodService.queryCustomerPaymentMethodList(param);
    }


    @ApiOperation(value = "Get customer payment method ",notes = "Get customer payment method information.")
    @PostMapping(value = "/getCustomerPaymentMethod")
    public Result<CustomerPaymentMethodResponse> getCustomerPaymentMethod(@RequestBody @Validated GetCustomerPaymentMethodRequest param) {


        CustomerPaymentMethodResponse customerPaymentMethod = customerPaymentMethodService.getCustomerPaymentMethod(param);

        // Return result object
        return Result.ok(customerPaymentMethod);
    }
    
    
    
}
