package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.schedulerreport.*;
import com.gtech.gvcore.common.response.schedulerreport.QuerySchedulerReportResponse;
import com.gtech.gvcore.service.report.SchedulerReportService;
import com.gtech.gvcore.service.report.SchedulerReportTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/31 14:21
 */

@RestController
@RequestMapping("/gv/schedulerReport")
@Api(tags = "Scheduler report")
@Slf4j
public class GvSchedulerReportController {

    @Autowired
    private SchedulerReportService schedulerReportService;

    @Autowired
    private SchedulerReportTaskService schedulerReportTaskService;

    @PostMapping("/createSchedulerReport")
    @ApiOperation("Create scheduler report")
    public Result<String> createSchedulerReport(@RequestBody @Validated CreateSchedulerReportRequest createSchedulerReportRequest) {
        log.info("createSchedulerReportRequest;{}", JSON.toJSONString(createSchedulerReportRequest));
        return schedulerReportService.createSchedulerReport(createSchedulerReportRequest);
    }

    @PostMapping("/updateSchedulerReport")
    @ApiOperation("update scheduler report")
    public Result<Void> updateSchedulerReport(@RequestBody @Validated UpdateSchedulerReportRequest updateSchedulerReportRequest) {
        log.info("updateSchedulerReportRequest;{}", JSON.toJSONString(updateSchedulerReportRequest));
        return schedulerReportService.updateSchedulerReport(updateSchedulerReportRequest);
    }

    @PostMapping("/querySchedulerReport")
    @ApiOperation("Query scheduler report")
    public PageResult<QuerySchedulerReportResponse> querySchedulerReport(@RequestBody @Validated QuerySchedulerReportRequest querySchedulerReportRequest) {
        log.info("querySchedulerReportRequest;{}", JSON.toJSONString(querySchedulerReportRequest));
        return schedulerReportService.querySchedulerReport(querySchedulerReportRequest);
    }

    @PostMapping("/updateSchedulerReportStatus")
    @ApiOperation("Update scheduler report status Enable or Disable")
    public Result<Void> updateSchedulerReportStatus(@RequestBody @Validated UpdateSchedulerReportsStatusRequest updateSchedulerReportsStatusRequest) {
        log.info("updateSchedulerReportStatusRequest;{}", JSON.toJSONString(updateSchedulerReportsStatusRequest));
        return schedulerReportService.updateSchedulerReportStatus(updateSchedulerReportsStatusRequest);
    }

    @PostMapping("/deleteSchedulerReport")
    @ApiOperation("Delete scheduler report")
    public Result<Void> deleteSchedulerReport(@RequestBody @Validated DeleteSchedulerReportRequest deleteSchedulerReportRequest) {
        log.info("deleteSchedulerReportRequest;{}", JSON.toJSONString(deleteSchedulerReportRequest));
        return schedulerReportService.deleteSchedulerReport(deleteSchedulerReportRequest);
    }

    @PostMapping("/execute")
    @ApiOperation(value = "execute", hidden = true)
    public Result<Void> execute() {

        schedulerReportTaskService.runTask();
        return Result.ok();
    }


}
