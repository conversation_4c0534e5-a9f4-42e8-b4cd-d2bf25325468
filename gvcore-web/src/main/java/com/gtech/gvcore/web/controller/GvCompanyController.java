package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.company.*;
import com.gtech.gvcore.common.response.company.CompanyResponse;
import com.gtech.gvcore.common.response.issuer.PermissionTreeResponse;
import com.gtech.gvcore.service.CompanyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/16 17:36
 */
@RestController
@RequestMapping(value = "/gv/company")
@Api(value = "Company data dictionary.", tags = { "GV company Api" })
public class GvCompanyController {


    @Autowired
    private CompanyService companyService;
    
    @ApiOperation(value = "Create company ",notes = "Create company information.")
    @PostMapping(value = "/createCompany")
    public Result<String> createCompany(@RequestBody @Validated CreateCompanyRequest param) {

        // Return result object
        return companyService.createCompany(param);
    }

    @ApiOperation(value = "Update company ",notes = "Update company information.")
    @PostMapping(value = "/updateCompany")
    public Result<Void> updateCompany(@RequestBody @Validated UpdateCompanyRequest param) {

        // Return result object
        return companyService.updateCompany(param);
    }


    @ApiOperation(value = "Update company status ",notes = "Update company status.")
    @PostMapping(value = "/updateCompanyStatus")
    public Result<Void> updateCompanyStatus(@RequestBody @Validated UpdateCompanyStatusRequest param) {

        // Return result object
        return companyService.updateCompanyStatus(param);
    }

    @ApiOperation(value = "Delete company ",notes = "Delete company information.")
    @PostMapping(value = "/deleteCompany")
    public Result<Void> deleteCompany(@RequestBody @Validated DeleteCompanyRequest param) {


        companyService.deleteCompany(param);

        // Return result object
        return Result.ok();
    }


    @ApiOperation(value = "Query company  list",notes = "Query company information list.")
    @PostMapping(value = "/queryCompanyList")
    public PageResult<CompanyResponse> queryCompanyList(@RequestBody @Validated QueryCompanyRequest param) {

        // Return result object
        return companyService.queryCompanyList(param);
    }


    @ApiOperation(value = "Get company ",notes = "Get company information.")
    @PostMapping(value = "/getCompany")
    public Result<CompanyResponse> getCompany(@RequestBody @Validated GetCompanyRequest param) {


        CompanyResponse company = companyService.getCompany(param);

        // Return result object
        return Result.ok(company);
    }


    @ApiOperation(value = "Query company by issuer codes",notes = "Query company information by issuer codes.")
    @PostMapping(value = "/queryCompanyByIssuerCodes")
    public PageResult<CompanyResponse> queryCompanyByIssuerCodes(@RequestBody @Validated QueryCompanyByIssuerCodesRequest param) {



        // Return result object
        return companyService.queryCompanyByIssuerCodes(param);
    }
    
	@ApiOperation(value = "Query permission tree ", notes = "Query permission tree.")
	@PostMapping(value = "/queryPermissionTree")
	public Result<List<PermissionTreeResponse>> queryPermissionTree() {

		List<PermissionTreeResponse> list = companyService.queryPermissionTree();

		// Return result object
		return Result.ok(list);
	}
    
}
