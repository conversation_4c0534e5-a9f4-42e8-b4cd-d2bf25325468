package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.articlemop.CreateArticleMopRequest;
import com.gtech.gvcore.common.request.articlemop.QueryArticleMopsByPageRequest;
import com.gtech.gvcore.common.request.articlemop.UpdateArticleMopRequest;
import com.gtech.gvcore.common.request.articlemop.UpdateArticleMopStatusRequest;
import com.gtech.gvcore.common.response.articlemop.QueryArticleMopsByPageResponse;
import com.gtech.gvcore.service.ArticleMopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
@RestController
@RequestMapping(value = "/gv/articleMop")
@Api(value = "Article code MOP code maintenance.", tags = { "GV Article code MOP code Api" })
public class ArticleMopController {

    @Autowired
    private ArticleMopService articleMopService;

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月17日
     */
    @ApiOperation(value = "Create article code mop code bind data", notes = "Create article code mop code bind data")
    @PostMapping(value = "/createArticleMop")
    public Result<Void> createArticleMop(@RequestBody @Validated CreateArticleMopRequest request) {

        return articleMopService.createArticleMop(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    @ApiOperation(value = "Update article code mop code bind data", notes = "Update article code mop code bind data")
    @PostMapping(value = "/updateArticleMop")
    public Result<Void> updateArticleMop(@RequestBody @Validated UpdateArticleMopRequest request) {

        return articleMopService.updateArticleMop(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    @ApiOperation(value = "Update article code mop code bind data status", notes = "Update article code mop code bind data status")
    @PostMapping(value = "/updateArticleMopStatus")
    public Result<Void> updateArticleMopStatus(@RequestBody @Validated UpdateArticleMopStatusRequest request) {

        return articleMopService.updateArticleMopStatus(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    @ApiOperation(value = "Query article code mop code bind data", notes = "Query article code mop code bind data")
    @PostMapping(value = "/queryArticleMopsByPage")
    public PageResult<QueryArticleMopsByPageResponse> queryArticleMopsByPage(
            @RequestBody @Validated QueryArticleMopsByPageRequest request) {

        return articleMopService.queryArticleMopsByPage(request);
    }

}
