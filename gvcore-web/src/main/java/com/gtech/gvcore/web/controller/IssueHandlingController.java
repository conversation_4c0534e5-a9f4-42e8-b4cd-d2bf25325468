package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.request.issuehandling.*;
import com.gtech.gvcore.common.response.issuehandling.GetIssueHandlingResponse;
import com.gtech.gvcore.common.response.issuehandling.QueryIssueHandlingByPageResponse;
import com.gtech.gvcore.common.response.issuehandling.RegenerateActivationCodeVoucherInfoResponse;
import com.gtech.gvcore.common.response.issuehandling.ValidateUploadedFileResponse;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import com.gtech.gvcore.service.IssueHandlingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/gv/issueHandling")
@Api(value = "Issue handling.", tags = { "GV Issue handling Api" })
public class IssueHandlingController {
	
	@Autowired
	private IssueHandlingService issueHandlingService;

	@Autowired
	private IssueHandlingDetailsService detailsService;

	/**
	 * 
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月5日
	 */
	@ApiOperation(value = "Create issue handling", notes = "Create issue handling")
    @PostMapping(value = "/createIssueHandling")
	public Result<String> createIssueHandling(@RequestBody @Validated CreateIssueHandlingRequest request){
		issueHandlingService.createIssueHandling(request);
		return Result.ok();
	}
	
	/**
	 * 
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月5日
	 */
	@ApiOperation(value = "Validate uploaded file", notes = "Validate uploaded file")
    @PostMapping(value = "/validateUploadedFile")
	public Result<ValidateUploadedFileResponse> validateUploadedFile(@RequestBody @Validated ValidateUploadedFileRequest request){
		issueHandlingService.validateUploadedFile(request);
		return Result.ok();
	}
	
    /**
     * 
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年4月15日
     */
    @ApiOperation(value = "Edit issue handling", notes = "Edit issue handling")
    @PostMapping(value = "/editIssueHandling")
    public Result<String> editIssueHandling(@RequestBody @Validated EditIssueHandlingRequest request) {
        issueHandlingService.editIssueHandling(request);
		return Result.ok();
	}
    
    /**
     * 
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年5月30日
     */
    @ApiOperation(value = "Submit", notes = "Submit")
    @PostMapping(value = "/submit")
    public Result<String> submit(@RequestBody @Validated IssueHandlingSubmitRequest request) {
        return issueHandlingService.submit(request);
    }

    /**
     * 
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年4月19日
     */
    @ApiOperation(value = "Approve", notes = "Approve")
    @PostMapping(value = "/approve")
    public Result<String> approve(@RequestBody @Validated IssueHandlingApproveRequest request){
        return issueHandlingService.approve(request);
    }

    /**
     * 
     * @param request
     * @return
     * <AUTHOR>
     * @date 2022年4月19日
     */
    @ApiOperation(value = "Cancel", notes = "Cancel")
    @PostMapping(value = "/cancel")
    public Result<String> cancel(@RequestBody @Validated IssueHandlingCancelRequest request){
        return issueHandlingService.cancel(request);
    }
	
	/**
	 * 
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月14日
	 */
	@ApiOperation(value = "Query issue handling by page", notes = "Query issue handling by page")
    @PostMapping(value = "/queryIssueHandlingByPage")
	public PageResult<QueryIssueHandlingByPageResponse> queryIssueHandlingByPage(@RequestBody @Validated QueryIssueHandlingByPageRequest request){
		return issueHandlingService.queryIssueHandlingByPage(request);
	}
	
	/**
	 * 
	 * @param request
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	@ApiOperation(value = "Get issue handling", notes = "Get issue handling")
    @PostMapping(value = "/getIssueHandling")
	public Result<GetIssueHandlingResponse> getIssueHandling(@RequestBody @Validated GetIssueHandlingRequest request){
		return issueHandlingService.getIssueHandling(request);
	}


	@ApiOperation(value = "Get issueHandling result detail", notes = "Get issue handling result detail")
	@PostMapping(value = "/getIssueHandlingResultDetail")
	public Result<List<IssueHandlingDetails>> getIssueHandlingResultDetail(@RequestBody @Validated GetIssueHandlingRequest request){
		List<IssueHandlingDetails> issueHandlingDetails = detailsService.queryByIssueHandlingCode(request.getIssueHandlingCode(), IssueHandlingProcessStatusEnum.FAILED.code(), 500, 0L);
		return Result.ok(issueHandlingDetails);
	}


	@ApiOperation(value = "Re send regenerate activation code to email", notes = "Re send regenerate activation code to email")
	@PostMapping(value = "/resendRegenerateActivationCodeEmail")
	public Result<Void> resendRegenerateActivationCodeEmail(@RequestBody @Validated ResendRegenerateActivationCodeEmailRequest request){
		issueHandlingService.resendRegenerateActivationCodeEmail(request);
		return Result.ok();
	}


	@ApiOperation(value = "Regenerate activation code voucher info", notes = "Regenerate activation code voucher info")
	@PostMapping(value = "/regenerateActivationCodeVoucherInfo")
	public PageResult<RegenerateActivationCodeVoucherInfoResponse> regenerateActivationCodeVoucherInfo(@RequestBody @Validated RegenerateActivationCodeVoucherInfoRequest request){

		return issueHandlingService.regenerateActivationCodeVoucherInfo(request);
	}




}
