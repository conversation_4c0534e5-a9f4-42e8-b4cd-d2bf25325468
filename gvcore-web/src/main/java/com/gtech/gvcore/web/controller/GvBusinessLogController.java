package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.businesslog.CreateBusinessLogRequest;
import com.gtech.gvcore.common.request.businesslog.QueryBusinessLogRequest;
import com.gtech.gvcore.common.response.businesslog.BusinessLogResponse;
import com.gtech.gvcore.service.BusinessLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/3/3 10:40
 */
@RestController
@Slf4j
@RequestMapping(value = "/gv/businessLog")
@Api(value = "Business log.", tags = { "GV Business log Api" })
public class GvBusinessLogController {


    @Autowired
    private BusinessLogService businessLogService;



    @ApiOperation(value = "Create business log",notes = "Create business log.")
    @PostMapping(value = "/createBusinessLog")
    public Result<Void> createBusinessLog(@RequestBody @Validated CreateBusinessLogRequest param) {
        // Return result object
        return businessLogService.createBusinessLog(param);
    }




    @ApiOperation(value = "Query business log",notes = "Query business log.")
    @PostMapping(value = "/queryBusinessLog")
    public PageResult<BusinessLogResponse> queryBusinessLog(@RequestBody @Validated QueryBusinessLogRequest param) {

        // Return result object
        return businessLogService.queryBusinessLog(param);
    }




}
