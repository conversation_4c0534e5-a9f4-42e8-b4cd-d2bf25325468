package com.gtech.gvcore.web.controller.distribution;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.distribution.QueryCpgInventoryDetailRequest;
import com.gtech.gvcore.common.request.distribution.QueryCustomerCpgInventoryDetailRequest;
import com.gtech.gvcore.common.request.distribution.QueryHistoricalInventoryChartRequest;
import com.gtech.gvcore.common.request.distribution.QueryOwnedCpgInfoListRequest;
import com.gtech.gvcore.common.response.distribution.CpgInventoryDetailResponse;
import com.gtech.gvcore.common.response.distribution.CustomerCpgEffectiveDateResult;
import com.gtech.gvcore.common.response.distribution.HistoricalInventoryChartResponse;
import com.gtech.gvcore.common.response.distribution.OwnedCpgInfoResponse;
import com.gtech.gvcore.service.CpgMonthInventorySnapshotService;
import com.gtech.gvcore.service.VoucherService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * @ClassName DistributionVPGController
 * @Description VPG Dashboard
 * <AUTHOR>
 * @Date 2022/7/6 10:13
 * @Version V1.0
 **/
@RestController
@RequestMapping("/gv/distribution/vpg")
@Api(value = "Distribution VPG", tags = {"GV Distribution Dashboard Api"})
public class DistributionVPGController {

    @Autowired
    private VoucherService voucherService;

    @Autowired
    private CpgMonthInventorySnapshotService cpgMonthInventorySnapshotService;

    @PostMapping("/infos/owned")
    @ApiOperation(value = "查询客户所有的VPG信息 ", notes = "查询客户所有的VPG信息,用于Distribution Dashborad 的Voucher Program Group筛选框下拉列表")
    public Result<List<OwnedCpgInfoResponse>> queryOwnedCpgInfoList(@RequestBody @Validated QueryOwnedCpgInfoListRequest request) {

        final List<OwnedCpgInfoResponse> cpgInfoList = this.voucherService.queryOwnedCpgInfoList(request);

        return Result.ok(cpgInfoList);
    }

    @PostMapping("'CpgInventoryDetail")
    @ApiOperation(value = "查询客户cpg库存明细 ", notes = "查询客户cpg库存a明细")
    public Result<List<CustomerCpgEffectiveDateResult>> queryCustomerCpgInventoryDetail(@RequestBody @Validated QueryCustomerCpgInventoryDetailRequest request) {

        return Result.ok(this.voucherService.queryCustomerCpgInventoryDetail(request.getCustomerCode(), request.getCpgCode()));
    }

    @PostMapping("/inventory")
    @ApiOperation(value = "查询VPG的库存信息 ", notes = "查询VPG的库存信息")
    public Result<CpgInventoryDetailResponse> queryCpgInventoryDetail(@RequestBody @Validated QueryCpgInventoryDetailRequest request) {

        final CpgInventoryDetailResponse inventoryDetail = this.voucherService.queryCpgInventoryDetail(request);

        return Result.ok(inventoryDetail);
    }

    @PostMapping("/historical/inventory/chart")
    @ApiOperation(value = "查询VPG的库存信息Chart ", notes = "查询VPG的库存信息Chart")
    public Result<HistoricalInventoryChartResponse> queryHistoricalInventoryChart(@RequestBody @Validated QueryHistoricalInventoryChartRequest request) {

        final HistoricalInventoryChartResponse historicalInventoryChart = this.cpgMonthInventorySnapshotService.queryHistoricalInventoryChart(request);

        return Result.ok(historicalInventoryChart);
    }


    @ApiIgnore
    @PostMapping("/create/inventory/snapshot")
    @ApiOperation(value = "创建VPG的库存信息快照", notes = "创建VPG的库存信息快照,由定时任务调用,每月1号凌晨执行一次")
    public Result<Void> createMonthInventorySnapshot() {

        this.cpgMonthInventorySnapshotService.createMonthInventorySnapshot();

        return Result.ok();
    }

}
