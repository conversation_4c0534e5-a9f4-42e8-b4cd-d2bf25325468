package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.redemption.RedemptionRequest;
import com.gtech.gvcore.common.request.voucher.GetStartAndEndVoucherRequest;
import com.gtech.gvcore.common.request.voucher.VoucherRangeCheckRequest;
import com.gtech.gvcore.common.request.voucherbatch.ReGenerateDigitalVouchersRequest;
import com.gtech.gvcore.common.response.redemption.RedemptionResponse;
import com.gtech.gvcore.common.response.voucher.GetStartAndEndVoucherResponse;
import com.gtech.gvcore.common.response.voucher.VoucherRangeCheckResponse;
import com.gtech.gvcore.service.VoucherService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping(value = "/gv/voucher")
@Api(value = "Voucher.", tags = { "GV Voucher Api" })
public class VoucherController {


    @Lazy
    @Autowired
    private VoucherService voucherService;


    @PostMapping("/check-range")
    @ApiOperation("checkVoucherRange")
    public VoucherRangeCheckResponse checkVoucherRange(@Valid @RequestBody VoucherRangeCheckRequest request) {
        return voucherService.checkVoucherRange(request);
    }

    @ApiOperation(value = "Get start and end voucher.",notes = "Get start and end voucher.")
    @PostMapping(value = "/getStartAndEndVoucher")
    public Result<List<GetStartAndEndVoucherResponse>> getStartAndEndVoucher(@RequestBody @Validated GetStartAndEndVoucherRequest request) {
    	List<GetStartAndEndVoucherResponse> list = voucherService.getStartAndEndVoucher(request);
    	if (CollectionUtils.isNotEmpty(list)) {
    		for (GetStartAndEndVoucherResponse getStartAndEndVoucherResponse : list) {
    			BigDecimal denomination = getStartAndEndVoucherResponse.getDenomination();
    			Long num = getStartAndEndVoucherResponse.getVoucherNum();
    			getStartAndEndVoucherResponse.setAmount(denomination.multiply(BigDecimal.valueOf(num)).setScale(0));
			}
    	}
    	return Result.ok(list);
    }


    @ApiOperation(value = "Re generate digital voucher.",notes = "Re generate digital voucher.")
    @PostMapping(value = "/regenerateDigitalVoucher")
    public Result<Void> regenerateDigitalVoucher(@RequestBody @Validated ReGenerateDigitalVouchersRequest request) {
        voucherService.regenerateDigitalVoucher(request.getCustomerOrderDetail(),request.getVoucherBatchCode());
        return Result.ok();
    }



    @ApiOperation(value = "Validate redemption.",notes = "Validate redemption.")
    @PostMapping(value = "/validateRedemption")
    public Result<RedemptionResponse> validateRedemption(@RequestBody @Validated RedemptionRequest request) {
        return voucherService.validateRedemption(request);
    }




    @ApiOperation(value = "Redemption voucher.",notes = "Redemption voucher.")
    @PostMapping(value = "/redemption")
    public Result<Void> redemption(@RequestBody @Validated RedemptionRequest request) {
        return voucherService.redemption(request);
    }







}
