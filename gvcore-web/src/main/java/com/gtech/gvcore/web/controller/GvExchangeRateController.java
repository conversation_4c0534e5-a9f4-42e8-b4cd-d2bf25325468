package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.exchangerate.*;
import com.gtech.gvcore.common.response.exchangerate.GvExchangeRateByPageResponse;
import com.gtech.gvcore.service.GvExchangeRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * exchange rate(GvExchangeRate)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-25 15:21:47
 */
@RestController
@RequestMapping("/gv/exchangeRate")
@Slf4j
@Api(value = "exchangeRate", tags = { "GV ExchangeRate Api" })
public class GvExchangeRateController {

    @Autowired
    private GvExchangeRateService gvExchangeRateService;

    @ApiOperation(value = "create exchangeRate", notes = "create exchangeRate")
    @PostMapping(value = "/createExchangeRate")
    public Result<Object> createExchangeRate(@RequestBody @Validated CreateExchangeRateRequest createExchangeRateRequest) {
        log.info("CreateExchangeRateRequest:{}", JSON.toJSONString(createExchangeRateRequest));
        return gvExchangeRateService.createExchangeRate(createExchangeRateRequest);
    }

    @ApiOperation(value = "Query exchangeRate", notes = "Query exchangeRate")
    @PostMapping(value = "/queryExchangeRateByPage")
    public PageResult<GvExchangeRateByPageResponse> queryExchangeRateByPage(@RequestBody @Validated QueryExchangeRateByPageRequest request) {
        return gvExchangeRateService.queryExchangeRateByPage(request);
    }


    @ApiOperation(value = "update exchangeRate", notes = "update exchangeRate")
    @PostMapping(value = "/updateExchangeRate")
    public Result<Object> updateExchangeRate(@RequestBody @Validated UpdateExchangeRateRequest updateExchangeRateRequest) {
        log.info("UpdateExchangeRateRequest:{}", JSON.toJSONString(updateExchangeRateRequest));
        return gvExchangeRateService.updateExchangeRate(updateExchangeRateRequest);
    }

    @ApiOperation(value = "update exchangeRate status", notes = "update exchangeRate status")
    @PostMapping(value = "/updateExchangeRateStatus")
    public Result<Object> updateExchangeRateStatus(@RequestBody @Validated UpdateExchangeRateStatusRequest request) {
        log.info("UpdateExchangeRateStatusRequest:{}", JSON.toJSONString(request));
        return gvExchangeRateService.updateExchangeRateStatus(request);
    }

    @ApiOperation(value = "get exchangeRate", notes = "get exchangeRate")
    @PostMapping(value = "/getExchangeRate")
    public Result<Object> getExchangeRate(@RequestBody @Validated GetExchangeRateRequest getExchangeRateRequest) {
        log.info("GetExchangeRateRequest:{}", JSON.toJSONString(getExchangeRateRequest));
        return gvExchangeRateService.getExchangeRate(getExchangeRateRequest);
    }

    @ApiOperation(value = "delete exchangeRate", notes = "delete exchangeRate")
    @PostMapping(value = "/deleteExchangeRate")
    public Result<Object> deleteExchangeRate(@RequestBody @Validated DeleteExchangeRateRequest deleteExchangeRateRequest) {
        log.info("DeleteExchangeRateRequest:{}", JSON.toJSONString(deleteExchangeRateRequest));
        return gvExchangeRateService.deleteExchangeRate(deleteExchangeRateRequest);
    }

}

