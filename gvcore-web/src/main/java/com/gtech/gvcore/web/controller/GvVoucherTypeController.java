
package com.gtech.gvcore.web.controller;


import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.vouchertype.*;
import com.gtech.gvcore.common.response.vouchertype.VoucherTypeResponse;
import com.gtech.gvcore.service.VoucherTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/gv/voucherType")
@Api(value = "VoucherType data dictionary.", tags = { "GV VoucherType Api" })
public class GvVoucherTypeController {



    @Autowired
    private VoucherTypeService voucherTypeService;



    @ApiOperation(value = "Create voucher type",notes = "Create voucher type information.")
	@PostMapping(value = "/createVoucherType")
	public Result<Void> createVoucherType(@RequestBody @Validated CreateVoucherTypeRequest param) {





        // Return result object
        return voucherTypeService.createVoucherType(param);
    }

    @ApiOperation(value = "Update voucher type",notes = "Update voucher type information.")
    @PostMapping(value = "/updateVoucherType")
    public Result<Void> updateVoucherType(@RequestBody @Validated UpdateVoucherTypeRequest param) {

        // Return result object
        return voucherTypeService.updateVoucherType(param);
    }


    @ApiOperation(value = "Update voucher type status",notes = "Update voucher type status.")
    @PostMapping(value = "/updateVoucherTypeStatus")
    public Result<Void> updateVoucherTypeStatus(@RequestBody @Validated UpdateVoucherTypeStatusRequest param) {

        // Return result object
        return voucherTypeService.updateVoucherTypeStatus(param);
    }


    @ApiOperation(value = "Delete voucher type",notes = "Delete voucher type information.")
    @PostMapping(value = "/deleteVoucherType")
    public Result<Void> deleteVoucherType(@RequestBody @Validated DeleteVoucherTypeRequest param) {


        voucherTypeService.deleteVoucherType(param);

        // Return result object
        return Result.ok();
    }


    @ApiOperation(value = "Query voucher type list",notes = "Query voucher type information list.")
    @PostMapping(value = "/queryVoucherTypeList")
    public PageResult<VoucherTypeResponse> queryVoucherTypeList(@RequestBody @Validated QueryVoucherTypeRequest param) {

        // Return result object
        return voucherTypeService.queryVoucherTypeList(param);
    }


    @ApiOperation(value = "Get voucher type",notes = "Get voucher type information.")
    @PostMapping(value = "/getVoucherType")
    public Result<VoucherTypeResponse> getVoucherType(@RequestBody @Validated GetVoucherTypeRequest param) {



        VoucherTypeResponse voucher = voucherTypeService.getVoucherType(param);

        // Return result object
        return Result.ok(voucher);
    }


}
