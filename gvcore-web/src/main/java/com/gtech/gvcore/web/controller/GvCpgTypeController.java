package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.cpg.*;
import com.gtech.gvcore.common.response.cpg.QueryCpgTypeByPageResponse;
import com.gtech.gvcore.service.CpgTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-21 17:49
 **/
@Slf4j
@RestController
@RequestMapping(value = "/gv/cpgType")
@Api(value = "cpg", tags = {"cpg Api"})
public class GvCpgTypeController {

    @Autowired
    private CpgTypeService cpgTypeService;

    @ApiOperation(value = "create cpgType", notes = "create cpgType")
    @PostMapping(value = "/createCpgType")
    public Result<String> createCpgType(@RequestBody @Validated CreateCpgTypeRequest createCpgTypeRequest) {
        log.info("CreateCpgTypeRequest:{}", JSON.toJSONString(createCpgTypeRequest));
        return cpgTypeService.createCpgType(createCpgTypeRequest);
    }

    @ApiOperation(value = "Query CpgType Data", notes = "Query CpgType Data")
    @PostMapping(value = "/queryCpgTypeDataByPage")
    public PageResult<QueryCpgTypeByPageResponse> queryCpgTypeDataByPage(@RequestBody @Validated QueryCpgTypeByPageRequest request) {

        return cpgTypeService.queryCpgTypeDataByPage(request);
    }

    @ApiOperation(value = "CpgType List", notes = "CpgType List")
    @PostMapping(value = "/cpgTypeList")
    public Result<Object> cpgTypeList() {
        return cpgTypeService.cpgTypeList();
    }


    @ApiOperation(value = "update CpgType Status", notes = "update CpgType Status")
    @PostMapping(value = "/updateCpgTypeStatus")
    public Result<Object> updateCpgTypeStatus(@RequestBody @Validated UpdateCpgTypeStatusRequest request) {

        return cpgTypeService.updateCpgTypeStatus(request);
    }

    @ApiOperation(value = "update CpgType", notes = "update CpgType")
    @PostMapping(value = "/updateCpgType")
    public Result<Object> updateCpgType(@RequestBody @Validated UpdateCpgTypeRequest request) {

        return cpgTypeService.updateCpgType(request);
    }


    @ApiIgnore
    @ApiOperation(value = "delete CpgType", notes = "delete CpgType")
    @PostMapping(value = "/deleteCpgType/{cpgTypeCode}")
    public Result<Object> deleteCpgType(@PathVariable String cpgTypeCode) {
        return cpgTypeService.deleteCpgTypeById(cpgTypeCode);
    }

    @ApiOperation(value = "get cpgType", notes = "get cpgType")
    @PostMapping(value = "/getCpgType")
    public Result<Object> getCpgType(@RequestBody @Validated GetCpgTypeRequest getCpgTypeRequest) {
        log.info("GetCpgTypeRequest:{}", JSON.toJSONString(getCpgTypeRequest));
        return cpgTypeService.getCpgType(getCpgTypeRequest);
    }
}
