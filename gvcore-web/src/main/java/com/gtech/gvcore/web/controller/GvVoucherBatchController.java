package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.voucher.SendVoucherRequest;
import com.gtech.gvcore.common.request.voucherbatch.*;
import com.gtech.gvcore.common.response.voucherbatch.QueryStartCodeResponse;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import com.gtech.gvcore.components.VoucherComponent;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.VoucherBatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2022/3/3 10:40
 */
@RestController
@Slf4j
@RequestMapping(value = "/gv/voucherBatch")
@Api(value = "Voucher batch.", tags = { "GV Voucher Batch Api" })
public class GvVoucherBatchController {


    @Autowired
    private VoucherBatchService voucherBatch;

    @Autowired
    private VoucherNumberHelper voucherNumberHelper;

    @Autowired
    private VoucherComponent voucherComponent;



    @ApiOperation(value = "Create voucher batch",notes = "Create voucher batch information.")
    @PostMapping(value = "/createVoucherBatch")
    public Result<String> createVoucherBatch(@RequestBody @Validated CreateVoucherBatchRequest param) {
        param.validation();
        // Return result object
        return voucherBatch.createVoucherBatch(param);
    }


    @ApiOperation(value = "Create voucher batch new",notes = "Create voucher batch information.")
    @PostMapping(value = "/createVoucherBatchNew")
    public Result<String> createVoucherBatchNew(@RequestBody @Validated CreateVoucherBatchRequest param) {
        param.validation();
        // Return result object
        return voucherComponent.createVoucherBatch(param);
    }


    @ApiOperation(value = "Regenerate voucher batch",notes = "Regenerate voucher batch information.")
    @PostMapping(value = "/regenerateVoucherBatch")
    public Result<Void> regenerateVoucherBatch(@RequestBody @Validated RegenerateVoucherBatchRequest param) {
        // Return result object
        return voucherBatch.regenerateVoucherBatch(param);
    }

    @ApiOperation(value = "Cancel voucher batch",notes = "Cancel voucher batch information.")
    @PostMapping(value = "/cancelVoucherBatch")
    public Result<Object> cancelVoucherBatch(@RequestBody @Validated CancelVoucherBatchRequest param) {
        // Return result object
        voucherBatch.cancelVoucherBatch(param);
        return Result.ok();
    }


    @ApiOperation(value = "Query voucher batch",notes = "Query voucher batch information.")
    @PostMapping(value = "/queryVoucherBatch")
    public PageResult<VoucherBatchResponse> queryVoucherBatch(@RequestBody @Validated QueryVoucherBatchRequest param) {

        // Return result object
        return voucherBatch.queryVoucherBatch(param);
    }


    @ApiOperation(value = "Update voucher batch",notes = "Update voucher batch information.")
    @PostMapping(value = "/updateVoucherBatch")
    public Result<Void> updateVoucherBatch(@RequestBody @Validated UpdateVoucherBatchRequest param) {

        // Return result object
        return voucherBatch.updateVoucherBatch(param);
    }

    @ApiOperation(value = "Get voucher batch",notes = "Get voucher batch information.")
    @PostMapping(value = "/getVoucherBatch")
    public Result<VoucherBatchResponse> getVoucherBatch(@RequestBody @Validated GetVoucherBatchRequest param) {

        // Return result object
        return new Result<>(voucherBatch.getVoucherBatch(param));
    }

    @ApiOperation(value = "Real time progress bar",notes = "Real time progress bar.")
    @PostMapping(value = "/realTimeProgressBar")
    public Result<Integer> realTimeProgressBar(@RequestBody @Validated QueryRealTimeProgressBarRequest param) {
        log.info("param:{}", JSON.toJSONString(param));
        // Return result object
        return voucherBatch.realTimeProgressBar(param);
    }

    @ApiOperation(value = "Query start code",notes = "Query start code.")
    @PostMapping(value = "/queryStartCode")
    public Result<QueryStartCodeResponse> queryStartCode(@RequestBody QueryStartCodeRequest request) {

        // Return result object
        return new Result<>(voucherBatch.queryStartCode(request));
    }


    @ApiOperation(value = "Send",notes = "Send.")
    @PostMapping(value = "/send")
    public Result<String> send(@RequestBody SendVoucherRequest request) throws IOException {

        // Return result object
        return Result.ok(voucherBatch.export(request));
    }


    @ApiOperation(value = "Generate digital voucher",notes = "Generate digital voucher.")
    @PostMapping(value = "/generateDigitalVoucher")
    public Result<String> generateDigitalVoucher(@RequestBody GenerateDigitalVouchersRequest request) throws IOException {

        // Return result object
        return voucherBatch.generateDigitalVoucher(request);
    }

    @ApiOperation(value = "BarCode to code",notes = "BarCode convert to code.")
    @PostMapping(value = "/barCodeToCode")
    public Result<String> barCodeToCode(@RequestBody BarCodeToCodeRequest barCode) {

        // Return result object
        return Result.ok(voucherBatch.barCodeToCode(barCode));
    }


}
