package com.gtech.gvcore;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import tk.mybatis.spring.annotation.MapperScan;

@SpringBootApplication(scanBasePackages = { "com.gtech" })
@EnableEurekaClient
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableApolloConfig
@MapperScan({ "com.gtech.basic.masterdata.web.dao", "com.gtech.basic.idm.dao.mapper", "com.gtech.message.dao", "com.gtech.gvcore.dao.mapper" })
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
public class GvcoreApplication {

    public static void main(String[] args) {

        SpringApplication.run(GvcoreApplication.class, args);

    }
}
