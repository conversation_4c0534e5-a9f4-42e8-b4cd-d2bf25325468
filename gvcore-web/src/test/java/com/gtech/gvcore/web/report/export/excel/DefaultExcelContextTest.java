package com.gtech.gvcore.web.report.export.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.basic.masterdata.web.service.MasterDataDdLangService;
import com.gtech.commons.page.PageParam;
import com.gtech.commons.utils.LocalCacheUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.OrderReportFileMapper;
import com.gtech.gvcore.dao.model.OrderReportFile;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.ReportRequestService;
import com.gtech.gvcore.service.report.extend.ReportUploadHelper;
import com.gtech.gvcore.web.report.CustomTestReport;
import com.gtech.gvcore.web.report.ReportTestHelper;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipInputStream;

/**
 * @ClassName DefaultExcelContextTest
 * @Description
 * <AUTHOR>
 * @Date 2023/3/3 14:35
 * @Version V1.0
 **/
@Component
public class DefaultExcelContextTest {

    @Autowired private ReportRequestService reportRequestService;
    @Autowired private OrderReportFileMapper reportFileMapper;
    @Autowired private MasterDataDdLangService masterDataDdLangService;


    public static class Param extends PageParam implements ReportQueryParam {}
    public static class Bean {
        @ExcelProperty(value = "name", index = 0)
        private final String name = "张三";
    }

    public static class DefaultExcelContextReportImpl implements CustomTestReport<Param, Bean>, PollReport {

        int number = 0;
        List<List<Bean>> data = new ArrayList<>();

        @Override
        public ReportExportTypeEnum exportTypeEnum() {
            return ReportExportTypeEnum.JUNIT_DEFAULT_EXCEL_CONTEXT;
        }

        @Override
        public Param builderQueryParam(CreateReportRequest reportParam) {

            data.add(new ArrayList<Bean>() {
                {
                    for (int i = 0; i < 100; i++) add(new Bean());
                }
            });
            data.add(new ArrayList<Bean>() {
                {
                    for (int i = 0; i < 57; i++) add(new Bean());
                }
            });
            data.add(new ArrayList<Bean>() {
                {
                    for (int i = 0; i < 32; i++) add(new Bean());
                }
            });
            data.add(new ArrayList<>());

            return new Param();
        }

        @Override
        public List<Bean> getExportData(Param param) {

            return data.get(number++);
        }

    }

    public int countZip(String path) {

        if (!path.contains(".zip")) return 0;

        int count = 0;
        try {

            URL url = new URL(path);

            try (ZipInputStream zis = new ZipInputStream(url.openStream())) {
                while (zis.getNextEntry() != null) count++;
            } catch (IOException e) {
                e.printStackTrace();
            }

        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }

        return count;
    }

    public void test() {

        try {

            ReportTestHelper.reportFactoryRegister(new DefaultExcelContextReportImpl());

            this.testReportUploadHelperGetExcelFileMaxSize();

            String reportCode = this.testBuilderDefaultReport();

            this.testReportFile(reportCode);

        } finally {

            LocalCacheUtil.remove(ReportUploadHelper.REPORT_FILE_MAX_SIZE_LOCAL_CACHE_KEY);

        }


    }

    private void testReportFile(String reportCode) {
        OrderReportFile orderReportFile = reportFileMapper.selectOne(OrderReportFile.builder().orderReportCode(reportCode).build());
        Assert.assertNotNull(orderReportFile);

        int count = countZip(orderReportFile.getReportFileUrl());
        Assert.assertEquals(4, count);
    }

    @NotNull
    private String testBuilderDefaultReport() {
        String reportCode = reportRequestService.createReport(new CreateReportRequest().setReportType(ReportExportTypeEnum.JUNIT_DEFAULT_EXCEL_CONTEXT.getExportType())
                .setCreateUser("user"), true);
        Assert.assertTrue(StringUtils.isNotBlank(reportCode));
        return reportCode;
    }

    private void testReportUploadHelperGetExcelFileMaxSize() {
        // find db and cache
        ReportUploadHelper.getExcelFileMaxSize();

        //find default
        masterDataDdLangService.deleteByDdCode(ReportUploadHelper.REPORT_FILE_MAX_SIZE_DD_LANG_KEY, GvcoreConstants.SYSTEM_DEFAULT);
        ReportUploadHelper.getExcelFileMaxSize();

        //setting cache and get cache
        LocalCacheUtil.save(ReportUploadHelper.REPORT_FILE_MAX_SIZE_LOCAL_CACHE_KEY, 50, 60 * 60 * 1000L);
        Assert.assertEquals(ReportUploadHelper.getExcelFileMaxSize(), 50);
    }


}
