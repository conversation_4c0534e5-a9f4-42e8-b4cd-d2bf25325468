package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.outlet.*;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.service.OutletService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/16 17:36
 */
@RestController
@RequestMapping(value = "/gv/outlet")
@Api(value = "Outlet data dictionary.", tags = {"GV outlet Api"})
public class GvOutletController {


    @Autowired
    private OutletService outletService;

    @ApiOperation(value = "Create outlet ", notes = "Create outlet information.")
    @PostMapping(value = "/createOutlet")
    public Result<String> createOutlet(@RequestBody @Validated CreateOutletRequest param) {

        param.validation();

        // Return result object
        return outletService.createOutlet(param);
    }

    @ApiOperation(value = "Update outlet ", notes = "Update outlet information.")
    @PostMapping(value = "/updateOutlet")
    public Result<Void> updateOutlet(@RequestBody @Validated UpdateOutletRequest param) {
        param.validation();

        // Return result object
        return outletService.updateOutlet(param);
    }


    @ApiOperation(value = "Update outlet status", notes = "Update outlet status.")
    @PostMapping(value = "/updateOutletStatus")
    public Result<Void> updateOutletStatus(@RequestBody @Validated UpdateOutletStatusRequest param) {

        // Return result object
        return outletService.updateOutletStatus(param);
    }

    @ApiOperation(value = "Delete outlet ", notes = "Delete outlet information.")
    @PostMapping(value = "/deleteOutlet")
    public Result<Void> deleteOutlet(@RequestBody @Validated DeleteOutletRequest param) {


        outletService.deleteOutlet(param);

        // Return result object
        return Result.ok();
    }


    @ApiOperation(value = "Query outlet  list", notes = "Query outlet information list.")
    @PostMapping(value = "/queryOutletList")
    public PageResult<OutletResponse> queryOutletList(@RequestBody @Validated QueryOutletRequest param) {

        // Return result object
        return outletService.queryOutletList(param);
    }


    @ApiOperation(value = "Get outlet ", notes = "Get outlet information.")
    @PostMapping(value = "/getOutlet")
    public Result<OutletResponse> getOutlet(@RequestBody @Validated GetOutletRequest param) {


        OutletResponse outlet = outletService.getOutlet(param);

        // Return result object
        return Result.ok(outlet);
    }


    @ApiOperation(value = "Query outlet by merchant codes", notes = "Query outlet information by merchant codes.")
    @PostMapping(value = "/queryOutletByMerchantCodes")
    public PageResult<OutletResponse> queryOutletByMerchantCodes(@RequestBody @Validated QueryOutletByMerchantCodesRequest param) {


        // Return result object
        return outletService.queryOutletByMerchantCodes(param);
    }

    @PostMapping(value = "/queryOutletByBusinessType")
    @ApiOperation("queryOutletByBusinessType")
    public Result<List<OutletResponse>> queryOutletByBusinessType(@Validated @RequestBody QueryOutletByBusinessRequest queryOutletByBusinessRequest) {
        return outletService.queryOutletByBusinessType(queryOutletByBusinessRequest);
    }

    @PostMapping(value = "/replaceOutletCode")
    @ApiOperation("replaceOutletCode")
    public Result<Void> replaceOutletCode(@RequestBody ReplaceOutletCodeRequest outlet) {
        return outletService.updateOutletCodeScript(outlet);
    }

    @GetMapping(value = "/checkColumWithOutlet")
    @ApiOperation("replaceOutletCode")
    public Result<Void> checkColumWithOutlet() {
        return outletService.checkColumWithOutlet();
    }


}
