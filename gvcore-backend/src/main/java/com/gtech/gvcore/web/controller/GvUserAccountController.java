
package com.gtech.gvcore.web.controller;


import com.gtech.basic.idm.common.exceptions.ErrorCodes;
import com.gtech.basic.idm.service.PageService;
import com.gtech.basic.idm.service.UserRoleMappingService;
import com.gtech.basic.idm.service.dto.GetUserAccountParamDto;
import com.gtech.basic.idm.service.dto.PageDto;
import com.gtech.basic.idm.service.dto.UserRoleDto;
import com.gtech.basic.idm.web.helper.CodeHelper;
import com.gtech.basic.idm.web.vo.bean.RoleInfoBean;
import com.gtech.basic.idm.web.vo.param.GetUserAccountParam;
import com.gtech.basic.idm.web.vo.param.QueryPageListByMenuListParam;
import com.gtech.basic.idm.web.vo.result.CreateUserAccountResult;
import com.gtech.basic.idm.web.vo.result.QueryPageListResult;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ApproveTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.flow.GetFlowNoticeRequest;
import com.gtech.gvcore.common.request.releaseapprove.QueryApproveNodeRequest;
import com.gtech.gvcore.common.request.useraccount.*;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.common.response.useraccount.UserAccountResponse;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping(value = "/gv/userAccount")
@Api(value = "UserAccount Basic information maintenance.", tags = { "GV User Account Api" })
public class GvUserAccountController {

    @Autowired
    private CodeHelper codeHelper;

    @Autowired
    private GvUserAccountService userAccountService;

    @Autowired
    private UserRoleMappingService userRoleMappingService;
    
    @Autowired
    private PageService pageService;

	@Autowired
	private FlowNoticeService flowNoticeService;

	@Autowired
	private ReleaseApproveService releaseApproveService;

	@Autowired
	private CustomerOrderService customerOrderService;
	@Autowired
	private VoucherRequestService voucherRequestService;

    @ApiOperation(value = "Create user account",notes = "Create user account information.")
	@PostMapping(value = "/createGvUserAccount")
	public Result<CreateUserAccountResult> createGvUserAccount(@RequestBody GvCreateUserAccountRequest param) {

        // Parameter validation
        param.validate();
		param.setOrgCode(GvcoreConstants.SYSTEM_DEFAULT);
        String userCode = param.getUserCode();
        if (StringUtil.isBlank(userCode)) {
            // If the userCode is blank, the system automatically generate a userCode.
            userCode = this.codeHelper.generateUserCode();
			param.setUserCode(userCode);
        }

		userAccountService.createUserAccount(param);

        if (CollectionUtils.isNotEmpty(param.getRoleCodeList())) {
            userRoleMappingService.bindUserRoles(param.getDomainCode(), param.getTenantCode(), userCode, param.getRoleCodeList(), param.getOperateUser());
        }

        // Return result object
        return Result.ok(CreateUserAccountResult.builder().userCode(userCode).build());
    }

    @ApiOperation(value = "Update user account",notes = "Update user account information.")
	@PostMapping(value = "/updateGvUserAccount")
	public Result<Void> updateGvUserAccount(@RequestBody GvUpdateUserAccountRequest param) {

        // Parameter validation
        param.validate();

		if (userAccountService.updateUserAccount(param) < 1) {
            throw Exceptions.fail(ErrorCodes.UPDATE_USERINFO_FAILED);
        }

        if (CollectionUtils.isNotEmpty(param.getRoleCodeList())) {
			userRoleMappingService.bindUserRoles(param.getDomainCode(), param.getTenantCode(), param.getUserCode(), param.getRoleCodeList(),
					param.getOperateUser());
        }

        // Return result object
        return Result.ok();
    }

    @ApiOperation(value = "Get user account",notes = "Get user account detail information .")
	@PostMapping(value = "/getGvUserAccount")
	public Result<UserAccountResponse> getGvUserAccount(@RequestBody GetUserAccountParam param) {

        // Parameter validation
        param.validate();

        GetUserAccountParamDto paramDto = BeanCopyUtils.jsonCopyBean(param, GetUserAccountParamDto.class);

		UserAccountResponse getUserAccountResponse = userAccountService.getUserAccount(paramDto);
		if (null == getUserAccountResponse) {
            throw Exceptions.fail(ErrorCodes.FIND_USER_FAILED, param.keyString());
        }

		List<UserRoleDto> userRoleDtoList = userRoleMappingService.queryUserRoles(getUserAccountResponse.getDomainCode(),
				getUserAccountResponse.getTenantCode(), getUserAccountResponse.getUserCode());
        if (CollectionUtils.isNotEmpty(userRoleDtoList)) {
			getUserAccountResponse.setRoleList(BeanCopyUtils.jsonCopyList(userRoleDtoList, RoleInfoBean.class));
        }

        // Return result object
		return Result.ok(getUserAccountResponse);
    }

	@ApiOperation(value = "Get user data permission code", notes = "Get user data permission code .")
	@PostMapping(value = "/getUserDataPermissionCode")
	public Result<List<PermissionCodeResponse>> getUserDataPermissionCode(@RequestBody @Validated GetUserPermissionCodeRequest request) {

		return Result.ok(userAccountService.queryPerrmissionCodeList(request.getUserCode()));
	}

	@ApiOperation(value = "Query user account list", notes = "Query user account information list by conditions.")
	@PostMapping(value = "/queryGvUserAccountList")
	public PageResult<UserAccountResponse> queryGvUserAccountList(@RequestBody GvQueryUserAccountRequest param) {

		// Parameter validation
		param.validate();

		PageData<UserAccountResponse> pageResultList = userAccountService.queryUserAccountList(param);

		ArrayList<UserAccountResponse> reslutList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(pageResultList.getList())) {
			for (UserAccountResponse userAccountResponse : pageResultList.getList()) {


				List<UserRoleDto> userRoleDtoList = userRoleMappingService.queryUserRoles(userAccountResponse.getDomainCode(),
						userAccountResponse.getTenantCode(), userAccountResponse.getUserCode());
				if (CollectionUtils.isNotEmpty(userRoleDtoList)) {

					userAccountResponse.setRoleList(BeanCopyUtils.jsonCopyList(userRoleDtoList, RoleInfoBean.class));
				}

				reslutList.add(userAccountResponse);
			}
		}
		// Return result object
		return PageResult.ok(reslutList, pageResultList.getTotal());
	}
	
	@ApiOperation(value = "Query page list by role")
	@PostMapping(value = "/queryPageListByRole")
	public Result<List<QueryPageListResult>> queryPageListByRole(@RequestBody GvGetPageListRequest param) {

		List<String> menuCodeList = userAccountService.getMenuCodeListByRoles(param.getRoleCodeList());
		if (CollectionUtils.isEmpty(menuCodeList)) {
			return Result.ok(Collections.emptyList());
		}
		QueryPageListByMenuListParam menuListParam = new QueryPageListByMenuListParam();
		menuListParam.setDomainCode(GvcoreConstants.SYSTEM_DEFAULT);
		menuListParam.setAppCode(GvcoreConstants.APP_CODE);
		menuListParam.setMenuCodeList(menuCodeList);
		List<PageDto> list = pageService.queryPageListByMenuList(menuListParam);
		// Return result object
		return Result.ok(BeanCopyUtils.jsonCopyList(list, QueryPageListResult.class));
	}

	@ApiOperation(value = "Query user list by flow notice")
	@PostMapping(value = "/queryUserByFlowNotice")
	public Result<List<UserAccountResponse>> queryUserByFlowNotice(@Validated @RequestBody GetFlowNoticeRequest request) {
		List<UserAccount> list = flowNoticeService.queryUserByFlowNotice(request.getFlowCode(), request.getFlowNodeCode(), request.getBusinessCode());
		return Result.ok(BeanCopyUtils.jsonCopyList(list, UserAccountResponse.class));
	}

	@ApiOperation(value = "Query user by approve config")
	@PostMapping(value = "/queryUserByApproveConfig")
	public Result<List<UserAccountResponse>> queryUserByApproveConfig(@RequestBody @Validated QueryApproveNodeRequest queryApproveNodeRequest) {
		String businessCode = queryApproveNodeRequest.getBusinessCode();
		if (ApproveTypeEnum.APPROVE_TYPE_01.getType().equals(queryApproveNodeRequest.getConfigType())) {
			CustomerOrder customerOrder = customerOrderService.queryByCustomerOrderCode(businessCode);
			if (customerOrder == null) {
				throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
			}
			queryApproveNodeRequest.setVoucherAmount(customerOrder.getVoucherAmount());
			queryApproveNodeRequest.setPermissionCode(customerOrder.getIssuerCode());
			queryApproveNodeRequest.setIssuerCode(customerOrder.getIssuerCode());
		} else if (ApproveTypeEnum.APPROVE_TYPE_02.getType().equals(queryApproveNodeRequest.getConfigType())) {
			VoucherRequest voucherRequest = voucherRequestService.queryByVoucherRequestCode(businessCode);
			if (voucherRequest == null) {
				throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
			}
			queryApproveNodeRequest.setVoucherAmount(voucherRequest.getVoucherAmount());
			queryApproveNodeRequest.setPermissionCode(voucherRequest.getIssuerCode());
			queryApproveNodeRequest.setIssuerCode(voucherRequest.getIssuerCode());
		}
		List<UserAccount> list = releaseApproveService.queryUserByApproveNode(queryApproveNodeRequest);
		return Result.ok(BeanCopyUtils.jsonCopyList(list, UserAccountResponse.class));
	}

}
