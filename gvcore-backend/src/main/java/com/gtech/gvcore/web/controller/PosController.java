package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.pos.*;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.service.PosService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/gv/pos")
@Api(value = "Pos maintenance.", tags = { "GV Pos Api" })
public class PosController {


    @Autowired
    private PosService posService;

    @ApiOperation(value = "Create pos ",notes = "Create pos information.")
    @PostMapping(value = "/createPos")
    public Result<String> createPos(@RequestBody @Validated CreatePosRequest param) {


        // Return result object
        return posService.createPos(param);
    }

    @ApiOperation(value = "Update pos ",notes = "Update pos information.")
    @PostMapping(value = "/updatePos")
    public Result<String> updatePos(@RequestBody @Validated UpdatePosRequest param) {

        // Return result object
        return posService.updatePos(param);
    }


    @ApiOperation(value = "Update pos status",notes = "Update pos status.")
    @PostMapping(value = "/updatePosStatus")
    public Result<String> updatePosStatus(@RequestBody @Validated UpdatePosStatusRequest param) {

        // Return result object
        return posService.updatePosStatus(param);
    }


    


    @ApiOperation(value = "Query pos  list",notes = "Query pos information list.")
    @PostMapping(value = "/queryPosList")
    public PageResult<PosResponse> queryPosList(@RequestBody @Validated QueryPosListRequest param) {

        // Return result object
        return posService.queryPosList(param);
    }


    @ApiOperation(value = "Get pos ",notes = "Get pos information.")
    @PostMapping(value = "/getPos")
    public Result<PosResponse> getPos(@RequestBody @Validated GetPosRequest param) {

        // Return result object
        return posService.getPos(param);
    }



    
    
    
}
