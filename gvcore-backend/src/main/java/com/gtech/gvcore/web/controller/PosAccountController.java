package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.posaccount.CreatePosAccountRequest;
import com.gtech.gvcore.common.request.posaccount.DeletePosAccountRequest;
import com.gtech.gvcore.common.request.posaccount.QueryPosAccountListRequest;
import com.gtech.gvcore.common.request.posaccount.UpdatePosAccountRequest;
import com.gtech.gvcore.common.response.posaccount.PosAccountResponse;
import com.gtech.gvcore.service.PosAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/gv/posAccount")
@Api(value = "Pos maintenance.", tags = { "GV Pos Account Api" })
public class PosAccountController {


    @Autowired
    private PosAccountService posAccountService;

    @ApiOperation(value = "Create pos account",notes = "Create pos information.")
    @PostMapping(value = "/createPosAccount")
    public Result<String> createPosAccount(@RequestBody @Validated CreatePosAccountRequest param) {
        // Return result object
        return posAccountService.createPosAccount(param);
    }

    @ApiOperation(value = "Update pos account",notes = "Update pos information.")
    @PostMapping(value = "/updatePosAccount")
    public Result<String> updatePosAccount(@RequestBody @Validated UpdatePosAccountRequest param) {
        // Return result object
        return posAccountService.updatePosAccount(param);
    }


    @ApiOperation(value = "Delete pos account",notes = "Delete pos account information.")
    @PostMapping(value = "/deletePosAccount")
    public Result<String> deletePosAccount(@RequestBody @Validated DeletePosAccountRequest param) {
        // Return result object
        return posAccountService.deletePosAccount(param);
    }


    @ApiOperation(value = "Query pos account list",notes = "Query pos account information.")
    @PostMapping(value = "/queryPosAccountList")
    public PageResult<PosAccountResponse> queryPosAccountList(@RequestBody @Validated QueryPosAccountListRequest param) {
        // Return result object
        return posAccountService.queryPosAccountList(param);
    }





}
