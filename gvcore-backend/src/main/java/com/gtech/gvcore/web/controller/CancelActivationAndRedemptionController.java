package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.transactiondata.CreateTransactionDataRequest;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.TransactionDataService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * @ClassName
 * @Description
 * <AUTHOR>
 * @Date 2024/3/14
 * @Since
 **/
@RestController
@RequestMapping(value = "/gv/cancel")
@Api(value = "CancelActivationAndRedemption.", tags = {"CancelActivationAndRedemptionApi"})
public class CancelActivationAndRedemptionController {

    @Autowired
    TransactionDataService transactionDataService;
    @Autowired
    private GvCodeHelper gvCodeHelper;

    @PostMapping("/cancel")
    public Result<String> saveTransactionData(@RequestBody @Validated CreateTransactionDataRequest transactionData){
        String transactionCode = gvCodeHelper.generateTransactionDataCode();

        transactionData.setTransactionCode(transactionCode);
        transactionData.setTransactionDate(new Date());
        transactionData.setVoucherEffectiveDate(new Date());
        transactionData.setNotes("Manual");
        transactionDataService.createTransactionData(transactionData);
        return Result.ok(transactionCode) ;
    }

}
