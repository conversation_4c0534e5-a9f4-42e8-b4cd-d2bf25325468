package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.outletproductcategory.*;
import com.gtech.gvcore.common.response.outletproductcategory.OutletProductCategoryResponse;
import com.gtech.gvcore.service.OutletProductCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/2/16 17:36
 */
@RestController
@RequestMapping(value = "/gv/outletProductCategory")
@Api(value = "Outlet product category data dictionary.", tags = { "GV outlet product category Api" })
public class GvOutletProductCategoryMethodController {


    @Autowired
    private OutletProductCategoryService outletProductCategoryService;
    
    @ApiOperation(value = "Create outlet product category ",notes = "Create outlet product category information.")
    @PostMapping(value = "/createOutletProductCategory")
    public Result<Void> createOutletProductCategory(@RequestBody @Validated CreateOutletProductCategoryRequest param) {




        // Return result object
        return outletProductCategoryService.createOutletProductCategory(param);
    }

    @ApiOperation(value = "Update outlet product category ",notes = "Update outlet product category information.")
    @PostMapping(value = "/updateOutletProductCategory")
    public Result<Void> updateOutletProductCategory(@RequestBody @Validated UpdateOutletProductCategoryRequest param) {




        // Return result object
        return outletProductCategoryService.updateOutletProductCategory(param);
    }

    @ApiOperation(value = "Delete outlet product category ",notes = "Delete outlet product category information.")
    @PostMapping(value = "/deleteOutletProductCategory")
    public Result<Void> deleteOutletProductCategory(@RequestBody @Validated DeleteOutletProductCategoryRequest param) {


        outletProductCategoryService.deleteOutletProductCategory(param);

        // Return result object
        return Result.ok();
    }


    @ApiOperation(value = "Query outlet product category  list",notes = "Query outlet product category information list.")
    @PostMapping(value = "/queryOutletProductCategoryList")
    public PageResult<OutletProductCategoryResponse> queryOutletProductCategoryList(@RequestBody @Validated QueryOutletProductCategoryRequest param) {

        // Return result object
        return outletProductCategoryService.queryOutletProductCategoryList(param);
    }


    @ApiOperation(value = "Get outlet product category ",notes = "Get outlet product category information.")
    @PostMapping(value = "/getOutletProductCategory")
    public Result<OutletProductCategoryResponse> getOutletProductCategory(@RequestBody @Validated GetOutletProductCategoryRequest param) {


        OutletProductCategoryResponse outletProductCategory = outletProductCategoryService.getOutletProductCategory(param);

        // Return result object
        return Result.ok(outletProductCategory);
    }
    
    
    
}
