package com.gtech.gvcore.web.controller.monitor;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.service.JvmMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * JVM监控接口
 * 用于监控JVM内存和对象使用情况
 */
@Slf4j
@RestController
@RequestMapping("/gv/monitor/jvm")
@Api(value = "JVM Monitor", tags = {"JVM Monitor Api"})
public class JvmMonitorController {

    @Autowired
    private JvmMonitorService jvmMonitorService;

    @GetMapping("/memory/simple")
    @ApiOperation("获取简化的内存统计信息")
    public Result<String> getSimpleMemoryStats() {
        try {
            String stats = jvmMonitorService.getSimpleObjectStatistics();
            return Result.ok(stats);
        } catch (Exception e) {
            log.error("获取简化内存统计失败", e);
            Result<String> result = new Result<>();
            result.setCode(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code());
            result.setMessage("获取内存统计失败: " + e.getMessage());
            return result;
        }
    }

    @GetMapping("/memory/detailed")
    @ApiOperation("获取详细的内存统计信息")
    public Result<String> getDetailedMemoryStats() {
        try {
            String stats = jvmMonitorService.getDetailedObjectStatistics();
            return Result.ok(stats);
        } catch (Exception e) {
            log.error("获取详细内存统计失败", e);
            Result<String> result = new Result<>();
            result.setCode(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code());
            result.setMessage("获取内存统计失败: " + e.getMessage());
            return result;
        }
    }

    @GetMapping("/memory/risk")
    @ApiOperation("评估内存泄漏风险")
    public Result<String> assessMemoryLeakRisk() {
        try {
            String risk = jvmMonitorService.assessMemoryLeakRisk();
            return Result.ok(risk);
        } catch (Exception e) {
            log.error("评估内存风险失败", e);
            Result<String> result = new Result<>();
            result.setCode(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code());
            result.setMessage("评估内存风险失败: " + e.getMessage());
            return result;
        }
    }

    @GetMapping("/objects/instances")
    @ApiOperation("获取详细的对象实例信息")
    public Result<String> getDetailedObjectInstances() {
        try {
            String instances = jvmMonitorService.getDetailedObjectInstances();
            return Result.ok(instances);
        } catch (Exception e) {
            log.error("获取详细对象实例失败", e);
            Result<String> result = new Result<>();
            result.setCode(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code());
            result.setMessage("获取详细对象实例失败: " + e.getMessage());
            return result;
        }
    }

    @GetMapping("/gc")
    @ApiOperation("强制执行垃圾回收")
    public Result<String> forceGarbageCollection() {
        try {
            String result = jvmMonitorService.forceGarbageCollection();
            return Result.ok(result);
        } catch (Exception e) {
            log.error("执行GC失败", e);
            Result<String> result = new Result<>();
            result.setCode(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code());
            result.setMessage("执行GC失败: " + e.getMessage());
            return result;
        }
    }
}
