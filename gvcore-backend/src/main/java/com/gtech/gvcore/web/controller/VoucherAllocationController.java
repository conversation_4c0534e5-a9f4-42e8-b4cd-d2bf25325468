package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.allocation.AllocateRequest;
import com.gtech.gvcore.common.request.allocation.GetVoucherAllocationRequest;
import com.gtech.gvcore.common.request.allocation.QueryVoucherAllocationByPageRequest;
import com.gtech.gvcore.common.response.allocation.GetVoucherAllocationResponse;
import com.gtech.gvcore.common.response.allocation.QueryVoucherAllocationByPageResponse;
import com.gtech.gvcore.service.VoucherAllocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022年3月14日
 */
@RestController
@RequestMapping(value = "/gv/allocation")
@Api(value = "Vourcher allocation.", tags = { "GV Vourcher Allocation Api" })
public class VoucherAllocationController {

    @Autowired
    private VoucherAllocationService voucherAllocationService;

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年3月14日
     */
    @ApiOperation(value = "Voucher Allocation", notes = "Voucher Allocation")
    @PostMapping(value = "/allocate")
    public Result<Void> allocate(@RequestBody @Validated AllocateRequest request) {

        return voucherAllocationService.allocate(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年3月14日
     */
    @ApiOperation(value = "Query voucher allocation by page", notes = "Query voucher allocation by page")
    @PostMapping(value = "/queryVoucherAllocationByPage")
    public PageResult<QueryVoucherAllocationByPageResponse> queryVoucherAllocationByPage(
            @RequestBody @Validated QueryVoucherAllocationByPageRequest request) {
        return voucherAllocationService.queryVoucherAllocationByPage(request);
    }

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年3月14日
     */
    @ApiOperation(value = "Get voucher allocation info", notes = "Get Voucher Allocation info")
    @PostMapping(value = "/getVoucherAllocation")
    public Result<GetVoucherAllocationResponse> getVoucherAllocation(
            @RequestBody @Validated GetVoucherAllocationRequest request) {
        return voucherAllocationService.getVoucherAllocation(request);
    }

}
