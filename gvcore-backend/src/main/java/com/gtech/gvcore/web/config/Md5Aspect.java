package com.gtech.gvcore.web.config;

import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.exception.GTechBaseException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
@Slf4j
public class Md5Aspect {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Pointcut("@annotation(com.gtech.gvcore.web.config.Idempotent)")
    public void md5ParamPointcut() {}

    @Before("md5ParamPointcut()")
    public void beforeMd5Param(JoinPoint joinPoint) {
        String args = Arrays.toString(joinPoint.getArgs());
        String md5Value = generateMD5(args);

        String key = "Param:" + md5Value;
        Boolean valid = stringRedisTemplate.opsForValue().setIfAbsent(key, "valid", 10, TimeUnit.SECONDS);
        if (!valid){
            log.error("重复请求异常：{}", JSONObject.toJSONString(args));
            throw new GTechBaseException("100001","REPEAT REQUEST");
        }
    }

    private String generateMD5(String input) {
        return DigestUtils.md5DigestAsHex(input.getBytes());
    }
}
