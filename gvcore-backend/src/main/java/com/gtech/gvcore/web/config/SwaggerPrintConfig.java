package com.gtech.gvcore.web.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
public class SwaggerPrintConfig implements ApplicationListener<ContextRefreshedEvent> {

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        String swaggerUrl = String.format("http://localhost:%s%s/api/gvcore/v2/api-docs", serverPort, contextPath);
        String swaggerUiUrl = String.format("http://localhost:%s%s/swagger-ui.html", serverPort, contextPath);
        System.out.println("\n===============================================");
        System.out.println("Swagger API: " + swaggerUrl);
        System.out.println("Swagger UI: " + swaggerUiUrl);
        System.out.println("===============================================\n");
    }
} 