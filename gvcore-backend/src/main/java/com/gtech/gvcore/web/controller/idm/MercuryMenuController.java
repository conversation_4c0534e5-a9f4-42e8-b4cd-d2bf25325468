package com.gtech.gvcore.web.controller.idm;

import com.alibaba.fastjson.JSONArray;
import com.gtech.basic.idm.common.exceptions.ErrorCodes;
import com.gtech.basic.idm.service.MenuService;
import com.gtech.basic.idm.service.dto.MenuDto;
import com.gtech.basic.idm.web.helper.CodeHelper;
import com.gtech.basic.idm.web.vo.param.CreateMenuParam;
import com.gtech.basic.idm.web.vo.result.CreateMenuResult;
import com.gtech.commons.exception.Exceptions;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "Menu")
@RestController
@RequestMapping(value = "/menu")
public class MercuryMenuController {
    @Autowired
    private MenuService menuService;
    @Autowired
    private CodeHelper codeHelper;

    @ApiOperation(
            value = "Create menu",
            notes = "Create menu information."
    )
    @PostMapping({"/createMenu"})
    public Result<CreateMenuResult> createMenu(@RequestBody CreateMenuParam param) {
        param.validate();
        MenuDto createDto = (MenuDto) BeanCopyUtils.jsonCopyBean(param, MenuDto.class);
        createDto.setMenuLang(JSONArray.toJSONString(param.getMenuLanguages()));
        if (StringUtil.isBlank(param.getMenuCode())) {
            String menuCode = this.codeHelper.generateMenuCode();
            createDto.setMenuCode(menuCode);
        }

        //createDto.setResourceCode(this.codeHelper.generateResourceCode());

        try {
            this.menuService.create(createDto);
        } catch (DuplicateKeyException var4) {
            throw Exceptions.fail(ErrorCodes.EMNU_CODE_DUPLICATE, new Object[]{createDto.getMenuCode()});
        }

        return Result.ok(CreateMenuResult.builder().menuCode(createDto.getMenuCode()).build());
    }


}
