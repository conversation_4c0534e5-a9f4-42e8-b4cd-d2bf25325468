package com.gtech.gvcore.web.controller.distribution;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.distribution.ConfirmDistributionRequest;
import com.gtech.gvcore.common.request.distribution.GetCustomerDistributionRequest;
import com.gtech.gvcore.common.request.distribution.QueryCustomerDistributionRequest;
import com.gtech.gvcore.common.request.distribution.SaveDistributionRequest;
import com.gtech.gvcore.common.response.distribution.GetCustomerDistributionResult;
import com.gtech.gvcore.common.response.distribution.QueryCustomerDistributionResult;
import com.gtech.gvcore.service.distribution.DistributionService;
import com.gtech.gvcore.threadpool.ThreadPoolCenter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @ClassName CustomerDistributionController
 * <AUTHOR>
 * @Date 2022/8/8 15:13
 * @Version V1.0
 **/
@Slf4j
@RestController
@Api(value = "customer distribution", tags = {"GV Customer Distribution Api"})
@RequestMapping("/gv/distribution/cd")
public class CustomerDistributionController {

    @Autowired
    private DistributionService distributionService;

    @PostMapping("/save")
    @ApiOperation(value = "Create distribution information ", notes = "Create distribution information")
    public Result<String> saveDistribution(@RequestBody @Validated SaveDistributionRequest request) {

        log.info("/gv/distribution/cd/create param: {}", JSON.toJSONString(request));

        final String distributionCode = this.distributionService.saveDistribution(request);

        return Result.ok(distributionCode);
    }

    @PostMapping("/distribution")
    @ApiOperation(value = "Confirm distribution", notes = "Confirm distribution")
    public Result<String> confirmDistribution(@RequestBody @Validated ConfirmDistributionRequest request) {

        log.info("/gv/distribution/cd/create param: {}", JSON.toJSONString(request));

        this.distributionService.confirmDistribution(request);

        ThreadPoolCenter.commonThreadPoolExecute(() -> this.distributionService.progressDistribution(request, this.distributionService));

        return Result.ok();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "delete distribution", notes = "delete distribution")
    public Result<String> delete(@RequestBody @Validated ConfirmDistributionRequest request) {

        log.info("/gv/distribution/cd/delete param: {}", JSON.toJSONString(request));

        this.distributionService.deleteCustomerDistribution(request);

        return Result.ok();
    }

    @ApiIgnore
    @PostMapping("/refreshDistributionCompletion")
    @ApiOperation(value = "刷新分发数据状态", notes = "供定时器调度使用,一分钟执行一次")
    public Result<Void> refreshDistributionCompletion() {

        this.distributionService.refreshDistributionCompletion();
        return Result.ok();
    }

    @PostMapping("/list")
    @ApiOperation(value = "Query customer distribution list", notes = "query customer distribution customer ")
    public PageResult<QueryCustomerDistributionResult> list(@Validated @RequestBody QueryCustomerDistributionRequest param) {

        return this.distributionService.queryDistributionList(param);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "Customer distribution detail ", notes = "Customer distribution detail")
    public Result<GetCustomerDistributionResult> detail(@Validated @RequestBody GetCustomerDistributionRequest param) {

        return Result.ok(this.distributionService.getCustomerDistribution(param.getDistributionCode()));
    }

}
