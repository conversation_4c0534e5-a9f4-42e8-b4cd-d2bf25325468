
package com.gtech.gvcore.web.controller;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.common.request.receive.GetVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.QueryVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.ReceiveVoucherRequest;
import com.gtech.gvcore.common.utils.ValidList;
import com.gtech.gvcore.service.VoucherReceiveService;

@RunWith(MockitoJUnitRunner.class)
public class VoucherReceiveControllerTest {

	@InjectMocks
	VoucherReceiveController voucherReceiveController;

	@Mock
	private VoucherReceiveService voucherReceiveService;

	@Test
	public void queryVoucherReceivePageTest() {

		voucherReceiveController.queryVoucherReceivePage(new QueryVoucherReceiveRequest());
		Assert.assertTrue(true);
	}

	@Test
	public void getVoucherReceiveTest() {

		voucherReceiveController.getVoucherReceive(new GetVoucherReceiveRequest());
		Assert.assertTrue(true);
	}

	@Test
	public void receiveTest() throws InterruptedException {
		ReceiveVoucherRequest request = new ReceiveVoucherRequest();
		voucherReceiveController.receive(request);
		ValidList<ReceiveVoucherRequest> validList = new ValidList<>();
		validList.add(request);
		voucherReceiveController.receiveBatch(validList);
		Assert.assertTrue(true);
	}
}
