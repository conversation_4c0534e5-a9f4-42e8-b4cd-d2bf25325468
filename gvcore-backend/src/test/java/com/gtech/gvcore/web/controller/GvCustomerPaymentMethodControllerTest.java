package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.customerpaymentmethod.*;
import com.gtech.gvcore.service.CustomerPaymentMethodService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2022/2/22 18:05
 */
@RunWith(MockitoJUnitRunner.class)
public class GvCustomerPaymentMethodControllerTest {



    @InjectMocks
    private GvCustomerPaymentMethodController gvCustomerPaymentMethodController;

    @Mock
    private CustomerPaymentMethodService outletService;


    @Test
    public void createCustomerPaymentMethod(){

        CreateCustomerPaymentMethodRequest request = new CreateCustomerPaymentMethodRequest();
        request.setCustomerCode("test");
        request.setMopGroup("test");
        request.setCreateUser("test");


        request.setCreateUser("1");
        gvCustomerPaymentMethodController.createCustomerPaymentMethod(request);

    }

    @Test
    public void updateCustomerPaymentMethod(){

        UpdateCustomerPaymentMethodRequest request = new UpdateCustomerPaymentMethodRequest();
        request.setCustomerPaymentMethodCode("2");

        request.setStatus(0);
        request.setUpdateUser("1");
        gvCustomerPaymentMethodController.updateCustomerPaymentMethod(request);

    }


    @Test
    public void deleteCustomerPaymentMethod(){
        DeleteCustomerPaymentMethodRequest request = new DeleteCustomerPaymentMethodRequest();
        request.setCustomerPaymentMethodCode("1");
        gvCustomerPaymentMethodController.deleteCustomerPaymentMethod(request);
    }


    @Test
    public void queryCustomerPaymentMethodList(){
        QueryCustomerPaymentMethodRequest request = new QueryCustomerPaymentMethodRequest();
        request.setCustomerPaymentMethodCode("test");
        request.setStatus(0);
        request.setPageSize(0);
        request.setPageNum(0);

        gvCustomerPaymentMethodController.queryCustomerPaymentMethodList(request);


    }


    @Test
    public void getCustomerPaymentMethod(){

        GetCustomerPaymentMethodRequest request = new GetCustomerPaymentMethodRequest();
        request.setCustomerPaymentMethodCode("1");

        gvCustomerPaymentMethodController.getCustomerPaymentMethod(request);

    }






}
