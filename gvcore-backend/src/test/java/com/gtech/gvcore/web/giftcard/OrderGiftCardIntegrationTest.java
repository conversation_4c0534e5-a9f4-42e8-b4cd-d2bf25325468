package com.gtech.gvcore.web.giftcard;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.GvcoreBackendApplication;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.request.customerorder.ApproveCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.IssuanceRequest;
import com.gtech.gvcore.common.request.customerorder.ReleaseRequest;
import com.gtech.gvcore.common.request.customerorder.SubmitCustomerOrderRequest;
import com.gtech.gvcore.dao.mapper.ArticleMopMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dao.GcCpgMapper;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.CustomerOrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@SpringBootTest(
    classes = GvcoreBackendApplication.class
)
@Import(SyncTaskExecutorConfig.class)
@Slf4j
public class OrderGiftCardIntegrationTest {
    @Autowired
    private CustomerOrderService customerOrderService; // 订单服务
    @Autowired
    private GcCpgMapper gcCpgMapper; // 礼品卡CPG数据访问

    @Autowired
    private CustomerOrderMapper customerOrderMapper; // 订单数据访问
    @Autowired
    private VoucherMapper voucherMapper; // 券数据访问
    @Autowired
    private ArticleMopMapper articleMopMapper;

    // 测试数据
    private String issuerCode; // 发行方编码
    private String cpgCode; // 卡券组编码
    private String outletCode; // 门店编码
    private String mopCode; // 支付方式编码
    private String articleMopCode; // 支付方式编码
    private BigDecimal denomination; // 面额
    private String customerOrderCode;
    private String testUser; // 测试用户
    private Integer loadingTime;

    @BeforeEach
    void setUp() {
        log.info("===============================================================");
        log.info("Starting OrderGiftCardIntegrationTest");
        // 设置测试数据
        issuerCode = "MAP";
        cpgCode = "GCC10250403163603000002";
        outletCode = "OU102205191658000006";
        mopCode = "GC";
        articleMopCode = "b8bb963cccad4d8eb5096c917935ffa9";
        denomination = new BigDecimal("1500000.00");
        testUser = "10220420000184";
        loadingTime = 10;
        prepareTestData();
    }

    /**
     * 准备测试所需的基础数据
     * 包括CPG和GcCPG的创建，模拟了实际系统中的前置条件
     */
    private void prepareTestData() {
        ArticleMop gcArticleMop = articleMopMapper.selectOne(new ArticleMop().setArticleMopCode(articleMopCode));

        if (null == gcArticleMop){
            // 创建ArticleMop记录
            ArticleMop articleMop = new ArticleMop();
            articleMop.setArticleMopCode(articleMopCode);
            articleMop.setArticleCode("TEST_ARTICLE");
            articleMop.setMopCode(mopCode);
            articleMop.setArticleCodeName("测试支付方式");
            articleMop.setStatus(1); // 启用状态
            articleMop.setCreateUser(testUser);
            articleMop.setCreateTime(new Date());
            articleMopMapper.insert(articleMop);
        }

        // 创建GcCpg记录
        // GcCpg是礼品卡特定的CPG配置，包含激活期限等信息
        GcCpg existingGcCpg = gcCpgMapper.selectOne(new GcCpg().setCpgCode(cpgCode));
        if (existingGcCpg == null) {
            GcCpg gcCpg = new GcCpg();
            gcCpg.setCpgCode(cpgCode);
            gcCpg.setCpgName("测试礼品卡CPG");
            gcCpg.setIssuerCode(issuerCode);
            gcCpg.setDenomination(denomination);
            gcCpg.setCurrency("CNY");
            gcCpg.setWalletHost("1");
            gcCpg.setConceptIdentifier("1");
            gcCpg.setProductBrand("1");
            gcCpg.setCoverFrontUrl("https://example.com/front.jpg");
            gcCpg.setCoverBackUrl("https://example.com/back.jpg");
            gcCpg.setAutomaticActivate(true);
            gcCpg.setActivationPeriod("01000000"); // 激活期1年
            gcCpg.setEffectivePeriod("01000000"); // 有效期1年
            gcCpg.setActivationGracePeriod("01000000"); // 有效期1年
            gcCpg.setArticleMopCode(mopCode);
            gcCpg.setStatus("ENABLED");
            gcCpg.setCreateUser(testUser);
            gcCpg.setCreateTime(new Date());
            gcCpgMapper.insert(gcCpg);
        }
    }

    /**
     * 测试订单完整流程
     * 
     * 此测试方法模拟了从订单创建到券发行的完整业务流程，
     * 验证了订单状态转换和券的生成是否符合预期。
     * 
     * 流程步骤：
     * 1. 创建订单 - 初始化订单信息
     * 2. 提交订单 - 将订单状态从草稿改为提交
     * 3. 审核订单 - 管理员审核订单
     * 4. 订单发行 - 准备发行券
     * 5. 订单发布 - 正式发行券并完成订单
     */
    @Test
    void testOrderGiftCardLifecycle() {
        log.info("Starting testOrderGiftCardLifecycle");
        // 1. 创建订单 - 预期得到订单编号
        String customerOrderCode = createCustomerOrder();
        assertNotNull(customerOrderCode, "订单创建失败，未返回订单编号");

        log.info("Created customer order with code: {}", customerOrderCode);
        // 2. 提交订单 - 预期提交成功
        boolean submitResult = submitCustomerOrder(customerOrderCode);
        assertTrue(submitResult, "订单提交失败");

        log.info("Submitted customer order with code: {}", customerOrderCode);
        
        // 3. 审核订单 - 预期审核通过
        boolean approveResult = approveCustomerOrder(customerOrderCode);
        assertTrue(approveResult, "订单审核失败");

        log.info("Approved customer order with code: {}", customerOrderCode);
        // 4. 订单发行 - 预期发行成功
        boolean issuanceResult = issueCustomerOrder(customerOrderCode);
        assertTrue(issuanceResult, "订单发行失败");

        //定时查询，直到订单状态为 ISSUANCE
        while (true) {
            if (loadingTime <= 0) {
                break;
            }
            loadingTime -= 1;
            CustomerOrder order = customerOrderMapper.selectOne(new CustomerOrder().setCustomerOrderCode(customerOrderCode));
            if (order.getStatus().equals(CustomerOrderStatusEnum.ISSUANCE.getStatus())) {
                break;
            }
        }
        log.info("Issued customer order with code: {}", customerOrderCode);


        // 5. 订单发布 - 预期发布成功
        boolean releaseResult = releaseCustomerOrder(customerOrderCode);
        assertTrue(releaseResult, "订单发布失败");
        //等待60秒

        while (true) {
            if (loadingTime <= 0) {
                break;
            }
            loadingTime -= 1;
            CustomerOrder order = customerOrderMapper.selectOne(new CustomerOrder().setCustomerOrderCode(customerOrderCode));
            if (order.getStatus().equals(CustomerOrderStatusEnum.RELEASE.getStatus())) {
                break;
            }
        }
        log.info("Released customer order with code: {}", customerOrderCode);
        
        // 验证订单最终状态 - 预期为RELEASE状态
        CustomerOrder order = customerOrderMapper.selectOne(new CustomerOrder().setCustomerOrderCode(customerOrderCode));
        assertNotNull(order, "无法查询到订单信息");
        assertEquals(CustomerOrderStatusEnum.RELEASE.getStatus(), order.getStatus(),
                "订单状态不正确，预期为RELEASE状态");

        log.info("Order {} status is RELEASE", customerOrderCode);

    }

    /**
     * 创建订单
     * 
     * 模拟通过API创建一个新的礼品卡订单，包含基本信息和订单详情
     * 
     * @return 创建成功的订单编号
     */
    private String createCustomerOrder() {
        // 构建订单创建请求
        CreateCustomerOrderRequest request = new CreateCustomerOrderRequest();
        // 设置基本信息
        request.setIssuerCode(issuerCode); // 发行方
        request.setOutletCode(outletCode); // 门店
        request.setMopCode(mopCode); // 支付方式
        request.setPurchaseOrderNo("TEST_PO_" + System.currentTimeMillis()); // 采购订单号
        request.setCustomerName("测试客户"); // 客户名称
        request.setVoucherNum(1); // 总券数
        request.setVoucherAmount(denomination); // 总券面额
        request.setAmount(denomination); // 订单总金额
        request.setDiscount(BigDecimal.ZERO); // 无折扣
        request.setCurrencyCode("CNY"); // 币种
        request.setCreateUser(testUser); // 创建用户
        request.setCustomerType(CustomerTypeEnum.CORPORATE.code());
        request.setCustomerCode("CUSTOMER_CODE");
        request.setContactPhone("1234567890");
        request.setContactEmail("<EMAIL>");
        request.setShippingAddress("测试地址");
        request.setContactFirstName("test");
        request.setContactLastName("test");

        // 添加订单详情 - 指定购买的卡券类型、数量和面额
        List<CreateCustomerOrderDetailsRequest> details = new ArrayList<>();
        CreateCustomerOrderDetailsRequest detail = new CreateCustomerOrderDetailsRequest();
        detail.setCpgCode(cpgCode); // 卡券组
        detail.setDenomination(denomination); // 面额
        detail.setVoucherNum(1); // 数量
        details.add(detail);
        request.setCreateCustomerOrderDetailsRequests(details);
        
        // 调用服务创建订单
        Result<String> result = customerOrderService.createCustomerOrder(request);
        assertTrue(result.isSuccess(), "创建订单失败: " + result.getMessage());
        
        return result.getData();
    }

    /**
     * 提交订单
     * 
     * 将订单状态从草稿改为提交，准备后续的审核流程
     * 
     * @param customerOrderCode 订单编号
     * @return 是否提交成功
     */
    private boolean submitCustomerOrder(String customerOrderCode) {
        // 构建订单提交请求
        SubmitCustomerOrderRequest request = new SubmitCustomerOrderRequest();
        request.setCustomerOrderCode(customerOrderCode);
        request.setUserCode(testUser); // 使用服务支持的方法
        
        // 调用服务提交订单
        Result<?> result = customerOrderService.submitCustomerOrder(request);
        return result.isSuccess();
    }

    /**
     * 审核订单
     * 
     * 模拟管理员审核订单流程，将订单状态改为已审核
     * 
     * @param customerOrderCode 订单编号
     * @return 是否审核成功
     */
    private boolean approveCustomerOrder(String customerOrderCode) {
        // 构建订单审核请求
        ApproveCustomerOrderRequest request = new ApproveCustomerOrderRequest();
        request.setCustomerOrderCode(customerOrderCode);
        request.setUserCode(testUser); // 使用服务支持的方法
        request.setStatus(true); // 设置审核状态为通过
        
        // 调用服务审核订单
        Result<?> result = customerOrderService.approveCustomerOrder(request);
        return result.isSuccess();
    }

    /**
     * 订单发行
     * 
     * 准备发行券，这一步会创建券的记录但还未正式激活
     * 
     * @param customerOrderCode 订单编号
     * @return 是否发行成功
     */
    private boolean issueCustomerOrder(String customerOrderCode) {
        // 构建订单发行请求
        IssuanceRequest request = new IssuanceRequest();
        request.setCustomerOrderCode(customerOrderCode);
        request.setUpdateUser(testUser);
        
        // 调用服务发行订单
        Result<?> result = customerOrderService.issuance(request);
        return result.isSuccess();
    }

    /**
     * 订单发布
     * 
     * 正式发行券并完成订单流程，券变为可用状态
     * 
     * @param customerOrderCode 订单编号
     * @return 是否发布成功
     */
    private boolean releaseCustomerOrder(String customerOrderCode) {
        // 构建订单发布请求
        ReleaseRequest request = new ReleaseRequest();
        request.setCustomerOrderCode(customerOrderCode);
        request.setUpdateUser(testUser);
        
        // 调用服务发布订单
        Result<?> result = customerOrderService.release(request);
        return result.isSuccess();
    }
} 