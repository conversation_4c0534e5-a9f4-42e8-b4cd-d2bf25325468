
package com.gtech.gvcore.web.controller;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.service.FlowService;

@RunWith(MockitoJUnitRunner.class)
public class GvFlowControllerTest {

	@InjectMocks
	FlowController flowController;

	@Mock
	FlowService flowService;

	@Test
	public void test() {

		flowController.createFlow(null);
		flowController.updateFlow(null);
		flowController.queryFlowList(null);
		flowController.saveFlowNode(null);
	}

}
