package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.distribution.ChangeEmailTemplateStatusRequest;
import com.gtech.gvcore.common.request.distribution.CreateDistributionEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.PreviewSendEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.QueryEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.UpdateDistributionEmailTemplateRequest;
import com.gtech.gvcore.common.response.distribution.DistributionEmailTemplateResponse;
import com.gtech.gvcore.service.distribution.DistributionEmailTemplateService;
import com.gtech.gvcore.web.controller.distribution.DistributionEmailTemplateController;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

/**
 * @ClassName GvDistributionEmailTemplateControllerTest
 * @Description GvDistributionEmailTemplateControllerTest
 * <AUTHOR>
 * @Date 2022/7/5 18:22
 * @Version V1.0
 **/
@RunWith(MockitoJUnitRunner.class)
public class DistributionEmailTemplateControllerTest {

    @InjectMocks
    private DistributionEmailTemplateController controller;

    @Mock
    private DistributionEmailTemplateService distributionEmailTemplateService;

    @Test
    public void createEmailTemplate() {
        Mockito.when(this.distributionEmailTemplateService.createEmailTemplate(Mockito.any())).thenReturn("");
        final CreateDistributionEmailTemplateRequest request = new CreateDistributionEmailTemplateRequest();
        Assert.assertNotNull(this.controller.createEmailTemplate(request));
    }

    @Test
    public void updateEmailTemplate() {
        Mockito.when(this.distributionEmailTemplateService.updateEmailTemplate(Mockito.any())).thenReturn("");
        final UpdateDistributionEmailTemplateRequest request = new UpdateDistributionEmailTemplateRequest();
        Assert.assertNotNull(this.controller.updateEmailTemplate(request));
    }

    @Test
    public void changeStatus() {
        Mockito.when(this.distributionEmailTemplateService.changeStatus(Mockito.any())).thenReturn("");
        final ChangeEmailTemplateStatusRequest request = new ChangeEmailTemplateStatusRequest();
        Assert.assertNotNull(this.controller.changeStatus(request));
    }

    @Test
    public void queryAllEmailTemplate() {
        Mockito.when(this.distributionEmailTemplateService.queryEmailTemplates(Mockito.any())).thenReturn(Collections.singletonList(new DistributionEmailTemplateResponse()));
        final QueryEmailTemplateRequest request = new QueryEmailTemplateRequest();
        Assert.assertNotNull(this.controller.queryEmailTemplates(request));
    }

    @Test
    public void previewSend() {
        Mockito.doNothing().when(this.distributionEmailTemplateService).previewSend(Mockito.any());
        final PreviewSendEmailTemplateRequest request = new PreviewSendEmailTemplateRequest();
        Assert.assertNotNull(this.controller.previewSend(request));
    }

}