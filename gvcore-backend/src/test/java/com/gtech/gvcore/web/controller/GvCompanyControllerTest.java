package com.gtech.gvcore.web.controller;

import com.gtech.gvcore.common.request.company.*;
import com.gtech.gvcore.service.CompanyService;
import com.gtech.gvcore.web.controller.GvCompanyController;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * @Date 2022/2/22 18:05
 */
@RunWith(MockitoJUnitRunner.class)
public class GvCompanyControllerTest {



    @InjectMocks
    private GvCompanyController gvCompanyController;

    @Mock
    private CompanyService outletService;


    @Test
    public void createCompany(){

        CreateCompanyRequest request = new CreateCompanyRequest();
        request.setCompanyName("Erwin Cummings");
        //request.setIssuerCode("123");
        request.setSbu("123");
        request.setCreateUser("1");
        gvCompanyController.createCompany(request);

    }

    @Test
    public void updateCompany(){

        UpdateCompanyRequest request = new UpdateCompanyRequest();
        request.setCompanyCode("2");
        request.setCompanyName("1");

        request.setStatus(0);
        request.setUpdateUser("1");
        gvCompanyController.updateCompany(request);

    }


    @Test
    public void updateCompanyStatus(){

        UpdateCompanyStatusRequest request = new UpdateCompanyStatusRequest();
        request.setCompanyCode("2");

        request.setStatus(0);
        request.setUpdateUser("1");
        gvCompanyController.updateCompanyStatus(request);

    }


    @Test
    public void deleteCompany(){
        DeleteCompanyRequest request = new DeleteCompanyRequest();
        request.setCompanyCode("1");
        gvCompanyController.deleteCompany(request);
    }


    @Test
    public void queryCompanyList(){
        QueryCompanyRequest request = new QueryCompanyRequest();

        request.setPageSize(0);
        request.setPageNum(0);

        gvCompanyController.queryCompanyList(request);


    }


    @Test
    public void getCompany(){

        GetCompanyRequest request = new GetCompanyRequest();
        request.setCompanyCode("1");

        gvCompanyController.getCompany(request);

    }






}
