package com.gtech.gvcore.web.controller;

import java.util.ArrayList;
import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.releaseapprove.CreateLogRecode;
import com.gtech.gvcore.common.request.releaseapprove.QueryLogByBusinessCodeRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAmountListRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAmountRequest;
import com.gtech.gvcore.common.response.releaseapprove.ApproveNodeRecordResponse;
import com.gtech.gvcore.service.impl.ReleaseApproveServiceImpl;

@RunWith(MockitoJUnitRunner.class)
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = com.gtech.gvcore.GvcoreApplication.class)
public class GvReleaseApproveControllerTest {

    @InjectMocks
    private GvReleaseApproveController gvReleaseApproveController;

    @Mock
//    @Autowired
    private ReleaseApproveServiceImpl releaseApproveService;

    @Test
    public void settingApproveConfig() {
        List<ReleaseApproveAmountRequest> releaseApproveAmountRequests = new ArrayList<>();
        ReleaseApproveAmountListRequest releaseApproveAmountListRequest = new ReleaseApproveAmountListRequest();
        releaseApproveAmountListRequest.setReleaseApproveAmountRequests(releaseApproveAmountRequests);
        Mockito.when(releaseApproveService.settingApproveConfig(releaseApproveAmountRequests)).thenReturn(new Result<>());
        Assert.assertNotNull(gvReleaseApproveController.settingApproveConfig(releaseApproveAmountListRequest));
    }

    @Test
    public void queryApproveConfig() {
		Mockito.when(releaseApproveService.queryApproveConfig("123")).thenReturn(new Result<>());
		Assert.assertNotNull(gvReleaseApproveController.queryApproveConfig("123"));
    }

    @Test
    public void createLogRecord() {
        CreateLogRecode build = CreateLogRecode.builder()
                .approveRoleCode("roleCode")
                .approveUser("zhangsan")
                .approveType("VoucherRequest-issuer")
                .businessCode("VR20220402")
                .note("ok,thinks")
                .status(true)
                .build();
        Result<String> logRecord = releaseApproveService.createLogRecord(build);
        System.out.println(logRecord);
        Result<List<ApproveNodeRecordResponse>> vr20220402 = releaseApproveService.queryLogByBusinessCode("VR20220402", null);
        System.out.println(vr20220402);

    }

    @Test
    public void queryLogByBusinessCode() {
        Mockito.when(releaseApproveService.queryLogByBusinessCode("xxx", null)).thenReturn(new Result<>());
        Assert.assertNotNull(gvReleaseApproveController.queryLogByBusinessCode(new QueryLogByBusinessCodeRequest("xxx")));
    }
}