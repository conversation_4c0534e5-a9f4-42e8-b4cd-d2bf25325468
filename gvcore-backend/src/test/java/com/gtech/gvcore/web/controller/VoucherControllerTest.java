
package com.gtech.gvcore.web.controller;

import java.math.BigDecimal;
import java.util.Arrays;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.common.request.redemption.RedemptionRequest;
import com.gtech.gvcore.common.request.voucher.GetStartAndEndVoucherRequest;
import com.gtech.gvcore.common.request.voucherbatch.ReGenerateDigitalVouchersRequest;
import com.gtech.gvcore.common.response.voucher.GetStartAndEndVoucherResponse;
import com.gtech.gvcore.service.VoucherService;

@RunWith(MockitoJUnitRunner.class)
public class VoucherControllerTest {

	@InjectMocks
	VoucherController voucherController;

	@Mock
	private VoucherService voucherService;

	@Test
	public void test() {

		voucherController.validateRedemption(new RedemptionRequest());
		voucherController.redemption(new RedemptionRequest());
		voucherController.regenerateDigitalVoucher(new ReGenerateDigitalVouchersRequest());
		GetStartAndEndVoucherResponse response = new GetStartAndEndVoucherResponse();
		response.setDenomination(BigDecimal.valueOf(50000l));
		response.setVoucherNum(10l);
		Mockito.when(voucherService.getStartAndEndVoucher(Mockito.any())).thenReturn(Arrays.asList(response));
		voucherController.getStartAndEndVoucher(new GetStartAndEndVoucherRequest());
	}
}
