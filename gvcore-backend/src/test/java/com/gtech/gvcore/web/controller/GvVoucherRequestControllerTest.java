package com.gtech.gvcore.web.controller;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.voucherrequest.ApproveVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.BulkApproveVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.BulkGetVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.CancelVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestDetailsRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.GetVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.QueryVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.UpdateVoucherRequestDetailsRequest;
import com.gtech.gvcore.common.request.voucherrequest.UpdateVoucherRequestRequest;
import com.gtech.gvcore.service.VoucherRequestService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class GvVoucherRequestControllerTest {
    @InjectMocks
    private GvVoucherRequestController controller1;
    @Mock
    private VoucherRequestService voucherRequestService;


    @Test
    public void updateVoucherRequest() {
        List<UpdateVoucherRequestDetailsRequest> updateVoucherRequestDetailsRequests = Arrays.asList(
                UpdateVoucherRequestDetailsRequest.builder()
                        .voucherRequestDetailsCode("VR102203091414000005")
                        .denomination(new BigDecimal(8))
                        .voucherNum(2)
                        .voucherAmount(new BigDecimal(2).multiply(new BigDecimal(8)))
                        .build(),
                UpdateVoucherRequestDetailsRequest.builder()
                        .denomination(new BigDecimal(100000))
                        .voucherNum(50)
                        .voucherAmount(new BigDecimal(100000).multiply(new BigDecimal(50)))
                        .build()
        );

        UpdateVoucherRequestRequest build = UpdateVoucherRequestRequest.builder()
                .voucherRequestCode("VR102203081740000003")
                .voucherOwnerCode("1122333")
                .address1("New York Financial Tower")
                .cityCode("cityCode")
                .updateUser("updateUser")
                .currencyCode("currencyCode")
                .districtCode("districtCode")
                .email("<EMAIL>")
                .mobile("+01-8846664")
                .receiverCode("OU102203071410000010")
                .receiverName("MAP")
                .permissionCode("1122333")
                .phone("8846664")
                .requestRemarks("thank you")
                .stateCode("stateCode")
                .voucherAmount(new BigDecimal("7500000"))
                .updateVoucherRequestDetailsRequests(updateVoucherRequestDetailsRequests)
                .voucherNum(100).build();
        System.out.println(JSON.toJSONString(build));
        Mockito.when(voucherRequestService.updateVoucherRequest(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller1.updateVoucherRequest(build));
    }

    @Test
    public void approveVoucherRequest() {
        ApproveVoucherRequestRequest build = ApproveVoucherRequestRequest.builder()
                .approveRemarks("test")
                .status(4)
                .updateUser("updateUserApprove")
                .voucherRequestCode("VR102203091414000005")
                .build();
        System.out.println(JSON.toJSONString(build));
        Mockito.when(voucherRequestService.approveVoucherRequest(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller1.approveVoucherRequest(build));

    }

    @Test
    public void createVoucherRequest() {
        List<CreateVoucherRequestDetailsRequest> createVoucherRequestDetailsRequests = Arrays.asList(
                CreateVoucherRequestDetailsRequest.builder()
                        .denomination(new BigDecimal(2))
                        .voucherNum(5)
                        .voucherAmount(new BigDecimal(5).multiply(new BigDecimal(2)))
                        .build(),
                CreateVoucherRequestDetailsRequest.builder()
                        .denomination(new BigDecimal(4))
                        .voucherNum(3)
                        .voucherAmount(new BigDecimal(3).multiply(new BigDecimal(4)))
                        .build()
        );

        CreateVoucherRequestRequest build = CreateVoucherRequestRequest.builder()
                .voucherOwnerCode("1122333")
                .address1("New York Financial Tower")
                .cityCode("cityCode")
                .createUser("createUser")
                .currencyCode("currencyCode")
                .districtCode("districtCode")
                .email("<EMAIL>")
                .mobile("+01-8846664")
                .receiverCode("OU102203071410000010")
                .receiverName("MAP")
                .permissionCode("1122333")
                .phone("8846664")
                .requestRemarks("thank you")
                .stateCode("stateCode")
                .voucherAmount(new BigDecimal(22))
                .detailsRequests(createVoucherRequestDetailsRequests)
                .voucherNum(8).build();
        System.out.println(JSON.toJSONString(build));
        Mockito.when(voucherRequestService.addVoucherRequest(Mockito.any(),Mockito.any() )).thenReturn(new Result<>());
        Assert.assertNotNull(controller1.createVoucherRequest(build));
    }

    @Test
    public void queryVoucherRequest() {
        QueryVoucherRequestRequest queryVoucherRequestRequest = new QueryVoucherRequestRequest();
        queryVoucherRequestRequest.setStatus(1);
        queryVoucherRequestRequest.setDenomination(new BigDecimal(5000));
        Mockito.when(voucherRequestService.queryVoucherRequest(Mockito.any())).thenReturn(new PageResult<>());
        Assert.assertNotNull(controller1.queryVoucherRequest(queryVoucherRequestRequest));
    }

    @Test
    public void getVoucherRequest() {
        Mockito.when(voucherRequestService.getVoucherRequest(Mockito.any())).thenReturn(new Result<>());
		GetVoucherRequestRequest getVoucherRequestRequest = new GetVoucherRequestRequest();
		getVoucherRequestRequest.setVoucherRequestCode("REQ2200006");
		Assert.assertNotNull(controller1.getVoucherRequest(getVoucherRequestRequest));
    }

    @Test
    public void cancelVoucherRequest() {
        Mockito.when(voucherRequestService.cancelVoucherRequest(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller1.cancelVoucherRequest(new CancelVoucherRequestRequest("lisa", "REQ2200006")));
    }

    @Test
    public void bulkApproveVoucherRequest() {
        Mockito.when(voucherRequestService.bulkApproveVoucherRequest(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller1.bulkApproveVoucherRequest(new BulkApproveVoucherRequestRequest()));

    }

    @Test
    public void bulkGetVoucherRequests() {
        Mockito.when(voucherRequestService.bulkGetVoucherRequests(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(controller1.bulkGetVoucherRequests(new BulkGetVoucherRequestRequest()));

    }
}