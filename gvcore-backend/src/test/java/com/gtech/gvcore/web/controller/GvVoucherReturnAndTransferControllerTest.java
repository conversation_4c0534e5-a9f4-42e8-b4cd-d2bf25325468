package com.gtech.gvcore.web.controller;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.CreateVoucherReturnOrTransferRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.QueryVoucherReturnAndTransferRequest;
import com.gtech.gvcore.common.request.voucherreturnantransfer.ReturnAndTransferRequest;
import com.gtech.gvcore.service.impl.VoucherReturnAndTransferServiceImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GvVoucherReturnAndTransferControllerTest {

    @InjectMocks
    private GvVoucherReturnAndTransferController gvVoucherReturnAndTransferController;

    @Mock
    private VoucherReturnAndTransferServiceImpl voucherRequestService;

    @Test
    public void createVoucherReturnOrTransfer() {
        Mockito.when(voucherRequestService.addVoucherRequest(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(gvVoucherReturnAndTransferController.createVoucherReturnOrTransfer(new CreateVoucherReturnOrTransferRequest()));
    }

    @Test
    public void queryVoucherReturnsOrTransfers() {
        Mockito.when(voucherRequestService.queryVoucherRequest(Mockito.any())).thenReturn(new PageResult<>());
        Assert.assertNotNull(gvVoucherReturnAndTransferController.queryVoucherReturnsOrTransfers(new QueryVoucherReturnAndTransferRequest()));
    }

    @Test
    public void approveVoucherReturnAndTransferAble() {
        Mockito.when(voucherRequestService.approveVoucherReturnAndTransferAble(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(gvVoucherReturnAndTransferController.approveVoucherReturnAndTransferAble(new ReleaseApproveAbleRequest()));
    }

    @Test
    public void approveVoucherReturnAndTransfer() {
        Mockito.when(voucherRequestService.approveVoucherReturnAndTransfer(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(gvVoucherReturnAndTransferController.approveVoucherReturnAndTransfer(new ApproveNodeRecordRequest()));
    }

    @Test
    public void returnAndTransfer() {
        Mockito.when(voucherRequestService.returnAndTransfer(Mockito.any())).thenReturn(new Result<>());
        Assert.assertNotNull(gvVoucherReturnAndTransferController.returnAndTransfer(new ReturnAndTransferRequest()));
    }
}