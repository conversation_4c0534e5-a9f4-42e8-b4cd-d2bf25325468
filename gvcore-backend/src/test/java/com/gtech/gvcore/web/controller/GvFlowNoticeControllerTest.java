
package com.gtech.gvcore.web.controller;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.service.FlowNoticeService;
import com.gtech.gvcore.service.FlowService;

@RunWith(MockitoJUnitRunner.class)
public class GvFlowNoticeControllerTest {

	@InjectMocks
	FlowNoticeController flowController;

	@Mock
	FlowNoticeService flowService;

	@Test
	public void test() {

		flowController.deleteFlowNotice(null);
		flowController.getFlowNotice(null);
		flowController.saveFlowNotice(null);
		flowController.send(null);
	}

}
