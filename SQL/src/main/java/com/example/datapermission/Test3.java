package com.example.datapermission;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.alibaba.fastjson.JSON;
import com.example.sql.ExcelUtil;
import com.example.sql.FileOutputUtils;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.util.StringUtil;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class Test3 {


	public static List<Entity> queryMysql(String sql) {
		List<Entity> query = null;
		try {
			query = Db.use().findAll(sql);
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
		return query;
	}

	public static void main(String[] args) throws InvalidKeyException, NoSuchAlgorithmException {

		List<Entity> gvIssuer = queryMysql("gv_issuer");
		List<Entity> gvCustomer = queryMysql("gv_customer");
		List<Entity> gvMerchant = queryMysql("gv_merchant");
		List<Entity> gvCompany = queryMysql("gv_company");
		List<Entity> gvOutlet = queryMysql("gv_outlet");



		Map<String, String> userMap = new HashMap<>();
		Map<String, String> issuerMap = gvIssuer.stream().collect(Collectors.toMap(e -> e.getStr("issuer_name"), e -> e.getStr("issuer_code")));
		Map<String, String> companyMap = gvCompany.stream().collect(Collectors.toMap(e -> e.getStr("company_name").toLowerCase(), e -> e.getStr("company_code")));
		Map<String, String> merchantMap = gvMerchant.stream().collect(Collectors.toMap(e -> e.getStr("merchant_name").toLowerCase(), e -> e.getStr("merchant_code")));
		Map<String, String> outletMap = gvOutlet.stream().collect(Collectors.toMap(e -> e.getStr("outlet_name").toLowerCase(), e -> e.getStr("outlet_code")));

			List<List<String>> list = ExcelUtil.getExcel("D:\\MD - User Data Permission_New Update.xlsx", "Sheet1");
			List<IssuerPermissionResponse> permissionList = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				if (i < 1) {
					continue;
				}
				List<String> list2 = list.get(i);
				try {

					String userName = list2.get(0);
					String issuerName = list2.get(1);
					String issuerR = list2.get(2);
					String companyName = list2.get(3);
					String companyR = list2.get(4);
					String merchantName = list2.get(5);
					String merchantR = list2.get(6);
					String outletName = list2.get(7);
					String outletR = list2.get(8);

					String userCode = StringUtil.isEmpty(userMap.get(userName)) ? userName : userMap.get(userName);
					// issuer
					String issuerCode = StringUtil.isEmpty(issuerMap.get(issuerName)) ? issuerName : issuerMap.get(issuerName);
					IssuerPermissionResponse issuerPermissionResponse = new IssuerPermissionResponse();
					issuerPermissionResponse.setUserCode(userCode);
					issuerPermissionResponse.setIssuerCode(issuerCode);
					issuerPermissionResponse.setIsLeaf("Absolute".equalsIgnoreCase(issuerR));
					List<DataPermissionResponse> dataPermissionList = new ArrayList<>();
					issuerPermissionResponse.setDataPermissionList(dataPermissionList);

					if (StringUtil.isNotEmpty(companyName)) {
						DataPermissionResponse dataPermissionResponse = new DataPermissionResponse();
						String companyCode = StringUtil.isEmpty(companyMap.get(companyName.toLowerCase())) ? companyName : companyMap.get(companyName.toLowerCase());
						dataPermissionResponse.setType(2);
						dataPermissionResponse.setCode(companyCode);
						dataPermissionResponse.setIsLeaf("Absolute".equalsIgnoreCase(companyR));
						dataPermissionList.add(dataPermissionResponse);
					}
					if (StringUtil.isNotEmpty(merchantName)) {
						DataPermissionResponse dataPermissionResponse = new DataPermissionResponse();
						String merchantCode = StringUtil.isEmpty(merchantMap.get(merchantName.toLowerCase())) ? merchantName : merchantMap.get(merchantName.toLowerCase());
						dataPermissionResponse.setType(3);
						dataPermissionResponse.setCode(merchantCode);
						dataPermissionResponse.setIsLeaf("Absolute".equalsIgnoreCase(merchantR));
						dataPermissionList.add(dataPermissionResponse);
					}
					if (StringUtil.isNotEmpty(outletName)) {
						DataPermissionResponse dataPermissionResponse = new DataPermissionResponse();
						String outletCode = StringUtil.isEmpty(outletMap.get(outletName.toLowerCase())) ? outletName : outletMap.get(outletName.toLowerCase());
						dataPermissionResponse.setType(4);
						dataPermissionResponse.setCode(outletCode);
						dataPermissionResponse.setIsLeaf("Absolute".equalsIgnoreCase(outletR));
						dataPermissionList.add(dataPermissionResponse);
					}
					permissionList.add(issuerPermissionResponse);
				} catch (Exception e) {
					log.info("error, line {}", i + 1);
				}
			}
			System.out.println(permissionList.size());
			Map<String, List<IssuerPermissionResponse>> userList = permissionList.stream()
					.collect(Collectors.groupingBy(IssuerPermissionResponse::getUserCode));
			userList.forEach((k, v) -> {
				v.forEach(vo -> vo.setUserCode(null));
				String extendParams = JSON.toJSONString(v);
				String sql = "update idm_user_account set extend_params = '" + extendParams + "' where user_code = '" + k + "';";
				FileOutputUtils.FileWrite("D:\\permission", sql);
			});
	}

}
