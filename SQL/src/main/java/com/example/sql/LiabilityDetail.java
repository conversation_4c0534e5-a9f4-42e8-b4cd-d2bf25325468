package com.example.sql;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class LiabilityDetail {
    final static String property = System.getProperty("user.dir")+ "/SQL/";
    final static String txt = property + "src/main/resources/liabilityD";


    public static void main(String[] args) {

        List<String> voucherCodes = new ArrayList<>();
        List<String> getTxt = LiabilityScript.getTxtFilesContentFromFolder(txt);
        for (String str : getTxt) {
            str = str.replaceAll("\\r?\\n", ",");
            String[] split = str.split(",");
            voucherCodes.addAll(Arrays.asList(split));


        }
        List<String> collect = voucherCodes.stream().distinct().filter(x -> StringUtil.isNotBlank(x)).collect(Collectors.toList());
        System.out.println(collect);
        System.out.println(collect.size());


    }








}
