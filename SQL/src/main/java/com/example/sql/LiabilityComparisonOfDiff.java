package com.example.sql;

import cn.hutool.core.lang.hash.Hash;
import org.apache.commons.collections4.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * Liability 对比上个月Liability和这个月Liability差异
 */
public class LiabilityComparisonOfDiff {

    final static String property = System.getProperty("user.dir") + "/SQL/";
    final static String salesDataFile = property + "src/main/resources/LiabilityComparisonOfDifferences/processed/salesData.txt";
    final static String usageDataFile = property + "src/main/resources/LiabilityComparisonOfDifferences/processed/usageData.txt";
    final static String DIFF_FILE = property + "src/main/resources/LiabilityComparisonOfDifferences/processed/";

    public static final ReentrantLock fileLock = new ReentrantLock();

    public static void main(String[] args) {
        LiabilityUtil.createDirectoryIfNotExists(property + "src/main/resources/processed");

        LiabilityUtil.readAndStoreFilteredDataUsingPOI(property + "src/main/resources/LiabilityComparisonOfDifferences/RedeemDetail", usageDataFile);
        LiabilityUtil.readAndStoreFilteredDataUsingPOI(property + "src/main/resources/LiabilityComparisonOfDifferences/SalesDetail", salesDataFile);
        // 获取resources下L98文件夹中的所有txt文件
        List<String> lastLiability = LiabilityUtil.getTxtFilesContentFromFolder(property + "src/main/resources/LiabilityComparisonOfDifferences/LastLiabilityDetail");
        List<String> nowLiability = LiabilityUtil.getTxtFilesContentFromFolder(property + "src/main/resources/LiabilityComparisonOfDifferences/NowLiabilityDetail");

        // 从文件中读取数据
        Set<LiabilityScript.TransactionData> salesData = LiabilityUtil.readTransactionDataFromFile(salesDataFile);
        Set<LiabilityScript.TransactionData> usageData = LiabilityUtil.readTransactionDataFromFile(usageDataFile);

        Set<LiabilityScript.TransactionData> lastLiabilitySet =
                new HashSet<>(LiabilityUtil.parseLiabilityData(lastLiability));
        Set<LiabilityScript.TransactionData> nowLiabilitySet =
                new HashSet<>(LiabilityUtil.parseLiabilityData(nowLiability));


        //Liability to HashSet
        HashSet<String> nowLSet = lastLiabilitySet.stream().map(LiabilityScript.TransactionData::getVoucherCode).collect(Collectors.toCollection(HashSet::new));
        HashSet<String> lastLSet = nowLiabilitySet.stream().map(LiabilityScript.TransactionData::getVoucherCode).collect(Collectors.toCollection(HashSet::new));

        //Sales usage to HashSet
        HashSet<String> salesVoucherCodes = salesData.stream().map(LiabilityScript.TransactionData::getVoucherCode).collect(Collectors.toCollection(HashSet::new));

        HashSet<String> usageVoucherCodes = usageData.stream().map(LiabilityScript.TransactionData::getVoucherCode).collect(Collectors.toCollection(HashSet::new));

        System.out.println("上个月Liability SIZE ："+lastLSet.size());
        System.out.println("这个月Liability SIZE ："+nowLSet.size());
        System.out.println("这个月销售 SIZE ："+salesVoucherCodes.size());
        System.out.println("这个月使用 SIZE ："+usageVoucherCodes.size());



        /*


         //Liability to HashSet
        HashSet<String> l8 = lastLiabilitySet.stream().map(LiabilityScript.TransactionData::getVoucherCode).collect(Collectors.toCollection(HashSet::new));
        HashSet<String> l9 = nowLiabilitySet.stream().map(LiabilityScript.TransactionData::getVoucherCode).collect(Collectors.toCollection(HashSet::new));

        //Sales usage to HashSet
        HashSet<String> s9 = salesData.stream().map(LiabilityScript.TransactionData::getVoucherCode).collect(Collectors.toCollection(HashSet::new));

        HashSet<String> r9 = usageData.stream().map(LiabilityScript.TransactionData::getVoucherCode).collect(Collectors.toCollection(HashSet::new));



        HashSet<String> l8l9 = new HashSet<>();
        for (String s : l9) {
            if (!l8.contains(s)) {
                l8l9.add(s);
            }
        }
        for (String s : l8) {
            if (!l9.contains(s)) {
                l8l9.add(s);
            }
        }
        HashSet<String> s9r9 = new HashSet<>();
        for (String s : s9) {
            if (!r9.contains(s)) {
                s9r9.add(s);
            }
        }
        for (String s : r9) {
            if (!s9.contains(s)) {
                s9r9.add(s);
            }
        }
        Set<String> diff = new HashSet<>();
        for (String s : l8l9) {
            if (!s9r9.contains(s)) {
                diff.add(s);
            }
        }
        for (String s : s9r9) {
            if (!l8l9.contains(s)) {
                diff.add(s);
            }
        }
        System.out.println(diff.size());
        LiabilityUtil.writeDataToFile(DIFF_FILE + "DIFF: ", diff);*/

        System.out.println("----------------------------------------------------------------");

        // 1. 计算Liability增量和减量
        Set<String> liabilityIntersection = new HashSet<>(lastLSet);//Liability 交集
        liabilityIntersection.retainAll(nowLSet);
        System.out.println("Liability 交集数量：" + liabilityIntersection.size());

        Set<String> liabilityIncrement = new HashSet<>(nowLSet);//Liability 增量
        liabilityIncrement.removeAll(liabilityIntersection);
        System.out.println("Liability 增量数量：" + liabilityIncrement.size());


        Set<String> liabilityDecrement = new HashSet<>(lastLSet);//Liability 减量
        liabilityDecrement.removeAll(liabilityIntersection);
        System.out.println("Liability 减量数量：" + liabilityDecrement.size());
        System.out.println("----------------------------------------------------------------");
        System.out.println("----------------------------------------------------------------");


        // 2. 计算销售增量和使用增量
        Set<String> salesIncrement = new HashSet<>(salesVoucherCodes);//销售增量
        salesIncrement.removeAll(usageVoucherCodes);
        System.out.println("销售增量数量：" + salesIncrement.size());


        Set<String> usageIncrement = new HashSet<>(usageVoucherCodes);//使用增量
        usageIncrement.removeAll(salesVoucherCodes);
        System.out.println("使用增量数量：" + usageIncrement.size());
        System.out.println("----------------------------------------------------------------");
        System.out.println("----------------------------------------------------------------");

        // 3. 处理销售增量

        //3.1 销售增量去除Liability交集
        Set<String> salesIncrementWithoutLiabilityIntersection = new HashSet<>(salesIncrement);
        salesIncrementWithoutLiabilityIntersection.removeAll(liabilityIntersection);

        System.out.println("销售增量和Liability交集：" + salesIncrementWithoutLiabilityIntersection.size());

        //3.2 销售增量和Liability增量交集
        Set<String> salesIncrementLiabilityIncrementIntersection = new HashSet<>(salesIncrementWithoutLiabilityIntersection);
        salesIncrementLiabilityIncrementIntersection.retainAll(liabilityIncrement);

        System.out.println("销售增量和Liability增量交集：" + salesIncrementLiabilityIncrementIntersection.size());
        //3.3 销售增量减去Liability增量交集
        Set<String> salesIncrementNotInLiabilityIncrement = new HashSet<>(salesIncrementWithoutLiabilityIntersection);
        salesIncrementNotInLiabilityIncrement.removeAll(salesIncrementLiabilityIncrementIntersection);
        System.out.println("销售增量减去Liability增量交集：" + salesIncrementNotInLiabilityIncrement.size());

        //3.4 Liability增量减去销售增量交集
        Set<String> liabilityIncrementNotInSalesIncrement = new HashSet<>(liabilityIncrement);
        liabilityIncrementNotInSalesIncrement.removeAll(salesIncrementLiabilityIncrementIntersection);
        System.out.println("Liability增量减去销售增量交集：" + liabilityIncrementNotInSalesIncrement.size());
        System.out.println("----------------------------------------------------------------");
        System.out.println("----------------------------------------------------------------");

        // 4. 处理使用增量

        //4.1 获取使用增量和Liability减量的交集
        Set<String> usageIncrementLiabilityDecrementIntersection = new HashSet<>(usageIncrement);
        usageIncrementLiabilityDecrementIntersection.retainAll(liabilityDecrement);
        System.out.println("使用增量和Liability减量的交集：" + usageIncrementLiabilityDecrementIntersection.size());

        //4.2  使用增量去除交集
        Set<String> usageIncrementNotInLiabilityDecrement = new HashSet<>(usageIncrement);
        usageIncrementNotInLiabilityDecrement.removeAll(usageIncrementLiabilityDecrementIntersection);
        System.out.println("使用增量去除交集：" + usageIncrementNotInLiabilityDecrement.size());

        //4.3  Liability减量去除交集
        Set<String> liabilityDecrementNotInUsageIncrement = new HashSet<>(liabilityDecrement);
        liabilityDecrementNotInUsageIncrement.removeAll(liabilityIntersection);
        liabilityDecrementNotInUsageIncrement.removeAll(usageIncrementLiabilityDecrementIntersection);
        System.out.println("Liability减量去除交集：" + liabilityDecrementNotInUsageIncrement.size());
        System.out.println("----------------------------------------------------------------");
        System.out.println("----------------------------------------------------------------");

        // 输出结果
        System.out.println("销售增量存在Liability增量不存在: " + salesIncrementNotInLiabilityIncrement.size());
        if (CollectionUtils.isNotEmpty(salesIncrementNotInLiabilityIncrement))
            LiabilityUtil.writeDataToFile(DIFF_FILE + "销售增量存在Liability增量不存在: ", salesIncrementNotInLiabilityIncrement);

        System.out.println("Liability增量存在销售增量不存在: " + liabilityIncrementNotInSalesIncrement.size());
        if (CollectionUtils.isNotEmpty(liabilityIncrementNotInSalesIncrement))
            LiabilityUtil.writeDataToFile(DIFF_FILE + "Liability增量存在销售增量不存在: ", liabilityIncrementNotInSalesIncrement);

        System.out.println("使用增量存在Liability减量不存在: " + usageIncrementNotInLiabilityDecrement.size());
        if (CollectionUtils.isNotEmpty(usageIncrementNotInLiabilityDecrement))
            LiabilityUtil.writeDataToFile(DIFF_FILE + "使用增量存在Liability减量不存在: ",usageIncrementNotInLiabilityDecrement );

        System.out.println("Liability减量存在使用增量不存在: " + liabilityDecrementNotInUsageIncrement.size());
        if (CollectionUtils.isNotEmpty(liabilityDecrementNotInUsageIncrement))
            LiabilityUtil.writeDataToFile(DIFF_FILE + "Liability减量存在使用增量不存在: ", liabilityDecrementNotInUsageIncrement);






    }

    private static HashSet<String> getIntersection(HashSet<String> lastLSet, HashSet<String> nowLSet) {
        HashSet<String> intersection = new HashSet<>(lastLSet);
        intersection.retainAll(nowLSet);
        return intersection;
    }


}
