package com.example.sql;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Arrays;


/**
 * Encrypt Utility Class.
 */
public class CryptoUtils {

    private static final Logger logger = LoggerFactory.getLogger(CryptoUtils.class);

    private static final String DEFAULT_CHARSET = "UTF-8";
    // default mixSalt
    private static final String DEFAULT_MIX_SALT = "gtech_201909";

    private CryptoUtils() {
    }

    /**
     * Generate the SHA512 hash key for plainText with specified mixSalt.
     */
    public static String sha512Encrypt(String plainText, String mixSalt) {

        if (StringUtils.isEmpty(plainText)) {
            return plainText;
        }

        String sha = "";
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-512");
            digest.reset();
            String charsetName = DEFAULT_CHARSET;
            digest.update(mixSalt.getBytes(charsetName));
            digest.update(plainText.getBytes(charsetName));
            byte[] bytes = digest.digest();
            for (int i = 0; i < 1024; i++) {
                digest.reset();
                bytes = digest.digest(bytes);
            }
            sha = Base64Utils.encodeToString(bytes);
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            logger.error("sha512Encrypt occur exception! ", e);
        }

        return sha;
    }

    /**
     * Generate the SHA512 hash key for plainText with defalut mixSalt.
     */
    public static String sha512Encrypt(String plainText) {

        return sha512Encrypt(plainText, DEFAULT_MIX_SALT);
    }

    /**
     * Verify cipherText is the SHA512 hash key that generated for plainText with specified mixSalt.
     */
    public static Boolean sha512Verify(String cipherText, String plainText, String mixSalt) {

        if (!StringUtils.isEmpty(cipherText) && !StringUtils.isEmpty(plainText)) {
            return cipherText.equals(sha512Encrypt(plainText, mixSalt));
        } else {
            return false;
        }
    }

    /**
     * Verify cipherText is the SHA512 hash key that generated for plainText with defalut mixSalt.
     */
    public static Boolean sha512Verify(String cipherText, String plainText) {

        return sha512Verify(cipherText, plainText, DEFAULT_MIX_SALT);
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////
    // 算法名称
    private static final String MD5_ALGORITHM = "MD5";

    /**
     * Generate the MD5 hash key for plainText with default mixSalt.
     */
    public static String md5Encrypt(String plainText) {

        return md5Encrypt(plainText, DEFAULT_MIX_SALT);
    }

    /**
     * Generate the MD5 hash key for plainText with specified mixSalt.
     */
    public static String md5Encrypt(String plainText, String mixSalt) {

        if (StringUtils.isEmpty(plainText)) {
            return plainText;
        }

        plainText += mixSalt;

        byte[] unencodedToken = plainText.getBytes();

        MessageDigest md;

        try {
            // first create an instance, given the provider
            md = MessageDigest.getInstance(MD5_ALGORITHM);
        } catch (Exception e) {
            logger.error("MD5 encrypt failed: ", e);

            return plainText;
        }

        md.reset();

        // call the update method one or more times
        // (useful when you don't know the size of your data, eg. stream)
        md.update(unencodedToken);

        // now calculate the hash
        byte[] encodedToken = md.digest();

        StringBuilder buf = new StringBuilder();

        for (int i = 0; i < encodedToken.length; i++) {
            if (((int) encodedToken[i] & 0xff) < 0x10) {
                buf.append("0");
            }

            buf.append(Long.toString((int) encodedToken[i] & 0xff, 16));
        }

        return buf.toString();
    }

    /**
     * Verify cipherText is the MD5 hash key that generated for plainText with default mixSalt.
     */
    public static Boolean md5Verify(String cipherText, String plainText) {

        return md5Verify(cipherText, plainText, DEFAULT_MIX_SALT);
    }

    /**
     * Verify cipherText is the MD5 hash key that generated for plainText with specified mixSalt.
     */
    public static Boolean md5Verify(String cipherText, String plainText, String mixSalt) {

        if (!StringUtils.isEmpty(cipherText) && !StringUtils.isEmpty(plainText)) {
            return cipherText.equals(md5Encrypt(plainText, mixSalt));
        } else {
            return false;
        }
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////
    private static final String AES_SECURE_RANDOM_KEY = "SHA1PRNG";
    private static final String AES_ALGORITHM_KEY = "AES";
    private static final String AES_GCM_CIPHER_KEY = "AES_256/GCM/NoPadding";
    private static final String AES_SEED = "7343680Ls435kj3468Ms312111014681";
    private static final int AES_GCM_KEY_SIZE = 256;
    private static final int AES_GCM_IV_LENGTH = 12;
    private static final int AES_GCM_TAG_LENGTH = 16;

    /**
     * Encrypt plainText using the AES algorithm with default secureKey and charset(utf-8).
     */
    public static String aesEncrypt(String plainText) {

        return aesGCM256Encrypt(plainText, DEFAULT_CHARSET, null);
    }

    /**
     * Encrypt plainText using the AES algorithm with specified secureKey and defalut charset(utf-8).
     */
    public static String aesEncrypt(String plainText, String userSecretKey) {

        return aesGCM256Encrypt(plainText, DEFAULT_CHARSET, userSecretKey);
    }

    /**
     * Decrypt data using the AES algorithm with specified secureKey and defalut charset(utf-8).
     */
    public static String aesDecrypt(String cipherText, String userSecretKey) {

        return aesGCM256Decrypt(cipherText, DEFAULT_CHARSET, userSecretKey);
    }

    /**
     * Decrypt data using the AES algorithm with defalut secureKey and charset(utf-8).
     */
    public static String aesDecrypt(String cipherText) {

        return aesGCM256Decrypt(cipherText, DEFAULT_CHARSET, null);
    }

    private static String aesGCM256Encrypt(String plainText, String charsetName, String userSecretKey) {

        try {
            if (StringUtils.isEmpty(plainText)) {
                return plainText;
            }

            SecureRandom secureRandom = SecureRandom.getInstance(AES_SECURE_RANDOM_KEY);
            secureRandom.setSeed(AES_SEED.getBytes());

            KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM_KEY);
            keyGenerator.init(AES_GCM_KEY_SIZE, secureRandom);
            // Generate Key
            byte[] key = keyGenerator.generateKey().getEncoded();
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, AES_ALGORITHM_KEY);

            byte[] iv = new byte[AES_GCM_IV_LENGTH]; //NEVER REUSE THIS IV WITH SAME KEY
            secureRandom.nextBytes(iv);
            // Generate parameterSpec
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(AES_GCM_TAG_LENGTH * 8, iv);

            final Cipher cipher = Cipher.getInstance(AES_GCM_CIPHER_KEY);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, gcmParameterSpec);

            if (!StringUtils.isEmpty(userSecretKey)) {
                cipher.updateAAD(userSecretKey.getBytes());
            }

            byte[] cipherText = cipher.doFinal(plainText.getBytes(charsetName));

            Arrays.fill(key, (byte) 0);

            return Base64Utils.encodeToString(cipherText);
        } catch (Exception e) {
            logger.error("aesEncrypt failed", e);
        }
        return null;
    }

    private static String aesGCM256Decrypt(String data, String charsetName, String userSecretKey) {

        try {
            if (StringUtils.isEmpty(data)) {
                return data;
            }

            byte[] cipherText = Base64Utils.decodeFromString(data);

            SecureRandom secureRandom = SecureRandom.getInstance(AES_SECURE_RANDOM_KEY);
            secureRandom.setSeed(AES_SEED.getBytes());

            KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM_KEY);
            keyGenerator.init(AES_GCM_KEY_SIZE, secureRandom);
            // Generate Key
            byte[] key = keyGenerator.generateKey().getEncoded();
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, AES_ALGORITHM_KEY);

            byte[] iv = new byte[AES_GCM_IV_LENGTH]; //NEVER REUSE THIS IV WITH SAME KEY
            secureRandom.nextBytes(iv);
            // Generate parameterSpec
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(AES_GCM_TAG_LENGTH * 8, iv);

            final Cipher cipher = Cipher.getInstance(AES_GCM_CIPHER_KEY);

            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec);

            if (!StringUtils.isEmpty(userSecretKey)) {
                cipher.updateAAD(userSecretKey.getBytes());
            }

            byte[] plainText = cipher.doFinal(cipherText);

            return new String(plainText, charsetName);
        } catch (Exception e) {
            logger.error("aesDecrypt failed", e);
        }
        return null;
    }

    public static void main(String[] args) {
        ////////////////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////////////////
//        String plainText = "CryptoUtilsEncrypt(String cipherText, AES 加密处理测试  String plainText)";
//        String cipherText = null;
//        String associatedData = "testAssociatedData";
//
//        System.err.println("************************************************************");
//        cipherText = aesGCM256Encrypt(plainText, DEFAULT_CHARSET, associatedData);
//        System.out.println(cipherText);
//        System.out.println(aesGCM256Decrypt(cipherText, DEFAULT_CHARSET, associatedData));
//
//        System.out.println("************************************************************");
//        cipherText = aesGCM256Encrypt(plainText, DEFAULT_CHARSET, null);
//        System.out.println(cipherText);
//        System.out.println(aesGCM256Decrypt(cipherText, DEFAULT_CHARSET, null));
//
//        String[] plainTexts = {"SDalzAkJYgpBPTFq1Ob89n5oGzowvnLTLr4GdQYn",
//                               "fjsashruhueNKJSNDKDKJkdsaskadksafkdshfsf",
//                               "NJKDvsnwsfadf2DJmvsfsansakvdbsbvjbvdsvds",
//                               "NKJvsaKSHDkjbvkawafkwlfeewiufqD===FAHDSK",
//                               "发电房价开始放松放松hsdafksdfhsadADHKDHKAaf"};
//        for(int i = 0; i< plainTexts.length; i++) {
//            cipherText = aesGCM256Encrypt(plainTexts[i], DEFAULT_CHARSET, null);
//            System.out.println(cipherText);
//            System.out.println(aesGCM256Decrypt(cipherText, DEFAULT_CHARSET, null));
//        }

//        ////////////////////////////////////////////////////////////////////////////////////////////
//        ////////////////////////////////////////////////////////////////////////////////////////////
//        System.err.println("************************************************************");
//        plainText = "Passw0rd001";
//        System.out.println(sha512Encrypt(plainText, ""));
//        System.out.println(sha512Encrypt(plainText, "salt001"));
//        System.out.println(sha512Encrypt(plainText, "salt002"));
//        System.out.println(sha512Encrypt(plainText, "salt002"));
    }

}
