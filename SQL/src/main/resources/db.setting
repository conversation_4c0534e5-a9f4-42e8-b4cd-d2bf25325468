
## db.setting文件



url = *************************************************************************************************************************************************************************
user = gvcoreadmin
pass = Gtech@123
maximumPoolSize = 64

#url = *************************************************************************************************************************************************************
#user = gtechtest
#pass = heT5MDxtk5PxIrYj


#url=**********************************************************************************************************************
#user=gtech-dev
#pass=gtech-dev



## 可选配置
# 是否在日志中显示执行的SQL
#showSql = true
# 是否格式化显示的SQL
#formatSql = false
# 是否显示SQL参数
#showParams = true


## 可选配置
# 是否在日志中显示执行的SQL
showSql = false
# 是否格式化显示的SQL
formatSql = false
# 是否显示SQL参数
showParams = false
# 打印SQL的日志等级，默认debug，可以是info、warn、error
sqlLevel = error