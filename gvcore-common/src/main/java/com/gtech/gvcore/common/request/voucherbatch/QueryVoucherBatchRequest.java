package com.gtech.gvcore.common.request.voucherbatch;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/3/7 10:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryVoucherBatchRequest")
public class QueryVoucherBatchRequest extends PageBean implements Serializable {
    private static final long serialVersionUID = -6714969100311337855L;


    @ApiModelProperty(value = "Issuer code.",example = "1231234",required = true)
    @NotBlank(message = "voucherBatchCode can not be empty")
    private String issuerCode;

    @ApiModelProperty(value = "Cpg code.", example = "1122333")
    private String cpgCode;

    /**
     * lot number
     */
    @ApiModelProperty(value = "Voucher batch code.", example = "1122333")
    private String voucherBatchCode;

    @ApiModelProperty(value = "Voucher start code.", example = "1002222220000001")
    private String voucherStartNo;

    @ApiModelProperty(value = "Voucher end code.", example = "1002222220003000")
    private String voucherEndNo;

    @ApiModelProperty(value = "Status.", example = "1")
    private String status;

    @ApiModelProperty(value = "Mop code")
    private String mopCode;





}
