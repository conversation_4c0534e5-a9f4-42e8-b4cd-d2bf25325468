package com.gtech.gvcore.common.request.customerorder;

import com.gtech.commons.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/19 19:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("Query customer order Request")
public class QueryCustomerOrderRequest extends PageParam {

    @ApiModelProperty(value = "Customer Name")
    private String customerName;
    @ApiModelProperty(value = "Customer Code")
    private String customerCode;
    @ApiModelProperty(value = "Email")
    private String email;
    @ApiModelProperty(value = "Denomination")
    private String denomination;
    @ApiModelProperty(value = "Voucher type code")
    private String voucherType;
    @ApiModelProperty(value = "Status")
    private String status;
    @ApiModelProperty(value = "Issuer code")
    @NotBlank(message = "IssuerCode is required")
    private String issuerCode;
    @ApiModelProperty(value = "Role list use ',' split", required = true)
    @NotBlank(message = "Role list can not be empty")
    private String roleList;
    @ApiModelProperty(value = "User code", required = true)
    @NotBlank(message = "User code can not be empty")
    private String userCode;

    @ApiModelProperty(value = "deliveType")
    private String deliveType;

	private String createTimeStart;
	private String createTimeEnd;
	private List<String> createUserList;
    @ApiModelProperty(value = "NoSystem")
    private Boolean noSystem;


}
