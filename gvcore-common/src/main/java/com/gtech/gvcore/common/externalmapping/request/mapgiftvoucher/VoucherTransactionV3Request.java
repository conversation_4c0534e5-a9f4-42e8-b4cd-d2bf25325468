package com.gtech.gvcore.common.externalmapping.request.mapgiftvoucher;

import com.gtech.gvcore.common.request.v3.Purchaser;
import com.gtech.gvcore.common.request.v3.TransactionsRequest;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class VoucherTransactionV3Request {

    private String transactionCode;
    private String inputType;
    private String referenceNumber;
    private String invoiceNumber;
    private String numberOfVouchers;
    private List<Vouchers> vouchers;
    private Purchaser buyer;
    private Purchaser holder;
    private String notes;
    private String tId;
    private String transactionId;
    private Integer batchId;
    private List<String> voucherCodes;

    private BigDecimal billAmount;
    private String invoiceDate;
    private String retryKey;
    private String transactionMode;
    private String importVoucherNumber;

    public TransactionsRequest toTransactionsRequest() {
        TransactionsRequest transactionsRequest = new TransactionsRequest();
        transactionsRequest.setTransactionTypeId(this.transactionCode);
        transactionsRequest.setInputType(this.inputType);
        transactionsRequest.setBusinessReferenceNumber(this.referenceNumber);
        transactionsRequest.setInvoiceNumber(this.invoiceNumber);
        transactionsRequest.setNumberOfCards(this.numberOfVouchers);
        transactionsRequest.setCards(this.vouchers.stream().map(Vouchers::toCards).collect(Collectors.toList()));
        transactionsRequest.setPurchaser(this.buyer);
        transactionsRequest.setNotes(this.notes);
        transactionsRequest.setTerminalId(this.tId);
        transactionsRequest.setTransactionId(this.transactionId);
        transactionsRequest.setBatchId(String.valueOf(this.batchId));
        transactionsRequest.setCardNumbers(this.voucherCodes);
        return transactionsRequest;
    }


}
