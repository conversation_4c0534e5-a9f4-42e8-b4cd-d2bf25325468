package com.gtech.gvcore.common.request.printer;

import com.gtech.gvcore.common.request.base.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022年2月17日
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class QueryPrinterByPageRequest extends PageBean {

    /*@ApiModelProperty(value = "issuerCode", example = "MAP", required = false)
    private String issuerCode;
*/
    @ApiModelProperty(value = "printerName")
    private String printerName;

}
