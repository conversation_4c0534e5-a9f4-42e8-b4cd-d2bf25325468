package com.gtech.gvcore.common.request.voucherbatch;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/3/4 15:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryRealTimeProgressBarRequest")
public class QueryRealTimeProgressBarRequest implements Serializable {
    private static final long serialVersionUID = 5465699059637708665L;


    @ApiModelProperty(value = "Voucher batch code.", example = "1122333",required = true)
    private String voucherBatchCode;

    @ApiModelProperty(value = "Cpg code.", example = "1122333")
    private String cpgCode;









}
