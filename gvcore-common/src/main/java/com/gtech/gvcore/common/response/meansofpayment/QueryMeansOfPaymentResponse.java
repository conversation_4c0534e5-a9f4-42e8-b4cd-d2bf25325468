package com.gtech.gvcore.common.response.meansofpayment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询支付方式响应
 */
@Data
@ApiModel(value = "QueryMeansOfPaymentResponse", description = "查询支付方式响应")
public class QueryMeansOfPaymentResponse {
    /**
     * 支付方式编码
     */
    @ApiModelProperty(value = "支付方式编码")
    private String mopCode;

    /**
     * 支付方式名称
     */
    @ApiModelProperty(value = "支付方式名称")
    private String mopName;

    /**
     * 支付方式组编码
     */
    @ApiModelProperty(value = "支付方式组编码")
    private String mopGroupCode;

    /**
     * 支付方式组名称
     */
    @ApiModelProperty(value = "支付方式组名称")
    private String mopGroupName;

    /**
     * 门店编码列表
     */
    @ApiModelProperty(value = "门店编码列表")
    private List<String> outletCodes;

    /**
     * 门店名称列表
     */
    @ApiModelProperty(value = "门店名称列表")
    private List<String> outletNames;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String updateTime;
} 