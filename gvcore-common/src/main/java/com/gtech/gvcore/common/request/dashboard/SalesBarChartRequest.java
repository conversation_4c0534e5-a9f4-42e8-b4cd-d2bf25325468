package com.gtech.gvcore.common.request.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/9/5 14:00
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "SalesBarChartRequest")
public class SalesBarChartRequest {


    @NotNull(message = "issuerCode can not be null")
    @ApiModelProperty(value = "IssuerCode", required = true, example = "0123123")
    private String issuerCode;

    @NotNull(message = "date can not be null")
    @ApiModelProperty(value = "Date", required = true, example = "2022-08-31 15:26:12")
    private Date date;



}
