package com.gtech.gvcore.common.response.allocation;

import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年3月11日
 */
@Data
public class QueryVoucherAllocationByPageResponse {

    private String voucherAllocationCode;

    private String voucherRequestCode;

    private String sourceDataCode;

    private String businessType;

    private String receiverCode;

    private String receiverName;

    private Integer voucherNum;

    private BigDecimal voucherAmount;

    private Integer status;

    private String createUser;

    private Date createTime;

    private List<BigDecimal> denominationList;

    private List<String> cpgList;
}
