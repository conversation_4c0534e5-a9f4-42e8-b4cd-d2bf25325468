package com.gtech.gvcore.common.request.voucher;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/3/9 16:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryVoucherRequest")
public class QueryVoucherRequest extends PageBean implements Serializable {
    private static final long serialVersionUID = -5292213000441842200L;

    private String voucherBatchCode;


}
