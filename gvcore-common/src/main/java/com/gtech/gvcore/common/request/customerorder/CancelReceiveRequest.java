package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2023/3/7 16:17
 */
@Data
public class CancelReceiveRequest {

    @ApiModelProperty(value = "receiveCode", required = true)
    @NotEmpty(message = "receiveCode can not be empty")
    private String receiveCode;

    @ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;


}
