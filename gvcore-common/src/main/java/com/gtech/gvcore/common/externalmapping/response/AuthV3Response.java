package com.gtech.gvcore.common.externalmapping.response;

import com.gtech.gvcore.common.response.authorize.AuthorizeResponseV3;
import lombok.Data;

@Data
public class AuthV3Response {

//    @JsonProperty("AuthToken")
    private String token;

//    @JsonProperty("BatchId")
    private Integer BatchId;

//    @JsonProperty("DateAtServer")
    private String serverTime;

//    @JsonProperty("MerchantName")
    private String storeName;

//    @JsonProperty("OutletAddress1")
    private String storeAddress1;

//    @JsonProperty("OutletAddress2")
    private String storeAddress2;

//    @JsonProperty("OutletCity")
    private String storetCity;

//    @JsonProperty("OutletState")
    private String storeState;

//    @JsonProperty("OutletPinCode")
    private String storeZipCode;

//    @JsonProperty("OutletTelephone")
    private String storeTelephone;

//    @JsonProperty("MaskCardNumber")
    private Boolean maskCardNumber;

//    @JsonProperty("PrintMerchantCopy")
    private Boolean printMerchantCopy;

//    @JsonProperty("InvoiceNumberMandatory")
    private Boolean invoiceNumberMandatory;

//    @JsonProperty("NumericUserPwd")
    private Boolean numericUserPwd;

//    @JsonProperty("IntegerAmounts")
    private Boolean integerAmounts;

//    @JsonProperty("CultureName")
    private String culture;

//    @JsonProperty("CurrencySymbol")
    private String currency;

//    @JsonProperty("CurrencyPosition")
    private Integer currencyPosition;

//    @JsonProperty("CurrencyDecimalDigits")
    private Integer currencyDecimalDigits;

//    @JsonProperty("DisplayUnitForPoints")
    private String displayUnitForPoints;

//    @JsonProperty("ReceiptFooterLine1")
    private String receiptLine1;

//    @JsonProperty("ReceiptFooterLine2")
    private String receiptLine2;

//    @JsonProperty("ReceiptFooterLine3")
    private String receiptLine3;

//    @JsonProperty("ReceiptFooterLine4")
    private String receiptLine4;

//    @JsonProperty("MerchantID")
    private Integer merchantId;

//    @JsonProperty("TransactionStatus")
    private Boolean transactionStatus;

//    @JsonProperty("TransactionId")
    private Integer transactionId;

//    @JsonProperty("TransactionType")
    private String transactionType;

//    @JsonProperty("Notes")
    private String notes;

//    @JsonProperty("ApprovalCode")
    private String approvalCode;

//    @JsonProperty("ResponseCode")
    private Integer ResponseCode;

//    @JsonProperty("ResponseMessage")
    private String ResponseMessage;

//    @JsonProperty("ErrorCode")
    private String errorCode;

//    @JsonProperty("ErrorDescription")
    private String errorDescription;

//    @JsonProperty("Result")
    private Boolean result;

    public AuthV3Response setAuthV3Response(AuthorizeResponseV3 response){
        if (response == null) {
            return this;
        }
        this.setToken(response.getAuthToken());
        this.setBatchId(response.getBatchId());
        this.setServerTime(response.getDateAtServer());
        this.setStoreName(response.getMerchantName());
        this.setStoreAddress1(response.getOutletAddress1());
        this.setStoreAddress2(response.getOutletAddress2());
        this.setStoretCity(response.getOutletCity());
        this.setStoreState(response.getOutletState());
        this.setStoreZipCode(response.getOutletPinCode());
        this.setStoreTelephone(response.getOutletTelephone());
        this.setMaskCardNumber(response.getMaskCardNumber());
        this.setPrintMerchantCopy(response.getPrintMerchantCopy());
        this.setInvoiceNumberMandatory(response.getInvoiceNumberMandatory());
        this.setNumericUserPwd(response.getNumericUserPwd());
        this.setIntegerAmounts(response.getIntegerAmounts());
        this.setCulture(response.getCultureName());
        this.setCurrency(response.getCurrencySymbol());
        this.setCurrencyPosition(response.getCurrencyPosition());
        this.setCurrencyDecimalDigits(response.getCurrencyDecimalDigits());
        this.setDisplayUnitForPoints(response.getDisplayUnitForPoints());
        this.setReceiptLine1(response.getReceiptFooterLine1());
        this.setReceiptLine2(response.getReceiptFooterLine2());
        this.setReceiptLine3(response.getReceiptFooterLine3());
        this.setReceiptLine4(response.getReceiptFooterLine4());
        this.setMerchantId(response.getMerchantId());
        this.setTransactionStatus(response.getTransactionStatus());
        this.setTransactionId(response.getTransactionId());
        this.setTransactionType(response.getTransactionType());
        this.setNotes(response.getNotes());
        this.setApprovalCode(response.getApprovalCode());
        this.setResponseCode(response.getResponseCode());
        this.setResponseMessage(response.getResponseMessage());
        this.setErrorCode(response.getErrorCode());
        this.setErrorDescription(response.getErrorDescription());
        this.setResult(response.getResult());
        return this;



    }



}
