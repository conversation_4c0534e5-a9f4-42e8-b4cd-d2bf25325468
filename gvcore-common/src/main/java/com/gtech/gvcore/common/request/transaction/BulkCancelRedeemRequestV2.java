package com.gtech.gvcore.common.request.transaction;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("批量取消兑换请求V2")
public class BulkCancelRedeemRequestV2 {
    

    @ApiModelProperty("交易ID")
    private Long transactionId;
    

    @ApiModelProperty("交易金额")
    private BigDecimal transactionAmount;
    
    @ApiModelProperty("券信息列表")
    private List<VoucherItem> voucherItems;
    
    @ApiModelProperty("原始金额")
    private Long originalAmount;
    
    @ApiModelProperty("原始审批码")
    private String originalApprovalCode;

    @ApiModelProperty("原始发票号")
    private String originalInvoiceNumber;
    
    @ApiModelProperty("原始批次号")
    private Long originalBatchNumber;
    
    @ApiModelProperty("原始交易ID")
    private Long originalTransactionId;
    

    @ApiModelProperty("客户端时间")
    private String clientTime;
    
    @Data
    public static class VoucherItem {

        private String itemNo;
        

        private String inputType;
        
        private String numberOfVouchers;
        
        private VoucherInfo voucherInfo;
    }
    
    @Data
    public static class VoucherInfo {
        private String voucherNumber;
        
        private String trackData;
    }
} 