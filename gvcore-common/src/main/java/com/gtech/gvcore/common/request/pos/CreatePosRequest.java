package com.gtech.gvcore.common.request.pos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreatePosRequest")
public class CreatePosRequest {





    @ApiModelProperty(value = "Issuer code.", example = "2123123",required = true)
    @NotEmpty(message = "issuerCode can not be empty")
    @Length(max = 100)
    private String issuerCode;


    @ApiModelProperty(value = "Pos name.", example = "Pos1",required = true)
    @NotEmpty(message = "posName can not be empty")
    @Length(max = 100)
    private String posName;


    @ApiModelProperty(value = "Machine id", example = "2123123",required = true)
    @NotEmpty(message = "machineId can not be empty")
    @Length(max = 100)
    private String machineId;

    @ApiModelProperty(value = "Pos entry mode id.", example = "2123123",required = true)
    @NotEmpty(message = "posEntryModeId can not be empty")
    @Length(max = 100)
    private String posEntryModeId;


    @ApiModelProperty(value = "Outlet code.", example = "2123123",required = true)
    @NotEmpty(message = "outletCode can not be empty")
    @Length(max = 100)
    private String outletCode;


    @ApiModelProperty(value = "Outlet code.", example = "[123,123]",required = true)
    private List<String> cpgCodes;


    @ApiModelProperty(value = "Create user.", example = "user123")
    private String createUser;


    @ApiModelProperty(value = "Create time.", example = "2020-03-12 12:12:12")
    private Date createTime;

    @ApiModelProperty(value = "Account.", example = "********")
    private String account;

    @ApiModelProperty(value = "Password.", example = "7457457")
    private String password;

}
