package com.gtech.gvcore.common.request.printer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-21 17:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetPrinterRequest")
public class GetPrinterFtpKeyFileRequest {

    /**
     * printerCode
     */
    @NotEmpty(message = "printerCode can not be empty")
    @ApiModelProperty(value = "printerCode", example = "CPC102202241512000241", required = true)
    private String printerCode;
}
