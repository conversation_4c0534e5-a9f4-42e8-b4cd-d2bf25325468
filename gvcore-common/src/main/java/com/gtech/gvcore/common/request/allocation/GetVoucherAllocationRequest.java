package com.gtech.gvcore.common.request.allocation;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年3月11日
 */
@Data
public class GetVoucherAllocationRequest {

    @ApiModelProperty(value = "voucherAllocationCode", required = true)
    @NotEmpty(message = "voucherAllocationCode can not be empty")
    private String voucherAllocationCode;

}
