package com.gtech.gvcore.common.request.customerorder;

import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReceiveRequest {
	
	@ApiModelProperty(value = "customerOrderCode", required = true)
    @NotEmpty(message = "customerOrderCode can not be empty")
	private String customerOrderCode;
	
	@ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}
