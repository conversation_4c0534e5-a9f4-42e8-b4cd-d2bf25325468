package com.gtech.gvcore.common.request.allocation;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年3月8日
 */
@Data
public class AllocateRequest {

    @ApiModelProperty(value = "voucherAllocationCode", required = true)
    @NotEmpty(message = "voucherAllocationCode can not be empty")
    private String voucherAllocationCode;

    @ApiModelProperty(value = "voucherBatchList", required = true)
    @NotEmpty(message = "voucherBatchList can not be empty")
    @Valid
    private List<AllocateVoucherBatch> voucherBatchList;

    @ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}
