package com.gtech.gvcore.common.response.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/8/31 10:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "HistogramResponse")
public class HistogramResponse {

    @ApiModelProperty(value = "Date")
    private String date;

    @ApiModelProperty(value = "Vouchercount")
    private Integer voucherCount;

    @ApiModelProperty(value = "Discount")
    private String discount;


}
