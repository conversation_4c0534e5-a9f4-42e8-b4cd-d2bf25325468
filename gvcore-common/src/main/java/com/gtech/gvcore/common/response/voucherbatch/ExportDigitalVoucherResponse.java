package com.gtech.gvcore.common.response.voucherbatch;

import com.gtech.basic.filecloud.exports.core.annotation.FileColumn;
import com.gtech.basic.filecloud.exports.core.annotation.NonFileColumn;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/3/9 17:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ExportDigitalVoucherResponse")
public class ExportDigitalVoucherResponse {


    @FileColumn(name = "Voucher Number")
    private String voucherCode;

    //@FileColumn(name = "ActivationCode")
    @NonFileColumn
    private String ActivationCode;

    //@FileColumn(name = "ActivationURL")
    @NonFileColumn
    private String ActivationUrl;

    @FileColumn(name = "Voucher Expiry Date")
    private String voucherEffectiveDate;

    @FileColumn(name = "27DigitBarcode")
    private String digitBarcode;

    @FileColumn(name = "Denomination")
    private String denomination;

    @FileColumn(name = "Voucher Pin")
    private String pinCode;

    @FileColumn(name = "Invoice Number")
    private String invoiceNumber;
}
