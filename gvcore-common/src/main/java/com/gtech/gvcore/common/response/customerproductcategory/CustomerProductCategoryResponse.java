package com.gtech.gvcore.common.response.customerproductcategory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CustomerPaymentMethodResponse")
public class CustomerProductCategoryResponse {

    @ApiModelProperty(value = "Customer product category.", example = "12341412")
    private String customerProductCategoryCode;

    @ApiModelProperty(value = "Customer code.", example = "12312312")
    private String customerCode;

    @ApiModelProperty(value = "Product category code.", example = "11221312")
    private String productCategoryCode;

    @ApiModelProperty(value = "Product category name.", example = "test")
    private String productCategoryName;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty(value = "Create user.", example = "user1")
    private String createUser;

    @ApiModelProperty( value="Update user.", example="user1")
    private String updateUser;

    @ApiModelProperty(value = "Update time.", example = "2022-02-17 16:27")
    private Date updateTime;

    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:27")
    private Date createTime;

}
