package com.gtech.gvcore.common.utils;


import java.util.List;
import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;

import com.gtech.commons.result.Result;

public class ParamCheckUtils {

	private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

	private static final String SEPARATOR = ":";

	private ParamCheckUtils() {
		throw new IllegalStateException("Utility class");
	}

	/**
	 * 
	 * <AUTHOR>
	 * @param requestParam
	 * @return
	 * @date 2020年4月21日
	 */
	public static <T> Result<T> commonValidate(Object requestParam) {

		// 遍历
		Set<ConstraintViolation<Object>> constraintViolations = validator.validate(requestParam);
		if (constraintViolations.isEmpty()) {
			return Result.ok();
		}
		ConstraintViolation<Object> constraintViolation = constraintViolations.iterator().next();
		return Result.failed(constraintViolation.getMessage() + SEPARATOR + constraintViolation.getPropertyPath().toString());
	}

	/**
	 * 
	 * <AUTHOR>
	 * @param <T>
	 * @param list
	 * @return
	 * @date 2020年4月21日
	 */
	public static <T> Result<T> listValidate(List<?> list) {
		for (Object object : list) {
			Result<T> result = commonValidate(object);
			if (!result.isSuccess()) {
				return result;
			}
		}
		return Result.ok();
	}

}
