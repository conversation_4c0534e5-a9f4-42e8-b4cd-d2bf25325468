package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
public class CancelActivateRequest {

    @ApiModelProperty(value = "Transaction ID. ", example = "00001", required = true, notes = "An integer value. This value has to be incremented with every transaction within the batch. For e.g, after the first time init is done, when doing a balance enquiry for the first time, the value for this can be 1, for the second transaction, this should be 2 and so on. Basically, a unique incrementing sequence within the current batch")
    private Integer transactionId;
    @ApiModelProperty(value = "Card Number. ", required = true, example = "123456", notes = "Mandatory in case of Physical card")
    @Length(max = 50, message = "Card Number maximum length 50")
    private String voucherNumber;
    @ApiModelProperty(value = "Card Pin. ", example = "123456", notes = "PIN associated with the card. Entered by the cardholder/Operator(optional)")
    @Length(max = 50, message = "Card Pin maximum length 50")
    private String voucherPIN;
    @ApiModelProperty(value = "Original Amount. ", example = "0", required = true, notes = "The amount of the original transaction")
    private BigDecimal originalAmount;
    @ApiModelProperty(value = "Original Approval Code. ", example = "0", notes = "The approval code of the original transaction. (optional for webpos)")
    @Length(max = 12, message = "Original Approval Code maximum length 12")
    private String originalApprovalCode;
    @ApiModelProperty(value = "Original Batch Number. ", example = "0", notes = "The batch number that was sent in the original transaction")
    private Integer originalBatchNumber;
    @ApiModelProperty(value = "Original Invoice Number. ", example = "0", notes = "The invoice number that was sent in the original transaction")
    @Length(max = 50, message = "Original Invoice Number maximum length 50")
    private String originalInvoiceNumber;
    @ApiModelProperty(value = "Original Transaction Id. ", example = "0", required = true, notes = "The TransactionId that was sent in the original transaction")
    private Integer originalTransactionId;
    @ApiModelProperty(value = "Notes. ", example = "0", notes = "Any Reference text to be captured along with this transaction.")
    @Length(max = 512, message = "Notes maximum length 512")
    private String notes;
    @ApiModelProperty(value = "Date At Client. ", example = "2022-02-02 18:00:00", required = true, notes = "The datetime at the client machine in YYYY-MM-DD HH:MM:SS")
    private String clientTime;
    @ApiModelProperty(value = "Idempotency Key. ", example = "SF00001", required = true, notes = "Every Request can have a unique Idempotency key, which is a reference number for the request. Say a request has reached server, but the response failed to reach the client. Using the idempotency key, client can resend the request, and the original response will be resent by server The following special characters are not allowed ^ < > ' \\ \" / ; ` % & SPACE , @ * ! ? % +")
    @Length(max = 255, message = "Idempotency Key maximum length 255")
    private String retryKey;

//    private String tId;

    public Map<String,Object> toMap(){
        Map<String,Object> map = new HashMap<>();
        map.put("transactionId",transactionId);
        map.put("cardNumber", voucherNumber);
        map.put("cardPIN", voucherPIN);
        map.put("originalAmount",originalAmount);
        map.put("originalApprovalCode",originalApprovalCode);
        map.put("originalBatchNumber",originalBatchNumber);
        map.put("originalInvoiceNumber",originalInvoiceNumber);
        map.put("originalTransactionId",originalTransactionId);
        map.put("notes",notes);
        map.put("dateAtClient", clientTime);
        map.put("idempotencyKey", retryKey);
//        map.put("terminalId", tId);
        return map;
    }


}
