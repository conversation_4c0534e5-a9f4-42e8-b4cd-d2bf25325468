package com.gtech.gvcore.common.externalmapping.response;

import com.gtech.gvcore.common.response.transaction.CancelCreateandissueResponse;
import lombok.Data;

import java.util.Date;

@Data
public class CancelCreateAndIssueResponse {


    private Long currentBatchNumber;
    private Integer transactionId;
    private String trackData;
    private String voucherNumber;
//
//    @JsonProperty("CardPIN")
    private String voucherPIN;
//    private String invoiceNumber;
//    private Integer amount;
//    private Integer billAmount;
//    private String originalInvoiceNumber;
//    private Integer originalTransactionId;
//    private Integer originalBatchNumber;
//    private String originalApprovalCode;
//    private String originalAmount;
    private String approvalCode;
//    private String addonCardNumber;
//    private String addonCardTrackData;
//    private String transferCardNumber;
//    private String merchantName;
//    private Integer adjustmentAmount;
    private Date voucherExpiry;
//    private String originalCardNumber;
//    private String originalCardPin;
//    private String cardProgramID;
//    private String corporateName;
//    private String notes;
//    private Date settlementDate;
//    private Date expiry;
//    private String purchaseOrderNumber;
//    private Integer purchaseOrderValue;
//    private Integer discountPercentage;
//    private Integer discountAmount;
//    private Integer paymentMode;
//    private String paymentDetails;
//    private Boolean bulkType;
//    private String externalCorporateId;
//    private String externalCardNumber;
    private Integer responseCode;
    private String responseMessage;
//    private String merchantOutletName;
//    private String merchantOutletAddress1;
//    private String merchantOutletAddress2;
//    private String merchantOutletCity;
//    private String merchantOutletState;
//    private String merchantOutletPinCode;
//    private String merchantOutletPhone;
//    private String maskCard;
//    private String printMerchantCopy;
//    private String invoiceNumberMandatory;
//    private String numericUserPwd;
//    private String integerAmount;
//    private String culture;
//    private String currencySymbol;
//    private String currencyPosition;
//    private String currencyDecimalDigits;
//    private String displayUnitForPoints;
//    private String receiptFooterLine1;
//    private String receiptFooterLine2;
//    private String receiptFooterLine3;
//    private String receiptFooterLine4;
//    private Boolean result;
//    private Date transferCardExpiry;
//    private Integer transferCardBalance;
    private Integer voucherStatusId;
//    private String cardStatus;
    private String voucherCurrency;
//    private Date activationDate;
//    private String SVRecentTransactions;
    private String voucherType;
//    private String transferCardTrackData;
//    private String transferCardPin;
//    private Integer cumulativeAmountSpent;
//    private Integer currencyConversionRate;
//    private Integer currencyConvertedAmount;
//    private String cardHolderName;
//    private Integer storedValueUnitID;
//    private Integer xactionAmountConvertedValue;
//    private Integer storedValueConvertedAmount;
//    private Integer promotionalValue;
//    private Integer earnedValue;
//    private Integer transactionAmount;
//    private Integer previousBalance;
//    private String upgradedCardProgramGroupName;
//    private Integer newBatchNumber;
//    private Integer originalActivationAmount;
//    private String cardCreationType;
//    private String errorCode;
//    private String errorDescription;
//    private String cardProgramGroupType;
//    private String activationCode;
//    private String activationURL;
//    @JsonProperty("MerchantID")
//    private Integer merchantID;
//    private Integer reloadableAmount;
//    private String barcode;
//    private String customer;
    private String transactionType;


    public CancelCreateAndIssueResponse setCancelCreateAndIssueResponse(CancelCreateandissueResponse cancelCreateAndIssueResponse) {
        if (cancelCreateAndIssueResponse == null) {
            return this;
        }
        this.setCurrentBatchNumber(cancelCreateAndIssueResponse.getCurrentBatchNumber());
        this.setTransactionId(cancelCreateAndIssueResponse.getTransactionId());
        this.setTrackData(cancelCreateAndIssueResponse.getTrackData());
        this.setVoucherNumber(cancelCreateAndIssueResponse.getCardNumber());
        this.setVoucherPIN(cancelCreateAndIssueResponse.getCardPIN());
//        this.setInvoiceNumber(cancelCreateAndIssueResponse.getInvoiceNumber());
//        this.setAmount(cancelCreateAndIssueResponse.getAmount());
//        this.setBillAmount(cancelCreateAndIssueResponse.getBillAmount());
//        this.setOriginalInvoiceNumber(cancelCreateAndIssueResponse.getOriginalInvoiceNumber());
//        this.setOriginalTransactionId(cancelCreateAndIssueResponse.getOriginalTransactionId());
//        this.setOriginalBatchNumber(cancelCreateAndIssueResponse.getOriginalBatchNumber());
//        this.setOriginalApprovalCode(cancelCreateAndIssueResponse.getOriginalApprovalCode());
//        this.setOriginalAmount(cancelCreateAndIssueResponse.getOriginalAmount());
        this.setApprovalCode(cancelCreateAndIssueResponse.getApprovalCode());
//        this.setAddonCardNumber(cancelCreateAndIssueResponse.getAddonCardNumber());
//        this.setAddonCardTrackData(cancelCreateAndIssueResponse.getAddonCardTrackData());
//        this.setTransferCardNumber(cancelCreateAndIssueResponse.getTransferCardNumber());
//        this.setMerchantName(cancelCreateAndIssueResponse.getMerchantName());
//        this.setAdjustmentAmount(cancelCreateAndIssueResponse.getAdjustmentAmount());
        this.setVoucherExpiry(cancelCreateAndIssueResponse.getCardExpiry());
//        this.setOriginalCardNumber(cancelCreateAndIssueResponse.getOriginalCardNumber());
//        this.setOriginalCardPin(cancelCreateAndIssueResponse.getOriginalCardPin());
//        this.setCardProgramID(cancelCreateAndIssueResponse.getCardProgramID());
//        this.setCorporateName(cancelCreateAndIssueResponse.getCorporateName());
//        this.setNotes(cancelCreateAndIssueResponse.getNotes());
//        this.setSettlementDate(cancelCreateAndIssueResponse.getSettlementDate());
//        this.setExpiry(cancelCreateAndIssueResponse.getExpiry());
//        this.setPurchaseOrderNumber(cancelCreateAndIssueResponse.getPurchaseOrderNumber());
//        this.setPurchaseOrderValue(cancelCreateAndIssueResponse.getPurchaseOrderValue());
//        this.setDiscountPercentage(cancelCreateAndIssueResponse.getDiscountPercentage());
//        this.setDiscountAmount(cancelCreateAndIssueResponse.getDiscountAmount());
//        this.setPaymentMode(cancelCreateAndIssueResponse.getPaymentMode());
//        this.setPaymentDetails(cancelCreateAndIssueResponse.getPaymentDetails());
//        this.setBulkType(cancelCreateAndIssueResponse.getBulkType());
//        this.setExternalCorporateId(cancelCreateAndIssueResponse.getExternalCorporateId());
//        this.setExternalCardNumber(cancelCreateAndIssueResponse.getExternalCardNumber());
        this.setResponseCode(cancelCreateAndIssueResponse.getResponseCode());
        this.setResponseMessage(cancelCreateAndIssueResponse.getResponseMessage());
//        this.setMerchantOutletName(cancelCreateAndIssueResponse.getMerchantOutletName());
//        this.setMerchantOutletAddress1(cancelCreateAndIssueResponse.getMerchantOutletAddress1());
//        this.setMerchantOutletAddress2(cancelCreateAndIssueResponse.getMerchantOutletAddress2());
//        this.setMerchantOutletCity(cancelCreateAndIssueResponse.getMerchantOutletCity());
//        this.setMerchantOutletState(cancelCreateAndIssueResponse.getMerchantOutletState());
//        this.setMerchantOutletPinCode(cancelCreateAndIssueResponse.getMerchantOutletPinCode());
//        this.setMerchantOutletPhone(cancelCreateAndIssueResponse.getMerchantOutletPhone());
//        this.setMaskCard(cancelCreateAndIssueResponse.getMaskCard());
//        this.setPrintMerchantCopy(cancelCreateAndIssueResponse.getPrintMerchantCopy());
//        this.setInvoiceNumberMandatory(cancelCreateAndIssueResponse.getInvoiceNumberMandatory());
//        this.setNumericUserPwd(cancelCreateAndIssueResponse.getNumericUserPwd());
//        this.setIntegerAmount(cancelCreateAndIssueResponse.getIntegerAmount());
//        this.setCulture(cancelCreateAndIssueResponse.getCulture());
//        this.setCurrencySymbol(cancelCreateAndIssueResponse.getCurrencySymbol());
//        this.setCurrencyPosition(cancelCreateAndIssueResponse.getCurrencyPosition());
//        this.setCurrencyDecimalDigits(cancelCreateAndIssueResponse.getCurrencyDecimalDigits());
//        this.setDisplayUnitForPoints(cancelCreateAndIssueResponse.getDisplayUnitForPoints());
//        this.setReceiptFooterLine1(cancelCreateAndIssueResponse.getReceiptFooterLine1());
//        this.setReceiptFooterLine2(cancelCreateAndIssueResponse.getReceiptFooterLine2());
//        this.setReceiptFooterLine3(cancelCreateAndIssueResponse.getReceiptFooterLine3());
//        this.setReceiptFooterLine4(cancelCreateAndIssueResponse.getReceiptFooterLine4());
//        this.setResult(cancelCreateAndIssueResponse.getResult());
//        this.setTransferCardExpiry(cancelCreateAndIssueResponse.getTransferCardExpiry());
//        this.setTransferCardBalance(cancelCreateAndIssueResponse.getTransferCardBalance());
        this.setVoucherStatusId(cancelCreateAndIssueResponse.getCardStatusId());
//        this.setCardStatus(cancelCreateAndIssueResponse.getCardStatus());
        this.setVoucherCurrency(cancelCreateAndIssueResponse.getCardCurrencySymbol());
//        this.setActivationDate(cancelCreateAndIssueResponse.getActivationDate());
//        this.setSVRecentTransactions(cancelCreateAndIssueResponse.getSVRecentTransactions());
        this.setVoucherType(cancelCreateAndIssueResponse.getCardType());
//        this.setTransferCardTrackData(cancelCreateAndIssueResponse.getTransferCardTrackData());
//        this.setTransferCardPin(cancelCreateAndIssueResponse.getTransferCardPin());
//        this.setCumulativeAmountSpent(cancelCreateAndIssueResponse.getCumulativeAmountSpent());
//        this.setCurrencyConversionRate(cancelCreateAndIssueResponse.getCurrencyConversionRate());
//        this.setCurrencyConvertedAmount(cancelCreateAndIssueResponse.getCurrencyConvertedAmount());
//        this.setCardHolderName(cancelCreateAndIssueResponse.getCardHolderName());
//        this.setStoredValueUnitID(cancelCreateAndIssueResponse.getStoredValueUnitID());
//        this.setXactionAmountConvertedValue(cancelCreateAndIssueResponse.getXactionAmountConvertedValue());
//        this.setStoredValueConvertedAmount(cancelCreateAndIssueResponse.getStoredValueConvertedAmount());
//        this.setPromotionalValue(cancelCreateAndIssueResponse.getPromotionalValue());
//        this.setEarnedValue(cancelCreateAndIssueResponse.getEarnedValue());
//        this.setTransactionAmount(cancelCreateAndIssueResponse.getTransactionAmount());
//        this.setPreviousBalance(cancelCreateAndIssueResponse.getPreviousBalance());
//        this.setUpgradedCardProgramGroupName(cancelCreateAndIssueResponse.getUpgradedCardProgramGroupName());
//        this.setNewBatchNumber(cancelCreateAndIssueResponse.getNewBatchNumber());
//        this.setOriginalActivationAmount(cancelCreateAndIssueResponse.getOriginalActivationAmount());
//        this.setCardCreationType(cancelCreateAndIssueResponse.getCardCreationType());
//        this.setErrorCode(cancelCreateAndIssueResponse.getErrorCode());
//        this.setErrorDescription(cancelCreateAndIssueResponse.getErrorDescription());
//        this.setCardProgramGroupType(cancelCreateAndIssueResponse.getCardProgramGroupType());
//        this.setActivationCode(cancelCreateAndIssueResponse.getActivationCode());
//        this.setActivationURL(cancelCreateAndIssueResponse.getActivationURL());
//        this.setMerchantID(cancelCreateAndIssueResponse.getMerchantID());
//        this.setReloadableAmount(cancelCreateAndIssueResponse.getReloadableAmount());
//        this.setBarcode(cancelCreateAndIssueResponse.getBarcode());
//        this.setCustomer(cancelCreateAndIssueResponse.getCustomer());
        this.setTransactionType(cancelCreateAndIssueResponse.getTransactionType());
        return this;


    }





}
