package com.gtech.gvcore.common.request.productcategory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 查询商品分类请求
 */
@Data
@ApiModel(value = "QueryProductCategoryRequest", description = "查询商品分类请求")
public class QueryProductCategoryRequest {
    /**
     * 分类编码
     */
    @ApiModelProperty(value = "分类编码")
    @Size(max = 50)
    private String categoryCode;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    @Size(max = 100)
    private String categoryName;

    /**
     * 父分类编码
     */
    @ApiModelProperty(value = "父分类编码")
    @Size(max = 50)
    private String parentCategoryCode;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", required = true)
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", required = true)
    private Integer pageSize = 10;
} 