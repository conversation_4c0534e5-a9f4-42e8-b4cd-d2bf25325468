package com.gtech.gvcore.common.request.voucherbatch;

import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GenerateElectronicVouchersRequest")
public class GenerateDigitalVouchersRequest {

    private String customerOrderCode;

    private String createUser;

    private Date createTime;

    private BigDecimal denomination;


    private String cpgCode;

    private String mopCode;

    private String issuerCode;


    private String purchaseOrderNo;


    private String activeUrl;

    private String invoiceNumber;

    private Date voucherEffectiveDate;

    List<GetCustomerOrderDetailsResponse> customerOrderDetails;

    private String approvalCode;

    private String invoiceNo;


}
