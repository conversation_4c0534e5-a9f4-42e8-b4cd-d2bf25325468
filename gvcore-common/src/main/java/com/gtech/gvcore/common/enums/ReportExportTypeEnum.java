package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/16 16:39
 */

public enum ReportExportTypeEnum {


    // business report enum

    AUDIT_TRAIL_REPORT(null, "Audit Trail Report", "Audit Trail Report", 1),

    BLOCKED_AND_DEACTIVATED_SUMMARY(null, "Blocked&Deactivated Summary Report", "Summary Deactivated", 3),
    BLOCKED_AND_DEACTIVATED_DETAILED(null, "Blocked&Deactivated Detailed Report", "Detail Deactivated", 4),

    BOOKLET_INVENTORY_SUMMARY_REPORT("templates/xlsx/BookletInventorySummaryReport.xlsx", "Booklet Inventory Summary Report", "Voucher Inventory Summary", 5),
    BOOKLET_INVENTORY_DETAILED_REPORT("templates/xlsx/BookletInventoryDetailedReport.xlsx", "Booklet Inventory Detailed Report", "Voucher Inventory Detailed", 6),

    BULK_ORDER_SUMMARY_REPORT(null, "Bulk Order Summary Report", "Bulk Order Summary", 7),
    BULK_ORDER_DETAILED_REPORT(null, "Bulk Order Detailed Report", "Bulk Order Detailed", 8),

    CANCEL_REDEEMED_SUMMARY_REPORT(null, "Cancel Redeemed Summary Report", "Summary Cancel Redeem", 9),
    CANCEL_REDEEMED_DETAILED_REPORT(null, "Cancel Redeemed Detailed Report", "Detail Cancel Redeem", 10),

    CANCEL_SALES_SUMMARY_REPORT(null, "Cancel Sales Summary Report", "Summary Cancel Sales", 11),
    CANCEL_SALES_DETAILED_REPORT(null, "Cancel Sales Detailed Report", "Detail Cancel Sales", 12),

    CARD_LIFE_CYCLE_REPORT("templates/xlsx/VoucherLifeCycleReport.xlsx", "Voucher Life Cycle Report", "Voucher Life Cycle", 13),
    CARD_LIFE_CYCLE_TRANSACTION_REPORT(null, null, "Voucher Transaction Report", 14),
    CARD_LIFE_CYCLE_MOVEMENT_REPORT(null, null, "Voucher Movement", 15),

    EGV_TRACKING_REPORT("templates/xlsx/EGVTrackingReport.xlsx", "EGV Tracking Report", "EGV Tracking Report", 16),

    EXPIRY_GV_SUMMARY_REPORT("templates/xlsx/ExpiryGVSummaryReport.xlsx", "Expiry GV Summary Report", "Summary Partner GV Expiry", 17),
    EXPIRY_GV_DETAILED_REPORT(null, "Expiry GV Detailed Report", "Detail Partner GV Expiry", 18),

    GOODS_IN_TRANSIT_SUMMARY_REPORT("templates/xlsx/GoodsInTransitSummaryReport.xlsx", "Goods in Transit (GIT )Summary Report", "Summary Goods In Transit", 19),
    GOODS_IN_TRANSIT_DETAILED_REPORT("templates/xlsx/GoodsInTransitDetailedReport.xlsx", "Goods in Transit (GIT ) Detailed Report", "Detail Goods In Transit", 20),

    LATEST_GV_STATUS_REPORT(null, "Latest GV Status Report", "Latest GV Status Report", 21),

    LIABILITY_SUMMARY_REPORT("templates/xlsx/LiabilitySummaryReport.xlsx", "Liability Summary Report", "Liability Summary", 22),
    LIABILITY_DETAILED_REPORT(null, "Liability Detailed Report", "Liability Detailed Report", 23),

    VOUCHER_MOVEMENT_SUMMARY_REPORT("templates/xlsx/VoucherMovementSummaryReport.xlsx", "Voucher Movement Summary Report", "Voucher Movement Summary Report", 24),
    VOUCHER_MOVEMENT_DETAILED_REPORT(null, "Voucher Movement Detail Report", "Voucher Movement Detail Report", 25),

    REDEMPTION_SUMMARY_REPORT(null, "Redemption Summary Report", "DayWise Bulk Redemption Summary", 26),
    REDEMPTION_DETAILED_REPORT(null, "Redemption Detailed Report", "Store Redemption-Detailed", 27),

    REISSUED_SUMMARY_REPORT(null, "Reissued Summary Report", "Summary Reissued Report", 28),
    REISSUED_DETAILED_REPORT(null, "Reissued Detailed Report", "Detail Reissued Report", 29),

    SALES_SUMMARY_REPORT(null, "Sales Summary Report", "Daywise Bulk Sales Summary", 30),
    SALES_DETAILED_REPORT(null, "Sales Detailed Report", "Store Sales-Detailed", 31),

    TRANSACTIONS_DETAILED_SUMMARY_REPORT(null, "Transactions Detailed Report", "Transactions Detailed Report", 32),

    FINANCE_RETAIL_ADMIN_REDEMPTION_SUMMARY_REPORT(null, "Finance Retail Admin Redemption Summary Report", "Finance Redemption Summary", 37),
    FINANCE_RETAIL_ADMIN_REDEMPTION_DETAILED_REPORT(null, "Finance Retail Admin Redemption Detailed Report", "Finance Redemption Detailed", 38),

    INVENTORY_SUMMARY_REPORT(null, "Inventory Summary Report", "Voucher Inventory Summary", 39),
    INVENTORY_DETAILED_REPORT(null, "Inventory Detailed Report", "Voucher Inventory Detailed", 40),

    AGING_REPORT_SUMMARY_REPORT("templates/xlsx/AgingReport.xlsx", "Aging Report", "Summary", 41),
    AGING_MONTH_SALES_AND_CONTRIBUTION_REPORT(null, null, "Month Sales And Contribution", 42),
    AGING_USED_AND_UNREDEEMED_REPORT(null, null, "Used And Unredeemed", 43),
    AGING_VOUCHER_USED_AGE_REPORT(null, null, "Voucher Used Age", 44),
    AGING_VOUCHER_USED_AGE_BY_SBU_REPORT(null, null, "Voucher Used Age by SBU", 45),
    AGING_VOUCHER_USED_AGE_BY_SBU_T_ONE_REPORT(null, null, "", 46),
    AGING_VOUCHER_USED_AGE_BY_SBU_T_TWO_REPORT(null, null, "", 47),
    AGING_AGE_BY_SBU_DETAILS_ONE_REPORT(null, null, "Age by SBU Details_1", 48),
    AGING_AGE_BY_SBU_DETAILS_TWO_REPORT(null, null, "Age by SBU Details_2", 49),

    FINANCE_RETAIL_ADMIN_SALES_SUMMARY_REPORT(null, "Finance Retail Admin Sales Summary Report", "Finance Sales Summary", 50),
    FINANCE_RETAIL_ADMIN_SALES_DETAILED_REPORT(null, "Finance Retail Admin Sales Detailed Report", "Finance Sales Detailed", 51),

    REACTIVATE_SUMMARY_REPORT(null, "Reactivated Summary Report", "Summary Reactivated", 52),
    REACTIVATE_DETAILED_REPORT(null, "Reactivated Detailed Report", "Detail Reactivated", 53),

    PERFORMANCE_REPORT("templates/xlsx/PerformanceSummary.xlsx", "Performance Summary Report", "Performance Summary", 56),
    PARTNER_ACTIVATION_REPORT(null, "Partner Activation Report", "Activation Detail", 54),
    PARTNER_REDEMPTION_REPORT(null, "Partner Redemption Report", "Redemption Detail", 55),
    PARTNER_SALES_REPORT(null, "Detail Partner Sales Report", "Detail Partner Sales", 57),

    VOUCHER_RETURN_AND_TRANSFER_REPORT(null, "Voucher Return And Transfer Report", "Voucher Return And Transfer", 58),
    VOUCHER_PRINTING_REPORT(null, "Voucher Printing Report", "Voucher Printing", 59),
    VOUCHER_REQUEST_ALLOCATE_RECEIVE_REPORT(null, "Voucher Request Allocate Receive Report", "Request Allocate Receive", 60),
    REGENERATE_ACTIVATION_CODE_REPORT(null, "Regenerate Activation Code Report", "sheet name", 61),



    //Gift card report enums
    GC_SALES_SUMMARY_REPORT(null, "Gift Card Sales Summary Report", "Daywise Bulk Sales Summary", 62),
    GC_SALES_DETAILED_REPORT(null, "Gift Card Sales Detailed Report", "Store Sales-Detailed", 63),
    GC_REDEMPTION_SUMMARY_REPORT(null, "Gift Card Redemption Summary Report", "DayWise Bulk Redemption Summary", 64),
    GC_REDEMPTION_DETAILED_REPORT(null, "Gift Card Redemption Detailed Report", "Store Redemption-Detailed", 65),
    GC_BULK_ORDER_SUMMARY_REPORT(null, "Gift Card Bulk Order Summary Report", "Bulk Order Summary", 66),
    GC_BULK_ORDER_DETAILED_REPORT(null, "Gift Card Bulk Order Detailed Report", "Bulk Order Detailed", 67),
    GC_CANCEL_SALES_SUMMARY_REPORT(null, "Gift Card Cancel Sales Summary Report", "Summary Cancel Sales", 68),
    GC_CANCEL_SALES_DETAILED_REPORT(null, "Gift Card Cancel Sales Detailed Report", "Detail Cancel Sales", 69),
    GC_BLOCKED_AND_DEACTIVATED_SUMMARY(null, "Gift Card Blocked&Deactivated Summary Report", "Summary Deactivated", 70),
    GC_BLOCKED_AND_DEACTIVATED_DETAILED(null, "Gift Card Blocked&Deactivated Detailed Report", "Detail Deactivated", 71),
    GC_REACTIVATED_SUMMARY(null, "Gift Card Reactivated (Block) Summary Report", "Summary Reactivated", 72),
    GC_REACTIVATED_DETAILED(null, "Gift Card Reactivated (Block) Detailed Report", "Detail Reactivated", 73),
    GC_DEACTIVATED_DETAILED(null, "Gift Card Deactivated (Block) Detailed Report", "Detail Deactivated", 74),
    GC_EXPIRY_SUMMARY(null, "Gift Card Gift Card Expiry Summary Report", "Summary Expiry", 75),
    GC_EXPIRY_DETAILED(null, "Gift Card Gift Card Expiry Detailed Report", "Detail Expiry", 76),
    GC_EXTEND_EXPIRY_SUMMARY(null, "Gift Card Extend Expiry Summary Report", "Summary Extend Expiry", 77),
    GC_EXTEND_EXPIRY_DETAILED(null, "Gift Card Extend Expiry Detailed Report", "Detail Extend Expiry", 78),
    GC_LATEST_GV_STATUS_REPORT(null, "Gift Card Latest Status Report", "Gift Card Latest Status Report", 79),
    GC_TRANSACTIONS_DETAILED_SUMMARY_REPORT(null, "Gift Card Transactions Detailed Report", "Gift Card Transactions Detailed Report", 80),
    GC_CARD_LIFE_CYCLE_REPORT("templates/xlsx/GcCardLifeCycleReport.xlsx", "Gift Card Life Cycle Report", "Gift Card Life Cycle", 81),
    GC_LIFE_CYCLE_TRANSACTION_REPORT(null, null, "Gift Card Transaction Report", 82),
    GC_REGENERATE_ACTIVATION_CODE_REPORT("templates/xlsx/GcRegenerateActivationCodeReport.xlsx", "Gift Card Regenerate Activation Code Report - Summary", "Regenerate Activation Code Report - Summary", 83),
    GC_REGENERATE_ACTIVATION_CODE_DETAIL_REPORT("templates/xlsx/GcRegenerateActivationCodeReport.xlsx", "Gift Card Regenerate Activation Code Report - Detail", "Regenerate Activation Code Report - Detail", 86),
    GC_BALANCE_CORRECTION_REPORT(null, "Gift Card Balance Correction Report - Summary", "Gift Card Balance Correction Report - Summary", 84),
    GC_BALANCE_CORRECTION_DETAIL_REPORT(null, "Gift Card Balance Correction Report - Detail", "Gift Card Balance Correction Report - Detail", 85),
    GC_LIABILITY_SUMMARY_REPORT("templates/xlsx/GcLiabilitySummaryReport.xlsx", "Liability Summary Report", "Liability Summary", 87),
    GC_LIABILITY_DETAILED_REPORT(null, "Liability Detailed Report", "Liability Detailed Report", 88),
    // JUNIT TEST ENUM
    JUNIT_DEFAULT_EXCEL_CONTEXT("/", "default excel context test name", "sheet name", -1),
    ;


    /**
     * properties
     */
    private final String templateName;

    /**
     * 报表导出名称
     */
    private final String exportName;

    private final String sheetName;

    /**
     * 对应t_masterdata_dd_lang表TYPE的type
     */
    private final Integer exportType;

    ReportExportTypeEnum(String templateName, String exportName, String sheetName, Integer exportType) {
        this.templateName = templateName;
        this.exportName = exportName;
        this.sheetName = sheetName;
        this.exportType = exportType;
    }

    public String getTemplateName() {
        return templateName;
    }

    public String getExportName() {
        return exportName;
    }

    public String getSheetName() {
        return sheetName;
    }

    public Integer getExportType() {
        return exportType;
    }

    public static ReportExportTypeEnum getExportTypeEnumByType(int type) {

        for (ReportExportTypeEnum value : ReportExportTypeEnum.values()) {
            if (value.getExportType() == type) {
                return value;
            }
        }
        return null;
    }

}
