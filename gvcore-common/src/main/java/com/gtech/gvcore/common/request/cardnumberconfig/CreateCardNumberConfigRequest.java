package com.gtech.gvcore.common.request.cardnumberconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CreateCardNumberConfigRequest {
    @ApiModelProperty(value = "description")
    private String description;
    @ApiModelProperty(value = "code")
    private String code;
    @ApiModelProperty(value = "denomination")
    private String denomination;
    @ApiModelProperty(value = "remark")
    private String remark;
    @ApiModelProperty(value = "status")
    private String status;
    @ApiModelProperty(value = "type")
    private String type;
}
