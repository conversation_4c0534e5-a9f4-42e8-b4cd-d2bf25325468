package com.gtech.gvcore.common.request.customer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateCustomerRequest")
public class UpdateCustomerRequest {

    @ApiModelProperty(value = "Customer code.", example = "11222334", required = true)
    @NotEmpty(message = "customerCode can not be empty")
    @Length(max = 100)
    private String customerCode;

    @ApiModelProperty(value = "Issuer code.", example = "123124", required = true)
    @Length(max = 100)
    private String issuerCode;


    @ApiModelProperty(value = "Outlet code.", example = "123124", required = true)
    @Length(max = 100)
    private String outletCode;


    @ApiModelProperty(value = "Contact first name.", example = "user1")
    @Length(max = 100)
    private String contactFirstName;

    @ApiModelProperty(value = "Contact first name.", example = "user2")
    @Length(max = 100)
    private String contactLastName;

    @ApiModelProperty(value = "productCategoryCodeList.")
    private List<String> productCategoryCodeList;

    //payment method
    @ApiModelProperty(value = "Mop group", example = "1")
    private String mopGroup;

    @ApiModelProperty(value = "Company name.", example = "*********")
    @Length(max = 100)
    private String companyName;


    @ApiModelProperty(value = "Contact division.", example = "user1")
    @Length(max = 100)
    private String contactDivision;

    @ApiModelProperty(value = "Contact phone.", example = "********")
    @Length(max = 100)
    private String contactPhone;


    @ApiModelProperty(value = "Customer name.", example = "user1")
    @Length(max = 100)
    private String customerName;

    @ApiModelProperty(value = "Customer type.", example = "1")
    @Length(max = 100)
    private String customerType;

    @ApiModelProperty(value = "Transfer account.", example = "**********")
    @Length(max = 100)
    private String transferAccount;

    @ApiModelProperty(value = "Band card issuer.", example = " ********")
    @Length(max = 100)
    private String bankCardIssuer;

    @ApiModelProperty(value = "Note.", example = "note")
    @Length(max = 500)
    private String note;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty(value = "Contact email.", example = "<EMAIL>")
    @Length(max = 100)
    @Email(message = "email format error ")
    private String contactEmail;

    @ApiModelProperty(value = "Shipping address1.", example = "2nd Floor NO.23")
    @Length(max = 512)
    private String shippingAddress1;

    @ApiModelProperty(value = "Shipping address2.", example = "2nd Floor NO.23")
    @Length(max = 512)
    private String shippingAddress2;

    @ApiModelProperty( value="Update user.", example="user1")
    @Length(max = 100)
    private String updateUser;

    @ApiModelProperty(value = "Distribution Function.", example = "0")
    @Length(max = 10)
    private String distributionFunction;

    @ApiModelProperty(value = "User email",example = "<EMAIL>;<EMAIL>")
    private String userEmail;

    @ApiModelProperty(value = "channel",example = "")
    private String channel;

    @ApiModelProperty(value = "Registration Year",example = "2021")
    private String registrationYear;

    @ApiModelProperty(value = "Beneficiary Name", example = "")
    private String beneficiaryName;

    @ApiModelProperty(value = "Branch Name", example = "")
    private String branchName;

    @ApiModelProperty(value = "Bank Name", example = "")
    private String bankName;

    @ApiModelProperty(value = "Account Number", example = "")
    private String accountNumber;

    @ApiModelProperty(value = "pph(0/1)", example = "0")
    private Integer pph;


}
