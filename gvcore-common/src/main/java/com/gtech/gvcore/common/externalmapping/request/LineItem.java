package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.HashMap;
import java.util.Map;

@Data
public class LineItem {


    @ApiModelProperty(value = "Line Item No. ", required = true, example = "123456", notes = "Represents line number from the line number list. LineItemNo will be unique within the order. Sequential numbers recommended")
    @Length(max = 10, message = "Line Item No maximum length 10")
    private String itemNo;
    @ApiModelProperty(value = "Input Type. ", required = true, example = "1", notes = "1 - INDIVIDUAL 2 - RANGE 3 - EGV (Only for bulk activate)")
    private String inputType;
    //"amountPerCard" : "50,000"    ETP的销售
    @ApiModelProperty(value = "Amount Per Card. ", example = "0", notes = "Mandatory in case of Bulk Redeem. For Activation amount will be of fixed denomination and will be derived from program setup.")
    private String amountPerVoucher;
    @ApiModelProperty(value = "Number Of Cards. ", required = true, example = "1", notes = "Quantity(if input type is Range and EGV)")
    private Integer numberOfVouchers;
    @ApiModelProperty(value = "Card Expiry. ", example = "2022-02-02", notes = "YYYY-MM-DD")
    private String voucherExpiryDate;
    @ApiModelProperty(value = "Start CardInfo. ", example = "1", notes = "See CardInfo below for details(if input type is Range)")
    private BulkProcessVoucherInfo startVoucherInfo;
    @ApiModelProperty(value = "End CardInfo. ", example = "1", notes = "See CardInfo below for details(if input type is Range)")
    private BulkProcessVoucherInfo endVoucherInfo;
    @ApiModelProperty(value = "CardInfo. ", example = "1", notes = "See CardInfo below for details( if input type is Individual or EGV)")
    private BulkProcessVoucherInfo voucherInfo;

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("lineItemNo", this.itemNo);
        map.put("inputType", this.inputType);
        map.put("amountPerCard", this.amountPerVoucher);
        map.put("numberOfCards", this.numberOfVouchers);
        map.put("cardExpiry", this.voucherExpiryDate);
        if (this.startVoucherInfo != null) {
            map.put("startCardInfo", this.startVoucherInfo.toMap());
        }
        if (this.endVoucherInfo != null) {
            map.put("endCardInfo", this.endVoucherInfo.toMap());
        }
        if (this.voucherInfo != null) {
            map.put("cardInfo", this.voucherInfo.toMap());
        }
        return map;
    }
}
