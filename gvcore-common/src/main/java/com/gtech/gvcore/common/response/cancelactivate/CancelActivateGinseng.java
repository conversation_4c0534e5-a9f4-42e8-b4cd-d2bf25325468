package com.gtech.gvcore.common.response.cancelactivate;


import com.gtech.gvcore.common.response.transaction.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Builder
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CancelActivateGinseng extends BaseResponse {

    @ApiModelProperty(value = "Response Code.", notes = "A 5 digit integer response code. A zero value indicates success and a non-zero value indicates failure.")
    private Integer responseCode;
    @ApiModelProperty(value = "Response Message.", notes = "Contains the response message or error message in case the transacƟ on has failed (response code is non-zero).")
    private String responseMessage;


}
