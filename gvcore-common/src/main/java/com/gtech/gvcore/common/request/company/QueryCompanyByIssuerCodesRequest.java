package com.gtech.gvcore.common.request.company;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/28 13:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCompanyByIssuerCodesRequest")
public class QueryCompanyByIssuerCodesRequest extends PageBean {


    @ApiModelProperty(value = "Issuer codes.", example = "11223344")
    private List<String> issuerCodes;


}
