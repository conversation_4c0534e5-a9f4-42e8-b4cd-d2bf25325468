package com.gtech.gvcore.common.externalmapping.response;

import com.gtech.gvcore.common.response.transaction.LineItemResponseInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class LineItemResponse {

    @ApiModelProperty(value = "Line Item No.", required = true, notes = "Represents line number from the line number list")
    private String itemNo;
    @ApiModelProperty(value = "Line Item Status.", required = true, notes = "SUCCESS FAILED")
    private String itemStatus;
    @ApiModelProperty(value = "Success Card Count.", required = true, notes = "Total success card count @ line item level")
    private Integer successVoucherCount;
    @ApiModelProperty(value = "Total Card Count.", required = true, notes = "Total Card Count @ line level")
    private Integer totalVoucherCount;
    @ApiModelProperty(value = "Design Code.", required = true, notes = "This is an article code")
    private String mopCode;
    @ApiModelProperty(value = "Product Code.", required = true, notes = "This is product code")
    private String articleCode;

    //PLIPos删除  Inside LineItems object, please remove “CardProgramGroupName” and “ResponseMessage”. >> May this that made yesterday test was error
	@ApiModelProperty(value = "Card Program Group Name.", required = true, notes = "Card Program Group Name")
	private String vpgName;
	@ApiModelProperty(value = "Response Message.", required = true, notes = "Success / Failure message")
	private String responseMessage;



    	@ApiModelProperty(value = "Total Amount.", required = true, notes = "Total success / fail amount")
	private BigDecimal totalAmount;
//    @ApiModelProperty(value = "Transaction Amount.", required = true, notes = "Total success / fail amount")
//    private BigDecimal transactionAmount;
    @ApiModelProperty(value = "Start Card Number.", required = true, notes = "Start card number in the lineitem")
    private String startVoucherNumber;
    @ApiModelProperty(value = "End Card Number.", required = true, notes = "End card number in the lineitem")
    private String endVoucherNumber;
    @ApiModelProperty(value = "Cards.", notes = "If sendOnlyFailedCards is true then only failed cards information in this range will be sent.")
    private List<VoucherResponse> vouchers;

    public LineItemResponse setLineItemResponse(LineItemResponseInfo lineItemResponse){
        if(lineItemResponse == null){
            return null;
        }
        this.setItemNo(lineItemResponse.getLineItemNo());
        this.setItemStatus(lineItemResponse.getLineItemStatus());
        this.setSuccessVoucherCount(lineItemResponse.getSuccessCardCount());
        this.setTotalVoucherCount(lineItemResponse.getTotalCardCount());
        this.setMopCode(lineItemResponse.getDesignCode());
        this.setArticleCode(lineItemResponse.getProductCode());
//        this.setTransactionAmount(lineItemResponse.getTransactionAmount());
        this.setStartVoucherNumber(lineItemResponse.getStartCardNumber());
        this.setEndVoucherNumber(lineItemResponse.getEndCardNumber());
        this.setTotalAmount(lineItemResponse.getTotalAmount());
        this.setVouchers(lineItemResponse.getCards().stream().map(cardResponse ->
                new VoucherResponse().setVoucherResponse(cardResponse))
                .collect(java.util.stream.Collectors.toList()));
        return this;
    }









}
