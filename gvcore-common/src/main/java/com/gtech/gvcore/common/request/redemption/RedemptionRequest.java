package com.gtech.gvcore.common.request.redemption;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

@Data
public class RedemptionRequest {


    @ApiModelProperty(value = "OutletCode", required = true)
    @NotEmpty(message = "outletCode can not be empty")
    private String outletCode;

    @ApiModelProperty(value = "Sbu")
    private String sbu;

    @ApiModelProperty(value = "CityCode")
    private String cityCode;

    @ApiModelProperty(value = "BillNumber", required = true)
    @NotEmpty(message = "billNumber can not be empty")
    private String billNumber;

    @ApiModelProperty(value = "BillAmount",required = true)
    private BigDecimal billAmount;

    @ApiModelProperty(value = "Note")
    private String note;


    @ApiModelProperty(value = "startAndEndVouchers", required = true)
    private List<StartAndEndVoucher> startAndEndVouchers;





}
