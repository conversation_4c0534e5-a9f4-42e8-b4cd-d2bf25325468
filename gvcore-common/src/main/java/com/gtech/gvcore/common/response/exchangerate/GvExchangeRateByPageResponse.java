package com.gtech.gvcore.common.response.exchangerate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * exchange rate(GvExchangeRate)实体类
 *
 * <AUTHOR>
 * @since 2022-02-25 15:03:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GvExchangeRateByPageResponse")
public class GvExchangeRateByPageResponse implements Serializable {

    /**
    * exchange rate code,data UNIQUE KEY
    */
    @ApiModelProperty(value = "exchangeRateCode")
    private String exchangeRateCode;
    /**
    * currency code
    */
    @ApiModelProperty(value = "currencyCode")
    private String currencyCode;
    /**
    * exchange rate
    */
    @ApiModelProperty(value = "exchangeRate")
    private BigDecimal exchangeRate;
    /**
    * exchange currency code
    */
    @ApiModelProperty(value = "exchangeCurrencyCode")
    private String exchangeCurrencyCode;

    /**
     * exchange exchange_rate_date
     */
    @ApiModelProperty(value = "exchangeRateDate")
    private Date exchangeRateDate;

    /**
    * status,0:disable,1:enable
    */
    @ApiModelProperty(value = "status")
    private Integer status;
    /**
    * create user
    */
    @ApiModelProperty(value = "createUser")
    private String createUser;
    /**
    * create time
    */
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
    * update user
    */
    @ApiModelProperty(value = "updateUser")
    private String updateUser;
    /**
    * update time
    */
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}