package com.gtech.gvcore.common.enums;

import java.util.Objects;

public enum GcTransactionTypeEnum {


    GIFT_CARD_SELL("1", "Gift Card Sell"),
    GIFT_CARD_CANCEL_SELL("2", "Gift Card Cancel Sell"),
    GIFT_CARD_REDEEM("3", "Gift Card Redeem"),
    GIFT_CARD_CANCEL_REDEEM("4", "Gift Card Cancel Redeem"),
    GIFT_CARD_ACTIVATE("5", "Gift Card Activate"),
    GIFT_CARD_ACTIVATION_EXTENSION("6", "Gift Card Activate Extension"),
    GIFT_CARD_DEACTIVATE("8", "Gift Card Deactivate"),
    GIFT_CARD_REACTIVATE("9", "Gift Card Reactivate"),
    ;

    private final String code;

    private final String desc;

    GcTransactionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * @param code
     * @return
     * <AUTHOR>
     * @date 2022年6月17日
     */
    public static String getTypeDesc(String code) {

        for (GcTransactionTypeEnum typeEnum : GcTransactionTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }

    public boolean equalsCode(String code) {

        return this.code.equals(code);
    }


    public static GcTransactionTypeEnum valueOfCode(String code) {
        for (GcTransactionTypeEnum type : GcTransactionTypeEnum.values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

}


