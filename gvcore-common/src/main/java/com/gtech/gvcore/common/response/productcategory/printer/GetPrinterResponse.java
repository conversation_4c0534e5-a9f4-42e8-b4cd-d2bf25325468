package com.gtech.gvcore.common.response.productcategory.printer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-22 14:49
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetPrinterByPageResponse")
public class GetPrinterResponse {

    /**
     * printerCode
     */
    @ApiModelProperty(value = "printerCode", example = "CPC102202241512000241", required = true)
    private String printerCode;


    /**
     * outlet name
     */
    @ApiModelProperty(value = "printerName")
    private String printerName;

    private String issuerCode;

    /**
     * stateCode
     */
    @ApiModelProperty(value = "stateCode")
    private String stateCode;

    /**
     * cityCode
     */
    @ApiModelProperty(value = "cityCode")
    private String cityCode;

    /**
     * districtCode
     */
    @ApiModelProperty(value = "districtCode")
    private String districtCode;


    /**
     * stateName
     */
    @ApiModelProperty(value = "stateName")
    private String stateName;

    /**
     * cityName
     */
    @ApiModelProperty(value = "cityName")
    private String cityName;

    /**
     * districtName
     */
    @ApiModelProperty(value = "districtName")
    private String districtName;


    /**
     * address
     */
    @ApiModelProperty(value = "address")
    private String address;

    /**
     * longitude
     */
    @ApiModelProperty(value = "longitude")
    private String longitude;

    /**
     * latitude
     */
    @ApiModelProperty(value = "latitude")
    private String latitude;


    /**
     * printer first name
     */
    @ApiModelProperty(value = "firstName")
    private String firstName;

    /**
     * printer last name
     */
    @ApiModelProperty(value = "lastName")
    private String lastName;

    /**
     * printer mobile
     */
    @ApiModelProperty(value = "mobile")
    private String mobile;

    /**
     * printer email
     */
    @ApiModelProperty(value = "email")
    private String email;


    @ApiModelProperty(value = "Receiving Method")
    private String receivingMethod;

    /**
     * authorization type
     */
    @ApiModelProperty(value = "ftpAuthorizationType")
    private String ftpAuthorizationType;

    /**
     * FTP Url
     */
    @ApiModelProperty(value = "ftpUrl")
    private String ftpUrl;

    /**
     * FTP username
     */
    @ApiModelProperty(value = "ftpUsername")
    private String ftpUsername;

    /**
     * FTP password
     */
    @ApiModelProperty(value = "ftpPassword")
    private String ftpPassword;


    @ApiModelProperty(value = "ftpKeyFileUrl")
    private String ftpKeyFileUrl;
}
