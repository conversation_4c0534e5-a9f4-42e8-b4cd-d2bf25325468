package com.gtech.gvcore.common.response.merchant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "MerchantResponse")
public class MerchantResponse {

    @ApiModelProperty( value = "Merchant code.", example = "1345566")
    private String merchantCode;

    @ApiModelProperty( value = "Merchant name.", example = "1345566")
    private String merchantName;

    @ApiModelProperty( value = "Company code.", example = "1345566")
    private String companyCode;

    @ApiModelProperty( value = "Issuer code.", example = "1345566")
    private String issuerCode;

    @ApiModelProperty( value = "Company name.", example = "55235")
    private String companyName;

    @ApiModelProperty( value = "Status.", example = "0")
    private Integer status;

    @ApiModelProperty( value = "Remarks.", example = "1345566")
    private String remarks;

    @ApiModelProperty( value="Update user.", example="user1")
    private String updateUser;

    @ApiModelProperty(value = "Create user.", example = "user2")
    private String createUser;

    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:22")
    private Date createTime;

    @ApiModelProperty(value = "Update time.", example = "2022-02-17 16:27")
    private Date updateTime;
    
    
    

}
