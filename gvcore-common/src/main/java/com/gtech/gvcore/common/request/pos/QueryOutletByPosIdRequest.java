package com.gtech.gvcore.common.request.pos;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryOutletByPosIdRequest")
public class QueryOutletByPosIdRequest {



    private String machineId;

    private String posAccount;

    private String posPassword;


}
