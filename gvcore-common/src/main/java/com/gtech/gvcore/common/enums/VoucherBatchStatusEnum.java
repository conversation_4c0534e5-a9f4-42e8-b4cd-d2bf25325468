package com.gtech.gvcore.common.enums;

/**
 * @ClassName VoucherBatchStatusEnum
 * @Description VoucherBatchStatus
 * <AUTHOR>
 * @Date 2022/7/12 16:26
 * @Version V1.0
 **/
public enum VoucherBatchStatusEnum implements IEnum<Integer> {

    GENERATING(0, "Generating"),
    GENERATED(1, "Generated"),
    PUBLISHED(2, "Published"),
    COMPLETED(3, "Completed"),
    FAILURE(4, "Generated (Error)"),
    DELETE(5, "DELETE"),
    CANCELED(6, "Canceled"),
    MERGE(9, "merge"),
    ;

    private final Integer code;
    private final String desc;

    VoucherBatchStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VoucherBatchStatusEnum valueOfCode(String status) {

        return valueOfCode(Integer.valueOf(status));
    }

    // value of code
    public static VoucherBatchStatusEnum valueOfCode(Integer code) {
        for (VoucherBatchStatusEnum status : VoucherBatchStatusEnum.values()) {
            if (status.code().equals(code)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public Integer code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(final Integer code) {
        return this.code().equals(code);
    }

}
