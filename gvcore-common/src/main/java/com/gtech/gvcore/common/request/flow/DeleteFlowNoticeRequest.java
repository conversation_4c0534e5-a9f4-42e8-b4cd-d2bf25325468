package com.gtech.gvcore.common.request.flow;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DeleteFlowNoticeRequest {
	

    /**
     * flow code
     */
    @ApiModelProperty(value = "flow code" ,required = true)
    @NotEmpty(message = "flow code can't be empty")
    private String flowCode;

    /**
     * flow node code
     */
    @ApiModelProperty(value = "flow node code" ,required = true)
    @NotEmpty(message = "flow node code can't be empty")
    private String flowNodeCode;

}