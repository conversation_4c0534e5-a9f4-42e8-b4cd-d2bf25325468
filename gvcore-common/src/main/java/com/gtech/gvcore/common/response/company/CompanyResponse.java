package com.gtech.gvcore.common.response.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CompanyResponse")
public class CompanyResponse {


    @ApiModelProperty( value = "Company code.", example = "1345566")
    private String companyCode;

    @ApiModelProperty(value = "Company name.", example = "PT AGUNG MANDIRI LESTARI")
    private String companyName;

    @ApiModelProperty(value = "Issuer code.", example = "1122334455")
    private String issuerCode;

    @ApiModelProperty(value = "Issuer name.", example = "1122334455")
    private String issuerName;

    @ApiModelProperty(value = "Sbu.", example = "Digimap")
    private String sbu;

    @ApiModelProperty(value = "Status 0:disable,1:enable.", example = "0")
    private Integer status;

    @ApiModelProperty(value = "Create user.", example = "user2")
    private String createUser;

    @ApiModelProperty( value="Update user.", example="user2")
    private String updateUser;

    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:28")
    private Date createTime;

    @ApiModelProperty(value = "Update time.", example = "2022-02-17 16:28")
    private Date updateTime;



}
