package com.gtech.gvcore.common.response.transaction;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("批量取消兑换响应V2")
public class BulkCancelRedeemResponseV2 {
    
    @NotNull(message = "responseCode is mandatory")
    private Integer responseCode;
    
    @NotNull(message = "responseMessage is mandatory")
    private String responseMessage;
    
    @NotNull(message = "transactionId is mandatory")
    private Long transactionId;
    
    private Integer transactionTypeId;
    
    private Integer actionType;
    private Long batchId;

    private Integer totalSuccessVoucherCount;
    
    @NotNull(message = "totalVoucherCount is mandatory")
    private Integer totalVoucherCount;
    
    @NotNull(message = "transactionAmount is mandatory")
    private BigDecimal transactionAmount;
    
    private String validationToken;
    
    private String approvalCode;
    
    private String referenceNumber;
    
    private String sourceId;
    
    private String messageId;
    
    private String orderNumber;
    
    @NotNull(message = "invoiceNumber is mandatory")
    private String invoiceNumber;
    
    @NotEmpty(message = "voucherItems is mandatory")
    private List<VoucherItemResponse> voucherItems = new ArrayList<>();
    
    @NotEmpty(message = "rejectedVoucherList is mandatory")
    private List<RejectedVoucherItem> rejectedVoucherList = new ArrayList<>();
    
    @Data
    public static class VoucherItemResponse {

        @NotNull(message = "itemNo is mandatory")
        private String itemNo;

        @NotNull(message = "itemStatus is mandatory")
        private String itemStatus = "SUCCESS";

        @NotNull(message = "totalVoucherCount is mandatory")
        private Integer totalVoucherCount;

        @NotNull(message = "mopCode is mandatory")
        private String mopCode;

        @NotNull(message = "articleCode is mandatory")
        private String articleCode;

        @NotNull(message = "vpgName is mandatory")
        private String vpgName;

        @NotNull(message = "responseMessage is mandatory")
        private String responseMessage;

        @NotNull(message = "totalAmount is mandatory")
        private BigDecimal totalAmount;


        @NotEmpty(message = "vouchers is mandatory")
        private List<VoucherDetail> vouchers;
    }
    
    @Data
    public static class VoucherDetail {
        @NotNull(message = "voucherNumber is mandatory")
        private String voucherNumber;

        private String voucherPIN;  // Optional

        private String activationCode;  // Optional

        @NotNull(message = "voucherBalance is mandatory")
        private BigDecimal voucherBalance;

        @NotNull(message = "voucherExpiryDate is mandatory")
        private Date voucherExpiryDate;

        @NotNull(message = "transactionAmount is mandatory")
        private BigDecimal transactionAmount;

        @NotNull(message = "voucherStatus is mandatory")
        private String voucherStatus;

        private String approvalCode;  // Optional

        @NotNull(message = "vpgName is mandatory")
        private String vpgName;

        private String sequenceNo;  // Not Required

        @NotNull(message = "articleCode is mandatory")
        private String articleCode;

        @NotNull(message = "mopCode is mandatory")
        private String mopCode;

        private String trackData;  // Optional

        private String barcode;  // Optional

        @NotNull(message = "responseCode is mandatory")
        private Integer responseCode = 0 ;

        @NotNull(message = "responseMessage is mandatory")
        private String responseMessage = "Transaction successful.";


    }
    
    @Data
    public static class RejectedVoucherItem extends VoucherItemResponse {
        public RejectedVoucherItem() {
            setItemStatus("FAILED");

        }
    }
} 