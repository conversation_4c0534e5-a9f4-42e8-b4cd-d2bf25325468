package com.gtech.gvcore.common.response.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName HistoricalInventoryChartResponse
 * @Description 历史库存图表数据
 * <AUTHOR>
 * @Date 2022/7/6 11:04
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "HistoricalInventoryChartResponse")
public class HistoricalInventoryChartResponse {

    @ApiModelProperty(value = "有效库存信息")
    private List<MonthInventoryResponse> availableData;

    @ApiModelProperty(value = "已分发库存数")
    private List<MonthInventoryResponse> distributedData;

}
