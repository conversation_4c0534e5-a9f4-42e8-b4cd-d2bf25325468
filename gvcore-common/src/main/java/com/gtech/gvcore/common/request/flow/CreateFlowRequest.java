package com.gtech.gvcore.common.request.flow;

import java.util.List;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CreateFlowRequest {

    /**
     * issuer code
     */
    @ApiModelProperty(value = "issuer code")
    private String issuerCode;

    /**
     * flow code
     */
    @ApiModelProperty(value = "flow code")
    private String flowCode;

    /**
     * flow name
     */
    @ApiModelProperty(value = "flow name", required = true)
    @NotEmpty(message = "flow name can't be empty")
    private String flowName;

    /**
     * remark
     */
    private String remark;

    /**
     * status
     */
    private Integer status;

    /**
     * create user
     */
    @ApiModelProperty(value = "create user" , required = true)
    @NotEmpty(message = "create user can't be empty")
    private String createUser;
    
    private List<FlowNodeRequest> flowNodeList;

}