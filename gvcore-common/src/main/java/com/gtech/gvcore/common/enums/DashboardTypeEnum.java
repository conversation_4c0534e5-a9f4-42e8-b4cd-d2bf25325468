package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/8/30 14:07
 */

public enum DashboardTypeEnum {


    SALES("0", "Sales"),
    REDEMPTION("1", "Redemption");

    private final String code;

    private final String desc;

    DashboardTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


}
