package com.gtech.gvcore.common.request.customer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GetCustomerRequest")
public class GetCustomerRequest {


    @ApiModelProperty(value = "Customer code.", example = "11222334", required = true)
    @NotEmpty(message = "customerCode can not be empty")
    @Length(max = 100)
    private String customerCode;






}
