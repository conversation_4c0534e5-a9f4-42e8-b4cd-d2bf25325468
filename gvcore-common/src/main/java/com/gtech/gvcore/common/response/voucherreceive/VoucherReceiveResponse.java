package com.gtech.gvcore.common.response.voucherreceive;

import com.gtech.gvcore.common.response.allocation.GetVoucherAllocationResponse;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class VoucherReceiveResponse {

    /**
     * voucher receive code
     */
	@ApiModelProperty(value = "voucher_receive_code")
    private String voucherReceiveCode;

	/**
	 * source type: generate, allocation
	 */
	@ApiModelProperty(value = "Source type:  generate, allocation")
    private String sourceType;

    /**
     * source data code
     */
	@ApiModelProperty(value = "Source data code")
    private String sourceDataCode;

	@ApiModelProperty(value = "Receiver code")
	private String receiverCode;

    /**
     * issuer code
     */
	@ApiModelProperty(value = "Issuer code")
    private String issuerCode;

	/**
	 * issuer name
	 */
	@ApiModelProperty(value = "Issuer name")
	private String issuerName;

    /**
     * outbound name
     */
	@ApiModelProperty(value = "Outbound name")
    private String outbound;

    /**
     * inbound name
     */
	@ApiModelProperty(value = "Inbound name")
    private String inbound;

    /**
     * Total number of vouchers
     */
	@ApiModelProperty(value = "Total number of vouchers")
    private Integer voucherNum;

    /**
     * received number of vouchers
     */
	@ApiModelProperty(value = "Received number of vouchers")
    private Integer receivedNum;

    /**
     * status, 0:Processing, 1:Completed
     */
	@ApiModelProperty(value = "Status, 0:Processing, 1:Completed")
    private Integer status;

    /**
	 * create user
	 */
	@ApiModelProperty(value = "Create user")
	private String createUser;

	/**
	 * Permission code.
	 */
	@ApiModelProperty(value = "Permission code")
    private String permissionCode;

	@ApiModelProperty(value = "Cpg name")
	private String cpgName;

	@ApiModelProperty(value = "Total amount")
	private BigDecimal totalAmount;

	@ApiModelProperty(value = "Purchase Order No")
	private String purchaseOrderNo;

    /**
     * create time
     */
	@ApiModelProperty(value = "Create time")
    private Date createTime;
	
	private List<VoucherReceiveBatchResponse> receiveBatchList;

	@ApiModelProperty(value = "Voucher alloction response, if sourceType is allocation")
	private GetVoucherAllocationResponse getVoucherAllocationResponse;

	@ApiModelProperty(value = "Voucher batch response, if sourceType is generate")
	private VoucherBatchResponse voucherBatchResponse;

	@ApiModelProperty(value = "toBeReceiveVoucher")
	private List<ToBeReceiveVoucher> toBeReceiveVoucher;

	@ApiModelProperty(value = "outboundCode")
	private String outboundCode;

	@ApiModelProperty(value = "inboundCode")
	private String inboundCode;

}