package com.gtech.gvcore.common.response.transaction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class TransactionResponse {
	@ApiModelProperty(value = "Response Code.", required = true, notes = "A zero value indicates successful response and non zero means failure")
	private Integer responseCode;
	@ApiModelProperty(value = "Response Message.", required = true, notes = "Contains the response message or error message in case the transacƟ on has failed (response code is non-zero).")
	private String responseMessage;
	@ApiModelProperty(value = "Transaction Id.", required = true, notes = "The transacƟ on id passed in the request. This transacƟ on id needs to be persisted to be passed as a OriginalTransacƟ onId for cancellaƟ on.")
	private Integer transactionId;
	@ApiModelProperty(value = "Transaction Type Id.", required = true, notes = "The transactionTypeId echoed back.")
	private Integer transactionTypeId;
	@ApiModelProperty(value = "Action Type.", required = true, notes = "action type echoed back.")
	private Integer actionType;
	@ApiModelProperty(value = "Batch Id.", required = true, notes = "The batch id extracted from auth token will be returned. This needs to be persisted to be passed as a OriginalBatchId for cancellaƟ on")
	private Integer batchId;
	@ApiModelProperty(value = "Total Success Card Count.", required = true, notes = "Total success cards count")
	private Integer totalSuccessCardCount;
	@ApiModelProperty(value = "Total Card Count.", required = true, notes = "Total cards count involved in this bulk transaction")
	private Integer totalCardCount;
	@ApiModelProperty(value = "Transaction Amount.", notes = "Total Success transaction amount")
	private BigDecimal transactionAmount;
	@ApiModelProperty(value = "Validation Token.", notes = "Token representing the validation is done successfully and will be used in subsequent transaction. Applicable when actiontype=validate")
	private String validationToken;
	@ApiModelProperty(value = "Approval Code.", notes = "Qwikcilver generated approval code for activations and redemptions. Not applicable for validations. This will be used for cancellations.")
	private String approvalCode;
	@ApiModelProperty(value = "Reference Number.", notes = "Qwikcilver generated reference number for the transaction")
	private String referenceNumber;
	@ApiModelProperty(value = "Source Id.", notes = "External source id - required for other integration.Echo from the input")
	private String sourceId;
	@ApiModelProperty(value = "Message Id.", notes = "External message id, required for other integration.Echo from the input")
	private String messageId;
	@ApiModelProperty(value = "Order Number.", required = true, notes = "Order number")
	private String ordernumber;

	@ApiModelProperty(value = "Line Items.", required = true, notes = "Array of LineItemResponseInfo, Please see below")
	private List<LineItemResponseInfo> lineItems;
	@ApiModelProperty(value = "Invalid Line Items.", required = true, notes = "Array of line items. Will contain information about failed cards in each line item. Structure will remain same as LineItemResponseInfo with status as failed.")
	private List<LineItemResponseInfo> invalidLineItems;



}
