package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2022年2月17日
 */
public enum ResultErrorCodeEnum implements IEnum<String> {
    /**
     * ResultError
     */
    FAILED("9999", "FAILED"),

    GENERAL_ERROR("0001", "General error"), 
    SYSTEM_EXCEPTION("0002", "System exception"),
    DATA_MISS("0003", "Data miss"), 
    REQUEST_DATA_ERROR("0004", "Request data contains error, please check data."),
    PARAMTER_ERROR("0005", "Paramter error"),
	PARAMTER_NULL_ERROR("0006", "Paramter empty"),
    GET_LOCK_TIMEOUT("0007", "get lock timeout"),

    // 未找到数据
    NO_DATA_FOUND("1001", "No data found"), 
    NO_CPG_DATA_FOUND("1002", "No CPG data found"),
    NO_PRODUCT_CATEGORY_DISSCOUNT_DATA_FOUND("1003", "No Product Category Disscount data found"),
    NO_VOUCHER_REQUEST_DETAIL_FOUND("1004", "There is no relevant data record"),
    NO_VOUCHER_REQUEST_DATA_FOUND("1005", "No Voucher Request data found"),
    NO_VOUCHER_REQUEST_DETAILS_DATA_FOUND("1006", "No Voucher Request Details data found"),
    NO_CPG_TYPE_DATA_FOUND("1007", "No CPG Type data found"),
    NO_ARTICLE_MOP_DATA_FOUND("1008", "No Article Code - MOP code data found"),
    NO_PRINTER_DATA_FOUND("1009", "No Printer data found"),
    NO_ALLOCATION_DATA_FOUND("1010", "No allocation data found"),
    NO_ORDER_DETAILS_DATA_FOUND("1011", "Order details data is empty"),
    NO_STORE_DATA_FOUND("1012", "No store data found."),
    NO_VOUCHER_DATA_FOUND("1013", "No voucher data found."),
    NO_ISSUER_DATA_FOUND("1014", "No issuer data found."),
    NO_OUTLET_DATA_FOUND("1015", "No outlet data found."),
    ORDER_HAS_UPDATE_FOUND("1016", "Order has update."),
    NO_POS_DATA_FOUND("1017", "No POS data found"),

    // 查询数据错误
    FIND_MORE_THAN_ONE_DATA("2001", "find more than one data"),

    // 更新数据失败
    FAILED_TO_UPDATE_DATA("3001", "Failed to update data"),
    //插入数据失败
    FAILED_TO_INSERT_DATA("3002", "Failed to insert data"),
    //删除数据失败
    FAILED_TO_DELETE_DATA("3003","Failed to delete data"),

    FAILED_TO_UPDATE_ALLOCATION_DATA("3004", "There is an issue during the allocation, so please refresh and redo it."),
    FAILED_TO_INSERT_ALLOCATION_DATA("3006", "There is an issue during the allocation, so please refresh and redo it."),
    FAILED_TO_VOUCHER_DATA_BY_ALLOCATE("3005", "There is an issue during the allocation, so please refresh and redo it."),

	// receive error 52 prefix
	VOUCHER_RECEIVE_STATUS_ERROR("5201", "Voucher receive status error"),
	RECEIVE_VOUCHER_NO_ERROR("5202", "Receive voucher number error"),
	RECEIVE_NOT_MATCH_ERROR("5203", "Received voucher do not match"),
	MERGE_VOUCHER_ERROR("5204", "Voucher status invalid"),
	MERGE_VOUCHER_NOT_FOUND_ERROR("5205", "Merger voucher not found "),
	MERGE_VOUCHER_CPG_NOT_FOUND_ERROR("5206", "CPG 105 not found "),
	RECEIVE_VOUCHER_STATUS_ERROR("5207", "Receive voucher status error"),

    // 业务错误
    DATA_ALREADY_EXISTS("5001", "The data already exists, no need to resubmit"),
    NAME_ALREADY_USED("5002", "This name has already been used"),
    DISSCOUNT_DETAIL_FROM_VALUE_ERROR("5003", "From purchase value must be greater than the Upto purchase value of the previous record"),
    DISSCOUNT_DETAIL_UPTO_VALUE_ERROR("5004", "Upto purchase value must be greater than the From purchase value"),
    CPG_TYPE_NAME_ALREADY_EXISTS("5005", "cpgTypeName already exists, no need to resubmit"),
    CPG_TYPE_PREFIX_ALREADY_EXISTS("5006", "cpgTypePrefix already exists, no need to resubmit"),
    PRINTER_NAME_ALREADY_EXISTS("5007", "printerName already exists, no need to resubmit"),
    ALREADY_ALLOCATED("5008", "The gift voucher request has been allocated, please do not repeat it."),
    VOUCHER_DISTRIBUTE_UNABLE_TO_GET_FILE("5009", "Unable to get file"),
    DISSCOUNT_VALID_DATE_ERROR("5010", "disscount valid date error"),
    VOUCHER_REQUEST_STATUS_ERROR("5011", "Please refresh the page to verify the correct status of the request."),
    ALLOCATE_VOUCHER_NO_ERROR("5012","Voucher NO error"),
    PRINTER_NAME_KEY_FILE_PATH_ISEMPTY("5013", "ftpKeyFileUrl can not be empty"),
    PRINTER_UNABLE_TO_GET_FILE("5014", "Unable to get keyFile"),
    VOUCHER_NO_ERROR("5015","Voucher NO error"),
    VOUCHER_DENOMINATION_ERROR("5016", "The denomination of the voucher is incorrect."),
    VOUCHER_START_END_DIFFERENT_DENOMINATION("5017","voucherStartNo and voucherEndNo have different denominations"),
    VOUCHER_MOP_CODE_NOT_VCR("5018", "Please use the correct paper vouchers."),
    SOME_VOUCHER_CANNOT_ALLOCATE("5019", "Some of the vouchers cannot be allocated, please check."),
    NO_VOUCHERS_ALLOCATED("5020", "Please ensure that the correct quantity and denomination of vouchers are allocated."),
    NUMBER_ARE_NOT_EQUAL("5021", "Please ensure that the correct quantity and denomination of vouchers are allocated."),
    CPG_EFFECTIVEHOUR_CANOT_BE_NULL("5022", "effectiveHour can not be null"),
    CPG_EFFECTIVE_YEAR_MONTH_DAY_CANOT_BE_NULL("5023", "effectiveYears & effectiveMonth & effectiveDay & effectiveHour cannot all be null or zero"),
    CUSTOMERORDER_NOT_APPROVAL("5024", "CustomerOrder not Approval"),
    VOUCHER_START_END_DIFFERENT_CPG("5025","voucherStartNo and voucherEndNo have different CPG"),
    VOUCHER_CPG_ERROR("5026","Voucher CPG error"),
    CUSTOMERORDER_NOT_RELEASE("5027", "CustomerOrder not Release"),
	CUSTOMERORDER_NOT_DELIVER("5028", "CustomerOrder not Deliver"),
	GENERATE_DIGITAL_VOUCHER_FAILED("5029", "Generate digital voucher_failed"),
	DELETE_DIGITAL_VOUCHER_FAILED("5030", "Delete digital voucher_failed"),
    GENERATE_CRON_ERROR("5031","Error generating cron expression"),
    REMOTE_INVOCATION_ERROR("5032","Description Failed to send a request to task-admin"),
    END_TIME_BLANK_ERROR("5033","Repeat end time cannot be empty when on date is selected"),
    VOUCHER_STATUS_ERROR("5034","Voucher status error"),
    CURRENT_STATUS_CANNOT_APPROVED("5035", "Current status cannot be approved"),
    CURRENT_STATUS_CANNOT_EXECUTE("5036", "Current status cannot be execute"),
    SCHEDULER_REPORT_NOT_EXIST("5037","Scheduler Report does not exist"),
    CANNOT_HANDLE_THIS_ISSUE_TYPE("5038", "Cannot handle this issue type."),
    NO_CONFIG_AMOUNT("5039","This voucher amount it is not configured, it cannot be approved"),
    UNSUPPORTED_COLUMN_NAME("5040", "unsupported column name："),
    UPLOADED_FILE_URL_IS_INVALID("5041", "uploaded file url is invalid."),
    UPLOADED_FILE_NOT_MEET_STANDARDS("5042", "The uploaded file does not meet the standards."),
    UPLOADED_FILE_ISEMPTY("5043", "The uploaded file is empty"),
    REQUEST_OBJECT_ERROR_WH("5044","The receiver cannot be WH"),
    WAREHOUSE_WH01_NOT_CONFIG("5045","No outlet information corresponding to WH01 is configured"),
    WAREHOUSE_HO01_NOT_CONFIG("5046","No outlet information corresponding to HO01 is configured"),
    REQUEST_OBJECT_ERROR_RETURN("5047","Cannot be Warehouse"),
    REQUEST_OBJECT_ERROR_HO01("5048","The receiver must be HO01"),
    REQUEST_NO_APPROVE("5049","The request has not yet been reviewed"),
    SAME_FILE_PROCESSING("5050", "The same file is being processed, please do not submit again."),
    VOUCHER_AMOUNT_DIFFERENT("5051", "Check that the total number or total amount of requested coupons is different"),
    VOUCHER_DETAIL_DENOMINATION_REPEAT("5052","There can only be one value of the same denomination"),
    ISSUER_NOT_CONFIG_WH("5053","No outlet corresponding to WH is configured for issuer"),
    ISSUER_NOT_CONFIG_HO("5054","No outlet corresponding to HO is configured for issuer"),
    ISSUER_NOT_CONFIG_OUTLET("5055","No outlet corresponding to the issuer is configured"),
    CURRENT_STATUS_CANNOT_EDIT("5056", "Current status cannot be edit"),
    WRONG_DATE_FORMAT("5057", "Wrong date format"),
    NOT_CANCEL_NOW_ORDER("5058","The current Customer order cannot be cancelled"),
    CURRENT_STATUS_CANNOT_CANCEL("5059", "Current status cannot be cancel"),
    OBTAIN_PRINTER_PIC_EMAIL_ERROR("5060", "Obtain printer pic email error"),
    GET_CUSTOMER_ORDER_ERROR("5061", "Get customer order information error"),
    GET_CPG_TYPE_ERROR("5062", "Get vpg type error"),
    HAVE_APPROVING_ORDER("5063","The configuration cannot be modified because an order is under review"),
    VOUCHER_NO_DATA_MISS("5064", "The voucher number is incorrect."),
    VOUCHER_OWNER_CODE_MISMATCH("5065", "The voucher is not in the correct location."),
    VOUCHER_ISSUER_CODE_MISMATCH("5066", "Your account cannot be allocated it."),
    VOUCHER_EFFECTIVE_DATE_TIMEOUT("5067", "The voucher has expired."),
    VOUCHER_OWNER_EQUALS_RECEIVER("5068", "The owner and recipient of the voucher cannot be the same."),
    ALREADY_IN_APPROVAL("5069", "It is already in approval, please do not re-approve."),

    GET_CPG_ERROR("5069", "Get vpg error"),
    LOCATION_CHANGE_ERROR("5070", "cancel voucher error, voucher a location change occurred."),
    DUPLICATE_DATA_ERROR("5071", "duplicate data error."),
    VOUCHER_ALREADY_USED("5072", "voucher already used."),

    //Voucher 错误
    VOUCHER_UPDATE_STATUS("5301","Update voucher status failure"),
    VOUCHER_DETAIL_BLANK("5302","Details cannot be blank"),
    EMAIL_FORMAT_ERROR("5303","Email format error"),
    VOUCHER_BATCH_ERROR("5304", "Voucher not the same batch"),
    VOUCHER_BOOKLET_BATCH_ERROR("5305", "Voucher booklet not the same batch"),
    VOUCHER_BAR_CODE_ERROR("5321", "Voucher barcode error."),
    VOUCHER_START_AND_END_ERROR("5322", "Start and End error."),
    BOOKLET_BAR_CODE_ERROR("5323", "Booklet barcode error."),

    //Release Approve is not configured in Setting
    RELEASE_APPROVE_NOT_CONFIG("5306", "Release Approve is not configured in Setting"),
    RELEASE_CONFIG_RANGE_ERROR("5307", "The range of configured data is not consecutive"),
    PURCHASE_ORDER_NUMBER_EXISTS("5308", "The purchase order number already exists"),
    NO_AUDIT_PERMISSION("5309", "You should not approve it at this time"),
    SEND_EMAIL_FAIL("5310", "Failed to send the email."),
    VALIDATED_CODE_ERROR("5311", "Verification code error"),
    VALIDATED_CODE_NOT_EXISTS("5312", "Verification code expired or does not exist"),
    CUSTOMERORDER_NOT_ISSUANCE("5313", "CustomerOrder not issuance"),
    TYPE_INCONSISTENCY_ERROR("5314", "They must be of the same type"),
    START_NUM_ERROR("5315", "You have to start at 1"),
    RECEIVE_DIGITAL_VOUCHER_ERROR("5316", "Receive digital voucher error."),
    ACTIVAT_DIGITAL_VOUCHER_ERROR("5317", "Activat digital voucher error."),
    MISS_APPROVE_CONFIG_LINE("5318", "Missing Approval Config line "),
    APPROVAL_NODE_CANNOT_BE_EMPTY("5319", "Missing approval node of Approval Config line "),
    APPROVE_CONFIG_NODE_MISS("5320", "Approval node missing from Approval Config line"),
    ISSUANCE_NOT_COMPLETED("5321", "Issuance not completed please wait"),

    //Printer 错误
    PRINTER_ERROR("5401", "Printer error or non existent"),

    // issuer handlling 错误
    CURRENT_STATUS_CANNOT_SUBMIT("5501", "Current status cannot be submit"),
    MISSING_PROOF_DOCUMENT("5502", "Missing proof document: "),
    UPLOADED_FILE_MORE_COLUMNS("5503", "The uploaded file row has more columns than headers"),

	SAP_MOP_GROUP_ERROR("6100", "SAP MOP group error."),
    STORE_TYPE_IS_NOT_MVSTORE("6101", "Store type is not mvstore."),

    // customer order
    CUSTOMERORDER_ALREADY_ALLOCATED("6401", "The customer order has been allocated, please do not repeat it."),
	CUSTOMERORDER_CALCULATEDISCOUNT_01("6402", "Product category disscount empty"),
	CUSTOMERORDER_CALCULATEDISCOUNT_02("6403", "Product category disscount disable"),
	CUSTOMERORDER_CALCULATEDISCOUNT_03("6404", "Product category disscount not started"),
	CUSTOMERORDER_CALCULATEDISCOUNT_04("6405", "Product category disscount has ended"),
	CUSTOMERORDER_CALCULATEDISCOUNT_05("6406", "Product category disscount details empty"),
    CUSTOMERORDER_CALCULATEDISCOUNT_06("6407", ""),

    VOUCHER_INVENTORY_SHORTAGE("6408","Voucher inventory shortage"),
    DENOMINATION_DOES_NOT_EXIST("6409","Denomination does not exist"),
    DENOMINATION_DOES_NOT_NULL("6410","Denomination can not be empty."),

    // allocate

    // setting Approval Config
    NO_MANUAL_APPROVAL_REQUIRED("7001", "No manual Approval required"),

    // report
    CANNOT_PROCESS_REPORT("8001", "Cannot process this type of report."),

    //cancelRedeem
    VOUCHER_PIN_CODE_ERROR("10086","Either card/voucher number or card/voucher pin is incorrect."),
    TRANSACTION_ID_ERROR("10236","OriginalTransactionId is incorrect."),
    APPROVAL_CODE_ERROR("10238","OriginalApprovalCode is incorrect."),
    INVOICE_NUMBER_ERROR("10195","OriginalInvoiceNumber is not provided."),
    BATCH_NUMBER_ERROR("10240","OriginalBatchNumber is incorrect."),
    VOUCHER_NO_TRANSACTION("10148","No transactions on card/voucher."),
    VOUCHER_CODE_CHECK_FAILED_CHICK("10061","Card/Voucher Number check failed."),
    VOUCHER_CODE_CHECK_FAILED("10004","Either Card Number or Card Pin is Incorrect."),

    //activate only
    VOUCHER_AlREADY_ACTIVATE("10015","Card/Voucher already active."),
    VOUCHER_AlREADY_EXPIRED("10001","Card/Voucher expired."),


    //op user account
    DUPLICATE_ACCOUNT("1101","Duplicate account error"),

    // distribution
    ILLEGAL_DOMAIN_NAME("1201","Subject or rich text contains disallowed domain names."),
    DISTRIBUTION_EMAIL_TEMPLATE_TYPE_NULL("1202","Email template has wrong type."),
    DISTRIBUTION_EMAIL_TEMPLATE_TYPE_REPEAT("1203","Type of email template does not allow duplicates."),

    TRANSACTION_DATE_INTERVAL_NOT_NULL("1301","You need to fill in the transaction date and time interval."),
    TRANSACTION_DATE_INTERVAL_ERROR("1302","The transaction date time interval cannot exceed 180 days."),

    REPORT_EXECUTION_MODEL_VALUE_INCORRECT("1301","The value of the Execution Model enumeration is set incorrectly"),
    NOT_FOUND_EXPORT_REPORT_FILE_SERVICE("1302","Not found ExportReportFileService instance."),
    DISTRIBUTION_RECIPIENT_IS_MALFORMED("1303","Distribution recipient is malformed"),
    DISTRIBUTION_DUPLICATE_RECIPIENT("1304","Recipients are not allowed to duplicate"),
    DISTRIBUTION_OPERATION_BUSY("1305","Distribution operation is busy, please try again later."),
    DISTRIBUTION_INSUFFICIENT_STOCK("1306","Insufficient stock of voucher {}"),
    DISTRIBUTION_CONFIRM_ERROR("1307","Confirm that the distribution failed, the distribution information may have already started distribution"),
    DISTRIBUTION_STATUS_NOT_MATCH("1307","Distribution status does not match, please try again later"),
    DISTRIBUTION_FAILED_POPULATE_EMAIL_TEMPLATE("1308", "Failed to populate email template"),
    OUTLET_CODE_ISSUER("1309", "Outlet code already exists."),

    ORDER_NOT_FOUND("140401", "Order details not found"),
    VOUCHER_NOT_FOUND("140402", "Vouchers not found in order VPG types"),
    VOUCHER_QUANTITY_EXCEEDED("140403", "Number of voucher has reached Max limit"),
    VOUCHER_NOT_SEQUENTIAL("140404", "Voucher codes must be sequential"),
    VOUCHER_ALREADY_ISSUED("140405", "Some vouchers have already been issued"),
    VOUCHER_START_END("140406", "Start value must be less than or equal to end value."),

    ;


    private final String code;

    private final String desc;

    ResultErrorCodeEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}
