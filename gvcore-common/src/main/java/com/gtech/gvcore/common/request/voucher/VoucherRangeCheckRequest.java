package com.gtech.gvcore.common.request.voucher;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigInteger;

@Data
@ApiModel("VoucherRangeCheckRequest")
public class VoucherRangeCheckRequest {
    
    @NotNull(message = "The starting coupon number cannot be empty")
    @ApiModelProperty("startNo")
    private String startNo;
    
    @NotNull(message = "The end coupon number cannot be empty")
    @ApiModelProperty("endNo")
    private String endNo;
    
    @NotNull(message = "The order number cannot be empty")
    @ApiModelProperty("orderCode")
    private String orderCode;



    public void checkStartEnd(){

        this.startNo = startNo.replaceAll("[a-zA-Z]", "");
        this.endNo = endNo.replaceAll("[a-zA-Z]", "");
        BigInteger start = new BigInteger(startNo.replaceAll("[a-zA-Z]", ""));
        BigInteger end = new BigInteger(endNo.replaceAll("[a-zA-Z]", ""));
        // 确保起始值小于等于结束值
        if (start.compareTo(end) > 0) {
            throw new GTechBaseException(ResultErrorCodeEnum.VOUCHER_START_END.code(),ResultErrorCodeEnum.VOUCHER_START_END.desc());
        }
    }
} 