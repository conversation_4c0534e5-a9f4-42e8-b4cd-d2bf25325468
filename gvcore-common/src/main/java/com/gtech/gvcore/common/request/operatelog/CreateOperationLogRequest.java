package com.gtech.gvcore.common.request.operatelog;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-21 17:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateOperationLogRequest")
public class CreateOperationLogRequest {

    @ApiModelProperty(value = "businessCode")
    private String businessCode;

    @ApiModelProperty(value = "input")
    private String input;

    @ApiModelProperty(value = "output")
    private String output;

    @ApiModelProperty(value = "method")
    private String method;

    @ApiModelProperty(value = "operateUser")
    private String operateUser;

    @ApiModelProperty(value = "firstName")
    private String firstName;

    @ApiModelProperty(value = "lastName")
    private String lastName;

    @ApiModelProperty(value = "success", example = "success")
    private String success;

    @ApiModelProperty(value = "remarks")
    private List<GvOperateLogRemarkJson> remarks;
}
