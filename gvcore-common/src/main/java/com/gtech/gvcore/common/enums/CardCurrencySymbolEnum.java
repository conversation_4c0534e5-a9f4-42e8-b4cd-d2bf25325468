package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/7/14 10:29
 */
public enum CardCurrencySymbolEnum {

    VOUCHER_NEWLY_GENERATED("IDR", "Rp"),
    VOUCHER_ACTIVATED("USD", "$");


    private final String code;

    private final String desc;

    CardCurrencySymbolEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }



    public static String getByCode(String code) {
        for(CardCurrencySymbolEnum flowNodeEunm : CardCurrencySymbolEnum.values()) {
            if (flowNodeEunm.code.equals(code) ) {
                return flowNodeEunm.getDesc();
            }
        }
        return null;
    }


}
