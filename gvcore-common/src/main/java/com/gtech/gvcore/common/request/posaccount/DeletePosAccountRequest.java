package com.gtech.gvcore.common.request.posaccount;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "DeletePosAccountRequest")
public class DeletePosAccountRequest {


    @ApiModelProperty(value = "Pos accountCode code.", example = "2123123",required = true)
    @NotEmpty(message = "posAccountCode can not be empty")
    private String posAccountCode;





}
