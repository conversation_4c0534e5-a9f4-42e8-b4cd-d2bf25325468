package com.gtech.gvcore.common.response.voucher;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("券号范围校验响应")
public class VoucherRangeCheckResponse {
    
    @ApiModelProperty("是否存在已发行券号")
    private Boolean hasIssued;
    
    @ApiModelProperty("已发行的券号列表")
    private List<String> issuedVoucherNos;
} 