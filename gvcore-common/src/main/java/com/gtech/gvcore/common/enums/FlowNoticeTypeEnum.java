package com.gtech.gvcore.common.enums;

public enum FlowNoticeTypeEnum {


	SEND_FLAG(0, "currency"), CC_FLAG(1, "cc");

    private final int code;

    private final String desc;

    FlowNoticeTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
