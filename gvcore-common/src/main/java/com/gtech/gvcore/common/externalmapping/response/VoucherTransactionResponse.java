package com.gtech.gvcore.common.externalmapping.response;

import com.gtech.gvcore.common.response.transaction.TransactionResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class VoucherTransactionResponse {


    @ApiModelProperty(value = "Response Code.", required = true, notes = "A zero value indicates successful response and non zero means failure")
    private Integer responseCode;
    @ApiModelProperty(value = "Response Message.", required = true, notes = "Contains the response message or error message in case the transacƟ on has failed (response code is non-zero).")
    private String responseMessage;
    @ApiModelProperty(value = "Transaction Id.", required = true, notes = "The transacƟ on id passed in the request. This transacƟ on id needs to be persisted to be passed as a OriginalTransacƟ onId for cancellaƟ on.")
    private Integer transactionId;
    @ApiModelProperty(value = "Transaction Type Id.", required = true, notes = "The transactionTypeId echoed back.")
    private Integer transactionTypeId;
    @ApiModelProperty(value = "Action Type.", required = true, notes = "action type echoed back.")
    private Integer actionType;
    @ApiModelProperty(value = "Batch Id.", required = true, notes = "The batch id extracted from auth token will be returned. This needs to be persisted to be passed as a OriginalBatchId for cancellaƟ on")
    private Integer batchId;
    @ApiModelProperty(value = "Total Success Card Count.", required = true, notes = "Total success cards count")
    private Integer totalSuccessVoucherCount;
    @ApiModelProperty(value = "Total Card Count.", required = true, notes = "Total cards count involved in this bulk transaction")
    private Integer totalVoucherCount;
    @ApiModelProperty(value = "Transaction Amount.", notes = "Total Success transaction amount")
    private BigDecimal transactionAmount;
    @ApiModelProperty(value = "Validation Token.", notes = "Token representing the validation is done successfully and will be used in subsequent transaction. Applicable when actiontype=validate")
    private String validationToken;
    @ApiModelProperty(value = "Approval Code.", notes = "Qwikcilver generated approval code for activations and redemptions. Not applicable for validations. This will be used for cancellations.")
    private String approvalCode;
    @ApiModelProperty(value = "Reference Number.", notes = "Qwikcilver generated reference number for the transaction")
    private String referenceNumber;
    @ApiModelProperty(value = "Source Id.", notes = "External source id - required for other integration.Echo from the input")
    private String sourceId;
    @ApiModelProperty(value = "Message Id.", notes = "External message id, required for other integration.Echo from the input")
    private String messageId;
    @ApiModelProperty(value = "Order Number.", required = true, notes = "Order number")
    private String orderNumber;

    @ApiModelProperty(value = "Line Items.", required = true, notes = "Array of LineItemResponseInfo, Please see below")
    private List<LineItemResponse> voucherItems;
    @ApiModelProperty(value = "Invalid Line Items.", required = true, notes = "Array of line items. Will contain information about failed cards in each line item. Structure will remain same as LineItemResponseInfo with status as failed.")
    private List<LineItemResponse> rejectedVoucherList;


    public VoucherTransactionResponse setVoucherTransactionResponse(TransactionResponse transactionResponse){
        if(transactionResponse == null){
            return null;
        }
        this.setResponseCode(transactionResponse.getResponseCode());
        this.setResponseMessage(transactionResponse.getResponseMessage());
        this.setTransactionId(transactionResponse.getTransactionId());
        this.setTransactionTypeId(transactionResponse.getTransactionTypeId());
        this.setActionType(transactionResponse.getActionType());
        this.setBatchId(transactionResponse.getBatchId());
        this.setTotalSuccessVoucherCount(transactionResponse.getTotalSuccessCardCount());
        this.setTotalVoucherCount(transactionResponse.getTotalCardCount());
        this.setTransactionAmount(transactionResponse.getTransactionAmount());
        this.setValidationToken(transactionResponse.getValidationToken());
        this.setApprovalCode(transactionResponse.getApprovalCode());
        this.setReferenceNumber(transactionResponse.getReferenceNumber());
        this.setSourceId(transactionResponse.getSourceId());
        this.setMessageId(transactionResponse.getMessageId());
        this.setOrderNumber(transactionResponse.getOrdernumber());
        if (transactionResponse.getLineItems()!=null){
            this.setVoucherItems(transactionResponse.getLineItems().stream().map(lineItemResponseInfo -> {
                LineItemResponse lineItemResponse = new LineItemResponse();
                lineItemResponse.setLineItemResponse(lineItemResponseInfo);
                return lineItemResponse;
            }).collect(java.util.stream.Collectors.toList()));
        }
        if(transactionResponse.getInvalidLineItems()!=null){
            this.setRejectedVoucherList(transactionResponse.getInvalidLineItems().stream().map(lineItemResponseInfo
                    -> new LineItemResponse().setLineItemResponse(lineItemResponseInfo)).collect(java.util.stream.Collectors.toList()));
        }
        return this;
    }






}
