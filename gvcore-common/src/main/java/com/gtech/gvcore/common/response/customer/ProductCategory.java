package com.gtech.gvcore.common.response.customer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/2/24 16:17
 */
@Data
public class ProductCategory {



    @ApiModelProperty(value = "Customer product category name.", example = "12341412")
    private String productCategoryName;

    @ApiModelProperty(value = "Product category code.", example = "11221312")
    private String productCategoryCode;

    @ApiModelProperty(value = "Status", example = "1")
    private Integer status;


}
