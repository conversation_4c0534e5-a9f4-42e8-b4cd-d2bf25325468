package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName queryHistoricalInventoryChartRequest
 * @Description 查询历史库存图表接口参数
 * <AUTHOR>
 * @Date 2022/7/6 10:57
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "QueryHistoricalInventoryChartRequest")
public class QueryHistoricalInventoryChartRequest {

    @ApiModelProperty(value = "CPG编码,为空时查询该customer下全部CPG", example = "CP001")
    private String cpgCode;

    @ApiModelProperty(value = "客户编码", example = "UC001",required = true)
    @NotEmpty(message = "Customer code can not be empty")
    private String customerCode;

}
