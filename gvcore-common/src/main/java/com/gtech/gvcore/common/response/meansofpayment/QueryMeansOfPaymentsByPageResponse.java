package com.gtech.gvcore.common.response.meansofpayment;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月21日
 */
@Data
public class QueryMeansOfPaymentsByPageResponse {

    private Long id;

    private String meansOfPaymentCode;

    private String mopName;

    private String mopGroup;

    private String grp;

    private String remarks;

    private Integer status;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private List<OutletInfo> outletInfoList;

}
