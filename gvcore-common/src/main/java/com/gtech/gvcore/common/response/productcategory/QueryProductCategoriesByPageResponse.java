package com.gtech.gvcore.common.response.productcategory;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询产品分类响应
 */
@Data
public class QueryProductCategoriesByPageResponse {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "productCategoryCode")
    private String productCategoryCode;

    @ApiModelProperty(value = "categoryName")
    private String categoryName;

    @ApiModelProperty(value = "issuerCode")
    private String issuerCode;

    @ApiModelProperty(value = "remarks")
    private String remarks;

    @ApiModelProperty(value = "status")
    private Integer status;

    @ApiModelProperty(value = "createUser")
    private String createUser;

    @ApiModelProperty(value = "createTime")
    private Date createTime;

    @ApiModelProperty(value = "updateUser")
    private String updateUser;

    @ApiModelProperty(value = "updateTime")
    private Date updateTime;

} 