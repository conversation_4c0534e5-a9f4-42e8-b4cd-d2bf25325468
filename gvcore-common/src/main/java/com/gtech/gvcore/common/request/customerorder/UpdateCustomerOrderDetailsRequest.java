package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/20 22:37
 */


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("Update customer order details request")
public class UpdateCustomerOrderDetailsRequest implements Serializable {

    private static final long serialVersionUID = 7177146912556471306L;
    @ApiModelProperty(value = "Customer order detail code", example = "COD102203201832000003")
    private String customerOrderDetailsCode;
    @ApiModelProperty(value = "Card program group code", required = true, example = "630c46f4f871408e8a17af6af09649b5")
    @NotBlank(message = "Card program group code cannot be empty")
    private String cpgCode;
    @ApiModelProperty(value = "Voucher number", required = true)
    @NotNull(message = "Voucher num cannot be empty")
    private Integer voucherNum;
    @ApiModelProperty(value = "Denomination ", required = true)
    @NotNull(message = "Denomination can not be empty")
    private BigDecimal denomination;

}
