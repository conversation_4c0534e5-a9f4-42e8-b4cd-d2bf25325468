package com.gtech.gvcore.common.request.receive;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetVoucherReceiveRequest {


	@ApiModelProperty(value = "Voucher receive code")
	@NotEmpty
	private String voucherReceiveCode;
}