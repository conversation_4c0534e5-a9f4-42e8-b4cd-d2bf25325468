package com.gtech.gvcore.common.request.receive;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import com.gtech.gvcore.common.request.base.PageBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class QueryVoucherReceiveRequest extends PageBean {


    /**
     * status, 0:Processing, 1:Completed
     */
	@ApiModelProperty(value = "Status, 0:Processing, 1:Completed")
	@Max(value = 1)
	@Min(value = 0)
    private Integer status;

	@ApiModelProperty(value = "Issuer code", required = true)
	@NotEmpty(message = "Issuer code can't be empty")
	private String issuerCode;

	@ApiModelProperty(value = "Voucher receive code")
	private String voucherReceiveCode;

	/**
	 * voucher start NO
	 */
	@ApiModelProperty(value = "Voucher start NO")
	private String voucherStartNo;

	/**
	 * voucher end NO
	 */
	@ApiModelProperty(value = "Voucher end NO")
	private String voucherEndNo;

	/**
	 * booklet start NO
	 */
	@ApiModelProperty(value = "Booklet start NO")
	private String bookletStartNo;

	/**
	 * booklet end NO
	 */
	@ApiModelProperty(value = "Booklet end NO")
	private String bookletEndNo;

	@ApiModelProperty(value = "login user code", example = "UC0001",required = true)
	@NotBlank(message = "UserCode is required")
	private String userCode;

	/**
	 * outbound name
	 */
	@ApiModelProperty(value = "Outbound name")
	private String outbound;

	/**
	 * inbound name
	 */
	@ApiModelProperty(value = "Inbound name")
	private String inbound;
	/**
	 * cpg code
	 */
	@ApiModelProperty(value = "Cpg code")
	private String cpgCode;

}