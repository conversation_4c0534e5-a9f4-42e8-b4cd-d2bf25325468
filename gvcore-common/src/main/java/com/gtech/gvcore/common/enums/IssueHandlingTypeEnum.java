package com.gtech.gvcore.common.enums;

public enum IssueHandlingTypeEnum implements IEnum<String> {
	CANCEL_SALES("bulk_cancel_sales", "Cancel sales"),
	CANCEL_REDEEM("bulk_cancel_redeem", "Cancel Redeem"),
	BULK_ACTIVATION("bulk_activation", "Bulk Activation"),
	BULK_REDEEM("bulk_redeem", "Bulk Redeem"),
	BULK_REISSUE("bulk_reissue", "Reissue"),
	BULK_DEACTIVATE("bulk_deactivate", "Deactivate/Block"),
	BULK_REACTIVATE("bulk_reactivate", "Reactivate/Unblock"),
	CHANGE_EXPIRY("bulk_change_expiry", "Change Expiry"),
	RESET_PIN_ACTIVE("bulk_reset_pin_active", "Reset PIN code & activation code"),
	RESET_ACTIVATION("bulk_regenerate_activation_code", "Regenerate Activation Code");

    private final String code;

    private final String desc;

    IssueHandlingTypeEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    public static IssueHandlingTypeEnum valueOfCode(final String issueType) {

        for (IssueHandlingTypeEnum value : IssueHandlingTypeEnum.values()) {
            if (value.equalsCode(issueType)) {
                return value;
            }
        }

        return null;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}
