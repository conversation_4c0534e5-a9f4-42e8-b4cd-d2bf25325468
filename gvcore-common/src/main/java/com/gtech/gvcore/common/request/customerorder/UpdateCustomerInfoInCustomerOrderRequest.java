package com.gtech.gvcore.common.request.customerorder;

import com.gtech.basic.idm.common.exceptions.ErrorCodes;
import com.gtech.commons.utils.CheckUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/10 14:19
 */

@Data
@ApiModel(value = "UpdateCustomerInfoInCustomerOrder")
@NoArgsConstructor
public class UpdateCustomerInfoInCustomerOrderRequest implements Serializable {

	private static final long serialVersionUID = -7405142897774255788L;

	public static final String CORPORATE = "Corporate";
	public static final String INDIVIDUAL = "Individual";

	@ApiModelProperty(value = "Customer order code", required = true)
	@NotBlank(message = "customerOrderCode can not be empty")
	private String customerOrderCode;

	@ApiModelProperty(value = "Customer code", required = true)
	@NotBlank(message = "customer code can not be empty")
	private String customerCode;

	@ApiModelProperty(value = "Customer name", required = true)
	@NotBlank(message = "customerName can not be empty")
	private String customerName;

	@ApiModelProperty(value = "Customer type", required = true)
	@NotBlank(message = "customerType can not be empty")
	private String customerType;

	@ApiModelProperty(value = "Company name")
	private String companyName;

	@ApiModelProperty(value = "Contact fist name")
	private String contactFirstName;

	@ApiModelProperty(value = "Contact last name")
	private String contactLastName;

	@ApiModelProperty(value = "Contact phone", required = true)
	@NotBlank(message = "contactPhone can not be empty")
	private String contactPhone;

	@ApiModelProperty(value = "Contact email", required = true)
	@NotBlank(message = "contactEmail can not be empty")
	private String contactEmail;

	@ApiModelProperty(value = "Update user code", required = true)
	@NotBlank(message = "updateUser can not be empty")
	private String updateUser;


	public void validation(){

		if (customerType.equals(CORPORATE)){

			CheckUtils.isNotBlank(this.contactFirstName, ErrorCodes.PARAM_EMPTY, "contactFirstName");
			CheckUtils.isNotBlank(this.contactLastName, ErrorCodes.PARAM_EMPTY, "contactLastName");

		}



	}



}
