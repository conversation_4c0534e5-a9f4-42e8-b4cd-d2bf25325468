package com.gtech.gvcore.common.request.orderreport;

import java.util.Date;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName QueryOrderReportDataRequest
 * @Description 查询报表结果入参
 * <AUTHOR>
 * @Date 2022/8/23 14:54
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "GetReportSystemLoggerRequest")
public class GetReportSystemLoggerRequest {

    @NotNull(message = "id is required")
    @ApiModelProperty(value = "id", required = true)
    private Integer id;

	@NotNull(message = "operationTime of logger is required")
	@ApiModelProperty(value = "operationTime", required = true)
	private Date operationTime;

}
