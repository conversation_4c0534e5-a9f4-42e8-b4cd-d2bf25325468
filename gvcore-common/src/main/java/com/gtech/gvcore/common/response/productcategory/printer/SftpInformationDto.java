package com.gtech.gvcore.common.response.productcategory.printer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SftpInformationDto {

    /**
     * authorization type
     */
    private String ftpAuthorizationType;

    /**
     * FTP Url
     */
    private String ftpUrl;

    /**
     * FTP username
     */
    private String ftpUsername;

    /**
     * FTP password
     */
    private String ftpPassword;

    /**
     * FTP passphrase
     */
    private String passphrase;

    /**
     * ftpKeyFile
     */
    private byte[] ftpKeyFile;
}