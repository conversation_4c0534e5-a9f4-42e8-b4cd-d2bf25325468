package com.gtech.gvcore.common.request.customerorder;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年3月23日
 */
@Data
public class ReleaseRequest {
	
	@ApiModelProperty(value = "customerOrderCode", required = true)
    @NotEmpty(message = "customerOrderCode can not be empty")
	private String customerOrderCode;
	
	@ApiModelProperty(value = "Status:  true:agree  false:reject", required = true)
    @NotNull(message = "status cannot be null")
    private Boolean status;
	
	@ApiModelProperty(value = "Notes", required = false)
	@Length(max = 500)
	private String notes;
	
	@ApiModelProperty(value = "updateUser", required = true)
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    private String updateUser;

}
