package com.gtech.gvcore.common.request.voucher;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/2 10:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateVoucherRequest")
public class CreateVoucherRequest implements Serializable {


    private static final long serialVersionUID = 53157181676373018L;


    private String issuerCode;

    private String voucherBatchCode;

    private String bookletCode;

    private Long bookletCodeNum;

    private String voucherCode;

    private Long voucherCodeNum;

    private String cpgCode;

    private String mopCode;

    private BigDecimal denomination;

    private String voucherPin;

    private String voucherBarcode;

    private Date voucherEffectiveDate;

    private Integer status;

    private Integer voucherStatus;

    private Integer circulationStatus;

    private String voucherActiveCode;

    private String voucherActiveUrl;

    private Date voucherUsedTime;

    private String permissionCode;

    private Date createTime;

    private Date updateTime;

    private String createUser;

    private String updateUser;


    private String logType;

    private String voucherOwnerCode;

    private String voucherOwnerType;
    private String salesOutlet;
    private Date salesTime;





    public void reset() {
        this.issuerCode = null;
        this.voucherBatchCode = null;
        this.bookletCode = null;
        this.bookletCodeNum = null;
        this.voucherCode = null;
        this.voucherCodeNum = null;
        this.cpgCode = null;
        this.mopCode = null;
        this.denomination = null;
        this.voucherPin = null;
        this.voucherBarcode = null;
        this.voucherEffectiveDate = null;
        this.status = null;
        this.voucherStatus = null;
        this.circulationStatus = null;
        this.voucherActiveCode = null;
        this.voucherActiveUrl = null;
        this.voucherUsedTime = null;
        this.permissionCode = null;
        this.createTime = null;
        this.updateTime = null;
        this.createUser = null;
        this.updateUser = null;
        this.logType = null;
        this.voucherOwnerCode = null;
        this.voucherOwnerType = null;
    }







}
