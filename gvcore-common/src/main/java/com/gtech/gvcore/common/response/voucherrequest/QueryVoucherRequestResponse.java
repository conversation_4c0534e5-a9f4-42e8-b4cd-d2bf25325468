package com.gtech.gvcore.common.response.voucherrequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/10 11:12
 */


@Data
@ApiModel("Query request voucher list")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryVoucherRequestResponse implements Serializable {

    private static final long serialVersionUID = -1009503465613612946L;
    @ApiModelProperty(value = "Request #")
    private String voucherRequestCode;
    @ApiModelProperty(value = "Request Source")
    private String requestSource;
    @ApiModelProperty(value = "Denomination")
    private String denomination;
    @ApiModelProperty(value = "CpgCode")
    private String cpgCode;
    @ApiModelProperty(value = "CpgName")
    private String cpgName;
    @ApiModelProperty(value = "Number of Vouchers")
    private Integer numberOfVouchers;
    @ApiModelProperty(value = "Voucher Amount")
    private BigDecimal voucherAmount;
    @ApiModelProperty(value = "Created on")
    private Date createdOn;
    @ApiModelProperty(value = "Created by")
    private String createdBy;
    @ApiModelProperty(value = "request status (" +
            "1:Pending Approval,2:Canceled,3:Rejected,4:Pending Allocation,5:Pending Receipt,6:Completed" +
            ")")
    private Integer status;

    private String businessType;
    private String showStatus;

	private String issuerCode;

	private String outletCode;
    @ApiModelProperty(value = "Business outlet code")
	private String businessOutletCode;

    @ApiModelProperty(value = "Can I approve it?")
    private Boolean approveAble;

}
