package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName QueryCpgInventoryDetailRequest
 * @Description 查询VPG库存信息参数
 * <AUTHOR>
 * @Date 2022/7/6 10:49
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "QueryCpgInventoryDetailRequest")
public class QueryCpgInventoryDetailRequest {

    @ApiModelProperty(value = "CPG编码", example = "CP001")
    private String cpgCode;

    @ApiModelProperty(value = "客户编码", example = "UC001",required = true)
    @NotEmpty(message = "Customer code can not be empty")
    private String customerCode;

}
