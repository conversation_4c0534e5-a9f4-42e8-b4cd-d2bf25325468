package com.gtech.gvcore.common.response.dashboard;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2022/9/5 14:01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SalesBarChartResponse")
public class SalesBarChartResponse {

    private String type;

    private BigDecimal gross;
    private String grossGrowthRate;

    private BigDecimal discount;
    private String discountGrowthRate;

    private BigDecimal net;
    private String netGrowthRate;

}
