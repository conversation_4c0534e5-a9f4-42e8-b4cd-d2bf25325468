package com.gtech.gvcore.common.request.productcategory;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
@Data
public class ProductCategoryCpgParamter {

    @ApiModelProperty(value = "cpgCode", required = true)
    @NotEmpty(message = "cpgCode can not be empty")
    @Length(max = 50)
    private String cpgCode;

    @ApiModelProperty(value = "status", required = true)
    @NotNull(message = "status can not be null")
    @Range(min = 0, max = 1)
    private Integer status;

    @ApiModelProperty(value = "categoryType", required = false, example = "voucher", notes = "分类类型，可能的值：voucher, gc")
    @Length(max = 20)
    private String categoryType;

}
