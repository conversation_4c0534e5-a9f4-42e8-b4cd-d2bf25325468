package com.gtech.gvcore.common.response.voucherbatch;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/2 10:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateVoucherBatchResponse")
public class VoucherBatchResponse implements Serializable {
    private static final long serialVersionUID = 4755873450986619350L;

    @ApiModelProperty(value = "Purchase order no.", example = "1122333" )
    @NotEmpty(message = "purchaseOrderNo can not be empty")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "Issuer code.", example = "1122333" )
    private String issuerCode;

    @ApiModelProperty(value = "Issuer name.", example = "user1" )
    private String issuerName;

    @ApiModelProperty(value = "Cpg name.", example = "cpgName" )
    private String cpgName;

    @ApiModelProperty(value = "Cpg code.", example = "1122333" )
    private String cpgCode;

    /**
     * lot number
     */
    @ApiModelProperty(value = "Voucher batch code.", example = "1122333" )
    private String voucherBatchCode;

    @ApiModelProperty(value = "Article code.", example = "1122333" )
    private String articleCode;

    @ApiModelProperty(value = "Article code name.", example = "1122333" )
    private String articleCodeName;

    @ApiModelProperty(value = "Article name.", example = "article name" )
    private String articleName;

    @ApiModelProperty(value = "Mop code.", example = "1122333" )
    private String mopCode;

    @ApiModelProperty(value = "Mop name.", example = "mop name" )
    private String mopName;

    @ApiModelProperty(value = "Printer code.", example = "1122333" )
    private String printerCode;

    @ApiModelProperty(value = "Printer name.", example = "Print name" )
    private String printerName;

    @ApiModelProperty(value = "Booklet start code.", example = "9001220000000001" )
    private String bookletStartNo;

    @ApiModelProperty(value = "Booklet end code.", example = "9001220000000100" )
    private String bookletEndNo;

    /**
     *  number of vouchers per booklet
     */
    @ApiModelProperty(value = "Booklet per code.", example = "100" )
    private Integer bookletPerNum;

    /**
     * number of booklets
     */
    @ApiModelProperty(value = "Booklet num.", example = "3" )
    private Integer bookletNum;

    @ApiModelProperty(value = "Voucher start code.", example = "1002222220000001" )
    private String voucherStartNo;

    @ApiModelProperty(value = "Voucher end code.", example = "1002222220003000" )
    private String voucherEndNo;

    /**
     * number of voucher
     */
    @ApiModelProperty(value = "Voucher num.", example = "100" )
    private Integer voucherNum;

	@ApiModelProperty(value = "Denomination.", example = "50000")
    private BigDecimal denomination;

    @ApiModelProperty(value = "Voucher effective date.", example = "2022-02-17 16:27" )
    private Date voucherEffectiveDate;

    @ApiModelProperty(value = "File name.", example = "MAP-MAPQCGV50K-022-122-001" )
    private String fileName;

    @ApiModelProperty(value = "File format.", example = "Excel" )
    private String fileFormat;

    @ApiModelProperty(value = "Permission code.", example = "1122333")
    private String permissionCode;

    @ApiModelProperty(value = "Voucher num active.", example = "1122333")
    private Integer voucherNumActive;

    @ApiModelProperty(value = "Voucher num used.", example = "1122333")
    private Integer voucherNumUsed;

    @ApiModelProperty(value = "Real time progress.", example = "123")
    private Integer realTimeProgress;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty(value = "Create user.", example = "user1")
    private String createUser;

    @ApiModelProperty(value = "Create time.", example = "2022-02-17 16:27")
    private Date createTime;
    
    @ApiModelProperty(value = "Update user.", example = "user1")
    private String updateUser;

    @ApiModelProperty(value = "Update time.", example = "2022-02-17 16:27")
    private Date updateTime;

    
    
    
    














}
