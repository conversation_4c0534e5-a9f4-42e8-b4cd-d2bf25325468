package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @date 2022年3月22日
 */
public enum CpgEffectiveTypeEnum implements IEnum<String> {
    YEAR("year", "year"), HOUR("hour", "hour");

    private final String code;

    private final String desc;

    CpgEffectiveTypeEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}
