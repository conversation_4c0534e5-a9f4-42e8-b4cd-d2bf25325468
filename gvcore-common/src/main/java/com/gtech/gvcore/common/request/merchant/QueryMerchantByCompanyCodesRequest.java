package com.gtech.gvcore.common.request.merchant;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/28 13:47
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryMerchantByCompanyCodesRequest")
public class QueryMerchantByCompanyCodesRequest extends PageBean {



    @ApiModelProperty( value = "Company codes.", example = "1345566",required = true)
    @NotEmpty(message = "companyCodes can not be empty")
    private List<String> companyCodes;

}
