package com.gtech.gvcore.common.externalmapping.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.HashMap;
import java.util.Map;

@Data
public class BulkProcessCustomerInfo {


    @ApiModelProperty(value = "Salutation. ", example = "123456", notes = "Salutation")
    @Length(max = 20, message = "Salutation maximum length 20")
    private String customerName;
    @ApiModelProperty(value = "First Name. ", required = true, example = "123456", notes = "First name of the customer")
    @Length(max = 50, message = "First Name maximum length 50")
    private String firstName;
    @ApiModelProperty(value = "Last Name. ", required = true, example = "123456", notes = "Last name of the customer")
    @Length(max = 50, message = "Last Name maximum length 50")
    private String lastName;
    @ApiModelProperty(value = "Mobile. ", example = "123456", notes = "Mobile number")
    @Length(max = 50, message = "Mobile maximum length 50")
    private String mobile;
    @ApiModelProperty(value = "Address Line 1. ", example = "123456", notes = "Address Line 1")
    @Length(max = 250, message = "Address Line 1 maximum length 250")
    private String address1;
    @ApiModelProperty(value = "Address Line 2. ", example = "123456", notes = "Address Line 2")
    @Length(max = 250, message = "Address Line 2 maximum length 250")
    private String address2;
    @ApiModelProperty(value = "Address Line 3. ", example = "123456", notes = "Address Line 3")
    @Length(max = 250, message = "Address Line 3 maximum length 250")
    private String address3;
    @ApiModelProperty(value = "Date Of Birth. ", example = "2022-02-02", notes = "YYYY-MM-DD")
    private String birthdayDate;
    @ApiModelProperty(value = "Email. ", example = "123456", notes = "email id of the customer")
    @Length(max = 50, message = "Email maximum length 50")
    private String email;
    @ApiModelProperty(value = "City. ", example = "123456", notes = "city in which customer resides")
    @Length(max = 50, message = "City maximum length 50")
    private String city;
    @ApiModelProperty(value = "State. ", example = "123456", notes = "state in which customer resides")
    @Length(max = 50, message = "State maximum length 50")
    private String state;
    @ApiModelProperty(value = "Country. ", example = "123456", notes = "country in which customer resides")
    @Length(max = 50, message = "Country maximum length 50")
    private String country;
    @ApiModelProperty(value = "Empid. ", example = "123456", notes = "employee id")
    @Length(max = 50, message = "Empid maximum length 50")
    private String employeeId;
    @ApiModelProperty(value = "Corporate Name. ", example = "123456", notes = "corporate name")
    @Length(max = 50, message = "Corporate Name maximum length 50")
    private String corporateName;




    public Map<String,Object> toMap(){

        Map<String, Object> map = new HashMap<>();
        map.put("salutation", this.customerName);
        map.put("firstName", this.firstName);
        map.put("lastName", this.lastName);
        map.put("mobile", this.mobile);
        map.put("address1", this.address1);
        map.put("address2", this.address2);
        map.put("address3", this.address3);
        map.put("dateOfBirth", this.birthdayDate);
        map.put("email", this.email);
        map.put("city", this.city);
        map.put("state", this.state);
        map.put("country", this.country);
        map.put("empid", this.employeeId);
        map.put("corporatename", this.corporateName);
//        map.put("customerType", this.customerType);
        return map;
    }

    public BulkProcessCustomerInfo toCustomerInfo(com.gtech.gvcore.common.request.transaction.CustomerInfo customerInfo){

        this.setCustomerName(customerInfo.getSalutation());
        this.setFirstName(customerInfo.getFirstName());
        this.setLastName(customerInfo.getLastName());
        this.setMobile(customerInfo.getMobile());
        this.setAddress1(customerInfo.getAddress1());
        this.setAddress2(customerInfo.getAddress2());
        this.setAddress3(customerInfo.getAddress3());
        this.setBirthdayDate(customerInfo.getDateOfBirth());
        this.setEmail(customerInfo.getEmail());
        this.setCity(customerInfo.getCity());
        this.setState(customerInfo.getState());
        this.setCountry(customerInfo.getCountry());
        this.setEmployeeId(customerInfo.getEmpid());
        this.setCorporateName(customerInfo.getCorporatename());
//        this.setCustomerType(customerInfo.getCustomerType());
        return this;
    }
}
