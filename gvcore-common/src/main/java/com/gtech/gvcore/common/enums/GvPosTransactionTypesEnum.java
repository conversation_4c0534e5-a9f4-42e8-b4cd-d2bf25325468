package com.gtech.gvcore.common.enums;

/**
 **/
public enum GvPosTransactionTypesEnum {

	BULK_REDEEM(702, "BULK REDEEM"),
	BULK_ACTIVATE(705, "BULK ACTIVATE"),

	PARAM_DOWNLOAD(19, "PARAM DOWNLOAD (AUTHORIZE)"),
	GIFT_CARD_ACTIVATE_AND_ISSUE(5, "GIFT CARD ACTIVATE/CREATE AND ISSUE"),
	GIFT_CARD_RELOAD(3, "GIFT CARD RELOAD"),
	GIFT_CARD_REDEEM(2, "GIFT CARD REDEEM/PREAUTH"),
	GIFT_CARD_ACTIVATE_ONLY(22, "GIFT CARD ACTIVATE ONLY"),
	CARD_TRANSACTION_HISTORY(35, "CARD TRANSACTION HISTORY"),
	GIFT_CARD_CANCEL_RELOAD(13, "GIFT CARD CANCEL RELOAD"),
	GIFT_CARD_CANCEL_REDEEM(12, "GIFT CARD CANCEL REDEEM"),
	GIFT_CARD_CANCEL_ACTIVATE(28, "GIFT CARD CANCEL ACTIVATE"),
	BATCH_CLOSE(20, "BATCH CLOSE"),
	RESETCARDPIN(30, "RESETCARDPIN"),
	REACTIVATE(31, "REACTIVATE"),
	CHECK_SERVER_HEALTH(200, "CHECK SERVER HEALTH"),
	DEACTIVATE(4, "DEACTIVATE"),
	MULTIPLE_REDEEM(302, "MULTIPLE REDEEM"),
	TRANSACTIONS_REPORT_FROM_BATCH_NUMBER(150, "TRANSACTIONS REPORT FROM BATCH NUMBER"),
	TRANSACTIONS_REPORT_FROM_DATE_RANGE(150, "TRANSACTIONS REPORT FROM DATE RANGE"),
	UPDATE_EXPIRY(32, "UPDATE EXPIRY"),
	REVERSE_RELOAD(103, "REVERSE RELOAD"),
	REVERSE_REDEEM(102, "REVERSE REDEEM"),
	REVERSE_ANCEL_REDEMPTION(112, "REVERSE CANCEL REDEMPTION"),
	REVERSE_ANCEL_RELOAD(113, "REVERSE CANCEL RELOAD"),
	REVERSE_ACTIVATE(105, "REVERSE ACTIVATE"),
	REVERSE_CANCEL_ACTIVATE(128, "REVERSE CANCEL ACTIVATE"),
	MULTIPLE_BALANCE_ENQUIRY(306, "MULTIPLE BALANCE ENQUIRY"),
	OUTLET_ADMIN(800, "OUTLET_ADMIN"),
	GET_CUSTOMER_INFO(129, "GET CUSTOMER INFO"),
	
	;

    private final int code;

    private final String desc;

    GvPosTransactionTypesEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
