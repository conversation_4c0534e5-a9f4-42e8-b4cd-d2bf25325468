package com.gtech.gvcore.common.request.cancelredeem;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CancelRedeemRequest {

    private String terminalId;

    private Integer transactionId;
    private String cardNumber;
    private String voucherPin;
    private BigDecimal amount;
    private String originalApprovalCode;
    private String originalInvoiceNumber;
    private String originalBatchNumber;
    private Integer originalTransactionId;
    private String notes;
    private Date dateAtClient;

    private String batchID;




}
