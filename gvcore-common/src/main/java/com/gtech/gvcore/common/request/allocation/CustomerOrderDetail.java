package com.gtech.gvcore.common.request.allocation;

import java.math.BigDecimal;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年3月21日
 */
@Data
public class CustomerOrderDetail {

    @ApiModelProperty(value = "cpgCode", required = true)
    @NotEmpty(message = "cpgCode can not be empty")
    private String cpgCode;

    @ApiModelProperty(value = "voucherStartNo", required = true)
    @NotEmpty(message = "voucherStartNo can not be empty")
    private String voucherStartNo;

    @ApiModelProperty(value = "voucherEndNo", required = true)
    @NotEmpty(message = "voucherEndNo can not be empty")
    private String voucherEndNo;

    @ApiModelProperty(value = "denomination", required = true)
    @NotNull(message = "denomination can not be null")
    private BigDecimal denomination;

}
