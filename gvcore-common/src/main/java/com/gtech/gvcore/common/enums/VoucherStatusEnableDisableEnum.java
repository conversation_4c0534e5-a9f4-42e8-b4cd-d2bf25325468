package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @Date 2022/3/11 11:44
 */
public enum VoucherStatusEnableDisableEnum {



    STATUS_DISABLE(0, "DISABLE"),
    STATUS_ENABLE(1, "ENABLE"),
    STATUS_DESTROY(2, "DESTROY"),
    ;

    private final Integer code;

    private final String desc;

    VoucherStatusEnableDisableEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public boolean equalsCode(Integer code) {

        return null != code && code.equals(this.code);
    }

    public static VoucherStatusEnableDisableEnum valueOfCode (Integer code) {
        for(VoucherStatusEnableDisableEnum flowNodeEnum : VoucherStatusEnableDisableEnum.values()) {
            if (flowNodeEnum.getCode().equals(code) ) {
                return flowNodeEnum;
            }
        }
        return null;
    }

    public static String getByCode(Integer code) {
        for(VoucherStatusEnableDisableEnum flowNodeEunm : VoucherStatusEnableDisableEnum.values()) {
            if (flowNodeEunm.getCode().equals(code)) {
                return flowNodeEunm.getDesc();
            }
        }
        return null;
    }

}
