package com.gtech.gvcore.common.request.transaction;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CardtransactionhistoryRequest {

	@ApiModelProperty(value = "Transaction ID. ", example = "00001", required = true, notes = "An integer value. This value has to be incremented with every transaction within the batch. For e.g, after the first time init is done, when doing a balance enquiry for the first time, the value for this can be 1, for the second transaction, this should be 2 and so on. Basically, a unique incrementing sequence within the current batch")
	private Integer transactionId;
	@ApiModelProperty(value = "Card Number. ", required = true, example = "123456", notes = "Mandatory in case of Physical card")
	@Length(max = 50, message = "Card Number maximum length 50")
	private String cardNumber;
	@ApiModelProperty(value = "Card Pin. ", example = "123456", notes = "PIN associated with the card. Entered by the cardholder/Operator(optional)")
	@Length(max = 25, message = "Card Pin maximum length 25")
	private String cardPIN;
	@ApiModelProperty(value = "Transactions Report Search Begin Date. ", example = "0", notes = "Beginning date for pulling out  Transactions History details(MMDDYYYY)")
	private String transactionsReportSearchBeginDate;
	@ApiModelProperty(value = "Transactions Report Search End Date. ", example = "0", notes = "Ending date for pulling out  Transactions History details(MMDDYYYY)")
	private String transactionsReportSearchEndDate;
	@ApiModelProperty(value = "No Of Transactions. ", example = "0", notes = "Count of transactions to be fetched in the response")
	private Integer noOfTransactions;
	@ApiModelProperty(value = "Transaction Type Id To Filter. ", example = "0",  notes = "Type of transaction which will appear in the response of Card Transaction History. E.g. Redeem")
	private Integer transactionTypeIdToFilter;
	@ApiModelProperty(value = "Start Index. ", example = "0",  notes = "For pulling out pagination based transaction history")
	private Integer startIndex;
	@ApiModelProperty(value = "Date At Client. ", example = "2022-02-02 18:00:00", required = true, notes = "The datetime at the client machine in YYYY-MM-DD HH:MM:SS")
	private String dateAtClient;

	private String terminalId;

}
