package com.gtech.gvcore.common.request.productcategory;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
@Data
public class CreateOrUpdateProductCategoryCpgRequest {

    @ApiModelProperty(value = "productCategoryCode", required = true)
    @NotEmpty(message = "productCategoryCode can not be empty")
    @Length(max = 50)
    private String productCategoryCode;

    @ApiModelProperty(value = "cpgList", required = true)
    @NotNull(message = "cpgList can not be null")
    @Valid
    private List<ProductCategoryCpgParamter> cpgList;

    @ApiModelProperty(value = "createUser", required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 50)
    private String createUser;

}
