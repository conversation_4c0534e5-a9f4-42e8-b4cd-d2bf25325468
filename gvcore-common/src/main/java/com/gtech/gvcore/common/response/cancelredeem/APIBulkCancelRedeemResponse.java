package com.gtech.gvcore.common.response.cancelredeem;

import com.gtech.gvcore.common.response.transaction.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class APIBulkCancelRedeemResponse extends BaseResponse {

    @ApiModelProperty(value = "Acquirer Id.", notes = "The acquirer of the transaction.")
    private String acquirerId;
    @ApiModelProperty(value = "Terminal Id.", notes = "The terminal ID of the POS which is doing the request")
    private String terminalId;
    @ApiModelProperty(value = "Response Code.", notes = "A 5 digit integer response code. A zero value indicates success and a non-zero value indicates failure.")
    private Integer responseCode;
    @ApiModelProperty(value = "Response Message.", notes = "Contains the response message or error message in case the transacƟ on has failed (response code is non-zero).")
    private String responseMessage;
    @ApiModelProperty(value = "Employee Id.", notes = "The employee Id associated with the card. Can be ignored for this integration.")
    private String employeeId;

    private List<VoucherInfo> voucherInfos;

    @Data
    public static class VoucherInfo{

        private String voucherNumber;
        private String cardStatus;
        private Integer cardStatusId;
        private BigDecimal amount;
        private BigDecimal previousBalance;
        private String cardCreationType;

    }


}
