package com.gtech.gvcore.common.request.releaseapprove;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 15:11
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("Release approve amount model")
public class ReleaseApproveAmountRequest {

	@NotBlank(message = "issuer code")
	@ApiModelProperty(value = "issuerCode", required = true)
	private String issuerCode;

    @NotBlank(message = "type can not be empty")
    @ApiModelProperty(value = "type (ReleaseApproveConfig/ReturnApproveConfig)", required = true, example = "ReleaseApproveConfig")
    private String type;

    @NotNull(message = "rangeName can not be null")
    @Min(1)
    @ApiModelProperty(value = "Range name (NO.)", required = true, example = "1")
    private Integer rangeName;

    @NotNull(message = "startNum can not be null")
    @ApiModelProperty(value = "Start num", required = true, example = "1")
    private BigDecimal startNum;

    @NotNull(message = "endNum can not be null")
    @ApiModelProperty(value = "End num", required = true, example = "200")
    private BigDecimal endNum;

    @ApiModelProperty(hidden = true)
    private String createUser;

    @Valid
    @NotEmpty
    @Size(min = 1)
    private List<ReleaseApproveNodeRequest> releaseApproveNodeRequests;
}
