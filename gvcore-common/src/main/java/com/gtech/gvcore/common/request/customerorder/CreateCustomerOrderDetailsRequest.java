package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/19 17:09
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("Create customer order details request")
public class CreateCustomerOrderDetailsRequest implements Serializable {

    private static final long serialVersionUID = 1932823806520818247L;
    @ApiModelProperty(value = "Card program group code", example = "630c46f4f871408e8a17af6af09649b5")
    private String cpgCode;
    @ApiModelProperty(value = "Voucher number", required = true)
    @NotNull(message = "Voucher num cannot be empty")
    private Integer voucherNum;
    @ApiModelProperty(value = "Denomination ", required = true)
    @NotNull(message = "Denomination can not be empty")
    private BigDecimal denomination;

    private Integer deleteStatus;

}
