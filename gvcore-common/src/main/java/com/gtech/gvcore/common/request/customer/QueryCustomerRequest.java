package com.gtech.gvcore.common.request.customer;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCustomerRequest")
public class QueryCustomerRequest extends PageBean {

    @ApiModelProperty(value = "Customer code.", example = "11222334")
    @Length(max = 100)
    private String customerCode;

    @ApiModelProperty(value = "Issuer code.", example = "11222334")
    @Length(max = 100)
    private String issuerCode;

    @ApiModelProperty(value = "Customer name.", example = "user1")
    @Length(max = 100)
    private String customerName;


    @ApiModelProperty(value = "Company Name and Individual Name.", example = "user1")
    @Length(max = 100)
    private String name;


    @ApiModelProperty(value = "Customer type.", example = "1")
    @Length(max = 10)
    private String customerType;

    @ApiModelProperty( value="Status.", example="1")
    @Length(max = 10)
    private String status;

    @ApiModelProperty(value = "Distribution Function.", example = "0")
    @Length(max = 10)
    private String distributionFunction;

    @ApiModelProperty(value = "User email",example = "<EMAIL>;<EMAIL>")
    @Length(max = 100)
    private String userEmail;

    @ApiModelProperty(value = "Contact division.", example = "user1")
    @Length(max = 100)
    private String contactDivision;

}
