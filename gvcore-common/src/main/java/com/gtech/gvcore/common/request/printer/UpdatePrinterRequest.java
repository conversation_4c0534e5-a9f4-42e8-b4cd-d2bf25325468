package com.gtech.gvcore.common.request.printer;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-21 17:53
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreatePrinterRequest")
public class UpdatePrinterRequest {

    /**
     * printerCode
     */
    @NotEmpty(message = "printerCode can not be empty")
    @ApiModelProperty(value = "printerCode", example = "CPC102202241512000241", required = true)
    private String printerCode;


    /**
     * outlet name
     */
    @NotEmpty(message = "printerName can not be empty")
    @Length(max = 500)
    @ApiModelProperty(value = "Printer Name", example = "Printer Gv",required = true)
    private String printerName;

    /**
     * stateCode
     */
    @Length(max = 50)
    @NotEmpty(message = "stateCode can not be empty")
    @ApiModelProperty(value = "stateCode", example = "xxx",required = true)
    private String stateCode;

    /**
     * cityCode
     */
    @Length(max = 50)
    @NotEmpty(message = "cityCode can not be empty")
    @ApiModelProperty(value = "cityCode", example = "xxx",required = true)
    private String cityCode;

    /**
     * districtCode
     */
    @Length(max = 50)
    @NotEmpty(message = "districtCode can not be empty")
    @ApiModelProperty(value = "districtCode", example = "xxx",required = true)
    private String districtCode;

    /**
     * address
     */
    @Length(max = 2000)
    @NotEmpty(message = "address can not be empty")
    @ApiModelProperty(value = "address", example = "xxx",required = true)
    private String address;

    /**
     * longitude
     */
    @Length(max = 50)
    @ApiModelProperty(value = "longitude", example = "xxx")
    private String longitude;

    /**
     * latitude
     */
    @Length(max = 50)
    @ApiModelProperty(value = "latitude", example = "xxx")
    private String latitude;

    /**
     * printer first name
     */
    @NotEmpty(message = "firstName can not be empty")
    @Length(max = 50)
    @ApiModelProperty(value = "firstName", example = "firstName",required = true)
    private String firstName;

    /**
     * printer last name
     */
    @NotEmpty(message = "lastName can not be empty")
    @Length(max = 50)
    @ApiModelProperty(value = "lastName", example = "lastName",required = true)
    private String lastName;

    /**
     * printer mobile
     */
    @NotEmpty(message = "mobile can not be empty")
    @Length(max = 50)
    @ApiModelProperty(value = "mobile", example = "+62-811109949",required = true)
    private String mobile;

    /**
     * printer email
     */
    @NotEmpty(message = "email can not be empty")
    @Length(max = 500)
    //@Email(message = "The mailbox format is incorrect")
    @ApiModelProperty(value = "email", example = "<EMAIL>",required = true)
    private String email;


    @ApiModelProperty(value = "Receiving Method", example = "0")
    private String receivingMethod;

    /**
     * authorization type
     */
    @Length(max = 50)
    @ApiModelProperty(value = "ftpAuthorizationType", example = "xxx",required = true)
    private String ftpAuthorizationType;

    /**
     * FTP Url
     */
    @Length(max = 1000)
    @ApiModelProperty(value = "ftpUrl", example = "ftp://192.168.1.1",required = true)
    private String ftpUrl;

    /**
     * FTP username
     */
    @Length(max = 1000)
    @ApiModelProperty(value = "ftpUsername", example = "ftp",required = true)
    private String ftpUsername;

    /**
     * FTP password
     */
    @Length(max = 1000)
    @ApiModelProperty(value = "ftpUsername", example = "password")
    private String ftpPassword;

    /**
     * ftpKeyFileUrl
     */
    @ApiModelProperty(value = "ftpKeyFileUrl", example = "https://static.gtech.asia/static/template/id_rsa")
    private String ftpKeyFileUrl;

    /**
     * updateUser
     */
    @NotEmpty(message = "updateUser can not be empty")
    @Length(max = 50)
    @ApiModelProperty(value = "updateUser", example = "gv", required = true)
    private String updateUser;
}
