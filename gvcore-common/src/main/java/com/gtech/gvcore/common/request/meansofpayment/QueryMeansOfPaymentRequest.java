package com.gtech.gvcore.common.request.meansofpayment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 查询支付方式请求
 */
@Data
@ApiModel(value = "QueryMeansOfPaymentRequest", description = "查询支付方式请求")
public class QueryMeansOfPaymentRequest {
    /**
     * 支付方式编码
     */
    @ApiModelProperty(value = "支付方式编码")
    @Size(max = 50)
    private String mopCode;

    /**
     * 支付方式名称
     */
    @ApiModelProperty(value = "支付方式名称")
    @Size(max = 100)
    private String mopName;

    /**
     * 支付方式组编码
     */
    @ApiModelProperty(value = "支付方式组编码")
    @Size(max = 50)
    private String mopGroupCode;

    /**
     * 门店编码
     */
    @ApiModelProperty(value = "门店编码")
    @Size(max = 50)
    private String outletCode;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码", required = true)
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", required = true)
    private Integer pageSize = 10;
} 