package com.gtech.gvcore.common.response.orderreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/7 0:56
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "QueryOrderReportResponse", description = "Query order report result list")
public class QueryReportReqResponse implements Serializable {
    private static final long serialVersionUID = -7908889223593593186L;

    @ApiModelProperty(value = "Report file url")
    private List<QueryReportFileUrlResponse> reportFileUrl;

    @ApiModelProperty(value = "Create user")
    private String createUser;

    @ApiModelProperty(value = "Query time")
    private Date createTime;

    @ApiModelProperty(value = "Query status")
    private Integer reportStatus;

    @ApiModelProperty(value = "Query conditions")
    private ReportQueryConditions queryConditions;

    @ApiModelProperty(value = "Order report code")
    private String orderReportCode;

}
