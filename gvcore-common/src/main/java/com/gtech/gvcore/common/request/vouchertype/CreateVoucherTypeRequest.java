package com.gtech.gvcore.common.request.vouchertype;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateVoucherTypeRequest")
public class CreateVoucherTypeRequest {

    @ApiModelProperty(value = "State.", example = "1122333",required = true)
    @NotEmpty(message = "state can not be empty")
    private Integer state;

    @ApiModelProperty(value = "Dd value.", example = "1122333",required = true)
    @NotEmpty(message = "ddValue can not be empty")
    @Length(max = 100)
    //ddValue prefix
    private String ddValue;

    @ApiModelProperty(value = "Dd text.", example = "1122333",required = true)
    @NotEmpty(message = "ddText can not be empty")
    @Length(max = 100)
    //ddText voucherTypeName
    private String ddText;

    @ApiModelProperty( value="Create user.", example="user1",required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 100)
    private String createUser;



}
