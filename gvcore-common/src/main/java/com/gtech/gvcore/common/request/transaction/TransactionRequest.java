package com.gtech.gvcore.common.request.transaction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Data
@Accessors(chain = true)
public class TransactionRequest {

	@ApiModelProperty(value = "Transaction ID. ", example = "SF00001", required = true, notes = "The transaction id passed in the request.")
	private Integer transactionId;
	@ApiModelProperty(value = "Transaction Mode ID. ", example = "0", required = true, notes = "The transaction mode passed in the request. This defines the mode of the transaction.")
	private String transactionModeId;
	@ApiModelProperty(value = "Date At Client. ", example = "2022-02-02 18:00:00", required = true, notes = "Timestamp of client application in YYYY-MM-DD HH:MM:SS format")
	private String dateAtClient;

	@ApiModelProperty(value = "Is Sync. ", example = "0", notes = "0 - False 1 - True Default = true Transaction will be processed inline and response will be sent after that. False: Transaction will be validated and responded with acknowledgement. Actual processing will be triggered offline.")
	private String isSync;
	@ApiModelProperty(value = "Send Only Failed Cards. ", example = "0", notes = "0 - False 1 - True This flag is applicable only to actionType = validate. Default is False, that means response will have success + failed card details in the cards response")
	private String sendOnlyFailedCards;
	@ApiModelProperty(value = "Transaction Type ID. ", example = "705", required = true, notes = "The transaction type ID passed in the request. 705 - Bulk Activate 702 - Bulk Redeem")
	private Integer transactionTypeId;
	@ApiModelProperty(value = "Action Type. ", example = "0", required = true, notes = "The action type ID passed in the request 1 - Execute 2 - Validate ‘Validate’ will perform all the necessary validations required for specified transaction, without actually doing the transaction.")
	private String actionType;
	//ETP 金额类型改为String
	@ApiModelProperty(value = "Transaction Amount. ", example = "0", required = true, notes = "Transaction Amount passed in the request.Total of all the line items")
	private String transactionAmount;
	@ApiModelProperty(value = "Idempotency Key. ", example = "123456", required = true, notes = "This key indicates that any subsequent request with this key will be an idempotent request. Applicable only for actiontype = execute")
	@Length(max = 50, message = "Idempotency Key maximum length 50")
	private String idempotencyKey;
	@ApiModelProperty(value = "Invoice Date. ", example = "2022-02-02 18:00:00", notes = "YYYY-MM-DD HH:MM:SS")
	private String invoiceDate;
	//ETP 金额类型改为String
	@ApiModelProperty(value = "Invoice Amount. ", example = "0", notes = "Optional Invoice amount from the client app")
	private String invoiceAmount;
	@ApiModelProperty(value = "Invoice Number. ", example = "123456", required = true, notes = "Optional Invoice number from the client app")
	@Length(max = 50, message = "Invoice Number maximum length 50")
	private String invoiceNumber;
	@ApiModelProperty(value = "Notes. ", example = "123456", notes = "Optional notes as per custom integrations.")
	@Length(max = 1000, message = "Notes maximum length 1000")
	private String notes;
	@ApiModelProperty(value = "Validation Token. ", example = "123456", required = true, notes = "Applicable when actionType = Execute use token from validation response and pass it to activation / redemption. This helps optimized execution at server side.")
	@Length(max = 4000, message = "Validation Token maximum length 4000")
	private String validationToken;

	@ApiModelProperty(value = "Actual Merchant Outlet Name. ", example = "123456", notes = "Actual merchant outlet name. Used in case of proxy transactions")
	@Length(max = 50, message = "Actual Merchant Outlet Name maximum length 50")
	private String actualMerchantOutletName;
	@ApiModelProperty(value = "Message Id. ", example = "123456", notes = "Optional field for client application to identify the request.")
	@Length(max = 50, message = "Message Id maximum length 50")
	private String messageId;
	@ApiModelProperty(value = "Source Id. ", example = "123456", notes = "Optional field for external client to pass any upstream reference numbers.")
	@Length(max = 50, message = "Source Id maximum length 50")
	private String sourceId;
	@ApiModelProperty(value = "Order Number. ", example = "123456", notes = "Bulk Order number. System will auto generate if this is empty.")
	@Length(max = 50, message = "Order Number maximum length 50")
	private String orderNumber;
	@ApiModelProperty(value = "Order Type. ", example = "1", notes = "The type of order passed in the request. This indicates whether the order placed is for corporate or, individual")
	private String orderType;
	@ApiModelProperty(value = "Corporate Name. ", example = "123456", notes = "Mandatory for ordertype Corporate")
	@Length(max = 50, message = "Corporate Name maximum length 50")
	private String corporateName;

	@ApiModelProperty(value = "Purchaser Info. ", notes = "CustomerInfo, Please see below for details Applicable for Activation")
	private CustomerInfo purchaserInfo;
	@ApiModelProperty(value = "Cardholder Info. ", notes = "CustomerInfo, Please see below for details Applicable for redemption")
	private CustomerInfo cardholderInfo;
	@ApiModelProperty(value = "Line Items. ", required = true, notes = "Array of order LineItem containing card/range details. Please see below of detail info")
	private List<LineItem> lineItems;
	@ApiModelProperty(value = "Discount Info. ", notes = "DiscountInfo, Please see below for details. Applicable for corporate orders")
	private DiscountInfo discountInfo;

	@ApiModelProperty(value = "Payment Info. ", notes = "Array of PaymentInfo, Please see below for details")
	private List<PaymentInfo> paymentInfo;

}
