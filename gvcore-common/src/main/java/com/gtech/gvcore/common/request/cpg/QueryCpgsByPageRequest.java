package com.gtech.gvcore.common.request.cpg;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年2月22日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryCpgsByPageRequest extends PageBean {

    @ApiModelProperty(value = "issuerCode", required = false)
    private String issuerCode;

    @ApiModelProperty(value = "issuerCode list", required = false)
    private List<String> issuerCodeList;

    @ApiModelProperty(value = "cpgName", required = false)
    private String cpgName;

    @ApiModelProperty(value = "cpgTypeCode", required = false)
    private String cpgTypeCode;
    
    @ApiModelProperty(value = "mopCode", required = false)
    private String mopCode;


    @ApiModelProperty(value = "disableGeneration", required = false)
    private String disableGeneration;



}
