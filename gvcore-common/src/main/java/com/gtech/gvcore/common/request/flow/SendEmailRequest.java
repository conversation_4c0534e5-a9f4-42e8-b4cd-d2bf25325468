package com.gtech.gvcore.common.request.flow;

import lombok.Data;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

@Data
public class SendEmailRequest {
	

	private String flow;

	private String node;
    
    private String businessCode;
    
    private List<String> emails;
    
    private Map<String, Object> extendParams;
    
	private List<FileVo> fileList;
    
    @Data
    public static class FileVo {
    	private String filename;
    	private String url;
        private InputStream inputStream;
    }

}