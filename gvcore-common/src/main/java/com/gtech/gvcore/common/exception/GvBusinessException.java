package com.gtech.gvcore.common.exception;

import com.alibaba.fastjson.JSON;
import com.gtech.gvcore.common.enums.GvPosCommonResponseCodesEnum;
import com.gtech.gvcore.common.enums.GiftCardResponseCodesEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class GvBusinessException extends RuntimeException {

    private final String responseCodeString;
    private final int responseCodeInt;
    private final String customMessage;


    public GvBusinessException(String code, String message) {
        super(message);
        this.responseCodeString = code;
        this.customMessage = message;
        this.responseCodeInt = parseCode(code);
    }

    public GvBusinessException(Integer code, String message) {
        super(message);
        this.responseCodeString = String.valueOf(code);
        this.customMessage = message;
        this.responseCodeInt = code;
    }

    public GvBusinessException(String code, String message, Exception cause) {
        super(message, cause);
        log.error("GvBusinessException with code: " + code + " and message: " + message + " Exception:"  + cause);
        this.responseCodeString = code;
        this.customMessage = message;
        this.responseCodeInt = parseCode(code);
    }


    public GvBusinessException(GvPosCommonResponseCodesEnum errorCodeEnum) {
        super(errorCodeEnum.getResponseMessage());
        this.responseCodeString = String.valueOf(errorCodeEnum.getResponseCode());
        this.customMessage = errorCodeEnum.getResponseMessage();
        this.responseCodeInt = errorCodeEnum.getResponseCode();
    }
    
    public GvBusinessException(GvPosCommonResponseCodesEnum errorCodeEnum, Throwable cause) {
        super(errorCodeEnum.getResponseMessage(),cause);
        this.responseCodeString = String.valueOf(errorCodeEnum.getResponseCode());
        this.customMessage = errorCodeEnum.getResponseMessage();
        this.responseCodeInt = errorCodeEnum.getResponseCode();
    }

    public GvBusinessException(GiftCardResponseCodesEnum errorCodeEnum) {
        super(errorCodeEnum.getResponseMessage());
        this.responseCodeString = String.valueOf(errorCodeEnum.getResponseCode());
        this.customMessage = errorCodeEnum.getResponseMessage();
        this.responseCodeInt = errorCodeEnum.getResponseCode();
    }

    public GvBusinessException(GiftCardResponseCodesEnum errorCodeEnum, Throwable cause) {
        super(errorCodeEnum.getResponseMessage(),cause);
        this.responseCodeString = String.valueOf(errorCodeEnum.getResponseCode());
        this.customMessage = errorCodeEnum.getResponseMessage();
        this.responseCodeInt = errorCodeEnum.getResponseCode();
    }

    private static int parseCode(String code) {
        try {
            return Integer.parseInt(code);
        } catch (NumberFormatException e) {
            System.err.println("Warning: GvBusinessException code is not a valid integer: " + code);
            return -1;
        }
    }


    public GvPosCommonResponseCodesEnum getMatchingErrorCodeEnum() {
        for (GvPosCommonResponseCodesEnum ec : GvPosCommonResponseCodesEnum.values()) {
            if (ec.getResponseCode() == this.responseCodeInt) {
                return ec;
            }
        }
        return null;
    }

    public GiftCardResponseCodesEnum getMatchingGiftCardErrorCodeEnum() {
        for (GiftCardResponseCodesEnum ec : GiftCardResponseCodesEnum.values()) {
            if (ec.getResponseCode() == this.responseCodeInt) {
                return ec;
            }
        }
        return null;
    }
}