package com.gtech.gvcore.common.request.productcategory;

import java.math.BigDecimal;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CalculateDiscountAmountRequest {

    @ApiModelProperty(value = "productCategoryCode", required = true)
    @NotEmpty(message = "productCategoryCode can not be empty")
    @Length(max = 50)
    private String productCategoryCode;

	@ApiModelProperty(value = "Total order amount", required = true)
	@NotNull(message = "Total order amount can't be empty")
	private BigDecimal amount;

}
