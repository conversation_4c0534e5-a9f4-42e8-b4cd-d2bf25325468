package com.gtech.gvcore.common.enums;

public enum ReceivingMethodEnum {



    EMAIL("0", "Email"),
    FTP("1", "FTP"),
    EMAIL_AND_FTP("1,0", "Email and Ftp"),
    EMAIL_AND_FTP_TWO("0,1", "Email and Ftp")

    ;

    private final String code;

    private final String desc;

    ReceivingMethodEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
