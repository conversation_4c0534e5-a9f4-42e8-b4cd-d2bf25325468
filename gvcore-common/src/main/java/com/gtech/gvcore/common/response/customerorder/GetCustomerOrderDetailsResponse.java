package com.gtech.gvcore.common.response.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/20 22:42
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "Get customer order details response")
public class GetCustomerOrderDetailsResponse implements Serializable {

    private static final long serialVersionUID = -2821678573169609179L;
    @ApiModelProperty(value = "Customer order detail code", example = "COD102203201832000003")
    private String customerOrderDetailsCode;
    @ApiModelProperty(value = "Card program group code", example = "630c46f4f871408e8a17af6af09649b5")
    private String cpgCode;
    @ApiModelProperty(value = "Cpg name")
    private String cpgName;
    @ApiModelProperty(value = "Voucher number")
    private Integer voucherNum;
    @ApiModelProperty(value = "Denomination ")
    private BigDecimal denomination;
	private String articleCode;
	private String articleCodeName;
	private String customerOrderCode;

}
