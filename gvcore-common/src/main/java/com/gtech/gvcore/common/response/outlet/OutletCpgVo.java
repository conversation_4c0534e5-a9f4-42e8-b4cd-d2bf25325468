package com.gtech.gvcore.common.response.outlet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "OutletCpgVo")
public class OutletCpgVo {

    @ApiModelProperty(value = "Cpg code.", example = "2123123")
    private String cpgCode;

    @ApiModelProperty(value = "Cpg name.", example = "2123123")
    private String cpgName;

    @ApiModelProperty(value = "Cpg type code.", example = "2123123")
    private String cpgTypeCode;

    @ApiModelProperty(value = "Denomination.", example = "50000")
    private BigDecimal denomination;

    @ApiModelProperty(value = "Disabled generate.", example = "0")
    private String disableGeneration;

    @ApiModelProperty(value = "mopCode.", example = "0")
    private String mopCode;


}
