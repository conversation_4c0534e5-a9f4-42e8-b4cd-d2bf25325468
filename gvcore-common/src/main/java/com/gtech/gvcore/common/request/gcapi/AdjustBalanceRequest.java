package com.gtech.gvcore.common.request.gcapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Gift Card Balance Adjustment Request
 * 礼品卡余额调整请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AdjustBalanceRequest", description = "Gift Card Balance Adjustment Request")
public class AdjustBalanceRequest {

    @ApiModelProperty(value = "Transaction Code", notes = "Fill in with 808 - Balance Adjustment", required = true, example = "808", position = 1)
    private Integer transactionCode;

    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number", required = true, example = "1234567890123456", position = 2)
    private String giftCardNumber;

    @ApiModelProperty(value = "Gift Card PIN", notes = "6 digit PIN code", example = "123456", position = 3)
    private String giftCardPIN;

    @ApiModelProperty(value = "Adjustment Amount", notes = "Amount to adjust (positive for increase, negative for decrease)", required = true, example = "50.00", position = 4)
    private BigDecimal adjustmentAmount;

    @ApiModelProperty(value = "Adjustment Type", notes = "Type of adjustment: ADD, SUBTRACT, or SET", required = true, example = "ADD", position = 5)
    private String adjustmentType;

    @ApiModelProperty(value = "Adjustment Reason", notes = "Reason code for adjustment", required = true, example = "COMPENSATION", position = 6)
    private String adjustmentReason;

    @ApiModelProperty(value = "Authorization Code", notes = "Authorization code for adjustment", required = true, example = "AUTH12345", position = 7)
    private String authorizationCode;

    @ApiModelProperty(value = "Terminal ID", notes = "Terminal identification", example = "TERM001", position = 8)
    private String terminalId;

    @ApiModelProperty(value = "Invoice Number", notes = "Reference invoice number", required = true, example = "INV-12345", position = 9)
    private String invoiceNumber;

    @ApiModelProperty(value = "Adjustment Date", notes = "Date of adjustment in YYYY-MM-DDTHH:MM:SS format", example = "2023-01-01T12:00:00", position = 10)
    private Date adjustmentDate;

    @ApiModelProperty(value = "Client Time", notes = "Client application timestamp, format YYYY-MM-DDTHH:MM:SS", example = "2023-01-01T12:00:00", position = 11)
    private Date clientTime;

    @ApiModelProperty(value = "Operator ID", notes = "ID of the operator adjusting the balance", required = true, example = "OP001", position = 12)
    private String operatorId;

    @ApiModelProperty(value = "Approval ID", notes = "ID of approver for the adjustment", required = true, example = "APV001", position = 13)
    private String approvalId;

    @ApiModelProperty(value = "Case Number", notes = "Customer service case number", example = "CASE12345", position = 14)
    private String caseNumber;

    @ApiModelProperty(value = "Notes", notes = "Additional notes or comments", example = "Adjustment due to system error", position = 15)
    private String notes;
}