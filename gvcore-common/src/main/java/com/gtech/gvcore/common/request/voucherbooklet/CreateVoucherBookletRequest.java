
package com.gtech.gvcore.common.request.voucherbooklet;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CreateVoucherBookletRequest")
public class CreateVoucherBookletRequest{



    private String issuerCode;
    
    private String bookletCode;
    
    private String bookletBarcode;
    
    private String voucherBatchCode;
    
    private String voucherStartNo;
    
    private String voucherEndNo;

    private Integer bookletPerNum;
    
    private Integer status;
    
    private Date createTime;
    
    private Date updateTime;
    
    private String createUser;
    
    private String updateUser;



}
