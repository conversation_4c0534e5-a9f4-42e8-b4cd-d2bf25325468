package com.gtech.gvcore.common.response.useraccount;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

@Data
public class PermissionCodeResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3564213313404393467L;

	private String issuerCode;
	
	private Boolean haveIssuer = false;

	private List<String> companyCodeList;

	private List<String> merchentCodeList;

	private List<String> outletCodeList;

	public static boolean hasOutlet(final PermissionCodeResponse permissionCodeResponse) {
		return null != permissionCodeResponse && CollectionUtils.isNotEmpty(permissionCodeResponse.getOutletCodeList());
	}

}
