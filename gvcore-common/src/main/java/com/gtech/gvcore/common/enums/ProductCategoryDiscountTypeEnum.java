package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @date 2022年3月17日
 */
public enum ProductCategoryDiscountTypeEnum implements IEnum<String> {

    AMOUNT("amount", "Pending Allocation"),
    PERCENTAGE("percentage", "Pending Receipt");

    private final String code;

    private final String desc;

    ProductCategoryDiscountTypeEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    @Override
    public String code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}
