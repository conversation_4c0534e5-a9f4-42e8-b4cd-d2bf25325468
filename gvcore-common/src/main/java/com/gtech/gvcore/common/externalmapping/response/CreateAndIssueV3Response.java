package com.gtech.gvcore.common.externalmapping.response;

import com.gtech.gvcore.common.response.v3.CardsResponse;
import com.gtech.gvcore.common.response.v3.CreateAndIssueResponse;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CreateAndIssueV3Response {


//    @JsonProperty("TransactionId")
    private Long transactionId;

//    @JsonProperty("CurrentBatchNumber")
    private Long currentBatchNumber;

//    @JsonProperty("TransactionTypeId")
    private Integer transactionCode;

//    @JsonProperty("TotalAmount")
    private BigDecimal totalAmount;

//    @JsonProperty("Notes")
    private String notes;

//    @JsonProperty("ApprovalCode")
    private String approvalCode;

//    @JsonProperty("ResponseCode")
    private Integer ResponseCode;

//    @JsonProperty("ResponseMessage")
    private String ResponseMessage;

//    @JsonProperty("ErrorCode")
    private String errorCode;

//    @JsonProperty("ErrorDescription")
    private String errorDescription;

//    @JsonProperty("InputType")
    private String inputType;

//    @JsonProperty("TotalCards")
//    private Integer TotalCards;

//    @JsonProperty("NumberOfCards")
    private Integer numberOfVouchers;

//    @JsonProperty("Cards")
    private List<CardsResponse> vouchers;

//    @JsonProperty("BusinessReferenceNumber")
    private String referenceNumber;
//
//    @JsonProperty("IdempotencyKey")
    private String retryKey;

//    @JsonProperty("GeneralLedger")
//    private String GeneralLedger;

//    @JsonProperty("CostCentre")
//    private String CostCentre;

//    @JsonProperty("ExecutionMode")
//    private Integer ExecutionMode;

    public CreateAndIssueV3Response setCreateAndIssueV3Response(CreateAndIssueResponse response){
        if (null == response){
            return this;
        }
        this.setTransactionId(response.getTransactionId());
        this.setCurrentBatchNumber(response.getCurrentBatchNumber());
        this.setTransactionCode(response.getTransactionTypeId());
        this.setTotalAmount(response.getTotalAmount());
        this.setNotes(response.getNotes());
        this.setApprovalCode(response.getApprovalCode());
        this.setResponseCode(response.getResponseCode());
        this.setResponseMessage(response.getResponseMessage());
        this.setErrorCode(response.getErrorCode());
        this.setErrorDescription(response.getErrorDescription());
        this.setInputType(response.getInputType());
//        this.setTotalCards(response.getTotalCards());
        this.setNumberOfVouchers(response.getNumberOfCards());
        this.setVouchers(response.getCards());
        this.setReferenceNumber(response.getBusinessReferenceNumber());
        this.setRetryKey(response.getIdempotencyKey());
//        this.setGeneralLedger(response.getGeneralLedger());
//        this.setCostCentre(response.getCostCentre());
//        this.setExecutionMode(response.getExecutionMode());
        return this;



    }



}
