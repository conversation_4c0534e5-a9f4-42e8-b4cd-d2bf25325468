package com.gtech.gvcore.common.response.voucher;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class VerifyVoucherGinseng {
    @ApiModelProperty(value = "Success Card Count.", required = true, notes = "Total success card count @ line item level")
    private Integer successCardCount;

    @ApiModelProperty(value = "Start Card Number.", required = true, notes = "Start card number in the lineitem")
    private String startCardNumber;
    @ApiModelProperty(value = "End Card Number.", required = true, notes = "End card number in the lineitem")
    private String endCardNumber;
    @ApiModelProperty(value = "Total Card Count.", required = true, notes = "Total Card Count @ line level")
    private Integer totalCardCount;
    @ApiModelProperty(value = "Design Code.", required = true, notes = "This is an article code")
    private String designCode;

    @ApiModelProperty(value = "Total Amount.", required = true, notes = "Total success / fail amount")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "Product Code.", required = true, notes = "This is product code")
    private String productCode;
    @ApiModelProperty(value = "Card Program Group Name.", required = true, notes = "Card Program Group Name")
    private String cardProgramGroupName;
    @ApiModelProperty(value = "Response Message.", required = true, notes = "Success / Failure message")
    private String responseMessage;
    private Boolean isSuccess = true;

    List<VerifyVoucherResponse> verifyVoucherResponses;


}
