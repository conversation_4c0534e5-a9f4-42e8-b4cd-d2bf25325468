package com.gtech.gvcore.common.request.outletcpg;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;


/**
 * <AUTHOR>
 * @Date 2022/2/11 16:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryOutletCpgRequest")
public class QueryOutletCpgRequest extends PageBean {

    @ApiModelProperty(value = "Outlet cpg code.", example = "112233")
    @Length(max = 100)
    private String outletCpgCode;

    @ApiModelProperty(value = "Outlet code.", example = "1122333")
    @Length(max = 100)
    private String outletCode;

    @ApiModelProperty(value = "Cpg code.", example = "2123123")
    @Length(max = 100)
    private String cpgCode;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;



}
