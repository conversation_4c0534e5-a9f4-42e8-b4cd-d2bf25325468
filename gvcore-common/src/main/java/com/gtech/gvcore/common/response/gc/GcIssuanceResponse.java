package com.gtech.gvcore.common.response.gc;

import com.gtech.gvcore.common.request.gcapi.CustomerInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Gift Card Issuance Response
 */
@Data
@ApiModel(value = "GcIssuanceResponse", description = "Gift Card Issuance Response")
public class GcIssuanceResponse extends BaseApiResponse {
    


    @ApiModelProperty(value = "Transaction Code", notes = "Fill in with 306 - GC Issuance")
    private Integer transactionCode;

    @ApiModelProperty(value = "Input Type", notes = "4-EGC (e-Gift Cards)")
    private String inputType;

    @ApiModelProperty(value = "Number of Gift Cards", notes = "Gift Cards quantity")
    private Integer numberOfGiftCards;

    @ApiModelProperty(value = "Invoice Number", notes = "Invoice number from POS/E-commerce")
    private String invoiceNumber;

    @ApiModelProperty(value = "Invoice Date", notes = "Invoice Date of client side in YYYY-MM-DDTHH:MM:SS format")
    private Date invoiceDate;

    @ApiModelProperty(value = "Buyer", notes = "Buyer information")
    private CustomerInfo buyer;
    
    @ApiModelProperty(value = "Notes", notes = "Reference text")
    private String notes;
    
    @ApiModelProperty(value = "Retry Key", notes = "Reference number for the request")
    private String retryKey;

    @ApiModelProperty(value = "GCPG", notes = "GCPG Name")
    private String gcpg;
    
    @ApiModelProperty(value = "Source", notes = "Reference for outletname/POSname/brandname/partnername")
    private String source;
    
    @ApiModelProperty(value = "Gift Cards", notes = "List of issued gift cards")
    private List<GiftCardInfo> giftCards;
    
    @ApiModelProperty(value = "Transaction Date", notes = "Transaction date")
    private Date transactionDate;
    
    @ApiModelProperty(value = "Batch Number", notes = "Batch number from token generation")
    private String batchNumber;

    @ApiModelProperty(value = "Store Code", notes = "Store code")
    private String storeCode;

    @ApiModelProperty(value = "Store Name", notes = "Store name")
    private String storeName;




} 