package com.gtech.gvcore.common.request.productcategory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
@Data
public class QueryProductCategoryCpgRequest {

    @ApiModelProperty(value = "productCategoryCode", required = true)
    @NotEmpty(message = "productCategoryCode can not be empty")
    private String productCategoryCode;


    @ApiModelProperty(value = "disableGeneration")
    private String disableGeneration;


}
