package com.gtech.gvcore.common.request.productcategory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
@Data
public class QueryProductCategoryCpgRequest {

    @ApiModelProperty(value = "productCategoryCode", required = true)
    @NotEmpty(message = "productCategoryCode can not be empty")
    private String productCategoryCode;


    @ApiModelProperty(value = "disableGeneration")
    private String disableGeneration;

    @ApiModelProperty(value = "categoryType", required = false, example = "voucher", notes = "分类类型，可能的值：voucher, gc")
    @Length(max = 20)
    private String cpgType = "voucher";

}
