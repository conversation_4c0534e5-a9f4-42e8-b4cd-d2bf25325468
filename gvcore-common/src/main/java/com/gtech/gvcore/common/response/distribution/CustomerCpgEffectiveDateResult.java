package com.gtech.gvcore.common.response.distribution;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CustomerCpgEffectiveDateResult
 * @Description CustomerCpgEffectiveDateResult
 * <AUTHOR>
 * @Date 2022/7/6 10:43
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "CustomerCpgEffectiveDateResult")
public class CustomerCpgEffectiveDateResult {

    private BigDecimal denomination;

    private Integer inventory;

    @JsonFormat(pattern = DateUtil.FORMAT_YYYYMMDDHHMISS)
    private Date voucherEffectiveDate;

    public String getSelectDesc() {

        return DateUtil.format(voucherEffectiveDate, DateUtil.FORMAT_YYYYMMDDHHMISS) + " | " + ConvertUtils.toInteger(inventory, 0);
    }

}
