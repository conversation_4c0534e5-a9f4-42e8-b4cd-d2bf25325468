package com.gtech.gvcore.common.request.customerorder;

import com.gtech.basic.idm.common.exceptions.ErrorCodes;
import com.gtech.commons.utils.CheckUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/20 22:13
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "Update customer order request")
public class UpdateCustomerOrderRequest implements Serializable {

    private static final long serialVersionUID = -4461389160112529121L;
    @ApiModelProperty(value = "Customer order code", required = true, example = "CO102203201832000005")
    @NotBlank(message = "Customer order code cannot empty")
    private String customerOrderCode;
    @ApiModelProperty(value = "Issuer code", required = true, example = "IS102203031638001252")
    @NotBlank(message = "Issuer code cannot be empty")
    private String issuerCode;
    @ApiModelProperty(value = "Outlet code", required = true, example = "OU102203031638001252")
    @NotBlank(message = "Outlet code cannot be empty")
    private String outletCode;
    @ApiModelProperty(value = "Purchase Order Number", example = "1221 - 1364")
    private String purchaseOrderNo;
    @ApiModelProperty(value = "Voucher type code", required = true, example = "VCE")
    @NotBlank(message = "mopCode cannot be empty")
    private String mopCode;
    @NotNull(message = "meansOfPaymentCode can not be empty")
    @ApiModelProperty(value = "MOP code", required = true, example = "85a4d8656d05442b982d328bc3f2c19b")
    private String meansOfPaymentCode;
    @ApiModelProperty(value = "Total voucher num", required = true, example = "4000")
    @NotNull(message = "Voucher number cannot be empty")
    private Integer voucherNum;
    @ApiModelProperty(value = "Total voucher amount", required = true, example = "120000000")
    @NotNull(message = "Voucher amount cannot be empty")
    private BigDecimal voucherAmount;
    @ApiModelProperty(value = "Discount", example = "2")
    private BigDecimal discount;
    @ApiModelProperty(value = "Discount Amount", required = true, example = "2.500.000.000")
    @NotNull(message = "Amount cannot be empty")
    private BigDecimal amount;
    @ApiModelProperty(value = "Currency code")
    private String currencyCode;
    @ApiModelProperty(value = "Customer code", example = "1234124512")
    private String customerCode;
    @ApiModelProperty(value = "Customer name", required = true)
    @NotBlank(message = "Customer name cannot be empty")
    private String customerName;
    @ApiModelProperty(value = "Customer type", required = true)
    @NotBlank(message = "Customer type cannot be empty")
    private String customerType;
    @ApiModelProperty(value = "Company name")
    private String companyName;
    @ApiModelProperty(value = "Contact first name")
    private String contactFirstName;
    @ApiModelProperty(value = "Contact last name")
    private String contactLastName;
    @ApiModelProperty(value = "Contact Phone", required = true)
    @NotBlank(message = "Contact phone cannot be empty")
    private String contactPhone;
    @ApiModelProperty(value = "Contact email", required = true)
    @NotBlank(message = "Contact email cannot empty")
    private String contactEmail;
    @ApiModelProperty(value = "Product category code")
    private String productCategoryCode;
    @ApiModelProperty(value = "Invoice number")
    private String invoiceNo;
    @ApiModelProperty(value = "Discount type")
    private String discountType;
    @ApiModelProperty(value = "Update user", required = true)
    @NotBlank(message = "Update user cannot be empty")
    private String updateUser;
    @NotBlank(message = "Shipping address cannot be empty")
    @ApiModelProperty(value = "Shipping address", required = true)
    private String shippingAddress;
    @ApiModelProperty(value = "notes")
    private String customerRemarks;

    @ApiModelProperty(value = "deliveType")
    private Integer deliveType;

    @ApiModelProperty(value = "deliveryDate")
    private String deliveryDate;

    @ApiModelProperty(value = "orderAddress")
    private String orderAddress;
    @NotNull(message = "updateCustomerOrderDetailsRequests cannot empty")
    @Size(min = 1, message = "At least one piece of data")
    private List<UpdateCustomerOrderDetailsRequest> updateCustomerOrderDetailsRequests;




    public static final String CORPORATE = "Corporate";
    public static final String INDIVIDUAL = "Individual";



    public void validation(){

        if (customerType.equals(CORPORATE)){

            CheckUtils.isNotBlank(this.contactFirstName, ErrorCodes.PARAM_EMPTY, "contactFirstName");
            CheckUtils.isNotBlank(this.contactLastName, ErrorCodes.PARAM_EMPTY, "contactLastName");

        }
    }

}
