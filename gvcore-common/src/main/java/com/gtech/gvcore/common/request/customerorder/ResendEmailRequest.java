package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName ResendEmailRequest
 * @Description 重新发送邮件请求入参
 * <AUTHOR>
 * @Date 2022/8/10 15:56
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "resend email request.")
public class ResendEmailRequest {

    @NotBlank(message = "email code")
    @ApiModelProperty(value = "email code", example = "OCE9928212313", required = true)
    private String emailCode;

    @ApiModelProperty(value = "login user code", example = "UC0001", required = true)
    @NotBlank(message = "UserCode is required")
    private String userCode;

}
