package com.gtech.gvcore.common.response.distribution;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gtech.commons.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName QueryCustomerDistributionResult
 * @Description 查询客户分发列表出参
 * <AUTHOR>
 * @Date 2022/9/2 15:50
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "QueryCustomerDistributionResult")
public class QueryCustomerDistributionResult {

    private String distributionCode;

    private String distributionType;

    private String cpgDesc;

    private Integer recipientsSum;

    @JsonFormat(pattern = DateUtil.FORMAT_YYYYMMDDHHMISS)
    private Date createTime;

    private String createUser;

    private String status;

}
