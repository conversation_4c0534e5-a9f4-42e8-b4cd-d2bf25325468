package com.gtech.gvcore.common.request.flow;

import java.util.List;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FlowNoticeRequest {
	

    /**
     * flow code
     */
    @ApiModelProperty(value = "flow code" ,required = true)
    @NotEmpty(message = "flow code can't be empty")
    private String flowCode;

    /**
     * flow node code
     */
    @ApiModelProperty(value = "flow node code" ,required = true)
    @NotEmpty(message = "flow node code can't be empty")
    private String flowNodeCode;

    /**
     * flow notice code type, 0-role,1-user, default 0
     */
    @ApiModelProperty(value = "flow notice code type")
    private Integer flowNoticeCodeType = 0;

    List<String> noticeCodeList;
    
    List<String> noticeCodeCCList;
}