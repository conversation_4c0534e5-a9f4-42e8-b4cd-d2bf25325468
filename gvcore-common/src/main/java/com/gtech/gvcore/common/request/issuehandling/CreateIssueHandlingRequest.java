package com.gtech.gvcore.common.request.issuehandling;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.URL;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CreateIssueHandlingRequest {
	
	@ApiModelProperty(value = "issuerCode", required = true)
    @NotEmpty(message = "issuerCode can not be empty")
    @Length(max = 50)
	private String issuerCode;
	
	@ApiModelProperty(value = "issueType", required = true)
    @NotEmpty(message = "issueType can not be empty")
    @Length(max = 40)
	private String issueType;
	
	@ApiModelProperty(value = "uploadedFileName", required = true)
    @NotEmpty(message = "uploadedFileName can not be empty")
    @Length(max = 200)
	private String uploadedFileName;
	
	@ApiModelProperty(value = "uploadedFileUrl", required = true)
    @NotEmpty(message = "uploadedFileUrl can not be empty")
    @Length(max = 500)
	private String uploadedFileUrl;
	
	@ApiModelProperty(value = "remarks", required = false)
    @Length(max = 400)
	private String remarks;
	
	@ApiModelProperty(value = "createUser", required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 50)
	private String createUser;
	
	@ApiModelProperty(value = "createUserEmail", required = true)
    @NotEmpty(message = "createUserEmail can not be empty")
    @Length(max = 256)
	private String createUserEmail;
	
	@ApiModelProperty(value = "proofFileList", required = false)
    @Valid
    @Size(max = 200, message = "The number of proof file cannot be greater than 200")
    @NotEmpty(message = "proofFileList can not be empty")
	private List<ProofFile> proofFileList;
 
}
