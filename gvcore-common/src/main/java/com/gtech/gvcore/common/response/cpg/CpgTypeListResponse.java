package com.gtech.gvcore.common.response.cpg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Gao.Yuhua
 * @date : 2022-02-22 14:49
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryCpgTypeByPageResponse")
public class CpgTypeListResponse {

    /**
     * cpgTypeCode
     */
    @ApiModelProperty(value = "cpgTypeCode")
    private String cpgTypeCode;

    /**
     * CPG type name
     */
    @ApiModelProperty(value = "cpgTypeName")
    private String cpgTypeName;

    @ApiModelProperty(value = "automaticActivate")
    private String automaticActivate;

    /**
     * prefix
     */
    @ApiModelProperty(value = "cpgTypeName", example = "100")
    private String prefix;
}
