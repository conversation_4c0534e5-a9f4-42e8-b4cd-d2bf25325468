package com.gtech.gvcore.common.request.customerorder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel(value = "DeliverCustomerOrderRequest")
public class DeliverCustomerOrderRequest implements Serializable {
    private static final long serialVersionUID = -2849738734253238374L;
    @ApiModelProperty(value = "Customer order code", required = true)
    @NotBlank(message = "Customer order code cannot be empty")
    private String customerOrderCode;
    @ApiModelProperty(value = "User code", required = true)
    @NotBlank(message = "User code cannot be empty")
    private String userCode;

	@ApiModelProperty(value = "Delive type,0:External Courier,1:Walk-in,2:Internal Courier", required = true)
	@NotNull(message = "Delive type cannot be null")
	private Integer deliveType;

	@ApiModelProperty(value = "Logistics name, if delive type is 0 and 2, required")
	private String logisticsName;

	@ApiModelProperty(value = "AWB")
	private String awb;

	@ApiModelProperty(value = "Scan Of Receipt, required",required = true)
	private String scanOfReceipt;

	@ApiModelProperty(value = "Track no, if delive type is 1, required")
	private String trackNo;

}
