package com.gtech.gvcore.common.request.customerorder;

import java.util.Date;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022年6月10日
 */
@Data
public class SapSalesPostingXmlRequest {

    @ApiModelProperty(value = "outletCode")
    private String outletCode;

    @ApiModelProperty(value = "queryDate", required = false)
    private Date queryDate;

}
