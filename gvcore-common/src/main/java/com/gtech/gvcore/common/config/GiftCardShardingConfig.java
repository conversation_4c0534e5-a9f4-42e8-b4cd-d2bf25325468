package com.gtech.gvcore.common.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Slf4j
@Component
public class GiftCardShardingConfig implements PreciseShardingAlgorithm<String> {
    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<String> preciseShardingValue) {
        String value = preciseShardingValue.getValue();
        if (StringUtils.isNotBlank(value)) {
            value = value.replaceAll("[a-zA-Z]", "");
            Long x = Long.valueOf(value) % collection.size();
            int i = x.intValue(); // NOSONAR
            return collection.toArray()[i].toString();
        } else {
            throw new IllegalArgumentException();
        }
    }
}
