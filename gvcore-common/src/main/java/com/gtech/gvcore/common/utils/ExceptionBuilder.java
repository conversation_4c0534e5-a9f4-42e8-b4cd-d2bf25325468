package com.gtech.gvcore.common.utils;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;

/**
 * @ClassName ExceptionBuilder
 * @Description ExceptionBuilder
 * <AUTHOR>
 * @Date 2022/8/10 14:00
 * @Version V1.0
 **/
public class ExceptionBuilder {

    private ExceptionBuilder() {}

    public static RuntimeException buildGTechBaseException(final ResultErrorCodeEnum resultErrorCodeEnum) {

        if (null == resultErrorCodeEnum) {
            return new GTechBaseException();
        }

        return new GTechBaseException(resultErrorCodeEnum.code(), resultErrorCodeEnum.desc());
    }

    public static RuntimeException buildGTechBaseException(final ResultErrorCodeEnum resultErrorCodeEnum, final String arg1) {

        if (null == resultErrorCodeEnum) {
            return new GTechBaseException();
        }

        return new GTechBaseException(resultErrorCodeEnum.code(), resultErrorCodeEnum.desc(), arg1);
    }

    public static RuntimeException buildGTechBaseException(final ResultErrorCodeEnum resultErrorCodeEnum, final String arg1, final String arg2) {

        if (null == resultErrorCodeEnum) {
            return new GTechBaseException();
        }

        return new GTechBaseException(resultErrorCodeEnum.code(), resultErrorCodeEnum.desc(), arg1, arg2);
    }

}
