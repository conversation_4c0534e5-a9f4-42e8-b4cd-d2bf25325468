
package com.gtech.gvcore.common.response.v3;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
@JsonPropertyOrder({"Key","Value"})
public class CardFormats implements Serializable {

    private static final long serialVersionUID = -3053670329364142686L;

    @JsonProperty("Key")
    private String Key;

    @JsonProperty("Value")
    private String Value;


}