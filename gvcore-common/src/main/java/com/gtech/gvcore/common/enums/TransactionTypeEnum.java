package com.gtech.gvcore.common.enums;

import java.util.Objects;

public enum TransactionTypeEnum {


    GIFT_CARD_REDEEM_VALIDATE("0", "GIFT VOUCHER REDEEM VALIDATE"),
    GIFT_CARD_REDEEM("1", "GIFT VOUCHER REDEEM"),
    //GIFT_CARD_BULK_REDEEM("2", "GIFT VOUCHER BULK REDEEM "),
    GIFT_CARD_REGENERATE_ACTIVATION_CODE("23", "GIFT VOUCHER REGENERATE ACTIVATION CODE"),
    GIFT_CARD_ACTIVATE_VALIDATE("4", "GIFT VOUCHER ACTIVATE VALIDATE"),
    GIFT_CARD_ACTIVATE("5", "GIFT VOUCHER ACTIVATE"),
    GIFT_CARD_ACTIVATE_ONLY("22", "GIFT VOUCHER ACTIVATE ONLY"),
    //GENERATED_BARCODE("6", "GENERATED BARCODE"),
    //MERGED_VOUCHER("7", "MERGED VOUCHER"),
    //PURCHASED("8", "PURCHASED"),
    GIFT_CARD_NEW_GENERATE("9","GIFT VOUCHER CREATED"),
    GIFT_CARD_SELL("10","GIFT VOUCHER SELL"),
    GIFT_CARD_BULK_CANCEL_REDEEM("11","GIFT VOUCHER BULK CANCEL REDEEM"),
    GIFT_CARD_CANCEL_ACTIVATE("12", "GIFT VOUCHER CANCEL ACTIVATE"),
    GIFT_CARD_CANCEL_SELL("20", "GIFT VOUCHER CANCEL SELL"),
    GIFT_CARD_DEACTIVATE("13", "GIFT VOUCHER DEACTIVATE"),
    GIFT_CARD_REACTIVATE("14", "GIFT VOUCHER REACTIVATE"),
    GIFT_CARD_EXPIRY("15", "GIFT VOUCHER EXPIRY"),
    //GIFT_CARD_DISABLE("16", "GIFT VOUCHER DISABLE"),
    //GIFT_CARD_ENABLE("17", "GIFT VOUCHER ENABLE"),
    GIFT_CARD_REISSUE("18", "GIFT VOUCHER REISSUE"),
    GIFT_CARD_ONE_TIME_BARCODE("19", "ONE TIME BARCODE"),
    RESET_ACTIVATION("21", "RESET ACTIVATION"),
    ;

    private final String code;

    private final String desc;

    TransactionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 
     * <AUTHOR>
     * @param code
     * @return
     * @date 2022年6月17日
     */
    public static String getTypeDesc(String code) {

        for (TransactionTypeEnum typeEnum : TransactionTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }

    public boolean equalsCode (String code) {

        return this.code.equals(code);
    }



    public static TransactionTypeEnum valueOfCode(String code) {
        for (TransactionTypeEnum type : TransactionTypeEnum.values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

}


