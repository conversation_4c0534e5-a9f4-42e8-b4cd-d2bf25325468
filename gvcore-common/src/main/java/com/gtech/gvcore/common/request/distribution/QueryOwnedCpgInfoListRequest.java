package com.gtech.gvcore.common.request.distribution;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName QueryInStockCpgRequest
 * @Description 查询有库存的VPG信息参数
 * <AUTHOR>
 * @Date 2022/7/6 10:18
 * @Version V1.0
 **/
@Getter
@Setter
@ApiModel(value = "QueryReceivedCpgInfoListRequest")
public class QueryOwnedCpgInfoListRequest {

    @ApiModelProperty(value = "客户编码", example = "UC001",required = true)
    @NotEmpty(message = "Customer code can not be empty")
    private String customerCode;

}
