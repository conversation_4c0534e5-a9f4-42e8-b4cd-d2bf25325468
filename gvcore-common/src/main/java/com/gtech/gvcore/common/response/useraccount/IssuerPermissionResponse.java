package com.gtech.gvcore.common.response.useraccount;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class IssuerPermissionResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4209319348509515810L;

	@ApiModelProperty(value = "Node is leaf.")
	private Boolean isLeaf;

	@ApiModelProperty(hidden = true)
	private Integer type;

	@ApiModelProperty(value = "Data Permission Code. ")
	private String issuerCode;

	private List<DataPermissionResponse> dataPermissionList;
}
