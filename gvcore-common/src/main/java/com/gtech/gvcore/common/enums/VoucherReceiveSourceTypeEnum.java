package com.gtech.gvcore.common.enums;

public enum VoucherReceiveSourceTypeEnum {

	GENERATE("generate", "Generate"), 
	SALES("sales", "Sales"),
	CUSTOMER_ORDER("customerorder", "CustomerOrder"),
	RETURN("return", "Return"),
	TRANSFER("transfer", "Transfer")
	;
	
	private final String code;

    private final String desc;

	VoucherReceiveSourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

	public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
