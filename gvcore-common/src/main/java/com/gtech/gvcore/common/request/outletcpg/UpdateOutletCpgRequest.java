package com.gtech.gvcore.common.request.outletcpg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2022/2/11 16:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateOutletCpgRequest")
public class UpdateOutletCpgRequest {

    @ApiModelProperty(value = "Outlet cpg code.", example = "112233",required = true)
    @NotEmpty(message = "outletCpgCode can not be empty")
    @Length(max = 100)
    private String outletCpgCode;

    @ApiModelProperty(value = "Outlet code.", example = "1122333")
    @Length(max = 100)
    private String outletCode;

    @ApiModelProperty(value = "Cpg code.", example = "2123123")
    @Length(max = 100)
    private String cpgCode;

    @ApiModelProperty(value = "Status.", example = "1")
    private Integer status;

    @ApiModelProperty( value="Update user.", example="user1")
    @Length(max = 100)
    private String updateUser;


}
