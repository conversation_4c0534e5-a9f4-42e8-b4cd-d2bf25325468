package com.gtech.gvcore.common.enums;

public enum IssueHandlingProcessStatusEnum implements IEnum<Integer> {
	CREATED(0, "created"),
	PROCESSING(1, "processing"),
	SUCCESS(2, "success"),
	FAILED(3, "failed");
	
    private final Integer code;

    private final String desc;

    IssueHandlingProcessStatusEnum(Integer code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer code() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.desc;
    }

    @Override
    public boolean equalsCode(Integer code) {
        return this.code.equals(code);
    }
}
