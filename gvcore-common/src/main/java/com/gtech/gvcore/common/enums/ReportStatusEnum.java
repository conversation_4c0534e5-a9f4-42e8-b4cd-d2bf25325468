package com.gtech.gvcore.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/24 16:51
 */

public enum ReportStatusEnum {
	CREATED(0,"created"),
	PROCESSING(1,"processing"),
	SUCCESS(2,"success"),
	FAILED(3,"failed"),
	NO_DATA(4,"NoData");

	private final Integer code;

	private final String desc;

	ReportStatusEnum(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public Integer getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}

	public static ReportStatusEnum valueOfCode(final Integer status) {

		for (ReportStatusEnum anEnum : ReportStatusEnum.values()) {
			if (anEnum.code.equals(status)) {
				return anEnum;
			}
		}

		return null;
	}
}
