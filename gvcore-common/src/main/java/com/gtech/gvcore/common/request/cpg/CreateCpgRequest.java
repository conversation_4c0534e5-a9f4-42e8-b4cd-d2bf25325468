package com.gtech.gvcore.common.request.cpg;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年2月21日
 */
@Data
public class CreateCpgRequest {

    @ApiModelProperty(value = "issuerCode", example = "MAP", required = true)
    @NotEmpty(message = "issuerCode can not be empty")
    @Length(max = 50)
    private String issuerCode;

    @ApiModelProperty(value = "cpgName", required = true)
    @NotEmpty(message = "cpgName can not be empty")
    @Length(max = 500)
    private String cpgName;

    @ApiModelProperty(value = "cpgTypeCode", required = true)
    @NotEmpty(message = "cpgTypeCode can not be empty")
    @Length(max = 50)
    private String cpgTypeCode;

    @ApiModelProperty(value = "gracePeriods", required = true)
    @NotNull(message = "gracePeriods can not be null")
    @Range(min = 0)
    private Integer gracePeriods;

    @ApiModelProperty(value = "effectiveYears", required = false)
    @Range(min = 0)
    private Integer effectiveYears;

    @ApiModelProperty(value = "effectiveMonth", required = false)
    @Range(min = 0)
    private Integer effectiveMonth;

    @ApiModelProperty(value = "effectiveDay", required = false)
    @Range(min = 0)
    private Integer effectiveDay;

    @ApiModelProperty(value = "effectiveHour", required = false)
    @Range(min = 0)
    private Integer effectiveHour;

    @ApiModelProperty(value = "currencyCode", required = true)
    @NotEmpty(message = "currencyCode can not be empty")
    @Length(max = 50)
    private String currencyCode;

    @ApiModelProperty(value = "denomination", required = true)
    @NotNull(message = "denomination can not be null")
    @Range(min = 0)
    private BigDecimal denomination;

    @ApiModelProperty(value = "articleMopCode", required = true)
    @NotEmpty(message = "articleMopCode can not be empty")
    @Length(max = 50)
    private String articleMopCode;

    @ApiModelProperty(value = "bookletVoucherNum", required = false)
    @Range(min = 2)
    private Integer bookletVoucherNum;

    @ApiModelProperty(value = "createUser", required = true)
    @NotEmpty(message = "createUser can not be empty")
    @Length(max = 50)
    private String createUser;

    @ApiModelProperty(value = "printerCodeList", required = false)
    private List<String> printerCodeList;

}
