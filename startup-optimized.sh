#!/bin/bash

# 优化的JVM启动参数
JAVA_OPTS="-Xms1g -Xmx2g"
JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=200"
JAVA_OPTS="$JAVA_OPTS -XX:+UnlockExperimentalVMOptions"
JAVA_OPTS="$JAVA_OPTS -XX:+UseStringDeduplication"
JAVA_OPTS="$JAVA_OPTS -XX:MetaspaceSize=256m"
JAVA_OPTS="$JAVA_OPTS -XX:MaxMetaspaceSize=512m"
JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="$JAVA_OPTS -XX:HeapDumpPath=./heapdumps/"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCDetails"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCTimeStamps"
JAVA_OPTS="$JAVA_OPTS -Xloggc:./logs/gc.log"

# 减少Spring启动时的内存占用
JAVA_OPTS="$JAVA_OPTS -Dspring.jmx.enabled=false"
JAVA_OPTS="$JAVA_OPTS -Dspring.main.lazy-initialization=true"

# 启动应用
java $JAVA_OPTS -jar gvcore-web/target/gvcore-web-1.3.0.jar

echo "应用启动完成，使用优化的JVM参数"
