package com.gtech.gvcore.aop;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.IssuerHandingUpperLevelTransactionTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.issuehandling.GetIssueHandlingRequest;
import com.gtech.gvcore.common.request.transactiondata.CreateTransactionDataRequest;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.issuehandling.GetIssueHandlingResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.CustomerService;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import com.gtech.gvcore.service.IssueHandlingService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.threadpool.ThreadPoolCenter;

import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName IssueHandlerTransactionAspect
 * @Description IssueHandler 的事务日志增强
 * <AUTHOR>
 * @Date 2022/7/14 10:20
 * @Version V1.0
 **/
@Slf4j
@Aspect
@Component
public class IssueHandlerTransactionAspect {

    @Autowired
    private List<IssueHandlingTransactionDataProgram> issueHandlingTransactionDataProgramList;

    @Autowired
    private CommonIssueHandlingTransactionDataProgram commonIssueHandlingTransactionDataProgram;


    @Pointcut("execution(java.util.List<com.gtech.gvcore.dao.model.IssueHandlingDetails> com.gtech.gvcore.service.IssueHandlerBaseService.execute(..))")
    public void pointcut() {
        // NO SONAR
    }

    @Async
    @AfterReturning(pointcut = "pointcut()", returning = "issueHandlingDetailsList")
    public void checkTokenAroundAspect(final JoinPoint joinPoint, final List<IssueHandlingDetails> issueHandlingDetailsList) {

        final IssueHandlerBaseService issueHandler = joinPoint.getThis() instanceof IssueHandlerBaseService
                ? (IssueHandlerBaseService) joinPoint.getThis()
                : null;

        if (null == issueHandler || null == issueHandler.getIssueHandlingType()) {
            return;
        }

        this.getHandlerProgram(issueHandler.getIssueHandlingType()).execute(issueHandlingDetailsList);
    }

    /**
     * 获取处理程序. 如果没有定制程序程序则返回通用处理程序
     *
     * @param issueHandlingType issueHandlingType
     * @return com.gtech.gvcore.aop.IssueHandlerTransactionAspect.TransactionDataHandleAble 可用的处理程序
     * <AUTHOR>
     * @date 2022/7/14 11:50
     * @since 1.0.0
     */
    private TransactionDataHandleAble getHandlerProgram(final IssueHandlingTypeEnum issueHandlingType) {

        for (IssueHandlingTransactionDataProgram program : this.issueHandlingTransactionDataProgramList) {
            if (program.getIssueHandlingType().equals(issueHandlingType)) {
                return program;
            }
        }

        return this.commonIssueHandlingTransactionDataProgram;
    }

    private interface TransactionDataHandleAble {
        void execute(Collection<IssueHandlingDetails> issueHandlingDetailsList);
    }

    private interface IssueHandlingTransactionDataProgram extends TransactionDataHandleAble {
        IssueHandlingTypeEnum getIssueHandlingType();
    }

    /**
     * @ClassName CommonIssueHandlingTransactionDataProgram
     * @Description 通用IssueHandling事务处理程序.可以处理大部分类型的IssueHandling的TransactionData
     * <AUTHOR>
     * @Date 2022/7/14 11:28
     * @Version V1.0
     **/
    @Component
    private static class CommonIssueHandlingTransactionDataProgram implements TransactionDataHandleAble {

        private final Map<IssueHandlingTypeEnum, List<TransactionTypeEnum>> issueHandlingTypeTransactionTypeMap = new EnumMap<>(IssueHandlingTypeEnum.class);

        @Autowired
        private TransactionDataService transactionDataService;

        @Autowired
        private VoucherService voucherService;

        @Autowired
        protected OutletService outletService;

        @Autowired
        protected CustomerService customerService;

        @Autowired
        protected IssueHandlingService issueHandlingService;

        @Autowired
        private GvCodeHelper gvCodeHelper;

        @Autowired
        private CpgService cpgService;

        @PostConstruct
        public void init() {
            this.issueHandlingTypeTransactionTypeMap.put(IssueHandlingTypeEnum.CANCEL_SALES, Lists.newArrayList(TransactionTypeEnum.GIFT_CARD_CANCEL_ACTIVATE,TransactionTypeEnum.GIFT_CARD_CANCEL_SELL));
            this.issueHandlingTypeTransactionTypeMap.put(IssueHandlingTypeEnum.CANCEL_REDEEM, Lists.newArrayList(TransactionTypeEnum.GIFT_CARD_BULK_CANCEL_REDEEM));
            this.issueHandlingTypeTransactionTypeMap.put(IssueHandlingTypeEnum.BULK_ACTIVATION, Lists.newArrayList(TransactionTypeEnum.GIFT_CARD_ACTIVATE,TransactionTypeEnum.GIFT_CARD_SELL));
            this.issueHandlingTypeTransactionTypeMap.put(IssueHandlingTypeEnum.BULK_REDEEM, Lists.newArrayList(TransactionTypeEnum.GIFT_CARD_REDEEM));
            this.issueHandlingTypeTransactionTypeMap.put(IssueHandlingTypeEnum.BULK_REISSUE, Lists.newArrayList(TransactionTypeEnum.GIFT_CARD_REISSUE));
            this.issueHandlingTypeTransactionTypeMap.put(IssueHandlingTypeEnum.BULK_DEACTIVATE, Lists.newArrayList(TransactionTypeEnum.GIFT_CARD_DEACTIVATE));
            this.issueHandlingTypeTransactionTypeMap.put(IssueHandlingTypeEnum.BULK_REACTIVATE, Lists.newArrayList(TransactionTypeEnum.GIFT_CARD_REACTIVATE));
            this.issueHandlingTypeTransactionTypeMap.put(IssueHandlingTypeEnum.CHANGE_EXPIRY, Lists.newArrayList(TransactionTypeEnum.GIFT_CARD_EXPIRY));
            this.issueHandlingTypeTransactionTypeMap.put(IssueHandlingTypeEnum.RESET_ACTIVATION, Lists.newArrayList(TransactionTypeEnum.RESET_ACTIVATION));
            this.issueHandlingTypeTransactionTypeMap.put(IssueHandlingTypeEnum.RESET_PIN_ACTIVE, null);
        }

        @Override
        public void execute(final Collection<IssueHandlingDetails> issueHandlingDetailsList) {

            if (CollectionUtils.isEmpty(issueHandlingDetailsList)) {
                return;
            }

            final List<String> voucherCodeList = issueHandlingDetailsList.stream()
                    .filter(detail -> IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()))
                    .map(IssueHandlingDetails::getVoucherCode)
                    .distinct()
                    .collect(Collectors.toList());

            //添加NewVoucher
            voucherCodeList.addAll(
                    issueHandlingDetailsList.stream()
                            .filter(detail ->
                                    IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()) &&
                                            StringUtils.isNotBlank(detail.getNewVoucherCode())
                            )
                            .map(IssueHandlingDetails::getNewVoucherCode)
                            .distinct()
                            .collect(Collectors.toList())
            );


            final Map<String, Voucher> voucherCodeVoucherMap = this.voucherService.queryByVoucherCodeList(null, voucherCodeList)
                    .stream().collect(Collectors.toMap(Voucher::getVoucherCode, Function.identity(), (v1, v2) -> v2));

            final Date createTime = new Date();
            List<IssueHandlingDetails> list = (List<IssueHandlingDetails>) issueHandlingDetailsList;
            Result<GetIssueHandlingResponse> issueHandling = issueHandlingService.getIssueHandling(GetIssueHandlingRequest.builder().issueHandlingCode(list.get(0).getIssueHandlingCode()).build());


            int manageSize = 500;
            if (issueHandlingDetailsList.size() > manageSize) {

                int count = issueHandlingDetailsList.size() / manageSize + (issueHandlingDetailsList.size() % manageSize == 0 ? 0 : 1);
                List<IssueHandlingDetails> issueHandlingDetails = new ArrayList<>(issueHandlingDetailsList);
                for (int i = 0; i < count; i++) {

                    int fromIndex = i * manageSize;
                    int toIndex = (i + 1) * manageSize;
                    List<IssueHandlingDetails> saveData = issueHandlingDetails.subList(fromIndex, Math.min(toIndex, issueHandlingDetails.size()));

                    ThreadPoolCenter.commonThreadPoolExecute(() -> saveTransactionData(new ArrayList<>(saveData), voucherCodeVoucherMap, createTime));
                }

            } else {

                saveTransactionData(issueHandlingDetailsList, voucherCodeVoucherMap, createTime);
            }
        }

        private void saveTransactionData(Collection<IssueHandlingDetails> issueHandlingDetailsList, Map<String, Voucher> voucherCodeVoucherMap, Date createTime) {
            final List<CreateTransactionDataRequest> transactionDataRequestList = issueHandlingDetailsList.stream()
                    .filter(Objects::nonNull)
                    .filter(detail -> IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()))
                    .filter(detail -> {
                        final boolean allow = null != this.getTransactionType(detail);
                        if (!allow) {
                            log.error("IssueHandling的TransactionData记录失败, 未找到与之对应的 TransactionType, 已忽略. detail : {}", JSON.toJSONString(detail));
                        }
                        return allow;
                    })
                    .map(detail -> {
                        List<CreateTransactionDataRequest> results= new ArrayList<>();
                        if (null == this.getTransactionType(detail)){
                            log.error("IssueHandling的TransactionData记录失败，未找到TransactionType{}",JSON.toJSONString(detail));
                            return results;
                        }
                        this.getTransactionType(detail).forEach(
                                transactionType -> {
                                    try {

                                        String approveCode = gvCodeHelper.generateApproveCode();
                                        //如果是REISSUE 新券和老券都要添加记录
                                        if (TransactionTypeEnum.GIFT_CARD_REISSUE.equals(transactionType)){
                                            final Voucher voucher = voucherCodeVoucherMap.get(detail.getNewVoucherCode());
                                            log.info("组装交易记录VoucherCodeVoucherMap = {} createTime = {} Detail =  {} TransactionType = {} approvalCode = {} Voucher =  {}",JSON.toJSONString(voucherCodeVoucherMap),createTime,JSON.toJSONString(detail),JSON.toJSONString(transactionType),approveCode,voucher);
                                            final CreateTransactionDataRequest transactionData = getCreateTransactionDataRequest(voucherCodeVoucherMap, createTime, detail, transactionType, approveCode,voucher);
                                            results.add(transactionData);
                                        }
                                        final Voucher voucher = voucherCodeVoucherMap.get(detail.getVoucherCode());
                                        log.info("组装交易记录VoucherCodeVoucherMap = {} createTime = {} Detail =  {} TransactionType = {} approvalCode = {} Voucher =  {}",JSON.toJSONString(voucherCodeVoucherMap),createTime,JSON.toJSONString(detail),JSON.toJSONString(transactionType),approveCode,voucher);
                                        final CreateTransactionDataRequest transactionData = getCreateTransactionDataRequest(voucherCodeVoucherMap, createTime, detail, transactionType, approveCode,voucher);
                                        results.add(transactionData);
                                    } catch (Exception e) {
                                        log.error("CreateTransactionDataRequest 转 CreateTransactionDataRequest 异常,已经忽略该行记录", e);
                                    }
                                }
                        );
                        return results;
                    })
                    .flatMap(List::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());


            if (CollectionUtils.isNotEmpty(transactionDataRequestList)) {
                /*List<CreateTransactionDataRequest> cancelSell = BeanCopyUtils.jsonCopyList(transactionDataRequestList, CreateTransactionDataRequest.class);
                //过滤交易类型为GIFT_CARD_SELL的数据并且设置交易类型为GIFT_CARD_CANCEL_SELL
                cancelSell = cancelSell.stream().filter(x -> TransactionTypeEnum.GIFT_CARD_CANCEL_ACTIVATE.getCode().equals(x.getTransactionType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(cancelSell)) {
                    cancelSell.forEach(x -> x.setTransactionType(TransactionTypeEnum.RESET_ACTIVATION.getCode()));
                    transactionDataRequestList.addAll(cancelSell);
                }*/
                this.transactionDataService.createTransactionDataList(transactionDataRequestList);

            }

        }

        private CreateTransactionDataRequest getCreateTransactionDataRequest(Map<String, Voucher> voucherCodeVoucherMap, Date createTime, IssueHandlingDetails detail, TransactionTypeEnum transactionType, String approveCode, Voucher voucher) {


            final CreateTransactionDataRequest transactionData = new CreateTransactionDataRequest();

            String voucherOwnerCode = Optional.ofNullable(voucher.getVoucherOwnerCode()).orElse("");

            //获取issuerCode
            Result<GetCpgResponse> cpg = cpgService.getCpg(GetCpgRequest.builder().cpgCode(voucher.getCpgCode()).build());
            String issuerCode = cpg.getData().getIssuerCode();


            if (StringUtils.isNotEmpty(detail.getOutletName())) {
                OutletResponse outletByOutletName = outletService.getOutletByOutletName(detail.getOutletName());
                if (null != outletByOutletName) {
                    transactionData.setMerchantCode(outletByOutletName.getMerchantCode());
                    transactionData.setOutletCode(outletByOutletName.getOutletCode());
                    if (StringUtils.isNotBlank(voucherOwnerCode)) {
                        transactionData.setCustomerCode(voucherOwnerCode);
                        Customer customerSummary = customerService.getCustomerSummary(voucherOwnerCode);
                        if (null != customerSummary)
                            transactionData.setCustomerType(customerSummary.getCustomerType());
                    }
                }

            } else if (VoucherOwnerTypeEnum.WAREHOUSE.equalsCode(voucher.getVoucherOwnerType())
                    || VoucherOwnerTypeEnum.OUTLET.equalsCode(voucher.getVoucherOwnerType())) {

                transactionData.setOutletCode(voucherOwnerCode);

                if (StringUtils.isNotBlank(voucherOwnerCode)) {
                    Outlet outlet = outletService.getOutletByCode(voucherOwnerCode);
                    if (null != outlet)
                        transactionData.setMerchantCode(outlet.getMerchantCode());
                }
            } else {
                final IssuerHandingUpperLevelTransactionTypeEnum upperLevel = IssuerHandingUpperLevelTransactionTypeEnum.valueOfIssueHandlingType(this.getIssueHandlingType(detail));

                TransactionData previousTransactionData = this.transactionDataService.getPreviousTransactionData(detail.getVoucherCode(), null == upperLevel ? null : upperLevel.getDesc());
                if (null == previousTransactionData) {
                    previousTransactionData = new TransactionData();
                }
                transactionData.setMerchantCode(previousTransactionData.getMerchantCode());
                transactionData.setOutletCode(previousTransactionData.getOutletCode());
                transactionData.setCustomerCode(voucherOwnerCode);

                if (StringUtils.isNotBlank(voucherOwnerCode)) {

                    Customer customerSummary = customerService.getCustomerSummary(voucherOwnerCode);
                    if (null != customerSummary)
                        transactionData.setCustomerType(customerSummary.getCustomerType());
                }

            }
            transactionData.setInvoiceNumber(ConvertUtils.toString(detail.getInvoiceNo()
                    , this.transactionDataService.getLastInvoiceNo(voucher.getVoucherCode())));

            transactionData.setMopCode(voucher.getMopCode());

            //MER-1884 issuer 从cpg中获取
            transactionData.setIssuerCode(issuerCode);
            transactionData.setTransactionType(transactionType.getCode());
            transactionData.setTransactionId(detail.getApprovalCode());
            transactionData.setCpgCode(voucher.getCpgCode());
            transactionData.setTransactionDate(createTime);
            transactionData.setVoucherCode(voucher.getVoucherCode());
            transactionData.setVoucherCodeNum(Long.valueOf(voucher.getVoucherCode().replaceAll("[a-zA-Z]", "")));
            transactionData.setInitiatedBy(null);
            transactionData.setPosCode(null);
            transactionData.setBatchCode(voucher.getVoucherBatchCode());
            transactionData.setLoginSource(null);
            transactionData.setDenomination(voucher.getDenomination());
            transactionData.setActualOutlet(null);
            transactionData.setForwardingEntityId(null);
            transactionData.setResponseMessage("Transaction successful.");
            transactionData.setTransactionMode(null);
            transactionData.setCustomerSalutation(null);
            transactionData.setCustomerFirstName(null);
            transactionData.setCustomerLastName(null);
            transactionData.setMobile(null);
            transactionData.setVoucherEffectiveDate(voucher.getVoucherEffectiveDate());
//            transactionData.setOtherInputParameter(null);
            transactionData.setSuccessOrFailure("0");
            transactionData.setCreateUser(detail.getCreateUser());
            transactionData.setCreateTime(createTime);
            transactionData.setReferenceNumber(detail.getApprovalCode());
            transactionData.setCardEntryMode("GV POS");
            transactionData.setApproveCode(approveCode);
            transactionData.setNotes(issueHandlingService.getIssueHandling(GetIssueHandlingRequest.builder()
                            .issueHandlingCode(detail.getIssueHandlingCode()).build())
                    .getData().getRemarks());
            return transactionData;
        }

        private IssueHandlingTypeEnum getIssueHandlingType(final IssueHandlingDetails issueHandlingDetails) {
            return IssueHandlingTypeEnum.valueOfCode(issueHandlingDetails.getIssueType());
        }

        private List<TransactionTypeEnum> getTransactionType(final IssueHandlingDetails issueHandlingDetails) {
            return this.issueHandlingTypeTransactionTypeMap.get(this.getIssueHandlingType(issueHandlingDetails));
        }
    }

    /**
     * RESET_PIN_ACTIVE 类型的issueHandling 事务处理程序
     * 这个类型的issueHandling比较特殊,需要单独处理
     *
     * <AUTHOR>
     * @date 2022/7/14 11:40
     * @since 1.0.0
     */
    @Component
    public static class ResetPinActiveIssueHandlingTransactionDataProgram implements IssueHandlingTransactionDataProgram {

        @Override
        public IssueHandlingTypeEnum getIssueHandlingType() {
            return IssueHandlingTypeEnum.RESET_PIN_ACTIVE;
        }

        @Override
        public void execute(final Collection<IssueHandlingDetails> issueHandlingDetailsList) {
            // TODO: 2022/7/14 处理逻辑待定
        }
    }

}
