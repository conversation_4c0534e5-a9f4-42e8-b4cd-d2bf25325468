package com.gtech.gvcore.service;

import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月8日
 */
public interface IssueHandlerBaseService {
	
	
	/**
	 * 
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月8日
	 */
	IssueHandlingTypeEnum getIssueHandlingType();
	
	/**
	 * processStatus 校验结果
	 * result 校验不通过的原因
	 * 
	 * @param details
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月8日
	 */
	List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode);
	
	/**
	 * 
	 * @param details
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月8日
	 */
	List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode);
	
	/**
	 * 
	 * <AUTHOR>
	 * @date 2022年4月14日
	 */
	void afterExecute(IssueHandling issueHandling);

}


