package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.basic.masterdata.web.dao.MasterDataDistrictMapper;
import com.gtech.basic.masterdata.web.entity.MasterDataDistrictEntity;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.enums.GvPrinterStatusEnum;
import com.gtech.gvcore.common.enums.PrinterFtpAuthorizationTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.printer.CreatePrinterRequest;
import com.gtech.gvcore.common.request.printer.GetPrinterRequest;
import com.gtech.gvcore.common.request.printer.QueryPrinterByPageRequest;
import com.gtech.gvcore.common.request.printer.UpdatePrinterRequest;
import com.gtech.gvcore.common.request.printer.UpdatePrinterStatusRequest;
import com.gtech.gvcore.common.response.productcategory.printer.GetPrinterResponse;
import com.gtech.gvcore.common.response.productcategory.printer.QueryPrinterByPageResponse;
import com.gtech.gvcore.common.utils.InputStreamUtils;
import com.gtech.gvcore.dao.dto.PrinterDto;
import com.gtech.gvcore.dao.mapper.PrinterMapper;
import com.gtech.gvcore.dao.model.Printer;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.PrinterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
@Service
@Slf4j
public class PrinterServiceImpl implements PrinterService {

    @Autowired
    private PrinterMapper printerMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private MasterDataDistrictMapper masterDataDistrictMapper;

    @Override
    public Result<Object> createPrinter(CreatePrinterRequest createPrinterRequestParam) {

        Printer printerCheck = new Printer();
        //printerCheck.setIssuerCode(createPrinterRequestParam.getIssuerCode());
        printerCheck.setPrinterName(createPrinterRequestParam.getPrinterName());

        //check printerName
        int count = printerMapper.selectCount(printerCheck);
        if (count > 0) {
            return Result.failed(ResultErrorCodeEnum.PRINTER_NAME_ALREADY_EXISTS.code(), ResultErrorCodeEnum.PRINTER_NAME_ALREADY_EXISTS.desc());
        }

        Printer printer = BeanCopyUtils.jsonCopyBean(createPrinterRequestParam, Printer.class);


        if (PrinterFtpAuthorizationTypeEnum.AUTHORIZATION_TYPE_KEY_FILE.getCode().equals(createPrinterRequestParam.getFtpAuthorizationType())) {

            if (StringUtils.isEmpty(createPrinterRequestParam.getFtpKeyFileUrl())) {
                return Result.failed(ResultErrorCodeEnum.PRINTER_NAME_KEY_FILE_PATH_ISEMPTY.code(), ResultErrorCodeEnum.PRINTER_NAME_KEY_FILE_PATH_ISEMPTY.desc());
            }
            InputStream inputStream = InputStreamUtils.getInputStreamByUrl(createPrinterRequestParam.getFtpKeyFileUrl());
            if (null == inputStream) {
                return Result.failed(ResultErrorCodeEnum.PRINTER_UNABLE_TO_GET_FILE.code(), ResultErrorCodeEnum.PRINTER_UNABLE_TO_GET_FILE.desc());
            }
            try {
                printer.setFtpKeyFile(Base64.getEncoder().encode(InputStreamUtils.toByteArray(inputStream)));
            } catch (IOException e) {
                log.error("inputStream toByteArray error,IOException:{}", e.getMessage());
                return Result.failed(ResultErrorCodeEnum.GENERAL_ERROR.code(), ResultErrorCodeEnum.GENERAL_ERROR.desc());
            }

        }

        printer.setCreateTime(new Date());
        printer.setStatus(GvPrinterStatusEnum.STATUS_ENABLE.getCode());
        printer.setPrinterCode(gvCodeHelper.generatePrinterCode());

        int insertSelectiveCount = printerMapper.insertSelective(printer);

        if (insertSelectiveCount > 0) {
            return Result.ok(printer.getPrinterCode());
        }

        return Result.failed(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code(), ResultErrorCodeEnum.SYSTEM_EXCEPTION.desc());
    }

    @Override
    public PageResult<QueryPrinterByPageResponse> queryPrinterDataByPage(QueryPrinterByPageRequest request) {


        Example example = new Example(Printer.class, true, false);

        /*example.createCriteria().andEqualTo(Printer.C_ISSUER_CODE, request.getIssuerCode()).andLike(
                Printer.C_PRINTER_NAME,
                StringUtils.isEmpty(request.getPrinterName()) ? null : "%" + request.getPrinterName() + "%");*/
        //MER-1884
        example.createCriteria().andLike(
                Printer.C_PRINTER_NAME,
                StringUtils.isEmpty(request.getPrinterName()) ? null : "%" + request.getPrinterName() + "%");

        //更新时间倒序
        example.orderBy(Printer.C_CREATE_TIME).desc();


        PageMethod.startPage(request.getPageNum(), request.getPageSize());

        List<Printer> list = printerMapper.selectByCondition(example);

        //查询省市区
        Map<String, String> districtMap = getDistrictMap(list);

        PageInfo<Printer> pageInfo = new PageInfo<>(list);


        List<QueryPrinterByPageResponse> responses = new ArrayList<>(request.getPageSize());

        list.forEach(item -> {

            QueryPrinterByPageResponse response = new QueryPrinterByPageResponse();
            BeanUtils.copyProperties(item, response);

            response.setStateName(districtMap.get(response.getStateCode()));
            response.setCityName(districtMap.get(response.getCityCode()));
            response.setDistrictName(districtMap.get(response.getDistrictCode()));

            responses.add(response);
        });

        return PageResult.ok(responses, pageInfo.getTotal());
    }

    /**
     * 获取省市区
     *
     * @param item
     * @return
     */
    private Map<String, String> getDistrictMap(List<Printer> item) {

        if (CollectionUtils.isEmpty(item)) {
            return new HashMap<>(1);
        }


        Set<String> stateCodeSet = item.stream().map(Printer::getStateCode).collect(Collectors.toSet());
        Set<String> cityCodeSet = item.stream().map(Printer::getCityCode).collect(Collectors.toSet());
        Set<String> districtCodeSet = item.stream().map(Printer::getDistrictCode).collect(Collectors.toSet());


        Set<String> codesSet = Stream.of(stateCodeSet, cityCodeSet, districtCodeSet).flatMap(Collection::stream).collect(Collectors.toSet());

        Example example = new Example(MasterDataDistrictEntity.class, true, false);
        example.createCriteria().andIn(Printer.C_MASTER_DATA_DISTRICT_DISTRICT_CODE, codesSet);

        List<MasterDataDistrictEntity> districtEntities = masterDataDistrictMapper.selectByExample(example);


        return CollectionUtils.isEmpty(districtEntities) ? new HashMap<>(1) : districtEntities.stream().collect(Collectors.toMap(MasterDataDistrictEntity::getDistrictCode, MasterDataDistrictEntity::getDistrictName));
    }

    @Override
    public Result<Void> updatePrinterStatus(UpdatePrinterStatusRequest request) {

        Printer printer = new Printer();
        printer.setPrinterCode(request.getPrinterCode());

        int i = printerMapper.selectCount(printer);
        if (i <= 0) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        printer.setUpdateTime(new Date());
        printer.setUpdateUser(request.getUpdateUser());
        printer.setStatus(request.getStatus());

        Example example = new Example(Printer.class);
        example.createCriteria().andEqualTo(Printer.C_PRINTER_CODE, request.getPrinterCode());

        int updateCount = printerMapper.updateByConditionSelective(printer, example);
        if (updateCount > 0) {
            return Result.ok();
        }

        return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
    }

    @Override
    public Result<Object> deletePrinterById(String printerCode) {

        Result<Object> noDataFound = getResult(printerCode);

        if (noDataFound != null) {
            return noDataFound;
        }

        Printer printer = new Printer();
        printer.setPrinterCode(printerCode);

        int primaryKey = printerMapper.delete(printer);
        if (primaryKey > 0) {
            return Result.ok();
        }

        return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
    }

    @Override
    public Result<Object> updatePrinter(UpdatePrinterRequest request) {

        //param check
        Result<Object> checkRequestResult = checkRequest(request);
        if (checkRequestResult != null) {
            return checkRequestResult;
        }
        Printer printer = BeanCopyUtils.jsonCopyBean(request, Printer.class);

        if (PrinterFtpAuthorizationTypeEnum.AUTHORIZATION_TYPE_KEY_FILE.getCode().equals(request.getFtpAuthorizationType())) {

            if (StringUtils.isEmpty(request.getFtpKeyFileUrl())) {
                return Result.failed(ResultErrorCodeEnum.PRINTER_NAME_KEY_FILE_PATH_ISEMPTY.code(), ResultErrorCodeEnum.PRINTER_NAME_KEY_FILE_PATH_ISEMPTY.desc());
            }
            InputStream inputStream = InputStreamUtils.getInputStreamByUrl(request.getFtpKeyFileUrl());
            if (null == inputStream) {
                return Result.failed(ResultErrorCodeEnum.PRINTER_UNABLE_TO_GET_FILE.code(), ResultErrorCodeEnum.PRINTER_UNABLE_TO_GET_FILE.desc());
            }
            try {
                printer.setFtpKeyFile(Base64.getEncoder().encode(InputStreamUtils.toByteArray(inputStream)));
            } catch (IOException e) {
                log.error("inputStream toByteArray error,IOException:{}", e.getMessage());
                return Result.failed(ResultErrorCodeEnum.GENERAL_ERROR.code(), ResultErrorCodeEnum.GENERAL_ERROR.desc());
            }

        }

        //更新时间
        printer.setUpdateTime(new Date());
        printer.setPrinterCode(null);

        Example example = new Example(Printer.class);
        example.createCriteria().andEqualTo(Printer.C_PRINTER_CODE, request.getPrinterCode());

        int updateCount = printerMapper.updateByConditionSelective(printer, example);
        if (updateCount > 0) {
            return Result.ok();
        }

        return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
    }

    @Override
    public Result<GetPrinterResponse> getPrinter(GetPrinterRequest request) {

        //数据不存在
        Result<Object> noDataFound = getResult(request.getPrinterCode());
        if (noDataFound != null) {
            return Result.failed(noDataFound.getCode(), noDataFound.getMessage());
        }

        Printer printer = new Printer();
        printer.setPrinterCode(request.getPrinterCode());

        Printer selectOne = printerMapper.selectOne(printer);

        GetPrinterResponse printerResponse = BeanCopyUtils.jsonCopyBean(selectOne, GetPrinterResponse.class);

        List<Printer> printerList = new ArrayList<>();
        printerList.add(selectOne);

        //查询省市区
        Map<String, String> districtMap = getDistrictMap(printerList);

        printerResponse.setStateName(districtMap.get(printerResponse.getStateCode()));
        printerResponse.setCityName(districtMap.get(printerResponse.getCityCode()));
        printerResponse.setDistrictName(districtMap.get(printerResponse.getDistrictCode()));

        return Result.ok(printerResponse);
    }

    private Result<Object> checkRequest(UpdatePrinterRequest request) {

        Printer printer = new Printer();
        printer.setPrinterCode(request.getPrinterCode());

        printer = printerMapper.selectOne(printer);
        if (printer == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        if (printer.getPrinterName().equals(request.getPrinterName())) {
            return null;
        }

        Printer printerCheck = new Printer();
        printerCheck.setIssuerCode(printer.getIssuerCode());
        printerCheck.setPrinterName(request.getPrinterName());

        // check printerName
        int count = printerMapper.selectCount(printerCheck);

        if (count > 0) {
            return Result.failed(ResultErrorCodeEnum.PRINTER_NAME_ALREADY_EXISTS.code(), ResultErrorCodeEnum.PRINTER_NAME_ALREADY_EXISTS.desc());
        }
        return null;
    }

    /**
     * check 数据是否存在
     *
     * @param printerCode
     * @return
     */
    private Result<Object> getResult(String printerCode) {

        Printer printer = new Printer();
        printer.setPrinterCode(printerCode);

        int i = printerMapper.selectCount(printer);
        if (i <= 0) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        return null;
    }

    @Override
    public List<Printer> queryBasicInfoByPrinterCodeList(List<String> printerCodeList) {

        if (CollectionUtils.isEmpty(printerCodeList)) {
            return Collections.emptyList();
        }
        return printerMapper.queryBasicInfoByPrinterCodeList(printerCodeList);
    }

    @Override
    public int countByPrinterCodeList(List<String> printerCodeList) {

        /*if (StringUtils.isBlank(issuerCode) || CollectionUtils.isEmpty(printerCodeList)) {
            return 0;
        }*/
        if (CollectionUtils.isEmpty(printerCodeList)) {
            return 0;
        }

        PrinterDto dto = new PrinterDto();
        //dto.setIssuerCode(issuerCode);
        dto.setPrinterCodeList(printerCodeList);
        return printerMapper.countByPrinterCodeList(dto);
    }
}
