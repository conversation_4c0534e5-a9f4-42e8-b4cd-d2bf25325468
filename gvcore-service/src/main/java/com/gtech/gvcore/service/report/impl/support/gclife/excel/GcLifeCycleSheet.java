package com.gtech.gvcore.service.report.impl.support.gclife.excel;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName GcLifeCycleSheet
 * @Description Gift Card Life Cycle Sheet
 * <AUTHOR> based on VoucherLifeCycleSheet
 * @Date 2025年6月19日
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcLifeCycleSheet {

    private String fillKey;

    private String sheetName;

    private List<?> data;

    public static GcLifeCycleSheet newSheet(String fillKey, ReportExportTypeEnum exportTypeEnum, List<?> data) {

        return new GcLifeCycleSheet()
                .setFillKey(fillKey)
                .setSheetName(exportTypeEnum.getSheetName())
                .setData(data);
    }

}
