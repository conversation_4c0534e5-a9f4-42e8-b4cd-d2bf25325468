package com.gtech.gvcore.service.report.export.snapshoot;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.mapper.OrderReportDataMapper;
import com.gtech.gvcore.dao.mapper.ReportRequestMapper;
import com.gtech.gvcore.dao.model.OrderReportData;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.service.report.export.snapshoot.label.OrderReportDataLabelComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName OrderReportDataHelper
 * @Description 订单报表执行结果帮助类
 * <AUTHOR>
 * @Date 2022/8/22 18:02
 * @Version V1.0
 **/
@Slf4j
@Component
public class ReportDataHelper {

    /**
     *
     * 分页数据大小
     * 该数据不会轻易变动
     * 默认一条数据存储3000条对应数据
     * index 取值为  0 3000 6000 9000 12000 ... 以此类推
     *
     */
    public static final int DEFAULT_PAGE_SIZE = 3000;

    @Autowired private OrderReportDataMapper orderReportDataMapper;
    @Autowired private ReportRequestMapper reportRequestMapper;
    @Autowired private OrderReportDataLabelComponent orderReportDataLabelComponent;
    @Autowired private ReportAmountFilter reportAmountFilter;

    /**
     * 存储报表数据
     */
    @SuppressWarnings("unused")
    @Transactional
    public void saveReportData(String reportCode, ReportExportTypeEnum reportExportTypeEnum, List<?> dataList, int pageSize) {

        //empty
        if (CollectionUtils.isEmpty(dataList)) return;

        //init
        Integer exportType = reportExportTypeEnum.getExportType();

        //save report data
        saveReportData(reportCode, exportType, dataList, pageSize);

        //total json
        Map<String, Integer> totalJson = new HashMap<>();
        totalJson.put(String.valueOf(exportType), dataList.size());

        //update total
        updateReportTotal(reportCode, totalJson, pageSize);
    }

    @Transactional
    public void saveReportHeadHtml (String reportCode, String headHtml) {

        reportRequestMapper.updateByConditionSelective(new ReportRequest().setHeadHtml(headHtml),
                Example.builder(ReportRequest.class)
                        .where(Sqls.custom().andEqualTo(ReportRequest.C_REPORT_CODE, reportCode))
                        .build()
        );

    }

    /**
     * 存储报表数据
     */
    @Transactional
    public void saveReportData(String reportCode, Map<ReportExportTypeEnum, List<?>> reportData) {

        //total
        Map<String, Integer> totalJson = new HashMap<>();

        //foreach data
        reportData.forEach((k , v) -> {

            //empty
            if (CollectionUtils.isEmpty(v)) return;

            //init
            Integer exportType = k.getExportType();

            //save report data
            saveReportData(reportCode, exportType, v, DEFAULT_PAGE_SIZE);

            //add total
            totalJson.put(String.valueOf(exportType), v.size());
        });

        //update total
        updateReportTotal(reportCode, totalJson, DEFAULT_PAGE_SIZE);
    }

    /**
     * 存储报表数据
     */
    private void saveReportData(String reportCode, Integer reportType, List<?> dataList, int pageSize) {

        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }
        //init
        final int size = dataList.size();
        final int number = size % pageSize > 0 ? (size / pageSize) + 1 : (size / pageSize);
        final List<OrderReportData> saveList = new ArrayList<>();

        for (int i = 0; i < number; i++) {

            //开始下标 = 当前循环 * 每页数量
            int beginIndex = i * pageSize;
            //结束下标 = 开始下标 + 每页数量
            int endIndex = Math.min((beginIndex + pageSize), size);
            //当前页数据
            List<?> pageList = dataList.subList(beginIndex, endIndex);
            //save
            saveList.add(new OrderReportData()
                    .setReportIndex(beginIndex)
                    .setOrderReportCode(reportCode)
                    .setReportType(reportType)
                    .setReportData(JSON.toJSONString(pageList)));
        }

        //save
        // 这里单条插入为了避免出现请求数据过载
        saveList.forEach(orderReportDataMapper::insert);
        HintManager.clear();
    }

    /**
     * 保存报表分断构建产生的数据
     * @param reportCode
     * @param reportExportTypeEnum
     * @param dataList
     * @param beginIndex
     */
    public void savePieceReportData(String reportCode, ReportExportTypeEnum reportExportTypeEnum, List<?> dataList, int beginIndex) {

        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }
        //save
        orderReportDataMapper.insertSelective(new OrderReportData()
                .setReportIndex(beginIndex)
                .setOrderReportCode(reportCode)
                .setReportType(reportExportTypeEnum.getExportType())
                .setReportData(JSON.toJSONString(dataList)));
        HintManager.clear();
    }

    /**
     * 结束报表数据分段构建
     * @param reportCode
     * @param reportExportTypeEnum
     * @param dataList
     * @param beginIndex
     */
    public void finishPieceReportData(String reportCode, ReportExportTypeEnum reportExportTypeEnum, List<?> dataList, int beginIndex, int pageSize, int total) {
        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }

        Integer exportType = reportExportTypeEnum.getExportType();

        if (CollectionUtils.isNotEmpty(dataList)) {

            //save
            orderReportDataMapper.insertSelective(new OrderReportData()
                    .setReportIndex(beginIndex)
                    .setOrderReportCode(reportCode)
                    .setReportType(exportType)
                    .setReportData(JSON.toJSONString(dataList)));
        }

        //total json
        Map<String, Integer> totalJson = new HashMap<>();
        totalJson.put(String.valueOf(exportType), total);

        updateReportTotal(reportCode, totalJson, pageSize);
        HintManager.clear();
    }

    /**
     * 报表构建异常清空原始数据
     * @param reportCode
     */
    public void errorRemovePieceReportData(String reportCode) {
        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }

        orderReportDataMapper.delete(new OrderReportData().setOrderReportCode(reportCode));
        HintManager.clear();
    }

    /**
     * 更新主表total
     */
    private void updateReportTotal(String reportCode, Map<String, Integer> totalJson, int pageSize) {

        //init
        final Example example = new Example(ReportRequest.class);
        example.createCriteria()
                //report code
                .andEqualTo(ReportRequest.C_REPORT_CODE, reportCode);

        //update
        ReportRequest orderReport = new ReportRequest()
                .setTotalJson(JSON.toJSONString(totalJson));

        if (DEFAULT_PAGE_SIZE != pageSize) orderReport.setPageSize(pageSize);

        reportRequestMapper.updateByConditionSelective(orderReport, example);
    }

    /**
     * 查询报表
     */
    public QueryOrderReportDataResponse selectReportData(String reportCode, Integer reportType, int pageSize, int pageNumber) {

        //default result
        final QueryOrderReportDataResponse result = new QueryOrderReportDataResponse()
                .setList(Collections.emptyList())
                .setLabelArray(Collections.emptyList());

        final ReportRequest reportRequest = this.reportRequestMapper.selectOne(new ReportRequest()
                .setReportCode(reportCode));
        if (null == reportRequest) return result;

        int dataPageSize = null == reportRequest.getPageSize() ? DEFAULT_PAGE_SIZE : reportRequest.getPageSize();

        // init index
        final int dataBeginIndex = pageSize * (pageNumber - 1);
        final int index = (dataBeginIndex / dataPageSize) * dataPageSize;

        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }

        //find
        final OrderReportData orderReportData = this.orderReportDataMapper
                .selectOne(new OrderReportData()
                        .setReportType(reportType)
                        .setOrderReportCode(reportCode)
                        .setReportIndex(index));

        HintManager.clear();

        //empty
        if (null == orderReportData) return result;

        //head html
        final String headHtml = reportRequest.getHeadHtml();

        // 取当前结果中对应的起始下标
        int findIndex = dataBeginIndex % dataPageSize;

        //get result type
        final Integer convertReportType = null != reportType ? reportType : reportRequest.getReportType();
        Class<?> resultType = OrderReportDataLabelComponent.getResultType(convertReportType);
        if (null == resultType) return result;

        //bean copy
        final List<?> pageResult = BeanCopyUtils.jsonCopyList(orderReportData.getReportData(), resultType);

        // 查询下标 大于 结果大小
        if (findIndex > pageResult.size()) return result;

        //构建结果集
        final List<?> pageResultData = pageResult.subList(findIndex, Math.min(findIndex + pageSize, pageResult.size()));
        //查询对应类型的label
        final List<QueryOrderReportDataResponse.LabelBean> labelArray = orderReportDataLabelComponent.getLabelArray(resultType, reportRequest, pageResultData);

        //total
        long total;
        String totalJsonString = reportRequest.getTotalJson();
        if (StringUtils.isEmpty(totalJsonString)) total = 0L;
        else total = ConvertUtils.toLong(JSON.parseObject(totalJsonString).getLong(String.valueOf(convertReportType)), 0L);

        //response
        QueryOrderReportDataResponse response = result.setList(pageResultData)
                .setLabelArray(labelArray)
                .setTotal(total)
                .setHeadHtml(headHtml);

        //return
        return reportAmountFilter.filter(response);
    }

}