package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.ReactivatedForDetailBean;
import com.gtech.gvcore.service.report.impl.bo.ReactivatedBo;
import com.gtech.gvcore.service.report.impl.param.ReactivatedQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-22 14:24
 */
@Service
public class ReactivatedForDetailImpl extends ReportSupport
        implements BusinessReport<ReactivatedQueryData, ReactivatedForDetailBean>, SingleReport {

    @Autowired protected IssueHandlingDetailsService issueHandlingDetailsService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.REACTIVATE_DETAILED_REPORT;
    }

    @Override
    public ReactivatedQueryData builderQueryParam(CreateReportRequest reportParam) {
        ReactivatedQueryData queryData = new ReactivatedQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());

        queryData.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());
        queryData.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        queryData.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());

        return queryData;
    }

    @Override
    public List<ReactivatedForDetailBean> getExportData(ReactivatedQueryData queryData) {
        //find
        final Collection<ReactivatedBo> list =
                Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectReactivated, queryData))
                        .orElse(Collections.emptyList());

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, ReactivatedBo::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, ReactivatedBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, ReactivatedBo::getCpgCode, Cpg.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, ReactivatedBo::getCustomerCode, Customer.class);
        final Map<String, String> voucherMap = issueHandlingDetailsService.queryRemarkByVoucherCodeAndIssueType(list.stream().map(ReactivatedBo::getVoucherCode)
                .distinct().collect(Collectors.toList()), IssueHandlingTypeEnum.BULK_REACTIVATE);

        //convert result
        return list.stream()
                .map(e -> new ReactivatedForDetailBean()
                        .setVoucherAmount(super.toAmount(ConvertUtils.toBigDecimal(e.getDenomination(), BigDecimal.ZERO)))
                        .setVoucherNumber(e.getVoucherCode())
                        .setCreatedOn(DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                        .setExpiryDate(DateUtil.format(e.getVoucherEffectiveDate(), DateUtil.FORMAT_DEFAULT))
                        .setInvoiceNumber(e.getInvoiceNumber())
                        .setDeactivateReason(voucherMap.get(e.getVoucherCode()))
                        .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setCompanyName(customerMap.findValue(e.getCustomerCode()).getCompanyName()))
                .collect(Collectors.toList());
    }

}
