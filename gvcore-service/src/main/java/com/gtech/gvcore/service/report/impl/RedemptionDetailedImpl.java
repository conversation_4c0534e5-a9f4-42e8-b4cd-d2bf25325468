package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.RedemptionDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.RedemptionBo;
import com.gtech.gvcore.service.report.impl.param.RedemptionQueryData;
import com.gtech.gvcore.service.report.impl.support.RedemptionHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:00
 * @Description:
 */
@Service
public class RedemptionDetailedImpl extends ReportSupport
        implements BusinessReport<RedemptionQueryData, RedemptionDetailedBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.REDEMPTION_DETAILED_REPORT;
    }

    @Override
    public RedemptionQueryData builderQueryParam(CreateReportRequest reportParam) {

        ReportParamConvertHelper.convertQueryDateCompanyCodeToMerchantCode(reportParam);

        return RedemptionHelper.getQueryData(reportParam);
    }

    @Override
    public List<RedemptionDetailedBean> getExportData(RedemptionQueryData queryData) {

        List<RedemptionBo> list = getBoList(queryData);

        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<Voucher> voucherMap = super.getMapByCode(list, RedemptionBo::getVoucherCode, Voucher.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, RedemptionBo::getOutletCode, Outlet.class);
        final JoinDataMap<Pos> posMap = super.getMapByCode(list, RedemptionBo::getPosCode, Pos.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, RedemptionBo::getCpgCode, Cpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, RedemptionBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(voucherMap.values(), Voucher::getVoucherOwnerCode, Customer.class);
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);

        return list.stream()
                .map(e -> {

                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Pos pos = posMap.findValue(e.getPosCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final Voucher voucher = voucherMap.findValue(e.getVoucherCode());
                    final Customer customer = customerMap.findValue(voucher.getVoucherOwnerCode());
                    final Cpg cpg = cpgMap.findValue(e.getCpgCode());
                    final Company company = companyMap.findValue(merchant.getCompanyCode());

                    final String posName = null != pos && StringUtil.isNotEmpty(pos.getPosName()) ? pos.getPosName() : "GV POS";
                    final String denomination = super.toAmount(e.getDenomination());
                    final String transactionDate = DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);

                    return new RedemptionDetailedBean()
                            .setCardNumber(voucher.getVoucherCode())
                            .setBookletNumber(ConvertUtils.toString(voucher.getBookletCode(), "NA"))
                            .setBatchNumber(voucher.getVoucherBatchCode())
                            .setMerchant(merchant.getMerchantName())
                            .setOutlet(outlet.getOutletName())
                            .setDescriptiveOutletName(company.getCompanyName())
                            .setOutletCode(outlet.getBusinessOutletCode())
                            .setTransactionDate(transactionDate)
                            .setPosName(posName)
                            .setVoucherProgramGroup(cpg.getCpgName())
                            .setTransactionType(TransactionTypeEnum.getTypeDesc(e.getTransactionType()))
                            .setAmount(denomination)
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setResponseMessage(e.getResponseMessage())
                            .setDateAtClient(transactionDate)
                            .setPreTransactionVoucherDenomination(denomination)
                            .setTransactionPostDate(transactionDate)
                            .setNotes(e.getNotes())
                            .setExpiryDate(DateUtil.format(e.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                            .setIssuanceYear(DateUtil.format(voucher.getCreateTime(), DateUtil.FORMAT_YYYY))
                            .setReferenceNumber(e.getReferenceNumber())
                            .setBatchNumber(e.getBatchCode())
                            .setRequestAmount(denomination)
                            .setApprovalCode(e.getApproveCode())
                            .autoFull(customer, RedemptionDetailedBean.class);

                }).collect(Collectors.toList());

    }

    private List<RedemptionBo> getBoList(RedemptionQueryData queryData) {

        return Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectRedemption, queryData))
                .map(e -> e.stream().filter(d -> TransactionTypeEnum.GIFT_CARD_REDEEM.equalsCode(d.getTransactionType())).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

}