package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2023-01-06 11:33
 */

@Setter
@Getter
@Accessors(chain = true)
public class PartnerActivationDetailBo {

    private String cardNumber;

    private String bookletNumber;

    private String sbuCompanyName;

    private String merchant;

    private String outlet;

    private String outletCode;

    // 留空
    private String outletGroup;

    private String outletType;

    private String region;

    private String issuer;

    private String corporateName;

    private String departmentDivisionBranch;

    private String customerFullName;

    private String firstName;

    private String lastName;

    private String email;

    private String transactionDate;

    private String transactionTime;

    private String transactionTimeZone = TimeZone.getDefault().getID();

    private String invoiceDate;

    private String invoiceTime;

    private String posName;

    private String initiatedBy;

    private String voucherProgramGroup;

    private String voucherProgramGroupType;

    private String transactionType = "GIFT VOUCHER ACTIVATE ONLY";

    private String baseCurrency;

    private String amount;

    private String transactingMerchantCurrency;

    private String responseMessage;

    private String denomination;

    private String expiryDate;

    private String issuanceYear;

    private String referenceNumber;

    private String originalCardNumberBeforeReissue;

    private String cardEntryMode;

    private String batchNumber;

    private String approvalCode;

    private String mopCode;
}
