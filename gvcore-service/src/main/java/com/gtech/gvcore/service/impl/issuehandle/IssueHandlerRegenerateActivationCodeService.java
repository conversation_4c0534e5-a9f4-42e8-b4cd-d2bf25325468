package com.gtech.gvcore.service.impl.issuehandle;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.gtech.basic.filecloud.api.model.Resp;
import com.gtech.basic.filecloud.commons.PagedData;
import com.gtech.basic.filecloud.commons.PagedDatas;
import com.gtech.basic.filecloud.exports.excel.spec.ExcelExportSpec;
import com.gtech.basic.filecloud.exports.management.FileExport;
import com.gtech.basic.filecloud.exports.management.FileExportManager;
import com.gtech.basic.filecloud.exports.management.FileExportResult;
import com.gtech.commons.page.PageData;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.*;
import com.gtech.gvcore.common.response.issuehandling.RegenerateActivationCodeToEmailResponse;
import com.gtech.gvcore.dao.dto.IssueHandlingDetailsDto;
import com.gtech.gvcore.dao.mapper.IssueHandlingDetailsMapper;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.helper.FileCompressionHelper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import com.gtech.gvcore.service.IssuerService;
import com.gtech.gvcore.service.impl.MessageComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IssueHandlerRegenerateActivationCodeService extends IssueHandlerValidateService implements IssueHandlerBaseService {

	@Autowired
	private VoucherNumberHelper voucherNumberHelper;


	@Autowired
	private IssueHandlingDetailsMapper issueHandlingDetailsMapper;

	@Autowired
	private GvCodeHelper gvCodeHelper;

	@Autowired
	private FileExportManager fileExportManager;

	@Autowired
	private MessageComponent messageComponent;

	@Autowired
	private CpgService cpgService;

	@Autowired
	private IssuerService issuerService;

	@Autowired
	private OssHelper ossHelper;



	@Override
	public IssueHandlingTypeEnum getIssueHandlingType() {
		return IssueHandlingTypeEnum.RESET_ACTIVATION;
	}

	@Override
	public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {
		checkIfExist(details, getIssueHandlingType(), issuerCode);

		checkEmail(details);




		return details;
	}

	private static void checkEmail(List<IssueHandlingDetails> details) {
		for (IssueHandlingDetails detail : details) {
			if (StringUtil.isEmpty(detail.getReceiverEmail())) {
				String result = "Email can't be empty!";
				if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
					detail.setResult(detail.getResult() + result);
				} else {
					detail.setResult(result);
				}
				detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			}
			//校验email格式
			if (!isValidEmail(detail.getReceiverEmail())) {
				String result = "Email format is incorrect!";
				if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
					detail.setResult(detail.getResult() + result);
				} else {
					detail.setResult(result);
				}
				detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			}
		}
	}

	public static boolean isValidEmail(String email) {
		String emailRegex = "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
		return email.matches(emailRegex);
	}



	@Override
	public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {
		validate(details, issuerCode);
		if (CollectionUtils.isEmpty(details)) {
			return Collections.emptyList();
		}
		CountDownLatch downlatch = new CountDownLatch(details.size());
		List<IssueHandlingDetails> returnList = new CopyOnWriteArrayList<>();
		for (IssueHandlingDetails issueHandlingDetails : details) {
			EXECUTOR.execute(() -> {
				try {
					returnList.add(issueHandlingDetails);
					makeIssueHandling(issueHandlingDetails);
				} catch (Exception e) {
					String msg = e.getMessage();
					log.error(e.getMessage(), e);
					issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
					issueHandlingDetails.setResult(msg.length() > 500 ? msg.substring(0, 499) : msg);
				} finally {
					downlatch.countDown();
				}
			});
		}
		try {
			downlatch.await();
		} catch (InterruptedException e) {
			log.error(e.getMessage(), e);
			Thread.currentThread().interrupt();
		}

		return returnList;
	}

	public void makeIssueHandling(IssueHandlingDetails issueHandlingDetails) {
		if (IssueHandlingProcessStatusEnum.FAILED.code().equals(issueHandlingDetails.getProcessStatus())) {
			return;
		}
		String voucherCode = issueHandlingDetails.getVoucherCode();
		Example example = new Example(Voucher.class);
		example.createCriteria().andEqualTo(Voucher.C_VOUCHER_CODE, voucherCode);
		Voucher voucher = new Voucher();
		voucher.setVoucherActiveCode(voucherNumberHelper.activationCode());
		voucher.setVoucherActiveUrl(activeUrl  + voucher.getVoucherActiveCode());
		int count = voucherMapper.updateByConditionSelective(voucher, example);
		if (count == 0) {
			issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			issueHandlingDetails.setResult(ResultErrorCodeEnum.DATA_MISS.code() + "【" + voucherCode + "】");
		} else {
			issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
		}
	}


	@Override
	public void afterExecute(IssueHandling issueHandling) {
		if (issueHandling == null) {
			return;
		}
		IssueHandlingDetailsDto dto = new IssueHandlingDetailsDto();
		dto.setIssueHandlingCode(issueHandling.getIssueHandlingCode());
		List<IssueHandlingDetails> details = issueHandlingDetailsMapper.queryDigitalByIssueHandlingCode(dto);
		if (CollectionUtils.isEmpty(details)) {
			return;
		}
		Map<String, List<IssueHandlingDetails>> map = details.stream().collect(Collectors.groupingBy(IssueHandlingDetails::getReceiverEmail));
		map.forEach((email, v) -> {
			try {
				sendToPic(issueHandling.getIssueHandlingCode(), details.get(0).getUpdateUser(), email, v.stream().map(IssueHandlingDetails::getVoucherCode).collect(Collectors.toList()));
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			}
		});
	}



	public void resendRegenerateActivationCodeEmail(String issueHandlingCode,String email,String voucherCode,String updateUser){
		IssueHandlingDetailsDto dto = new IssueHandlingDetailsDto();
		dto.setIssueHandlingCode(issueHandlingCode);
		List<IssueHandlingDetails> details = issueHandlingDetailsMapper.queryDigitalByIssueHandlingCode(dto);
		if (CollectionUtils.isEmpty(details)) {
			return;
		}

		//根据券号获取源数据
		Map<String, IssueHandlingDetails> issueHandlingDetailsMap = details.stream().collect(Collectors.toMap(IssueHandlingDetails::getVoucherCode, x -> x));
		IssueHandlingDetails issueHandling = issueHandlingDetailsMap.get(voucherCode);
		if (null == issueHandling) {
			return;
		}

		Map<String, List<IssueHandlingDetails>> byEmail = details.stream().collect(Collectors.groupingBy(IssueHandlingDetails::getReceiverEmail));


		//获取源email所有的券
		List<String> voucherList=  byEmail.get(issueHandling.getReceiverEmail()).stream().map(IssueHandlingDetails::getVoucherCode).collect(Collectors.toList());

		sendToPic(issueHandlingCode, updateUser, email,voucherList);

	}


	private void sendToPic(String issueHandlingCode,String  updateUser, String email, List<String> voucherCodeList) {
		String fileName = gvCodeHelper.generateFileNameCode();
		PageData<RegenerateActivationCodeToEmailResponse> pageData = new PageData<>();

		List<Voucher> voucherList = voucherService.queryByVoucherCodeList(null, voucherCodeList);
		if (CollectionUtils.isEmpty(voucherList)) {
			return;
		}

		List<RegenerateActivationCodeToEmailResponse> list = new ArrayList<>();
		for (Voucher voucher : voucherList) {
			RegenerateActivationCodeToEmailResponse response = new RegenerateActivationCodeToEmailResponse();
			response.setVoucherCode(voucher.getVoucherCode());
			response.setNewActivationCode(voucher.getVoucherActiveCode());
			list.add(response);
		}
		pageData.setList(list);
		// 电子券excel
		String secretCode = voucherNumberHelper.randomPassword(10);
		PagedData<RegenerateActivationCodeToEmailResponse> voucherData = PagedDatas
				.<RegenerateActivationCodeToEmailResponse, PageData<RegenerateActivationCodeToEmailResponse>>builder()
				.query(() -> pageData)
				.queryResultConverter(PageData::getList)
				.hasNextPage(pageResult -> false)
				.afterQuery(() -> log.info("do nothing"))
				.build();

		ExcelExportSpec.SheetBuilder<RegenerateActivationCodeToEmailResponse> sheet = ExcelExportSpec.builder()
				.sheet(RegenerateActivationCodeToEmailResponse.class, "Digital Voucher")
				.dataSource(voucherData);



		FileExport fileExport = FileExport.builder()
				.domainCode(GvcoreConstants.SYSTEM_DEFAULT)
				.tenantCode(GvcoreConstants.SYSTEM_DEFAULT)
				.userId(updateUser)
				.name(fileName + ".xlsx")
				.category("voucher.digital")
				.spec(sheet.build())
				.build();

		String accessUrl = "";
		Resp<FileExportResult> result = FileExportResult.from(fileExportManager.export(fileExport));
		accessUrl = result.getData().getAccessUrl();
		final FileCompressionHelper.EncryptCompressionParameter parameter = FileCompressionHelper.EncryptCompressionParameter.builder()
				.fileUrl(accessUrl)
				.fileName(fileName + ".xlsx")
				.password(secretCode)
				.build();
		accessUrl = ossHelper.compressionUploadToOss(parameter, fileName);

		List<String> cpgCodeList = new ArrayList<>();
		List<String> issuerCodeList = new ArrayList<>();
		BigDecimal voucherAmount = BigDecimal.ZERO;
		for (Voucher voucher : voucherList) {
			cpgCodeList.add(voucher.getCpgCode());
			issuerCodeList.add(voucher.getIssuerCode());
			voucherAmount = voucherAmount.add(voucher.getDenomination());
		}

		Map<String, Object> extendParams = new HashMap<>();
		extendParams.put("fileName", fileName + ".zip");
		extendParams.put("businessCode", issueHandlingCode);
		extendParams.put("email", email);
		//发送EXCEL邮件 TEST
		sendExcelToEmail(fileName, ossHelper.grantAccessUrl(accessUrl), extendParams);

		sendPwdToEmail(secretCode, extendParams,fileName);

	}

	private void sendPwdToEmail(String pwd, Map<String, Object> extendParams,String fileName) {
		JSONObject messageRequest = new JSONObject();
		messageRequest.put("eventCode", MessageEnventEnum.REGENERATE_ACTIVATION_CODE_PWD.getCode());
		JSONObject param = new JSONObject();
		param.putAll(extendParams);
		param.put("password", pwd);
		messageRequest.put("param", param);
		messageComponent.send(messageRequest);
	}

	private void sendExcelToEmail(String fileName, String fileUrl, Map<String, Object> extendParams) {
		JSONObject messageRequest = new JSONObject();
		messageRequest.put("eventCode", MessageEnventEnum.REGENERATE_ACTIVATION_CODE.getCode());
		JSONObject param = new JSONObject();
		param.putAll(extendParams);
		JSONArray attachments = new JSONArray();
		JSONObject files = new JSONObject();
		files.put("filename", fileName + ".zip");
		files.put("url", fileUrl);
		attachments.add(files);
		param.put("attachments", attachments);
		messageRequest.put("param", param);
		messageComponent.send(messageRequest);
	}

}
