package com.gtech.gvcore.service.report.impl.support;

import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bo.PartnerDetailBo;
import com.gtech.gvcore.service.report.impl.param.PartnerDetailQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName PartnerDetailBase
 * @Description
 * <AUTHOR>
 * @Date 2023/1/10 19:10
 * @Version V1.0
 **/
public abstract class PartnerDetailBaseImpl<R> extends ReportSupport
        implements BusinessReport<PartnerDetailQueryData, R>, SingleReport {

    @Value("${gv.outlet.warehouse.MV01}")
    private String mv01Code;

    @Override
    public PartnerDetailQueryData builderQueryParam(CreateReportRequest reportParam) {

        //is run report
        List<String> outletCodes = reportParam.getOutletCodes();
        if (CollectionUtils.isEmpty(outletCodes) || !outletCodes.contains(mv01Code)) {
            ReportContextHelper.noData();
            return PartnerDetailQueryData.EMPTY_PARAM;
        }

        //init
        PartnerDetailQueryData partnerDetailQueryData = new PartnerDetailQueryData();

        //outlet
        partnerDetailQueryData.setOutletCode(mv01Code);

        //transactionDateStart
        partnerDetailQueryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        partnerDetailQueryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        //customer
        partnerDetailQueryData.setTransactionCorporateName(reportParam.getTransactionCorporateName());

        //cpg
        partnerDetailQueryData.setCpgCodeList(partnerDetailQueryData.getCpgCodeList());

        //voucher number
        partnerDetailQueryData.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        partnerDetailQueryData.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        //invoiceNumber
        partnerDetailQueryData.setInvoiceNumber(reportParam.getInvoiceNo());

        return partnerDetailQueryData;
    }

    public final List<PartnerDetailBo> getBoList(PartnerDetailQueryData param) {

        return Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::partnerSalesDetail, param))
                .map(e -> e.stream().filter(s -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(s.getTransactionType())).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    /**
     * filter data
     * @param boList bo list
     * @param matchVoucherStatus match voucher status
     * @return
     */
    protected final List<PartnerDetailBo> filterDate(List<PartnerDetailBo> boList,
                                                   ReportVoucherStatusEnum matchVoucherStatus) {

        //voucher
        final JoinDataMap<Voucher> voucherMap = this.getMapByCode(boList, PartnerDetailBo::getVoucherCode, Voucher.class);

        //voucher status
        return boList.stream()
                .filter(e -> voucherMap.containsKey(e.getVoucherCode()))
                .filter(e -> {

                    final Voucher voucher = voucherMap.findValue(e.getVoucherCode());
                    ReportVoucherStatusEnum voucherStatus = this.getVoucherStatus(voucher);

                    e.setVoucher(voucher);

                    return voucherStatus == matchVoucherStatus;
                }).collect(Collectors.toList());
    }

}
