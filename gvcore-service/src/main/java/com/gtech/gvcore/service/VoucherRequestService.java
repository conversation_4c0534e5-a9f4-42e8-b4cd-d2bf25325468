package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.voucherrequest.ApproveVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.BulkApproveVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.CancelVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.CreateVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.GetVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.QueryHeadOfficeAndWareHouseRequest;
import com.gtech.gvcore.common.request.voucherrequest.QueryVoucherRequestRequest;
import com.gtech.gvcore.common.request.voucherrequest.UpdateVoucherRequestRequest;
import com.gtech.gvcore.common.response.voucherrequest.GetVoucherRequestResponse;
import com.gtech.gvcore.common.response.voucherrequest.QueryHeadOfficeAndWareHouseResponse;
import com.gtech.gvcore.common.response.voucherrequest.QueryVoucherRequestResponse;
import com.gtech.gvcore.dao.model.VoucherRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 9:53
 */


public interface VoucherRequestService {
    /**
     * Create voucher request
     *
     * @param createVoucherRequestRequest request data
     * @param requestOrReturn
     * @return result
     */
    Result<String> addVoucherRequest(CreateVoucherRequestRequest createVoucherRequestRequest, String requestOrReturn);

    /**
     * Update voucher request
     *
     * @param updateVoucherRequestRequest request data
     * @return result
     */
    Result<Void> updateVoucherRequest(UpdateVoucherRequestRequest updateVoucherRequestRequest);

    /**
     * Update voucher request status
     *
     * @param requestCode code of the request whose state you want to change
     * @param status      The state to be modified
     * @param updateUser  modifier
     * @return result
     */
    Result<Void> updateRequestStatus(String requestCode, Integer status, String updateUser);

    /**
     * Review the request
     *
     * @param approveVoucherRequestRequest request data
     * @return result
     */
    Result<String> approveVoucherRequest(ApproveVoucherRequestRequest approveVoucherRequestRequest);

    /**
     * @param voucherRequestCode
     * @return
     * <AUTHOR>
     * @date 2022年3月9日
     */
    VoucherRequest queryByVoucherRequestCode(String voucherRequestCode);

	/**
	 * get email params
	 * 
	 * @param voucherRequest
	 * @return
	 */
	Map<String, Object> getExtendparams(VoucherRequest voucherRequest);

    /**
     * Query voucher request list
     *
     * @param queryVoucherRequestRequest Query conditions
     * @return Query result
     */
    PageResult<QueryVoucherRequestResponse> queryVoucherRequest(QueryVoucherRequestRequest queryVoucherRequestRequest);

    /**
     * Get the voucher request details via requestCode
     *
     * @param voucherRequestCode code
     * @return response
     */
	Result<GetVoucherRequestResponse> getVoucherRequest(GetVoucherRequestRequest getVoucherRequestRequest);

	GetVoucherRequestResponse getVoucherRequestByCode(String code);

    /**
     * Cancellation voucher request
     *
     * @param cancelVoucherRequestRequest request data
     * @return result
     */
    Result<Void> cancelVoucherRequest(CancelVoucherRequestRequest cancelVoucherRequestRequest);

    /**
     * @param voucherRequestCodeList
     * @return
     * <AUTHOR>
     * @date 2022年3月11日
     */
    List<VoucherRequest> queryByVoucherRequestCodeList(List<String> voucherRequestCodeList);

    Result<List<GetVoucherRequestResponse>> bulkGetVoucherRequests(List<GetVoucherRequestRequest> bulkGetVoucherRequestRequest);

    Result<Void> bulkApproveVoucherRequest(BulkApproveVoucherRequestRequest bulkApproveVoucherRequestRequest);

    Result<QueryHeadOfficeAndWareHouseResponse> queryHeadOfficeAndWareHouse(QueryHeadOfficeAndWareHouseRequest cancelVoucherRequestRequest);
}
