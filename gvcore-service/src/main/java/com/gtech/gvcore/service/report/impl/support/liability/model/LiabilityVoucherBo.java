package com.gtech.gvcore.service.report.impl.support.liability.model;

import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName VoucherBo
 * @Description VoucherBo
 * <AUTHOR>
 * @Date 2023/4/20 16:01
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class LiabilityVoucherBo {

    private final String tableCode;

    private final String voucherCode;

    private final Date lastMoonTime;

    private final LiabilityVoucherMode voucher;

    private final ReportVoucherStatusEnum voucherStatusEnum;

    private LiabilityTransactionModel statusTransactionData;

    public LiabilityVoucherBo(final String tableCode, final Date lastMoonTime, final LiabilityVoucherMode voucher, final ReportVoucherStatusEnum voucherStatusEnum) {
        this.tableCode = tableCode;
        this.voucherCode = voucher.getVoucherCode();
        this.lastMoonTime = lastMoonTime;
        this.voucher = voucher;
        this.voucherStatusEnum = voucherStatusEnum;
    }

}
