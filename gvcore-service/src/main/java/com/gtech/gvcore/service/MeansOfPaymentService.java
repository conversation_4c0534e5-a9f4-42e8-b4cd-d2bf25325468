package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.meansofpayment.CreateMeansOfPaymentRequest;
import com.gtech.gvcore.common.request.meansofpayment.QueryMeansOfPaymentsByPageRequest;
import com.gtech.gvcore.common.request.meansofpayment.UpdateMeansOfPaymentRequest;
import com.gtech.gvcore.common.request.meansofpayment.UpdateMeansOfPaymentStatusRequest;
import com.gtech.gvcore.common.response.meansofpayment.QueryMeansOfPaymentsByPageResponse;
import com.gtech.gvcore.dao.model.MeansOfPayment;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
public interface MeansOfPaymentService {

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    Result<Void> createMeansOfPayment(CreateMeansOfPaymentRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    Result<Void> updateMeansOfPayment(UpdateMeansOfPaymentRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    Result<Void> updateMeansOfPaymentStatus(UpdateMeansOfPaymentStatusRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年2月21日
     */
    PageResult<QueryMeansOfPaymentsByPageResponse> queryMeansOfPaymentsByPage(
            QueryMeansOfPaymentsByPageRequest request);

    QueryMeansOfPaymentsByPageResponse getMeansOfPayments(String meansOfPaymentsCode);

    /**
     * 
     * <AUTHOR>
     * @param meansOfPaymentCodeList
     * @return
     * @date 2022年6月15日
     */
    Map<String, MeansOfPayment> queryByCodeList(List<String> meansOfPaymentCodeList);


    MeansOfPayment getMeansOfPaymentsByName(String meansOfPaymentsName);

}
