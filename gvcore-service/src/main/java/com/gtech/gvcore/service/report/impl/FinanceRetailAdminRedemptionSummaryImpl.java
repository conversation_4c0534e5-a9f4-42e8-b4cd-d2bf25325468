package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.FinanceRetailAdminSummaryRedemptionBean;
import com.gtech.gvcore.service.report.impl.bo.FinanceRetailAdminRedemptionBo;
import com.gtech.gvcore.service.report.impl.bo.FinanceRetailAdminRedemptionSummaryBo;
import com.gtech.gvcore.service.report.impl.param.FinanceRetailAdminRedemptionQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName FinanceRetailAdminRedemptionSummaryImpl
 * @Description FinanceRetailAdminRedemptionSummaryImpl
 * <AUTHOR>
 * @Date 2022/7/8 17:02
 * @Version V1.0
 **/
@Service
public class FinanceRetailAdminRedemptionSummaryImpl extends ReportSupport
        implements BusinessReport<FinanceRetailAdminRedemptionQueryData, FinanceRetailAdminSummaryRedemptionBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.FINANCE_RETAIL_ADMIN_REDEMPTION_SUMMARY_REPORT;
    }

    @Override
    public FinanceRetailAdminRedemptionQueryData builderQueryParam(CreateReportRequest reportParam) {

        //convert
        ReportParamConvertHelper.convertQueryDateCompanyCodeToMerchantCode(reportParam);

        //param
        FinanceRetailAdminRedemptionQueryData queryData = new FinanceRetailAdminRedemptionQueryData();

        // issuer merchant outlet
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());

        //transaction
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());

        //cpg
        queryData.setCpgCodeList(reportParam.getCpgCodes());

        //voucher code
        queryData.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        queryData.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        //invoice number
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());

        return queryData;
    }

    @Override
    public List<FinanceRetailAdminSummaryRedemptionBean> getExportData(FinanceRetailAdminRedemptionQueryData queryData) {

        final Collection<FinanceRetailAdminRedemptionSummaryBo> list = this.queryBoList(queryData);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, FinanceRetailAdminRedemptionSummaryBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, FinanceRetailAdminRedemptionSummaryBo::getOutletCode, Outlet.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, FinanceRetailAdminRedemptionSummaryBo::getCpgCode, Cpg.class);

        return list.stream()
                .map(e -> new FinanceRetailAdminSummaryRedemptionBean()
                        .setMerchantName(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setOutletName(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setMerchantOutletCode(outletMap.findValue(e.getOutletCode()).getBusinessOutletCode())
                        .setInvoiceNo(e.getInvoiceNumber())
                        .setTransactionType(TransactionTypeEnum.GIFT_CARD_REDEEM.getDesc())
                        .setVoucherProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setQtyOfVoucher(ConvertUtils.toBigDecimal(e.getRedemptionCount(), BigDecimal.ZERO).toPlainString())
                        .setAmount(super.toAmount(ConvertUtils.toBigDecimal(e.getDenomination(), BigDecimal.ZERO))))
                .collect(Collectors.toList());
    }


    private Collection<FinanceRetailAdminRedemptionSummaryBo> queryBoList(FinanceRetailAdminRedemptionQueryData queryData) {

        // 查询最新并且是兑换的交易
        List<FinanceRetailAdminRedemptionBo> financeRetailAdminRedemptionBoList = Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectFinanceRetailRedeemAdmin, queryData))
                .map(e -> e.stream().filter(d -> TransactionTypeEnum.GIFT_CARD_REDEEM.equalsCode(d.getTransactionType())).collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        //根据条件分组
        return financeRetailAdminRedemptionBoList.stream()
                .map(FinanceRetailAdminRedemptionSummaryBo::convert)
                .collect(Collectors.toMap(
                                // key
                                FinanceRetailAdminRedemptionSummaryBo::getGroupKey,
                                // value
                                FinanceRetailAdminRedemptionSummaryBo::newInstance,
                                // merge
                                FinanceRetailAdminRedemptionSummaryBo::merge)
                        //to map
                ).values();

    }

}
