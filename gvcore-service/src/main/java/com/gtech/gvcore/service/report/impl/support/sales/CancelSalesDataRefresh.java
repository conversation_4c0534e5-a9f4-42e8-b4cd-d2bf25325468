package com.gtech.gvcore.service.report.impl.support.sales;

import com.github.pagehelper.PageHelper;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.dao.mapper.CancelSalesDataMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.service.report.impl.bo.SalesBo;
import com.gtech.gvcore.service.report.impl.support.sales.bo.CancelSalesDataBo;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class CancelSalesDataRefresh {


    /**
     * 1.查询数据库中最新一条的创建时间 maxCreateTime
     * 2.根据 maxCreateTime 查询transactionData中所有cancelSales记录
     * 3.添加到数据库中， voucherCode，id，createTime
     */

    @Autowired
    private TransactionDataMapper transactionDataMapper;


    @Autowired
    private CancelSalesDataMapper cancelSalesDataMapper;

    public static final RowBounds LIMIT_1 = new RowBounds(0, 1);


    @Transactional
    public List<CancelSalesDataBo> refresh(Date queryDate, List<SalesBo> boList) {
        Example example = refreshData(queryDate);
        Example.Criteria criteria = example.createCriteria();
        criteria.andGreaterThan("createTime", DateUtil.parseDate(DateUtil.format(queryDate, DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS));

        if (!CollectionUtils.isEmpty(boList))
            criteria.andIn("voucherCode",boList.stream().map(SalesBo::getVoucherCode).collect(Collectors.toList()));

        example.orderBy("createTime").desc();

        return cancelSalesDataMapper.selectByCondition(example);
    }




    @Transactional
    public void init(Date queryDate) {
        refreshData(queryDate);
    }

    private Example refreshData(Date queryDate) {
        int pageSize = 1000;
        Date maxCreateTime = queryDate;

        Example example = new Example(CancelSalesDataBo.class);
        example.orderBy("createTime").desc().orderBy("id").desc();
        List<CancelSalesDataBo> cancelSalesDataBos = cancelSalesDataMapper.selectByExampleAndRowBounds(example, LIMIT_1);
        if (!CollectionUtils.isEmpty(cancelSalesDataBos)) {
            maxCreateTime = cancelSalesDataBos.get(0).getCreateTime();
        }

        int pageNum = 1;
        while (true) {
            PageHelper.startPage(pageNum, pageSize);
            List<CancelSalesDataBo> cancelSalesTransactionList = transactionDataMapper.selectCancelSalesTransactionList(maxCreateTime);
            if (cancelSalesTransactionList.isEmpty()) {
                break;
            }
            cancelSalesDataMapper.insertList(cancelSalesTransactionList);
            pageNum++;
        }
        return example;
    }


}
