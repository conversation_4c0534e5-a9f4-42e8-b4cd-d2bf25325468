package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcRedemptionDetailedBean;
import com.gtech.gvcore.service.report.impl.bean.GcRedemptionSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GcRedemptionBo;
import com.gtech.gvcore.service.report.impl.param.RedemptionQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:00
 * @Description:
 */
@Service
public class GcRedemptionDetailedImpl extends ReportSupport
        implements BusinessReport<RedemptionQueryData, GcRedemptionDetailedBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_REDEMPTION_DETAILED_REPORT;
    }

    @Override
    public RedemptionQueryData builderQueryParam(CreateReportRequest reportRequest) {

        ReportParamConvertHelper.convertQueryDateCompanyCodeToMerchantCode(reportRequest);

        RedemptionQueryData redemptionQueryData = new RedemptionQueryData();

        //transaction
        redemptionQueryData.setTransactionDateEnd(reportRequest.getTransactionDateEnd());
        redemptionQueryData.setTransactionDateStart(reportRequest.getTransactionDateStart());

        // issuer merchant outlet
        redemptionQueryData.setMerchantCodeList(reportRequest.getMerchantCodes());
        redemptionQueryData.setOutletCodeList(reportRequest.getOutletCodes());

        //cpg
        redemptionQueryData.setCpgCodeList(reportRequest.getCpgCodes());

        //voucher code
        redemptionQueryData.setVoucherCodeNumStart(reportRequest.getVoucherCodeNumStart());
        redemptionQueryData.setVoucherCodeNumEnd(reportRequest.getVoucherCodeNumEnd());

        //invoice number
        redemptionQueryData.setInvoiceNumber(reportRequest.getInvoiceNo());

        return redemptionQueryData;
    }

    @Override
    public List<GcRedemptionDetailedBean> getExportData(RedemptionQueryData queryData) {

        List<GcRedemptionBo> list = getBoList(queryData);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<GiftCardEntity> voucherMap = super.getMapByCode(list, GcRedemptionBo::getVoucherCode, GiftCardEntity.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcRedemptionBo::getOutletCode, Outlet.class);
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, GcRedemptionBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, GcRedemptionBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);

        return list.stream()
                .map(e -> {

                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final GiftCardEntity voucher = voucherMap.findValue(e.getVoucherCode());
                    final GcCpg cpg = cpgMap.findValue(e.getCpgCode());
                    final Company company = companyMap.findValue(merchant.getCompanyCode());

                    final String denomination = super.toAmount(e.getDenomination());
                    final String amount = super.toAmount(e.getAmount());
                    final String balanceAfter = super.toAmount(e.getBalanceAfter());
                    final String transactionDate = DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);

                    return new GcRedemptionDetailedBean()
                            .setTransactionDate(transactionDate)
                            .setCardNumber(voucher.getCardNumber())
                            .setMerchant(merchant.getMerchantName())
                            .setOutlet(outlet.getOutletName())
                            .setVoucherProgramGroup(cpg.getCpgName())
                            .setTransactionType("GIFT CARD REDEEM")
                            .setDenomination(denomination)
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setExpiryDate(DateUtil.format(voucher.getExpiryTime(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                            .setIssuanceYear(DateUtil.format(voucher.getCreateTime(), DateUtil.FORMAT_YYYY))
                            .setRedeemAmount(amount)
                            .setRedeemBalance(balanceAfter)
                            .setSbuCompanyName(company.getCompanyName())
                            .setApprovalCode(e.getApprovalCode());

                }).sorted(Comparator.comparing(GcRedemptionDetailedBean::getTransactionDate).reversed()).collect(Collectors.toList());

    }

    private List<GcRedemptionBo> getBoList(RedemptionQueryData queryData) {

        return new ArrayList<>(PollPageHelper.pollGroupNewTransactionByCodeSelect(gcReportBusinessMapper::selectGcRedemption, queryData));
    }

}