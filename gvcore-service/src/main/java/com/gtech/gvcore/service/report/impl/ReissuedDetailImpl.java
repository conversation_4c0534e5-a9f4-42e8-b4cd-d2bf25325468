package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.IssueHandlingDetailsMapper;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.helper.GvPageHelper;
import com.gtech.gvcore.service.CustomerService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.ReissuedDetailBean;
import com.gtech.gvcore.service.report.impl.bo.ReissuedBo;
import com.gtech.gvcore.service.report.impl.param.ReissuedQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:00
 * @Description:
 */
@Service
public class ReissuedDetailImpl extends ReportSupport
		implements BusinessReport<ReissuedQueryData, ReissuedDetailBean>, PollReport {

	@Autowired private IssueHandlingDetailsMapper issueHandlingDetailsMapper;
	@Autowired protected CustomerService customerService;
	@Autowired protected VoucherService voucherService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.REISSUED_DETAILED_REPORT;
    }

    @Override
    public ReissuedQueryData builderQueryParam(CreateReportRequest reportParam) {

	    ReissuedQueryData param = new ReissuedQueryData();

	    //transaction
	    param.setTransactionDateEnd(reportParam.getTransactionDateEnd());
	    param.setTransactionDateStart(reportParam.getTransactionDateStart());

	    // issuer merchant outlet
	    param.setMerchantCodeList(reportParam.getMerchantCodes());
	    param.setOutletCodeList(reportParam.getOutletCodes());

	    //voucher code
	    param.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
	    param.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

	    //cpg
	    param.setCpgCodeList(reportParam.getCpgCodes());

	    //invoice number
	    param.setInvoiceNumber(reportParam.getInvoiceNo());

		final ReportContext context = ReportContextHelper.findContext();
		final ReportRequest reportRequest = context.getReportRequest();
		final CreateReportRequest sourceParam = BeanCopyUtils.jsonCopyBean(reportRequest.getRequestJson(), CreateReportRequest.class);

		param.setNullMerchantCode(CollectionUtils.isEmpty(sourceParam.getMerchantCodes()));
		param.setNullOutletCode(CollectionUtils.isEmpty(sourceParam.getOutletCodes()));

	    return param;
    }

	@Override
    public List<ReissuedDetailBean> getExportData(ReissuedQueryData queryData) {

		List<ReissuedBo> reissuedDetailBos = this.reportBusinessMapper.selectReissued(queryData, GvPageHelper.getRowBounds(queryData));
		if (CollectionUtils.isEmpty(reissuedDetailBos)) return new ArrayList<>();

		//code array
		List<String> voucherCodeSet = reissuedDetailBos.stream().map(ReissuedBo::getVoucherCode).distinct().collect(Collectors.toList());

		//init map
		final JoinDataMap<Cpg> cpgMap = super.getMapByCode(reissuedDetailBos, ReissuedBo::getCpgCode, Cpg.class);
		final JoinDataMap<Merchant> merchantMap = super.getMapByCode(reissuedDetailBos, ReissuedBo::getMerchantCode, Merchant.class);
		final JoinDataMap<Outlet> outletMap = super.getMapByCode(reissuedDetailBos, ReissuedBo::getOutletCode, Outlet.class);
		final Map<String, IssueHandlingDetails> issueHandlingDetailsMap = new HashMap<>();
		final Map<String, String> customerMap = new HashMap<>();

		//查询关联信息
		if (CollectionUtils.isNotEmpty(voucherCodeSet)) {
			issueHandlingDetailsMap.putAll(getStringIssueHandlingDetailsMap(voucherCodeSet));
			settingVoucherCustomerMap(voucherCodeSet, customerMap);
		}

		return reissuedDetailBos.stream().map(reissuedDetailBo -> {

			ReissuedDetailBean reissuedDetailBean = new ReissuedDetailBean();

			reissuedDetailBean.setOldVoucherNumber(reissuedDetailBo.getVoucherCode())
					.setExpiryDate(DateUtil.format(reissuedDetailBo.getEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
					.setTransactionAmount(super.toAmount(reissuedDetailBo.getDenomination()))
					.setTransactionDate(DateUtil.format(reissuedDetailBo.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
					.setInvoiceNumber(reissuedDetailBo.getInvoiceNumber());

			reissuedDetailBean.setOutlet(outletMap.findValue(reissuedDetailBo.getOutletCode()).getOutletName());
			reissuedDetailBean.setVoucherProgramGroup(cpgMap.findValue(reissuedDetailBo.getCpgCode()).getCpgName());
			reissuedDetailBean.setMerchant(merchantMap.findValue(reissuedDetailBo.getMerchantCode()).getMerchantName());
			reissuedDetailBean.setNewVoucherNumber(issueHandlingDetailsMap.getOrDefault(reissuedDetailBo.getVoucherCode(), new IssueHandlingDetails()).getNewVoucherCode());

			reissuedDetailBean.setCorporateName(customerMap.get(reissuedDetailBo.getVoucherCode()));

			return reissuedDetailBean;

		}).collect(Collectors.toList());
    }

	private void settingVoucherCustomerMap(List<String> voucherCodeSet, Map<String, String> customerMap) {
		List<Voucher> vouchers = voucherService.queryByVoucherCodeList(null, voucherCodeSet);

		List<String> customerCodeSet = vouchers.stream()
				.map(Voucher::getVoucherOwnerCode).distinct().collect(Collectors.toList());
		Map<String, String> customerCodeMap = customerService.getCustomerByCode(customerCodeSet).values().stream().collect(Collectors.toMap(Customer::getCustomerCode, Customer::getCustomerName));

		vouchers.stream()
				.filter(v -> VoucherOwnerTypeEnum.CUSTOMER.equalsCode(v.getVoucherOwnerType()))
				.forEach(v -> customerMap.put(v.getVoucherCode(), customerCodeMap.get(v.getVoucherOwnerCode())));
	}

	private Map<String, IssueHandlingDetails> getStringIssueHandlingDetailsMap(List<String> voucherCodeSet) {

		Example example = new Example(IssueHandlingDetails.class, true);

		example.createCriteria()
				.andIn(IssueHandlingDetails.C_VOUCHER_CODE, voucherCodeSet)
				.andEqualTo(IssueHandlingDetails.C_ISSUE_TYPE, IssueHandlingTypeEnum.BULK_REISSUE.code());
		List<IssueHandlingDetails> issueHandlingDetails = issueHandlingDetailsMapper.selectByCondition(example);

		if (CollectionUtils.isEmpty(issueHandlingDetails)) return new HashMap<>();

		return issueHandlingDetails.stream().collect(Collectors.toMap(IssueHandlingDetails::getVoucherCode, e -> e, (a, b) -> b));
	}

}
