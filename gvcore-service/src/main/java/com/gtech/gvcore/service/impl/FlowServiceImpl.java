package com.gtech.gvcore.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.flow.CreateFlowRequest;
import com.gtech.gvcore.common.request.flow.FlowNodeRequest;
import com.gtech.gvcore.common.request.flow.QueryFlowRequest;
import com.gtech.gvcore.common.request.flow.UpdateFlowRequest;
import com.gtech.gvcore.common.response.flow.FlowNodeResponse;
import com.gtech.gvcore.common.response.flow.FlowResponse;
import com.gtech.gvcore.dao.mapper.FlowMapper;
import com.gtech.gvcore.dao.mapper.FlowNodeMapper;
import com.gtech.gvcore.dao.model.Flow;
import com.gtech.gvcore.dao.model.FlowNode;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.FlowService;

import tk.mybatis.mapper.entity.Example;

@Service
public class FlowServiceImpl implements FlowService {

	@Autowired
	private FlowMapper flowMapper;

	@Autowired
	private FlowNodeMapper flowNodeMapper;
	@Autowired
	private GvCodeHelper codeHelper;

	@Override
	@Transactional
	public String createFlow(CreateFlowRequest request) {
		Flow flow = BeanCopyUtils.jsonCopyBean(request, Flow.class);
		setDefaultValue(flow);
		flowMapper.insert(flow);
		saveFlowNode(request.getFlowNodeList(), request.getFlowCode());
		return flow.getFlowCode();
	}

	@Override
	public String saveFlowNode(FlowNodeRequest request) {
		FlowNode oldFlowNode = selectOneFlowNode(request.getFlowCode(), request.getFlowNodeCode());
		if (oldFlowNode == null) {
			FlowNode node = BeanCopyUtils.jsonCopyBean(request, FlowNode.class);
			flowNodeMapper.insert(node);
		} else {
			BeanUtils.copyProperties(request, oldFlowNode);
			flowNodeMapper.updateByPrimaryKeySelective(oldFlowNode);
		}
		return request.getFlowCode();
	}

	private void saveFlowNode(List<FlowNodeRequest> flowNodeList, String flowCode) {
		if (CollectionUtils.isEmpty(flowNodeList)) {
			return;
		}
		flowNodeList.forEach(vo -> {
			if (Boolean.TRUE.equals(vo.getDeleteFlag())) {
				deleteFlowNode(flowCode, vo.getFlowNodeCode());
			} else {
				vo.setFlowCode(flowCode);
				saveFlowNode(vo);
			}
		});
	}

	private void deleteFlowNode(String flowCode, String flowNodeCode) {
		FlowNode flowNode = new FlowNode();
		flowNode.setFlowCode(flowCode);
		flowNode.setFlowNodeCode(flowNodeCode);
		flowNodeMapper.delete(flowNode);
	}

	private void setDefaultValue(Flow flow) {
		flow.setCreateTime(new Date());
		if (StringUtil.isEmpty(flow.getFlowCode())) {
			flow.setFlowCode(codeHelper.generateFlowCode());
		}
	}

	@Override
	@Transactional
	public String updateFlow(UpdateFlowRequest request) {
		Flow oldFlow = selectOneFlow(request.getFlowCode());
		if (oldFlow == null) {
			throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(),
					ResultErrorCodeEnum.NO_DATA_FOUND.desc());
		}
		BeanUtils.copyProperties(request, oldFlow);
		flowMapper.updateByPrimaryKeySelective(oldFlow);
		saveFlowNode(request.getFlowNodeList(), request.getFlowCode());
		return request.getFlowCode();
	}

	private Flow selectOneFlow(String flowCode) {
		Flow flow = new Flow();
		flow.setFlowCode(flowCode);
		return flowMapper.selectOne(flow);
	}

	private FlowNode selectOneFlowNode(String flowCode, String flowNodeCode) {
		FlowNode flowNode = new FlowNode();
		flowNode.setFlowNodeCode(flowNodeCode);
		flowNode.setFlowCode(flowCode);
		return flowNodeMapper.selectOne(flowNode);
	}

	@SuppressWarnings("unchecked")
	@Override
	public PageResult<FlowResponse> queryFlowList(QueryFlowRequest request) {
		PageMethod.startPage(request.getPageNum(), request.getPageSize());

		Map<String, Object> map = JSON.parseObject(JSON.toJSONString(request), Map.class);
		List<Flow> flowList = flowMapper.query(map);
		PageInfo<Flow> pageInfo = new PageInfo<>(flowList);
		List<FlowResponse> list = BeanCopyUtils.jsonCopyList(flowList, FlowResponse.class);
		queryFlowNodeList(list);
		return new PageResult<>(list, pageInfo.getTotal());
	}

	private void queryFlowNodeList(List<FlowResponse> list) {
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		List<String> flowCodeList = list.stream().map(FlowResponse::getFlowCode).collect(Collectors.toList());
		Example example = new Example(FlowNode.class);
		example.createCriteria().andIn(FlowNode.C_FLOW_CODE, flowCodeList);
		List<FlowNode> flowNodeList = flowNodeMapper.selectByCondition(example);
		if (CollectionUtils.isEmpty(flowNodeList)) {
			return;
		}
		Map<String, List<FlowNode>> flowMap = flowNodeList.stream()
				.collect(Collectors.groupingBy(FlowNode::getFlowCode));
		for (FlowResponse flowResponse : list) {
			List<FlowNode> nodeList = flowMap.get(flowResponse.getFlowCode());
			if (CollectionUtils.isEmpty(nodeList)) {
				continue;
			}
			flowResponse.setFlowNodeList(BeanCopyUtils.jsonCopyList(nodeList, FlowNodeResponse.class));
		}
	}
}
