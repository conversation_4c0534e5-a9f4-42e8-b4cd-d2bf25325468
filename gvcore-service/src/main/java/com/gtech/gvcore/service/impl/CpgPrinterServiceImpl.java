package com.gtech.gvcore.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.dao.mapper.CpgPrinterMapper;
import com.gtech.gvcore.dao.model.CpgPrinter;
import com.gtech.gvcore.service.CpgPrinterService;

/**
 * <AUTHOR>
 * @date 2022年3月7日
 */
@Service
public class CpgPrinterServiceImpl implements CpgPrinterService {

    @Autowired
    private CpgPrinterMapper cpgPrinterMapper;

    @Transactional
    @Override
    public int insertList(List<CpgPrinter> list) {
        return cpgPrinterMapper.insertList(list);
    }

    @Override
    public Map<String, CpgPrinter> queryMapByCpgCode(String cpgCode) {

        if (StringUtils.isBlank(cpgCode)) {
            return Collections.emptyMap();
        }
        CpgPrinter cpgPrinter = new CpgPrinter();
        cpgPrinter.setCpgCode(cpgCode);
        List<CpgPrinter> list = cpgPrinterMapper.select(cpgPrinter);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(CpgPrinter::getPrinterCode, v -> v));
    }

    @Override
    public int updateById(CpgPrinter cpgPrinter) {
        return cpgPrinterMapper.updateByPrimaryKeySelective(cpgPrinter);
    }

    @Override
    public List<String> queryPrinterCodeByCpgCode(String cpgCode) {

        if (StringUtils.isBlank(cpgCode)) {
            return Collections.emptyList();
        }

        CpgPrinter cpgPrinter = new CpgPrinter();
        cpgPrinter.setCpgCode(cpgCode);
        cpgPrinter.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        List<CpgPrinter> list = cpgPrinterMapper.select(cpgPrinter);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(CpgPrinter::getPrinterCode).collect(Collectors.toList());
    }

}
