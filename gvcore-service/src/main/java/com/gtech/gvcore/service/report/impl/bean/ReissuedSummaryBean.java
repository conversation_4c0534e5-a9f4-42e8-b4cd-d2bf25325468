package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 14:07
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class ReissuedSummaryBean {

    @ExcelProperty(value = "Merchant")
    private String merchant;

    @ExcelProperty(value = "Outlet")
    private String merchantOut;

    @ExcelProperty(value = "Voucher Program Group")
    private String vpg;

    @ExcelProperty(value = "Number of Vouchers", converter = ExportExcelNumberConverter.class)
    private String numberOfVouchers;

    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;

}
