package com.gtech.gvcore.service.report.impl.support.aging.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName VoucherUsedAgeBo
 * @Description Voucher Used Age Bo
 * <AUTHOR>
 * @Date 2022/11/2 15:12
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherUsedAgeBo {

    private String detail;

    private int auOne;

    private int auTwo;

    private int auThree;

    private int auFour;

    private int auSix;

    public int getUsed() {
        return auOne +
                auTwo +
                auThree +
                auFour +
                auSix;
    }

    private int anuOne;

    private int anuTwo;

    private int anuThree;

    private int anuFour;

    private int anuSix;

    public int getUnused() {

        return anuOne +
                anuTwo +
                anuThree +
                anuFour +
                anuSix;
    }

    public int sum() {
        return getUsed() + getUnused();
    }

}
