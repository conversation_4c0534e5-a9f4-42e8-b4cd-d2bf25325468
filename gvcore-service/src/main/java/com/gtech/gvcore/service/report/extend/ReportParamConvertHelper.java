package com.gtech.gvcore.service.report.extend;

import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.MerchantService;
import com.gtech.gvcore.service.OutletService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @ClassName ReportParamConvertHelper
 * @Description 参数编码转换帮助类
 * <AUTHOR>
 * @Date 2023/1/18 16:42
 * @Version V1.0
 **/
@Component
public class ReportParamConvertHelper implements ApplicationContextAware {

    private static final List<String> EMPTY_INTERSECTION_LIST = Collections.singletonList("-1");

    /**
     * 将查询条件内的 CompanyCode 转换成 MerchantCode
     */
    @Setter(AccessLevel.PRIVATE)
    private static MerchantService merchantService;
    public static void convertQueryDateCompanyCodeToMerchantCode (CreateReportRequest createOrderReportRequest) {

        ReportParamConvertHelper.convertCode(createOrderReportRequest::setMerchantCodes
                , createOrderReportRequest::getCompanyCodes
                , createOrderReportRequest::getMerchantCodes
                , merchantService::queryMerchantByCompanyCodesNotPageParam
                , Merchant::getMerchantCode);
    }

    /**
     * 将查询条件里的merchant code 转换成 outlet code
     */
    @Setter(AccessLevel.PRIVATE)
    private static OutletService outletService;
    public static void convertQueryDateMerchantCodeToOutletCode(CreateReportRequest createOrderReportRequest) {

        ReportParamConvertHelper.convertCode(createOrderReportRequest::setOutletCodes
                , createOrderReportRequest::getMerchantCodes
                , createOrderReportRequest::getOutletCodes
                , outletService::queryOutletByMerchantCodes, Outlet::getOutletCode);
    }

    /**
     * 父子级编码转换
     * @param setChildCode 设置子编码方法
     * @param parentCodes 获取父编码方法
     * @param childCodes 获取子编码方法
     * @param findChildCodeFunction 通过父编码查询子对象方法
     * @param mapper 子对象编码映射
     * @param <T> 子对象类型
     */
    private static <T> void convertCode(Consumer<List<String>> setChildCode, Supplier<List<String>> parentCodes, Supplier<List<String>> childCodes, Function<List<String>, List<T>> findChildCodeFunction, Function<T, String> mapper) {

        setChildCode.accept(ReportParamConvertHelper.convertCode(parentCodes, childCodes, findChildCodeFunction, mapper));
    }

    /**
     * 父子级编码转换
     * 该方法如果不存在父编码则直接返回子编码
     * 如果存在父编码则查询其相关子编码并与入参子编码进行交集并返回
     * 如若交集结果为空则直接返回固定集合 ["-1"]
     * @param parentCodes 获取父编码方法
     * @param childCodes 获取子编码方法
     * @param findChildCodeFunction 通过父编码查询子对象方法
     * @param mapper 子对象编码映射
     * @return 返回子对象编码集合
     * @param <T> 子对象类型
     */
    private static <T> List<String> convertCode (Supplier<List<String>> parentCodes, Supplier<List<String>> childCodes, Function<List<String>, List<T>> findChildCodeFunction, Function<T, String> mapper) {

        List<String> parentCodeList = parentCodes.get();
        List<String> childCodeList = childCodes.get();

        if (CollectionUtils.isEmpty(parentCodeList)) return childCodeList;
        List<String> findChildCode = findChildCodeFunction.apply(parentCodeList).stream().map(mapper).distinct().collect(Collectors.toList());

        return intersectionParam(childCodeList, findChildCode);
    }

    /**
     * 获得A& B的交集参数
     * @param listA
     * @param listB
     * @return
     */
    public static List<String> intersectionParam(List<String> listA, List<String> listB) {

        if (CollectionUtils.isEmpty(listB) || CollectionUtils.isEmpty(listA)) return EMPTY_INTERSECTION_LIST;

        Collection<String> intersection = CollectionUtils.intersection(listB, listA);
        if (CollectionUtils.isEmpty(intersection)) return EMPTY_INTERSECTION_LIST;

        return intersection.stream().distinct().collect(Collectors.toList());
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {

        setOutletService(applicationContext.getBean(OutletService.class));
        setMerchantService(applicationContext.getBean(MerchantService.class));
    }
}
