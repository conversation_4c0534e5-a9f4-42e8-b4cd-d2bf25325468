 package com.gtech.gvcore.service.report.impl.support.expiry;

 import com.gtech.gvcore.service.report.base.ReportSupport;
 import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
 import com.gtech.gvcore.service.report.impl.param.ExpiryGvMigrationDataQueryData;
 import org.springframework.stereotype.Component;

 import java.util.ArrayList;
 import java.util.List;

/**
 * ExpiryMigrationDataSupport
 * 查询历史迁移的卡券数据
 *
 */
@Component
public class ExpiryMigrationDataSupport extends ReportSupport {

    public List<String> queryMigrationData (final ExpiryGvMigrationDataQueryData param) {

        // 迁移数据查询的特出处理请在此处实现

        final List<String> voucherCode = new ArrayList<>();

        PollPageHelper.pollSelect(this.reportBusinessMapper::selectExpiryGvOldData, param, voucherCode::addAll);

        return voucherCode;
    }

}
