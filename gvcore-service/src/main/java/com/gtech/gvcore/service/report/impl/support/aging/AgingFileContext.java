package com.gtech.gvcore.service.report.impl.support.aging;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSON;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.service.report.export.file.FileContext;
import com.gtech.gvcore.service.report.extend.ReportUploadHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import org.apache.commons.collections4.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Objects;

/**
 * aging excel context
 */
public class AgingFileContext implements FileContext {

    private static final String TEMPLATE_NAME = ReportExportTypeEnum.AGING_REPORT_SUMMARY_REPORT.getTemplateName();

    private final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    private final FillConfig fillConfig = FillConfig.builder().autoStyle(false).forceNewRow(Boolean.TRUE).build();
    private ExcelWriter excelWriter;

    @Override
    public void init() {

        excelWriter = EasyExcelFactory.write(outputStream)
                .withTemplate(Thread.currentThread().getContextClassLoader().getResourceAsStream(TEMPLATE_NAME))
                .build();

    }

    public void doFill(AgingSheet sheet) {

        ExcelWriterSheetBuilder builder = EasyExcelFactory.writerSheet(sheet.getSheetName());
        if (Objects.nonNull(sheet.getCellWriteHandler())) builder.registerWriteHandler(sheet.getCellWriteHandler());

        WriteSheet writeSheet = builder.build();

        List<AgingSheet.Data> sheetData = sheet.getSheetData();
        if (CollectionUtils.isNotEmpty(sheetData))
            sheetData.forEach(e -> excelWriter.fill(new FillWrapper(e.getFillKey(), e.getList()), fillConfig, writeSheet));

        Object head = sheet.getHead();
        if (Objects.nonNull(head)) excelWriter.fill(JSON.parseObject(JSON.toJSONString(head)), writeSheet);

    }


    @Override
    public String finish() {

        excelWriter.finish();

        return ReportUploadHelper.fileUpload(ReportContextHelper.findContext(), new ByteArrayInputStream(outputStream.toByteArray()));
    }

    @Override
    public void error() {

        excelWriter.close();
    }
}