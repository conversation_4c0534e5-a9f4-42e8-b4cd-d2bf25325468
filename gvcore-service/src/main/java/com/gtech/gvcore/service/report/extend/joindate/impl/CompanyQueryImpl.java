package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.CompanyMapper;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName CompanyQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:48
 * @Version V1.0
 **/
@Component
public class CompanyQueryImpl implements QuerySupport<Company> {

    public static final Company EMPTY = new Company();

    @Autowired
    private CompanyMapper companyMapper;

    @Override
    public List<Company> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(Company.class);
        example.createCriteria().andIn(Company.C_COMPANY_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<Company> list = companyMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

    @Override
    public Function<Company, String> codeMapper() {
        return Company::getCompanyCode;
    }

    @Override
    public Company emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<Company> supportType() {
        return Company.class;
    }
}
