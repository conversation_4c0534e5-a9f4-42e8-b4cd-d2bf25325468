package com.gtech.gvcore.service.report.impl;

import com.gtech.basic.masterdata.web.entity.MasterDataDistrictEntity;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.CpgType;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.PartnerSalesDetailBean;
import com.gtech.gvcore.service.report.impl.bo.PartnerDetailBo;
import com.gtech.gvcore.service.report.impl.param.PartnerDetailQueryData;
import com.gtech.gvcore.service.report.impl.support.PartnerDetailBaseImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName PartnerSalesDetailImpl
 * @Description partner sales detail impl
 * <AUTHOR>
 * @Date 2023/1/3 14:41
 * @Version V1.0
 **/
@Service
public class PartnerSalesDetailImpl extends PartnerDetailBaseImpl<PartnerSalesDetailBean>
        implements BusinessReport<PartnerDetailQueryData, PartnerSalesDetailBean>, SingleReport {

    @Autowired private IssueHandlingDetailsService issueHandlingDetailsService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.PARTNER_SALES_REPORT;
    }

    @Override
    public List<PartnerSalesDetailBean> getExportData(PartnerDetailQueryData param) {

        final List<PartnerDetailBo> boList = super.getBoList(param);
        if (CollectionUtils.isEmpty(boList)) return Collections.emptyList();

        //voucher
        final JoinDataMap<Voucher> voucherMap = super.getMapByCode(boList, PartnerDetailBo::getVoucherCode, Voucher.class);
        //merchant
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(boList, PartnerDetailBo::getMerchantCode, Merchant.class);
        //outlet
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(boList, PartnerDetailBo::getOutletCode, Outlet.class);
        //pos
        final JoinDataMap<Pos> posMap = super.getMapByCode(boList, PartnerDetailBo::getPosCode, Pos.class);
        //cpg
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(boList, PartnerDetailBo::getCpgCode, Cpg.class);
        //cpg type
        final JoinDataMap<CpgType> cpgTypeMap = super.getMapByCode(cpgMap.values(), Cpg::getCpgTypeCode, CpgType.class);
        //customer order
        final JoinDataMap<Customer> customerMap = super.getMapByCode(boList, PartnerDetailBo::getCustomerCode, Customer.class);
        // company
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);
        //district
        final JoinDataMap<MasterDataDistrictEntity> districtMap = super.getMapByCode(outletMap.values(), Outlet::getDistrictCode, MasterDataDistrictEntity.class);
        //原始卡券编号 map
        final Map<String, String> souceVoucherCodeMap = this.issueHandlingDetailsService.queryReIssuerSourceVoucherCode(super.getCodeList(voucherMap.values(), Voucher::getVoucherCode));

        return boList.stream()
                .map(e -> {

                    final Voucher voucher = voucherMap.findValue(e.getVoucherCode());
                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final Pos pos = posMap.findValue(e.getPosCode());
                    final Cpg cpg = cpgMap.findValue(e.getCpgCode());
                    final CpgType cpgType = cpgTypeMap.findValue(cpg.getCpgTypeCode());
                    final Company company = companyMap.findValue(merchant.getCompanyCode());
                    final Customer customer = customerMap.findValue(e.getCustomerCode());
                    final MasterDataDistrictEntity district = districtMap.findValue(outlet.getDistrictCode());

                    final String posName = null != pos && StringUtil.isNotEmpty(pos.getPosName()) ? pos.getPosName() : "GV POS";
                    final String denomination = super.toAmount(voucher.getDenomination());

                    return new PartnerSalesDetailBean()
                            .setVoucherNumber(e.getVoucherCode())
                            .setBookletNumber(GvcoreConstants.MOP_CODE_VCE.equals(voucher.getMopCode()) ? "NA" : voucher.getBookletCode())
                            .setSbuCompanyName(company.getCompanyName())
                            .setMerchant(merchant.getMerchantName())
                            .setOutlet(outlet.getOutletName())
                            .setOutletCode(outlet.getBusinessOutletCode())
                            .setOutletType(outlet.getOutletType())
                            .setRegion(district.getDistrictName())
                            .setTransactionDate(DateUtil.format(e.getTransactionDate(), GvDateUtil.FORMAT_DD_MM_YYYY_JOIN_SLASH))
                            .setTransactionTime(DateUtil.format(e.getTransactionDate(), GvDateUtil.FORMAT_HH_MM_SS_JOIN_POINT))
                            .setPosName(posName)
                            .setVoucherProgramGroup(cpg.getCpgName())
                            .setVoucherProgramGroupType(cpgType.getCpgTypeName())
                            .setAmount(denomination)
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setDenomination(denomination)
                            .setExpiryDate(DateUtil.format(voucher.getVoucherEffectiveDate(), GvDateUtil.FORMAT_DD_MM_YYYY_JOIN_SLASH))
                            .setIssuanceYear(DateUtil.format(voucher.getCreateTime(), DateUtil.FORMAT_YYYY))
                            .setReferenceNumber(e.getReferenceNumber())
                            .setOriginalCardNumberBeforeReissue(souceVoucherCodeMap.get(e.getVoucherCode()))
                            .setBatchNumber(e.getInvoiceNumber())
                            .setApprovalCode(e.getApproveCode())
                            .autoFull(customer, PartnerSalesDetailBean.class)
                            ;
                })
                .collect(Collectors.toList());
    }

}
