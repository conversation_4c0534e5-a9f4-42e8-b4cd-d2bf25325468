package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.FinanceRetailAdminSalesDetailBean;
import com.gtech.gvcore.service.report.impl.bo.FinanceRetailAdminSalesBo;
import com.gtech.gvcore.service.report.impl.param.FinanceRetailAdminSalesQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName FinanceRetailAdminSalesDetailedImpl
 * @Description FinanceRetailAdminSalesDetailedImpl
 **/
@Service
public class FinanceRetailAdminSalesDetailedImpl extends ReportSupport
        implements BusinessReport<FinanceRetailAdminSalesQueryData, FinanceRetailAdminSalesDetailBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.FINANCE_RETAIL_ADMIN_SALES_DETAILED_REPORT;
    }

    @Override
    public FinanceRetailAdminSalesQueryData builderQueryParam(CreateReportRequest reportParam) {

        FinanceRetailAdminSalesQueryData financeRetailAdminSalesDetailQueryData = new FinanceRetailAdminSalesQueryData();

        //transaction
        financeRetailAdminSalesDetailQueryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        financeRetailAdminSalesDetailQueryData.setTransactionDateStart(reportParam.getTransactionDateStart());

        // issuer merchant outlet
        financeRetailAdminSalesDetailQueryData.setMerchantCodeList(reportParam.getMerchantCodes());
        financeRetailAdminSalesDetailQueryData.setOutletCodeList(reportParam.getOutletCodes());

        //cpg
        financeRetailAdminSalesDetailQueryData.setCpgCodeList(reportParam.getCpgCodes());

        //voucher code
        financeRetailAdminSalesDetailQueryData.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        financeRetailAdminSalesDetailQueryData.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        //invoice number
        financeRetailAdminSalesDetailQueryData.setInvoiceNumber(reportParam.getInvoiceNo());

        return financeRetailAdminSalesDetailQueryData;
    }

    @Override
    public List<FinanceRetailAdminSalesDetailBean> getExportData(FinanceRetailAdminSalesQueryData param) {

        List<FinanceRetailAdminSalesBo> list = getBoList(param);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<Voucher> voucherMap = super.getMapByCode(list, FinanceRetailAdminSalesBo::getVoucherCode, Voucher.class
                , Voucher.C_VOUCHER_CODE, Voucher.C_VOUCHER_BATCH_CODE, Voucher.C_BOOKLET_CODE, Voucher.C_CREATE_TIME);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, FinanceRetailAdminSalesBo::getOutletCode, Outlet.class);
        final JoinDataMap<Pos> posMap = super.getMapByCode(list, FinanceRetailAdminSalesBo::getPosCode, Pos.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, FinanceRetailAdminSalesBo::getCpgCode, Cpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, FinanceRetailAdminSalesBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, FinanceRetailAdminSalesBo::getCustomerCode, Customer.class);
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);

        return list.stream()
                .map(e -> {

                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Pos pos = posMap.findValue(e.getPosCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final Customer customer = customerMap.findValue(e.getCustomerCode());
                    final Cpg cpg = cpgMap.findValue(e.getCpgCode());
                    final Voucher voucher = voucherMap.findValue(e.getVoucherCode());
                    final Company company = companyMap.findValue(merchant.getCompanyCode());

                    final String posName = null != pos && StringUtil.isNotEmpty(pos.getPosName()) ? pos.getPosName() : "GV POS";
                    final String denomination = super.toAmount(e.getDenomination());
                    final String transactionDate = DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);
                    return new FinanceRetailAdminSalesDetailBean()
                            .setCardNumber(voucher.getVoucherCode())
                            .setBookletNumber(ConvertUtils.toString(voucher.getBookletCode(), "NA"))
                            .setBatchNumber(voucher.getVoucherBatchCode())
                            .setMerchant(merchant.getMerchantName())
                            .setOutlet(outlet.getOutletName())
                            .setDescriptiveOutletName(company.getCompanyName())
                            .setOutletCode(outlet.getBusinessOutletCode())
                            .setCustomerFullName(ConvertUtils.toString(e.getCustomerFirstName(), StringUtils.EMPTY) + ConvertUtils.toString(e.getCustomerLastName(), StringUtils.EMPTY))
                            .setTransactionDate(transactionDate)
                            .setPosName(posName)
                            .setVoucherProgramGroup(cpg.getCpgName())
                            .setTransactionType(TransactionTypeEnum.getTypeDesc(e.getTransactionType()))
                            .setAmount(denomination)
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setResponseMessage(e.getResponseMessage())
                            .setDateAtClient(transactionDate)
                            .setPreTransactionVoucherDenomination(denomination)
                            .setTransactionPostDate(transactionDate)
                            .setNotes(e.getNotes())
                            .setExpiryDate(DateUtil.format(e.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                            .setIssuanceYear(DateUtil.format(voucher.getCreateTime(), DateUtil.FORMAT_YYYY))
                            .setReferenceNumber(e.getReferenceNumber())
                            .setRequestAmount(denomination)
                            .setApprovalCode(e.getApproveCode())
                            .autoFull(customer, FinanceRetailAdminSalesDetailBean.class);

                }).collect(Collectors.toList());
    }


    private List<FinanceRetailAdminSalesBo> getBoList(FinanceRetailAdminSalesQueryData param) {

        // select transaction
        return Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::financeRetailAdminSales, param))
                // filter gift card sell
                .map(e -> e.stream().filter(s -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(s.getTransactionType())).collect(Collectors.toList()))
                // empty list
                .orElse(Collections.emptyList());
    }

}