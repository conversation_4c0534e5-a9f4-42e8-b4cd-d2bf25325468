package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.ReactivatedForSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.ReactivatedBo;
import com.gtech.gvcore.service.report.impl.param.ReactivatedQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-22 14:08
 */
@Service
public class ReactivatedForSummaryImpl extends ReportSupport
        implements BusinessReport<ReactivatedQueryData, ReactivatedForSummaryBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.REACTIVATE_SUMMARY_REPORT;
    }

    @Override
    public ReactivatedQueryData builderQueryParam(CreateReportRequest reportParam) {
        ReactivatedQueryData queryData = new ReactivatedQueryData();
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setOutletCodeList(reportParam.getOutletCodes());
        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());
        queryData.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        queryData.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        return queryData;
    }

    @Override
    public List<ReactivatedForSummaryBean> getExportData(ReactivatedQueryData queryData) {

        //query data
        Collection<ReactivatedSummaryStatisticalBo> list =
                Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectReactivated, queryData))
                        .map(e -> e.stream()
                                .collect(Collectors.toMap(
                                        ReactivatedSummaryStatisticalBo::groupKey,
                                        ReactivatedSummaryStatisticalBo::convert,
                                        ReactivatedSummaryStatisticalBo::merge))
                        ).map(Map::values)
                        .orElseGet(Collections::emptyList);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, ReactivatedSummaryStatisticalBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, ReactivatedSummaryStatisticalBo::getCpgCode, Cpg.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, ReactivatedSummaryStatisticalBo::getOutletCode, Outlet.class);

        //convert result
        return list.stream()
                .map(e -> new ReactivatedForSummaryBean()
                        .setVoucherAmount(super.toAmount(ConvertUtils.toBigDecimal(e.getTotalAmount(), BigDecimal.ZERO)))
                        .setNumberOfVouchers(String.valueOf(e.getNumberOfVouchers()))
                        .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName()))
                .collect(Collectors.toList());
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class ReactivatedSummaryStatisticalBo {

        /**
         * Merchant code.
         */
        private String merchantCode;

        /**
         * Outlet code.
         */
        private String outletCode;

        /**
         * Cpg code.
         */
        private String cpgCode;

        /**
         * Number of vouchers.
         */
        private int numberOfVouchers = 1;

        /**
         * Total amount.
         */
        private BigDecimal totalAmount;

        public static ReactivatedSummaryStatisticalBo convert (ReactivatedBo reactivatedBo) {

            return new ReactivatedSummaryStatisticalBo()
                    .setMerchantCode(reactivatedBo.getMerchantCode())
                    .setOutletCode(reactivatedBo.getOutletCode())
                    .setCpgCode(reactivatedBo.getCpgCode())
                    .setTotalAmount(reactivatedBo.getDenomination());
        }

        public ReactivatedSummaryStatisticalBo merge (ReactivatedSummaryStatisticalBo bo) {

            this.numberOfVouchers += bo.getNumberOfVouchers();
            this.totalAmount = this.totalAmount.add(bo.getTotalAmount());

            return this;
        }

        public static String groupKey (ReactivatedBo reactivatedBo) {

            return StringUtils.join(",", reactivatedBo.getMerchantCode(), reactivatedBo.getOutletCode(), reactivatedBo.getCpgCode());
        }

    }
}
