package com.gtech.gvcore.service.impl;


import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.request.receive.QueryVoucherReceiveBatchRequest;
import com.gtech.gvcore.common.request.receive.VoucherReceiveBatchRequest;
import com.gtech.gvcore.common.response.voucherreceive.VoucherReceiveBatchResponse;
import com.gtech.gvcore.dao.mapper.VoucherReceiveBatchMapper;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.VoucherReceiveBatch;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.VoucherReceiveBatchService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class VoucherReceiveBatchServiceImpl implements VoucherReceiveBatchService {

	@Autowired
	private VoucherReceiveBatchMapper voucherReceiveBatchMapper;
	
	@Autowired
	private CpgService cpgService;

	@Autowired
	private GvCodeHelper gvCodeHelper;

	@SuppressWarnings("unchecked")
	@Override
	public List<VoucherReceiveBatchResponse> queryReceiveBatchList(QueryVoucherReceiveBatchRequest request) {
		
		Map<String, Object> map = JSON.parseObject(JSON.toJSONString(request), Map.class);
		List<VoucherReceiveBatch> list = voucherReceiveBatchMapper.query(map);
		if (CollectionUtils.isEmpty(list)) {
			return Collections.emptyList();
		}
		List<VoucherReceiveBatchResponse> responseList = BeanCopyUtils.jsonCopyList(list, VoucherReceiveBatchResponse.class);
		List<String> cpgCodeList = list.stream().map(VoucherReceiveBatch::getCpgCode).collect(Collectors.toList());
		Map<String, Cpg> cpgMap = cpgService.queryCpgMapByCpgCodeList(cpgCodeList);
		for (VoucherReceiveBatchResponse vo : responseList) {
			Cpg cpg = cpgMap.get(vo.getCpgCode());
			if(cpg != null) {
				vo.setCpgName(cpg.getCpgName());
			}
		}
		return responseList;
	}

	@Override
	public int saveReceiveBatch(List<VoucherReceiveBatchRequest> receiveBatchList) {
		if (CollectionUtils.isEmpty(receiveBatchList)) {
			return 0;
		}
		for (VoucherReceiveBatchRequest voucherReceiveBatchRequest : receiveBatchList) {
			voucherReceiveBatchRequest.setVoucherReceiveBatchCode(gvCodeHelper.generateVoucherReceiveBatchCode());
		}
		List<VoucherReceiveBatch> list = BeanCopyUtils.jsonCopyList(receiveBatchList, VoucherReceiveBatch.class);
		return voucherReceiveBatchMapper.insertList(list);
	}

	@Override
	public void updateReceivedNum(String voucherReceiveBatchCode, int count) {
		voucherReceiveBatchMapper.updateVoucherBatchReceivedNum(voucherReceiveBatchCode, count);
	}

}
