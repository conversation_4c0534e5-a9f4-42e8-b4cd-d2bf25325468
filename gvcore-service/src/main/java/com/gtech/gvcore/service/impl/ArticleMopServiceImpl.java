package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.articlemop.CreateArticleMopRequest;
import com.gtech.gvcore.common.request.articlemop.QueryArticleMopsByPageRequest;
import com.gtech.gvcore.common.request.articlemop.UpdateArticleMopRequest;
import com.gtech.gvcore.common.request.articlemop.UpdateArticleMopStatusRequest;
import com.gtech.gvcore.common.response.articlemop.QueryArticleMopsByPageResponse;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.mapper.ArticleMopMapper;
import com.gtech.gvcore.dao.model.ArticleMop;
import com.gtech.gvcore.service.ArticleMopService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
@Service
public class ArticleMopServiceImpl implements ArticleMopService {

    @Autowired
    private ArticleMopMapper articleMopMapper;

    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    public Result<Void> createArticleMop(CreateArticleMopRequest request) {

        ArticleMop articleMop = new ArticleMop();
        articleMop.setArticleCode(request.getArticleCode());
        articleMop.setMopCode(request.getMopCode());

        BeanUtils.copyProperties(request, articleMop);
        articleMop.setArticleMopCode(UUIDUtils.generateCode());
        articleMop.setStatus(GvcoreConstants.STATUS_ENABLE);
        articleMop.setCreateTime(new Date());
        try {
            articleMopMapper.insertSelective(articleMop);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }

        return Result.ok();
    }

    @Override
    public Result<Void> updateArticleMop(UpdateArticleMopRequest request) {

        ArticleMop articleMop = articleMopMapper.selectByPrimaryKey(request.getId());
        if (articleMop == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        BeanUtils.copyProperties(request, articleMop);
        articleMop.setUpdateTime(new Date());
        int i = articleMopMapper.updateByPrimaryKey(articleMop);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }

        masterDataCache.updateArticleMopCache(articleMop);
        return Result.ok();
    }

    @Override
    public Result<Void> updateArticleMopStatus(UpdateArticleMopStatusRequest request) {

        ArticleMop articleMop = new ArticleMop();
        articleMop.setId(request.getId());
        int i = articleMopMapper.selectCount(articleMop);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        articleMop.setStatus(request.getStatus());
        articleMop.setUpdateUser(request.getUpdateUser());
        articleMop.setUpdateTime(new Date());
        i = articleMopMapper.updateByPrimaryKeySelective(articleMop);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }

        return Result.ok();
    }

    @Override
    public PageResult<QueryArticleMopsByPageResponse> queryArticleMopsByPage(QueryArticleMopsByPageRequest request) {

        ArticleMop articleMop = new ArticleMop();
        articleMop.setArticleCodeName(request.getArticleCodeName());

        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<ArticleMop> list = articleMopMapper.selectSelective(articleMop);
        PageInfo<ArticleMop> pageInfo = new PageInfo<>(list);

        List<QueryArticleMopsByPageResponse> responses = new ArrayList<>(request.getPageSize());
        list.forEach(item -> {
            QueryArticleMopsByPageResponse response = new QueryArticleMopsByPageResponse();
            BeanUtils.copyProperties(item, response);
            responses.add(response);
        });

        return PageResult.ok(responses, pageInfo.getTotal());
    }

    @Override
    public ArticleMop queryByArticleMopCode(String articleMopCode) {

        if (StringUtils.isBlank(articleMopCode)) {
            return null;
        }

        ArticleMop articleMop = new ArticleMop();
        articleMop.setArticleMopCode(articleMopCode);
        return articleMopMapper.selectOne(articleMop);
    }

	@Override
	public Map<String, ArticleMop> queryByArticleMopCodeList(List<String> articleMopCodeList) {
		
		if(CollectionUtils.isEmpty(articleMopCodeList)) {
			return Collections.emptyMap();
		}
		
		List<ArticleMop> list = articleMopMapper.queryByArticleMopCodeList(articleMopCodeList);
		if(CollectionUtils.isEmpty(list)) {
			return Collections.emptyMap();
		}
		return list.stream().collect(Collectors.toMap(ArticleMop :: getArticleMopCode, v -> v));
	}

}
