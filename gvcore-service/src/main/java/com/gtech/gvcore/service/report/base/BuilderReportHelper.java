package com.gtech.gvcore.service.report.base;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportStatusEnum;
import com.gtech.gvcore.common.request.flow.SendEmailRequest;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.dao.mapper.ReportRequestMapper;
import com.gtech.gvcore.dao.model.OrderReportFile;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.OrderReportFileService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.SchedulerReportTaskService;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.export.snapshoot.ReportDataHelper;
import com.gtech.gvcore.service.report.export.snapshoot.ReportDateContext;
import com.gtech.gvcore.service.report.extend.ReportFactory;
import com.gtech.gvcore.service.report.extend.ReportOutletTreeParamBuilder;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @ClassName BuilderReportServiceImpl
 * @Description 报表构建
 * <AUTHOR>
 * @Date 2022/10/18 10:44
 * @Version V1.0
 **/
@Slf4j
@Service
public class BuilderReportHelper {

    @Autowired
    protected ReportRequestMapper reportRequestMapper;
    @Autowired
    protected ReportDataHelper reportDataHelper;
    @Autowired
    protected SchedulerReportTaskService schedulerReportTaskService;
    @Autowired
    protected GvUserAccountService userAccountService;
    @Autowired
    protected OutletService outletService;
    @Autowired
    protected VoucherService voucherService;
    @Autowired
    private OrderReportFileService orderReportFileService;
    @Autowired
    private ReportOutletTreeParamBuilder reportOutletTreeParamBuilder;
    @Autowired
    private OssHelper ossHelper;

    /**
     * 构建一个业务报表
     */
    public String fastBuilder(final CreateReportRequest request) {

        //为空则为数据为命中
        try (final ReportContext context = ReportContextBuilder.builder()
                .builderReportRequest(new ReportRequest()
                        .setReportCode(UUID.randomUUID().toString())
                        .setRequestJson(JSON.toJSONString(request))
                        .setReportType(request.getReportType()))
                .builderBusinessReport(ReportFactory.getReportImpl(request.getReportType()))
                .bindDefaultFileContext()
                .bindReportDateContext(new ReportDateContext() {})
                .builderInitFunction(this::builderInit)
                .builderReportFunction(this::builderReportByContext)
                .customContext()
                .build()
        ) {

            builderReport(context);

            return context.getResultFilePath();

        } catch (Exception e) {

            log.error("BuilderReportServiceImpl.builder error", e); // NOSONAR sonar 警告该该部份代码中字符串重复需要提成变量 但此处为日志忽略错误
        }

        return null;
    }

    /**
     * 构建一个业务报表
     *
     * @param reportCode
     */
    public String reBuilder(final String reportCode) {

        //为空则为数据为命中
        try (final ReportContext context = reBuilderContextByReport(reportCode)) {

            if (null == context) return null;

            builderReport(context);

            return ossHelper.grantAccessUrl(context.getResultFilePath());

        } catch (Exception e) {

            log.error("BuilderReportServiceImpl.builder error", e); // NOSONAR sonar 警告该该部份代码中字符串重复需要提成变量 但此处为日志忽略错误
        }

        return null;
    }

    /**
     * 构建一个业务报表
     *
     * @param reportCode
     */
    public void builder(final String reportCode) {

        //为空则为数据为命中
        try (final ReportContext context = builderDefaultContext(reportCode)) {

            if (null == context) return;

            builderReport(context);

        } catch (Exception e) {

            log.error("BuilderReportServiceImpl.builder error", e);
        }

    }

    /**
     * 构建一个业务报表根据上下文对象
     *
     * @param context
     */
    private void builderReport(final ReportContext context) {

        try {

            //上下文初始化
            context.init();

            //构建
            context.builderReport();

            //完成
            context.finish();

        } catch (Exception e) {

            log.error("builder error reportCode => " + context.getReportCode(), e);
            context.error(e.getMessage());
        }

    }

    /**
     * 根据业务包表构建上下文对象
     *
     * @param reportCode
     * @return
     */
    private ReportContext builderDefaultContext(final String reportCode) {

        final ReportRequest reportRequest = validAndGetOrdAnderReport(reportCode);
        if (null == reportRequest) return null;

        return ReportContextBuilder.builder()
                .builderReportRequest(reportRequest)
                .builderBusinessReport(ReportFactory.getReportImpl(reportRequest.getReportType()))
                .bindDefaultFileContext()
                .bindDefaultReportDateContext(reportDataHelper)
                .builderInitFunction(this::builderInit)
                .builderReportFunction(this::builderReportByContext)
                .builderCallbackFunction(this::callback)
                .builderUpdateReportStatusFunction(this::updateReportStatus)
                .customContext()
                .build();
    }

    /**
     * 根据业务包表构建上下文对象
     *
     * @param reportCode
     * @return
     */
    private ReportContext reBuilderContextByReport(final String reportCode) {

        final ReportRequest reportRequest = getReportRequest(reportCode);
        if (null == reportRequest) return null;

        return ReportContextBuilder.builder()
                .builderReportRequest(reportRequest)
                .builderBusinessReport(ReportFactory.getReportImpl(reportRequest.getReportType()))

                .bindDefaultFileContext()
                .bindReportDateContext(new ReportDateContext() {
                })

                .builderInitFunction(this::builderInit)
                .builderReportFunction(this::builderReportByContext)
                .builderCallbackFunction(this::callback)
                .customContext()
                .build();
    }

    /**
     * 上下文初始化方法
     *
     * @param context
     */
    public void builderInit(final ReportContext context) {

        //如果失败直接终止流程
        //权限相关
        if (!this.makeDataPermission(context)) return;

        //bind outlet codes
        //outlet父子级加载
        bindOutletChildCode(context);

    }

    /**
     * 绑定outlet 子编码
     *
     * @param context
     */
    private void bindOutletChildCode(final ReportContext context) {

        final CreateReportRequest reportParam = context.getReportParam();
        reportParam.setOutletCodes(reportOutletTreeParamBuilder.builderOutletCodeList(reportParam.getOutletCodes()));
    }

    /**
     * 检查并获得业务报表实体
     *
     * @param reportCode
     * @return
     */
    private ReportRequest validAndGetOrdAnderReport(@NotBlank final String reportCode) {

        final ReportRequest reportRequest = getReportRequest(reportCode);
        if (reportRequest == null) return null;

        if (!ReportStatusEnum.PROCESSING.getCode().equals(reportRequest.getReportStatus())) {
            log.info("报表状态不正确 reportStatus={}, reportCode={}, 构建请求被拒绝。", reportRequest.getReportStatus(), reportCode);
            return null;
        }

        return reportRequest;
    }

    /**
     * 获取业务报表实体
     *
     * @param reportCode
     * @return
     */
    private ReportRequest getReportRequest(@NotBlank final String reportCode) {

        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }

        final ReportRequest reportRequest = this.reportRequestMapper.selectOne(new ReportRequest().setReportCode(reportCode));

        if (reportRequest == null) {
            log.info("builderReport reportRequest is null, reportCode={}", reportCode);
            return null;
        }
        HintManager.clear();
        return reportRequest;
    }

    /**
     * 获取执行许可
     *
     * @param context
     * @return
     */
    private boolean makeDataPermission(final ReportContext context) {

        final ReportRequest reportRequest = context.getReportRequest();
        final CreateReportRequest reportParam = context.getReportParam();

        String createUser = reportRequest.getCreateUser();
        if (StringUtil.isEmpty(createUser)) {
            context.updateReportStatus(ReportStatusEnum.FAILED, "createUser isEmpty");
            log.info("builderReport No permission, reportCode={}", reportRequest.getReportCode());
            return false;
        }

        final List<PermissionCodeResponse> list = userAccountService.queryPerrmissionCodeList(createUser);
        if (CollectionUtils.isEmpty(list)) {
            context.error("No permission data found");
            log.info("builderReport No permission, reportCode={}", reportRequest.getReportCode());
            return false;
        }

        return setPermissionData(context, reportParam, list);
    }


    /**
     * 权限筛选
     *
     * @param context
     * @param reportParam
     * @param permissionList
     * @return
     */
    private boolean setPermissionData(final ReportContext context,
                                      final CreateReportRequest reportParam,
                                      final List<PermissionCodeResponse> permissionList) {

        final List<String> companyCodeList = new ArrayList<>();
        final List<String> merchantCodeList = new ArrayList<>();
        final List<String> outletCodeList = new ArrayList<>();

        permissionList.forEach(permission -> {
            if (!CollectionUtils.isEmpty(permission.getCompanyCodeList())) companyCodeList.addAll(permission.getCompanyCodeList());
            if (!CollectionUtils.isEmpty(permission.getMerchentCodeList())) merchantCodeList.addAll(permission.getMerchentCodeList());
            if (!CollectionUtils.isEmpty(permission.getOutletCodeList())) outletCodeList.addAll(permission.getOutletCodeList());
        });

        //company
        return settingListIntersection(context, reportParam.getCompanyCodes(), companyCodeList, reportParam::setCompanyCodes)
                        //merchant
                        && settingListIntersection(context, reportParam.getMerchantCodes(), merchantCodeList, reportParam::setMerchantCodes)
                        //outlet
                        && settingListIntersection(context, reportParam.getOutletCodes(), outletCodeList, reportParam::setOutletCodes);
    }

    /**
     * 获取交集编码
     * 调用源 权限筛选 setPermissionData
     *
     * @param context
     * @param requestList
     * @param findList
     * @param setFunction
     * @return
     */
    public boolean settingListIntersection(final ReportContext context, final List<String> requestList,
                                           final List<String> findList, final Consumer<List<String>> setFunction) {

        if (CollectionUtils.isEmpty(requestList)) {
            setFunction.accept(findList);
            return true;
        }

        Collection<String> intersection = CollectionUtils.intersection(requestList, findList);
        if (CollectionUtils.isEmpty(intersection)) {
            context.updateReportStatus(ReportStatusEnum.NO_DATA, null);
            return false;
        }

        setFunction.accept(new ArrayList<>(intersection));
        return true;
    }

    /**
     * 更新报表状态
     *
     * @param context
     * @param reportStatusEnum
     * @return
     */
    private boolean updateReportStatus(final ReportContext context, final ReportStatusEnum reportStatusEnum) {

        final ReportRequest reportRequest = context.getReportRequest();

        final Example updateExample = new Example(ReportRequest.class);
        updateExample.createCriteria()
                .andEqualTo(ReportRequest.C_ID, ConvertUtils.toString(reportRequest.getId(), ""))
                .andEqualTo(ReportRequest.C_REPORT_STATUS, reportStatusEnum.getCode());

        log.info("report builder updateReportStatus => {} ", JSON.toJSONString(reportRequest));

        if (!HintManager.isMasterRouteOnly()) {
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }

        boolean result = this.reportRequestMapper.updateByConditionSelective(new ReportRequest()
                .setReportStatus(reportRequest.getReportStatus())
                .setErrorMessage(StringUtils.substring(reportRequest.getErrorMessage(), 0, 2047)), updateExample) > 0;

        HintManager.clear();

        return result;
    }

    /**
     * 构建报表
     *
     * @param context
     */
    private void builderReportByContext(final ReportContext context) {

        final String reportCode = context.getReportCode();
        final BusinessReport<ReportQueryParam, ?> reportImpl = context.getBusinessReport();

        //如果报表被声明关闭则直接返回
        if (context.getStatus().close()) return;

        log.info("builderReportByContext start execute. reportCode:{}", reportCode);

        if (reportImpl instanceof PollReport) ((PollReport) reportImpl).builder(context);
        else if (reportImpl instanceof SingleReport) ((SingleReport) reportImpl).builder(context);
        else throw new UnsupportedOperationException("报表没有明确标识请注意继承结构");

    }

    /**
     * 报表回调
     *
     * @param context
     */
    private void callback(final ReportContext context) {

        saveReportFile(context);

        sendEmail(context);
    }

    /**
     * 保存报表文件
     *
     * @param context
     */
    private void saveReportFile(final ReportContext context) {

        if (HintManager.isMasterRouteOnly()){
            HintManager.clear();
            HintManager hintManager = HintManager.getInstance();
            hintManager.setMasterRouteOnly();
        }

        final String resultFilePath = context.getResultFilePath();
        final String reportCode = context.getReportCode();
        final ReportRequest reportRequest = context.getReportRequest();
        final String createUser = reportRequest.getCreateUser();
        final OrderReportFile orderReportFile = OrderReportFile.builder()
                .orderReportCode(reportCode)
                .createUser(createUser)
                .reportFileType(ExportTypeEnum.EXCEL_TYPE.getType()) // 导出类型目前已经废弃默认设置为excel但不做业务用途
                .reportFileUrl(resultFilePath)
                .build();

        orderReportFileService.insertList(Collections.singletonList(orderReportFile));
        HintManager.clear();

    }

    /**
     * 发送email
     *
     * @param context
     */
    private void sendEmail(final ReportContext context) {

        final ReportRequest reportRequest = context.getReportRequest();
        final String emailAddress = reportRequest.getReceiveEmail();
        final ReportExportTypeEnum exportTypeEnumByType = context.getReportExportTypeEnum();
        final String schedulerReportCode = reportRequest.getSchedulerReportCode();
        final String resultFilePath = context.getResultFilePath();

        if (StringUtil.isEmpty(emailAddress)) return;
        if (StringUtils.isBlank(schedulerReportCode)) return;

        final Map<String, Object> params = new HashMap<>();
        params.put("bulkOrderReport", exportTypeEnumByType.getExportName());

        //request
        final SendEmailRequest request = new SendEmailRequest();
        request.setExtendParams(params);
        request.setBusinessCode(String.valueOf(reportRequest.getReportType()));
        request.setEmails(Arrays.stream(emailAddress.split(",")).filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toList()));

        //vo
        final SendEmailRequest.FileVo fileVo = new SendEmailRequest.FileVo();
        fileVo.setFilename(exportTypeEnumByType.getExportName() + ExportTypeEnum.getSuffix(ExportTypeEnum.EXCEL_TYPE.getType()));
        fileVo.setUrl(ossHelper.grantAccessUrl(resultFilePath));

        schedulerReportTaskService.rollbackSchedulerReport(request, schedulerReportCode, emailAddress, fileVo);
    }

}