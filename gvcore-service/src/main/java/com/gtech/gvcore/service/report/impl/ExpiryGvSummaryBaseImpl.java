package com.gtech.gvcore.service.report.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.handler.context.WorkbookWriteHandlerContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.export.file.FileContext;
import com.gtech.gvcore.service.report.export.file.ReportExcelUtils;
import com.gtech.gvcore.service.report.export.snapshoot.label.ReportLabelContextBean;
import com.gtech.gvcore.service.report.extend.ReportUploadHelper;
import com.gtech.gvcore.service.report.extend.context.ReportContextBuilder;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.ExpiryGvSummaryBean;
import com.gtech.gvcore.service.report.impl.param.ExpiryGvQueryData;
import com.gtech.gvcore.service.report.impl.support.expiry.ExpiryGvBaseImpl;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:59
 * @Description:
 */
@Service
public class ExpiryGvSummaryBaseImpl extends ExpiryGvBaseImpl<ExpiryGvSummaryBean>
        implements BusinessReport<ExpiryGvQueryData, ExpiryGvSummaryBean>, SingleReport {

    private static final String TOTAL_BALANCE = "TOTAL BALANCE";
    private static final String SUM_OF_AMOUNT = "sumOfAmount";

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.EXPIRY_GV_SUMMARY_REPORT;
    }

    @Override
    public void customContext(ReportContextBuilder builder) {

        builder.bindFileContext(new ExpiryGvSummaryFileContext());
    }

    @Override
    public List<ExpiryGvSummaryBean> getExportData(ExpiryGvQueryData queryData) {

        //result map
        final Map<String, Map<String, BigDecimal>> resultMap = getResultMap(queryData);
        if (MapUtils.isEmpty(resultMap)) return Collections.emptyList();

        //title
        ReportContextHelper.findContext().putCache("TITLE_LIST", getAllMonthList(queryData.getVoucherEffectiveDateStart(), queryData.getVoucherEffectiveDateEnd()));

        //init
        final Date voucherEffectiveDateEnd = queryData.getVoucherEffectiveDateEnd();
        final String expiryDate = GvDateUtil.formatUs(voucherEffectiveDateEnd, DateUtil.FORMAT_DEFAULT);
        final List<String> monthList = getAllMonthList(queryData.getVoucherEffectiveDateStart(), voucherEffectiveDateEnd);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(resultMap.keySet(), Cpg.class);
        final Map<String, BigDecimal> total = new HashMap<>();
        final AtomicInteger index = new AtomicInteger(1);

        //result convert to bean
        final List<ExpiryGvSummaryBean> result = resultMap.keySet().stream()
                .sorted()
                .map(cpgCode -> {

                    Map<String, BigDecimal> amountMap = resultMap.get(cpgCode);

                    //map to bean
                    ExpiryGvSummaryBean bean = getBean(monthList, amountMap)
                            .setVoucherProgramGroup(cpgMap.findValue(cpgCode).getCpgName())
                            .setExpiryDate(expiryDate)
                            .setNo(String.valueOf(index.getAndIncrement()));

                    //total add all amount map
                    amountMap.forEach((k, v) -> total.merge(k, v, BigDecimal::add));

                    return bean;
                }).collect(Collectors.toList());

        //add total
        result.add(getBean(monthList, total).setNo(TOTAL_BALANCE));

        //return result
        return result;
    }

    /**
     * get result bean
     *
     * @param queryData query data
     * @return result bean
     */
    private Map<String, Map<String, BigDecimal>> getResultMap(ExpiryGvQueryData queryData) {

        // init query data
        final Map<String, Map<String, BigDecimal>> resultMap = new HashMap<>();

        getVoucherList(queryData).forEach(voucher -> {

            // init data
            final String cpgCode = voucher.getCpgCode();
            final String date = GvDateUtil.formatUs(voucher.getVoucherEffectiveDate(), GvDateUtil.FORMAT_YYYY_MM_BAR);

            // put data
            final Map<String, BigDecimal> map = resultMap.computeIfAbsent(cpgCode, k -> new HashMap<>());
            final BigDecimal amount = map.computeIfAbsent(date, k -> BigDecimal.ZERO);
            map.put(date, amount.add(voucher.getDenomination()));
        });

        // return result
        return resultMap;
    }

    /**
     * get all month list
     *
     * @param queryData query data
     * @return month list
     */
    private Collection<Voucher> getVoucherList(ExpiryGvQueryData queryData) {

        //find voucher code
        final List<String> voucherCodes = super.getVoucherCodeList(queryData);

        // return result
        return super.getMapByCode(voucherCodes, Voucher.class).values();
    }

    /**
     * get bean
     *
     * @param monthList month list
     * @param amountMap amount map
     * @return bean
     */
    private ExpiryGvSummaryBean getBean(List<String> monthList, Map<String, BigDecimal> amountMap) {

        //init json object
        final JSONObject jsonObject = new JSONObject();

        final AtomicInteger index = new AtomicInteger(1);

        //foreach month list
        IntStream.rangeClosed(1, monthList.size())
                .map(i -> monthList.size() + 1 - i)
                .forEach(i -> jsonObject.put(SUM_OF_AMOUNT + index.getAndIncrement(), ConvertUtils.toString(amountMap.getOrDefault(monthList.get(i - 1), null), "-")));

        //return bean
        return JSON.toJavaObject(jsonObject, ExpiryGvSummaryBean.class);

    }

    /**
     * get all month list
     *
     * @param startDate start date
     * @param endDate   end date
     * @return month list
     */
    public List<String> getAllMonthList(Date startDate, Date endDate) {

        // init start and end year and month
        final int startYear = Integer.parseInt(DateUtil.format(startDate, DateUtil.FORMAT_YYYY));
        final int startMonth = Integer.parseInt(DateUtil.format(startDate, DateUtil.FORMAT_MM));
        final int endYear = Integer.parseInt(DateUtil.format(endDate, DateUtil.FORMAT_YYYY));
        final int endMonth = Integer.parseInt(DateUtil.format(endDate, DateUtil.FORMAT_MM));
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(GvDateUtil.FORMAT_YYYY_MM_BAR);

        //return month list
        return IntStream.rangeClosed(startYear, endYear)
                .boxed()
                .flatMap(year -> IntStream.rangeClosed(1, 12)
                        .filter(month ->
                                (startYear == endYear && month >= startMonth && month <= endMonth) || // 如果同年则只返回区间内的月份
                                (startYear != endYear && year == startYear && month >= startMonth) || //排除起始年份的前置月份
                                (startYear != endYear && year == endYear && month <= endMonth) || // 排除结束年份的后置月份
                                (year > startYear && year < endYear) // 符合区间的年份
                        ).mapToObj(month -> YearMonth.of(year, month))
                )
                .map(formatter::format)
                .collect(Collectors.toCollection(ArrayList::new));
    }

    /**
     * get field label name (for report label)
     *
     * @param bean report label context bean
     * @return field label name
     */
    @SuppressWarnings("unused")
    public String getFieldLabelName(ReportLabelContextBean bean) {

        final CreateReportRequest reportParam = bean.getReportParam();
        final String fieldName = bean.getFieldName();
        final Date startDate = reportParam.getVoucherEffectiveDateStart();
        final Date endDate = GvConvertUtils.toObject(reportParam.getVoucherEffectiveDateEnd(), new Date());

        // 生成所有月份的列表
        final List<String> monthList = getAllMonthList(startDate, endDate);
        // 解析字段名中的数字，得到要求的月份在monthList中的索引号
        final int monthIndex = Integer.parseInt(fieldName.replace(SUM_OF_AMOUNT, "")) - 1;

        return monthList.stream()
                .sorted(Comparator.reverseOrder())
                // 跳过前面的monthIndex个元素
                .skip(monthIndex)
                // 获取第一个元素，即要求的月份
                .findFirst()
                // 解析要求的月份日期，并获取该月份的第一天 拼接报表字符串，包含要求的月份的总金额和日期信息
                .map(str -> "Total Amount " + GvDateUtil.formatUs(DateUtil.getStartDayOfMonth(DateUtil.parseDate(str, "yyyy-MM")), GvDateUtil.FORMAT_US_DATETIME_MMM_YYYY))
                .orElse("");// 如果没有获取到要求的月份，则返回空字符串

    }

    /**
     * excel context
     */
    public static class ExpiryGvSummaryFileContext implements FileContext {

        // template name
        private static final String TEMPLATE_NAME = ReportExportTypeEnum.EXPIRY_GV_SUMMARY_REPORT.getTemplateName();
        // output stream
        private final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        // excel writer
        private ExcelWriter excelWriter;

        @Override
        public void init() {

            excelWriter = EasyExcelFactory.write(outputStream)
                    .registerWriteHandler(new ExpirySummaryWorkbookWriteHandler())
                    .withTemplate(Thread.currentThread().getContextClassLoader().getResourceAsStream(TEMPLATE_NAME))
                    .build();
        }

        @Override
        public void doFill(List<?> beanList) {

            excelWriter.write(beanList, EasyExcelFactory.writerSheet(ReportExportTypeEnum.EXPIRY_GV_SUMMARY_REPORT.getSheetName()).build());
        }

        @Override
        public String finish() {

            excelWriter.finish();

            return ReportUploadHelper.fileUpload(ReportContextHelper.findContext(), new ByteArrayInputStream(outputStream.toByteArray()));
        }
    }

    public static class ExpirySummaryWorkbookWriteHandler implements WorkbookWriteHandler {

        // style map
        private final EnumMap<Style, CellStyle> styleMap = new EnumMap<>(Style.class);
        // style sheet name
        private static final String STYLE_SHEET_NAME = "STYLE";
        // number value and total value begin index
        private static final int CELL_BEGIN_INDEX = 3;


        /**
         * style type enum
         */
        private enum Style {
            TITLE, DEFAULT, TOTAL, TOTAL_AMOUNT
        }

        private void initStyle(SXSSFWorkbook workbook) {

            if (!styleMap.isEmpty()) return;

            //load style
            final Sheet sheet = workbook.getXSSFWorkbook().getSheet(STYLE_SHEET_NAME);
            styleMap.put(Style.TITLE, ReportExcelUtils.getCell(sheet, 0, 0).getCellStyle());
            styleMap.put(Style.DEFAULT, ReportExcelUtils.getCell(sheet, 1, 0).getCellStyle());
            styleMap.put(Style.TOTAL, ReportExcelUtils.getCell(sheet, 2, 0).getCellStyle());
            styleMap.put(Style.TOTAL_AMOUNT, ReportExcelUtils.getCell(sheet, 3, 0).getCellStyle());
        }

        @Override
        public void afterWorkbookDispose(WorkbookWriteHandlerContext context) {

            SXSSFWorkbook workbook = (SXSSFWorkbook) context.getWriteWorkbookHolder().getWorkbook();

            //init style
            this.initStyle(workbook);

            //title data
            final List<String> titleList = ReportContextHelper.findContext().getCacheList("TITLE_LIST");
            //get SXSSFSheet
            final SXSSFSheet sheet = workbook.getSheet(ReportExportTypeEnum.EXPIRY_GV_SUMMARY_REPORT.getSheetName());
            //get row iterator
            final Iterator<Row> rowIterator = sheet.rowIterator();

            //init title
            initTitle(titleList, workbook);

            //for other row
            while (rowIterator.hasNext()) {

                final Row row = rowIterator.next();
                final Cell cell = row.getCell(0);

                //skip title row
                if (row.getRowNum() == 0) continue;

                //total row
                if (Objects.equals(TOTAL_BALANCE, cell.getStringCellValue())) {

                    //merge cell
                    ReportExcelUtils.mergeCol(cell.getSheet(), 0, 2, cell.getRowIndex());

                    IntStream.rangeClosed(0, 2)
                            .boxed()
                            .map(e -> ReportExcelUtils.getCell(row, e))
                            .forEach(e -> e.setCellStyle(styleMap.get(Style.TOTAL)));

                    // setting total style
                    IntStream.rangeClosed(CELL_BEGIN_INDEX, titleList.size() + CELL_BEGIN_INDEX -1)
                            .boxed()
                            .map(e -> ReportExcelUtils.getCell(row, e))
                            .forEach(e -> e.setCellStyle(styleMap.get(Style.TOTAL_AMOUNT)));
                } else {

                    IntStream.rangeClosed(0, titleList.size() + CELL_BEGIN_INDEX -1)
                            .boxed()
                            .map(e -> ReportExcelUtils.getCell(row, e))
                            .forEach(e -> e.setCellStyle(styleMap.get(Style.DEFAULT)));
                }
            }
            }

        private void initTitle(List<String> titleList, SXSSFWorkbook workbook) {

            //reverse title list
            Collections.reverse(titleList);

            // get xssf sheet
            final XSSFSheet sheet = workbook.getXSSFWorkbook().getSheet(ReportExportTypeEnum.EXPIRY_GV_SUMMARY_REPORT.getSheetName());

            //get top row
            final Row topRow = ReportExcelUtils.getRow(sheet, 0);

            //setting static title
            IntStream.rangeClosed(0, 2)
                    .boxed()
                    .map(e -> ReportExcelUtils.getCell(topRow, e))
                    .forEach(e -> e.setCellStyle(styleMap.get(Style.TITLE)));

            // auto title
            IntStream.rangeClosed(CELL_BEGIN_INDEX, titleList.size() + CELL_BEGIN_INDEX - 1)
                    .boxed()
                    .map(e -> ReportExcelUtils.getCell(topRow, e))
                    .forEach(e -> {
                        // cell index
                        int columnIndex = e.getColumnIndex();
                        //set title
                        e.setCellValue("Total Amount " + GvDateUtil.formatUs(DateUtil.parseDate(titleList.get(columnIndex - 3), "yyyy-MM"), "MMM YYYY"));
                        //set style
                        e.setCellStyle(styleMap.get(Style.TITLE));
                        //set column width
                        sheet.setColumnWidth(columnIndex, 20 * 256);
                    });

        }


    }
}
