package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class BookletInventoryQueryData extends PageParam implements ReportQueryParam {

    private String issuerCode;

    private List<String> issuerCodes;

    private String merchantCode;

    private List<String> merchantCodes;

    private List<String> outletCodes;

    private List<String> cpgCodes;


    private List<String> orderStatus;

    private String startVoucherNumber;

    private String endVoucherNumber;

    private String startBookletNo;

    private String endBookletNo;

    private List<String> bookletStatus;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

    /**
     * 操作起始时间
     */
    private Date transactionDateStart;

    /**
     * 操作截止时间
     */
    private Date transactionDateEnd;

    //voucher effective date start 有效时间 开始
    private Date voucherEffectiveDateStart;

    //Voucher effective date end 有效时间 结束
    private Date voucherEffectiveDateEnd;

}
