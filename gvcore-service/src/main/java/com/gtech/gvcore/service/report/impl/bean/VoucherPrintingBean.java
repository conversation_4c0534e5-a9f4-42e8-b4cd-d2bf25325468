package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName VoucherPrintingBean
 * @Description
 * <AUTHOR>
 * @Date 2023/2/13 18:39
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class VoucherPrintingBean {

    // issuer name
    @ExcelProperty(value = "Issuer")
    private String issuerName;

    // cpg name
    @ExcelProperty(value = "Voucher Program Group")
    private String cpgName;

    // purchase order no
    @ExcelProperty(value = "PO Number")
    private String purchaseOrderNo;

    // voucher batch code
    @ExcelProperty(value = "Lot Number")
    private String voucherBatchCode;

    // create time
    @ExcelProperty(value = "Generated On")
    private String createTime;

    // create user
    @ExcelProperty(value = "Generated by")
    private String createUser;

    // printer name
    @ExcelProperty(value = "Printing Vendor")
    private String printerName;

    // effective date
    @ExcelProperty(value = "Expiry Date")
    private String voucherEffectiveDate;

    // booklet number
    @ExcelProperty(value = "Booklet Number (Number of Booklets)")
    private String bookletNumber;

    // voucher number
    @ExcelProperty(value = "Voucher Number (Number of Voucher)")
    private String voucherNumber;

    // status
    @ExcelProperty(value = "Status")
    private String status;


}