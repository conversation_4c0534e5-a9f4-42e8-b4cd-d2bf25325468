package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcReactivatedForDetailBean;
import com.gtech.gvcore.service.report.impl.bo.GcReactivatedBo;
import com.gtech.gvcore.service.report.impl.param.GcReactivatedQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description: Gift Card Reactivated (Block) Report Detail Implementation
 */
@Service
public class GcReactivatedForDetailImpl extends ReportSupport
        implements BusinessReport<GcReactivatedQueryData, GcReactivatedForDetailBean>, SingleReport {

    @Autowired
    protected IssueHandlingDetailsService issueHandlingDetailsService;

    @Override
    public GcReactivatedQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcReactivatedQueryData queryData = new GcReactivatedQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());

        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setCustomerCodes(reportParam.getCustomerCodes());
        if (StringUtils.isNotBlank(reportParam.getVoucherCode())) {
            queryData.setVoucherCode(Arrays.asList(reportParam.getVoucherCode().split(",")));
        }
        return queryData;
    }

    @Override
    public List<GcReactivatedForDetailBean> getExportData(GcReactivatedQueryData queryData) {
        List<GcReactivatedBo> list = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectGcReactivated, queryData, list::addAll);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcReactivatedBo::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, GcReactivatedBo::getMerchantCode, Merchant.class);
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, GcReactivatedBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, GcReactivatedBo::getOwnerCustomer, Customer.class);
        final Map<String, String> voucherMap = issueHandlingDetailsService.queryRemarkByVoucherCodeAndIssueType(list.stream().map(GcReactivatedBo::getVoucherCode)
                .distinct().collect(Collectors.toList()), IssueHandlingTypeEnum.GC_BULK_REACTIVATE);

        //convert result
        return list.stream()
                .map(e -> new GcReactivatedForDetailBean()
                        .setVoucherAmount(super.toAmount(ConvertUtils.toBigDecimal(e.getDenomination(), BigDecimal.ZERO)))
                        .setVoucherNumber(e.getVoucherCode())
                        .setTransactionDate(DateUtil.format(e.getTransactionDate(), "yyyy-MM-dd HH:mm:ss"))
                        .setInvoiceNumber(e.getInvoiceNumber())
                        .setReactivatedReason(StringUtils.isNotBlank(voucherMap.get(e.getVoucherCode())) ? voucherMap.get(e.getVoucherCode()) : e.getUnblockReason())
                        .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setCustomerName(customerMap.findValue(e.getOwnerCustomer()).getCustomerName()))
                .collect(Collectors.toList());
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_REACTIVATED_DETAILED;
    }
}
