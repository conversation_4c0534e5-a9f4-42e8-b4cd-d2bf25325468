package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName CancelSalesBo
 * @Description Cancel Sales BO
 * <AUTHOR>
 * @Date 2023/4/14 15:33
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class CancelSalesBo implements GroupNewTransactionByVoucherCodeSupport {


    /**
     * Voucher code.
     */
    private String voucherCode;

    /**
     * Merchant code.
     */
    private String merchantCode;

    /**
     * Outlet code.
     */
    private String outletCode;

    /**
     * Cpg code.
     */
    private String cpgCode;

    /**
     * Voucher effective date.
     */
    private String voucherEffectiveDate;

    /**
     * Denomination.
     */
    private BigDecimal denomination;

    /**
     * Transaction date.
     */
    private String transactionDate;

    /**
     * Invoice number.
     */
    private String invoiceNumber;

    /**
     * Customer code.
     */
    private String customerCode;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    private String transactionCode;

    private String transactionType;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }
}
