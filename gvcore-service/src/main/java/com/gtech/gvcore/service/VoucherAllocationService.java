package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.allocation.AllocateRequest;
import com.gtech.gvcore.common.request.allocation.GetVoucherAllocationRequest;
import com.gtech.gvcore.common.request.allocation.QueryVoucherAllocationByPageRequest;
import com.gtech.gvcore.common.request.customerorder.IssuanceRequest;
import com.gtech.gvcore.common.response.allocation.GetVoucherAllocationResponse;
import com.gtech.gvcore.common.response.allocation.QueryVoucherAllocationByPageResponse;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.dao.model.VoucherRequest;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022年3月8日
 */
public interface VoucherAllocationService {

    /**
     * 
     * <AUTHOR>
     * @return
     * @date 2022年3月8日
     */
	String createByVoucherRequest(VoucherRequest voucherRequest);
    
    /**
     * 
     * @param customerOrder
     * @return
     */
    int createOrUpdate(CustomerOrder customerOrder);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年3月8日
     */
    Result<Void> allocate(AllocateRequest request);
    
    /**
     * 
     * @param request
     * @param customerOrder
     * @param details
     * @return
     */
    Result<String> allocateByCustomerOrder(IssuanceRequest request, CustomerOrder customerOrder, List<CustomerOrderDetails> details);
    
    /**
     * @param invocieNo
     * @param customerOrder
     * @param isRelease
     * @param updateUser
     * @param approvalCode
     * @return
     * <AUTHOR>
     * @date 2022年3月24日
     */
    void customerOrderRelease(CustomerOrder customerOrder, boolean isRelease, String updateUser, String approvalCode);

    /**
     * 
     * <AUTHOR>
     * @param voucherAllocationCode
     * @param updateUser
     * @return
     * @date 2022年3月10日
     */
    void completed(String voucherAllocationCode, String updateUser);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年3月11日
     */
    PageResult<QueryVoucherAllocationByPageResponse> queryVoucherAllocationByPage(
            QueryVoucherAllocationByPageRequest request);

    /**
     * 
     * <AUTHOR>
     * @param request
     * @return
     * @date 2022年3月11日
     */
    Result<GetVoucherAllocationResponse> getVoucherAllocation(GetVoucherAllocationRequest request);

    Result<GetVoucherAllocationResponse> getVoucherAllocationByReceive(GetVoucherAllocationRequest request);

    VoucherAllocation getAllocationBySourceDataCode(String sourceDataCode, String businessType);

	VoucherAllocation getAllocationByCode(String voucherAllocationCode);

    /**
     * 
     * @param voucherAllocationCode
     * @return
     * <AUTHOR>
     * @date 2022年5月6日
     */
    VoucherAllocation queryByVoucherAllocationCode(String voucherAllocationCode);

    List<VoucherAllocation> queryByVoucherAllocationCodeList(List<String> voucherAllocationCode);

    Set<String> querySourceDataCodeVoucherAllocation(String voucherNumberStart, String voucherNumberEnd);

    int updateVoucherAllocationStatusByAllocationCode(String allocationCode, Integer status);


}
