package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.outletcpg.*;
import com.gtech.gvcore.common.response.outletcpg.OutletCpgResponse;
import com.gtech.gvcore.dao.mapper.OutletCpgMapper;
import com.gtech.gvcore.dao.model.OutletCpg;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.OutletCpgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/21 11:48
 */
@Service
public class OutletCpgServiceImpl implements OutletCpgService {
    @Autowired
    private OutletCpgMapper outletCpgMapper;

    @Autowired
    private GvCodeHelper codeHelper;

    @Override
    public Result<Void> createOutletCpg(CreateOutletCpgRequest param) {
        OutletCpg entity = BeanCopyUtils.jsonCopyBean(param, OutletCpg.class);
        entity.setOutletCpgCode(codeHelper.generateOutletCpgCode());
        entity.setCreateTime(new Date());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);
        try {
            outletCpgMapper.insertSelective(entity);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> updateOutletCpg(UpdateOutletCpgRequest param) {
        OutletCpg entity = BeanCopyUtils.jsonCopyBean(param, OutletCpg.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(OutletCpg.class);
        example.createCriteria()
                .andEqualTo(OutletCpg.C_OUTLET_CPG_CODE,param.getOutletCpgCode());

        try {
            outletCpgMapper.updateByConditionSelective(entity,example);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> deleteOutletCpg(DeleteOutletCpgRequest param) {
        Example example = new Example(OutletCpg.class);
        example.createCriteria()
                .andEqualTo(OutletCpg.C_OUTLET_CPG_CODE,param.getOutletCpgCode());
        outletCpgMapper.deleteByCondition(example);

        return Result.ok();
    }

    @Override
    public Result<Void> deleteOutletCpgByOutletCode(DeleteOutletCpgByOutletCodeRequest param) {

        Example example = new Example(OutletCpg.class);
        example.createCriteria()
                .andEqualTo(OutletCpg.C_OUTLET_CODE,param.getOutletCode());
        outletCpgMapper.deleteByCondition(example);
        return Result.ok();
    }

    @Override
    public PageResult<OutletCpgResponse> queryOutletCpgList(QueryOutletCpgRequest param) {

        PageHelper.startPage(param.getPageNum(),param.getPageSize());

        Example example = new Example(OutletCpg.class);
        example.createCriteria()
                .andEqualTo(OutletCpg.C_OUTLET_CPG_CODE,param.getOutletCpgCode())
                .andEqualTo(OutletCpg.C_CPG_CODE,param.getCpgCode())
                .andEqualTo(OutletCpg.C_OUTLET_CODE,param.getOutletCode())
                .andEqualTo(OutletCpg.C_STATUS,param.getStatus());
        //创建时间倒序
        example.orderBy(OutletCpg.C_CREATE_TIME).desc();

        List<OutletCpg> gvOutletCpgEntities = outletCpgMapper.selectByCondition(example);
        PageInfo<OutletCpg> info = PageInfo.of(gvOutletCpgEntities);

        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(),OutletCpgResponse.class),info.getTotal());
    }

    @Override
    public OutletCpgResponse getOutletCpg(GetOutletCpgRequest param) {
        OutletCpg entity = BeanCopyUtils.jsonCopyBean(param, OutletCpg.class);
        OutletCpg outletCpg = outletCpgMapper.selectOne(entity);

        return BeanCopyUtils.jsonCopyBean(outletCpg,OutletCpgResponse.class);
    }

    @Override
    public List<OutletCpgResponse> queryOutletCpgListByOutlet(String outletCode) {


        return outletCpgMapper.queryOutletCpgListByOutlet(outletCode);
    }

    @Override
    public List<OutletCpgResponse> queryOutletCpgListByOutletList(List<String> outletCodeList) {

        return outletCpgMapper.queryOutletCpgListByOutletList(outletCodeList);
    }
}
