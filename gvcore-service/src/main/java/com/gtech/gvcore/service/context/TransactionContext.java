package com.gtech.gvcore.service.context;

import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.pos.GetPosRequest;
import com.gtech.gvcore.common.request.transaction.TransactionRequest;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.pos.PosResponse;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.PosService;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Component
public class TransactionContext {

    @Autowired
    private PosService posService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private OutletService outletService;

    private static OutletService staticOutletService;

    private static GvCodeHelper staticGvCodeHelper;


    private static StringRedisTemplate staticRedisTemplate;

    private static PosService staticPosService;

    @PostConstruct
    public void init() {
        TransactionContext.staticPosService = posService;
        TransactionContext.staticRedisTemplate = redisTemplate;
        TransactionContext.staticGvCodeHelper = gvCodeHelper;
        TransactionContext.staticOutletService = outletService;

    }


    private static final ThreadLocal<TransactionInfoContext> transactionRequest = new ThreadLocal<>();



    @Data
    @Accessors(chain = true)
    public static class TransactionInfoContext{
        private TransactionRequest request;

        private PosResponse pos;
        private OutletResponse outlet;


        private String invoiceNumber;
        private String approvalCode;
        private String TransactionId;
        private String terminalId;
        private String batchId;

    }


    public static void init(TransactionRequest request,String terminalId,String batchId){
        TransactionInfoContext context = new TransactionInfoContext()
                .setRequest(request)
                .setInvoiceNumber(initInvoiceNumber(request))
                .setTerminalId(terminalId)
                .setBatchId(batchId)
                .setPos(initPos(terminalId))
                .setApprovalCode(initApproval(request))
                .setOutlet(initOutlet(initPos(terminalId)));

        transactionRequest.set(context);

    }

    private static PosResponse initPos(String terminalId){
        Result<PosResponse> pos = staticPosService.getPos(GetPosRequest.builder().machineId(terminalId).build());
        return pos.getData();
    }

    private static String initApproval(TransactionRequest request){

        String approvalCode = (String)staticRedisTemplate.opsForValue().get("APPROVALCODE:" + request.getTransactionId());
        if (StringUtils.isEmpty(approvalCode)) {
            approvalCode = staticGvCodeHelper.generateApproveCode();
            staticRedisTemplate.opsForValue().set("APPROVALCODE:" + request.getTransactionId(),approvalCode,2, TimeUnit.DAYS);
        }
        return approvalCode.replaceAll("\"", "");
    }

    private static OutletResponse initOutlet(PosResponse pos){
        OutletResponse outletResponse;
        try {
            outletResponse = staticOutletService.getOutlet(GetOutletRequest.builder().outletCode(pos.getOutletCode()).build());
        } catch (Exception e) {
            //cpg error
            throw new GTechBaseException(ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.code(), ResultErrorCodeEnum.NO_OUTLET_DATA_FOUND.desc());
        }
        return outletResponse;
    }

    private static String initInvoiceNumber(TransactionRequest request) {
        String invoiceNo = request.getInvoiceNumber();
        if (StringUtils.isEmpty(invoiceNo)) {
            invoiceNo = staticGvCodeHelper.generateInvoiceNumber();
        }
        return invoiceNo;
    }
    public static TransactionInfoContext getContext(){
       return transactionRequest.get();
    }

    public static TransactionRequest getRequest(){
       return transactionRequest.get().getRequest();
    }


    public static void clear() {
        transactionRequest.remove();
    }

}
