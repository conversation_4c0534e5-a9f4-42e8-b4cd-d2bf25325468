package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GiftCardMapper;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName VoucherQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:47
 * @Version V1.0
 **/
@Component
public class GcGiftCardQueryImpl implements QuerySupport<GiftCardEntity> {

    public static final GiftCardEntity EMPTY = new GiftCardEntity();

    @Autowired
    private GiftCardMapper voucherMapper;

    @Override
    public List<GiftCardEntity> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(GiftCardEntity.class);
        example.createCriteria().andIn("cardNumber", codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<GiftCardEntity> voucherList = voucherMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(voucherList)) return Collections.emptyList();

        return voucherList;
    }

    @Override
    public Function<GiftCardEntity, String> codeMapper() {
        return GiftCardEntity::getCardNumber;
    }

    @Override
    public GiftCardEntity emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<GiftCardEntity> supportType() {
        return GiftCardEntity.class;
    }
}
