package com.gtech.gvcore.service.report.extend.context;

import com.alibaba.fastjson.JSON;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportStatusEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.export.file.FileContext;
import com.gtech.gvcore.service.report.export.snapshoot.ReportDateContext;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.function.BiPredicate;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * @ClassName ReportContext
 * @Description 报表上下文
 * <AUTHOR>
 * @Date 2022/10/20 20:03
 * @Version V1.0
 **/
@Getter
@Slf4j
public class ReportContext implements AutoCloseable {

    //报表编码
    private final String reportCode;
    //报表类型
    private final ReportExportTypeEnum reportExportTypeEnum;
    //报表请求实体
    private final ReportRequest reportRequest;
    //报表请求参数
    private final CreateReportRequest reportParam;
    //数据快照上下文
    private final ReportDateContext reportDateContext;
    //excel表格上下文
    private final FileContext fileContext;
    //业务报表实现
    private final BusinessReport<ReportQueryParam, ?> businessReport;
    //查询条件
    private ReportQueryParam queryParam;
    //上下文状态
    private ReportContextStatusEnum status;

    //全局缓存
    private final ReportContextCacheHelper reportCache = new ReportContextCacheHelper();

    //结果地址
    private String resultFilePath;

    //上下文构建时间/报表构建时间 可用于判断过期卡券
    private final Date contextBuilderTime = new Date();

    //修改订单状态支持方法
    BiPredicate<ReportContext, ReportStatusEnum> updateReportStatusFunction;
    //初始化方法
    private final Consumer<ReportContext> initFunction;
    //执行结束回调方法
    private final Consumer<ReportContext> callbackFunction;
    //构建报表方法
    private final Consumer<ReportContext> builderReportFunction;

    private int dataTotal = 0;

    /**
     * 上下文构造函数
     * 该方法构建上下文对象
     * 并尝试锁定报表数据
     * 存在两种构建结果
     * Status.INSTANTIATE 构建成功
     * Status.ERROR 构建失败 (report type找不到, 锁定报表数据失败)
     * 构建结束需要判断构建状态 ERROR 情况不应该处理报表数据而应直接丢弃该上下文
     */
    public ReportContext(final ReportRequest reportRequest,
                         final BusinessReport<ReportQueryParam, ?> businessReport,
                         final ReportContextBuilder.ProcessFunctionBean processFunctionBean,
                         final ReportDateContext reportDateContext,
                         final FileContext fileContext) {

        log.info("ReportContext [constructor] _begin_ report code => {}", reportRequest.getReportCode());

        ReportExportTypeEnum reportTypeEnum = ReportExportTypeEnum.getExportTypeEnumByType(reportRequest.getReportType());

        this.reportExportTypeEnum = reportTypeEnum;
        this.reportCode = reportRequest.getReportCode();
        this.reportRequest = reportRequest;
        this.reportParam = JSON.parseObject(reportRequest.getRequestJson(), CreateReportRequest.class);

        this.businessReport = businessReport;

        this.initFunction = processFunctionBean.getInitFunction();
        this.builderReportFunction = processFunctionBean.getBuilderReportFunction();
        this.callbackFunction = processFunctionBean.getCallbackFunction();
        this.updateReportStatusFunction = processFunctionBean.getUpdateReportStatusFunction();

        this.reportDateContext = reportDateContext;
        this.fileContext = fileContext;

        this.status = null == reportTypeEnum ? ReportContextStatusEnum.ERROR : ReportContextStatusEnum.INSTANTIATE;

        log.info("ReportContext [constructor] _end_ report code => {}", reportRequest.getReportCode());
    }

    /**
     * 初始化方法 构建报表基础信息
     * 该方法 获取报表预期大小(total)
     * 如果大小 《= 0 则直接返回正常结束并修改报表数据为no data
     * 存在有效数据则构建file 需要的基础设施 以及推进context 状态
     */
    public ReportContextStatusEnum init() {

        log.info("ReportContext [init] _begin_ report code => {}", reportRequest.getReportCode());

        if (status != ReportContextStatusEnum.INSTANTIATE) {
            log.warn("ReportContext [init] _end_ status not eq, report code => {} status => {}", reportRequest.getReportCode(), status);
            return status;
        }

        //注册 ReportContextHelper
        ReportContextHelper.register(this);

        log.info("ReportContext [init] execute initFunction, report code => {}", reportRequest.getReportCode());

        //执行扩展初始化方法
        this.initFunction.accept(this);

        //获取查询参数
        this.queryParam = businessReport.builderQueryParam(this.reportParam);

        log.info("ReportContext [init] init report fileContext and data context, report code => {}", reportRequest.getReportCode());

        // data & file context init
        this.fileContext.init();
        this.reportDateContext.init();

        log.info("ReportContext [init] init report status, report code => {}", reportRequest.getReportCode());

        this.status = ReportContextStatusEnum.INITIALIZATION;

        log.info("ReportContext [init] _end_ report code => {}", reportRequest.getReportCode());

        return this.status;
    }

    public void builderReport() {

        log.info("ReportContext [builder] _begin_, report code => {}", reportRequest.getReportCode());

        if (status.close()) {
            log.warn("ReportContext [builder] _end_ status not eq, report code => {} status => {}", reportRequest.getReportCode(), status);
            return;
        }

        this.builderReportFunction.accept(this);

        log.info("ReportContext [builder] _end_, report code => {}", reportRequest.getReportCode());

    }

    public <T extends FileContext> T getFileContext(Class<T> type) {

        return type.cast(fileContext);
    }

    public <T extends ReportDateContext> T getReportDateContext(Class<T> type) {

        return type.cast(reportDateContext);
    }

    /**
     * 修改报表状态
     */
    public void updateReportStatus(ReportStatusEnum reportStatusEnum, String errorMessage) {

        ReportRequest report = this.reportRequest;

        Integer reportStatus = report.getReportStatus();
        report.setReportStatus(reportStatusEnum.getCode())
                .setErrorMessage(errorMessage)
                .setUpdateTime(new Date());

        this.updateReportStatusFunction.test(this, ReportStatusEnum.valueOfCode(reportStatus));
    }

    public void putCache(String key, Object value) {

        this.reportCache.putCache(key, value);
    }

    public <T> T getCacheIfAbsent(String key, Class<T> type, Supplier<T> supplier) {

        return this.reportCache.getCacheIfAbsent(key, type, supplier);
    }

    public <T> T getCache(String key, Class<T> type) {

        return this.reportCache.getCache(key, type);
    }

    public <V> JoinDataMap<V> getCacheJoinMap(String key) {

       return this.reportCache.getCacheJoinMap(key);
    }

    public <V> List<V> getCacheList(String key) {

        return this.reportCache.getCacheList(key);
    }

    /**
     * 结束
     */
    public void finish() {

        log.info("ReportContext [finish] _begin_, report code => {}", reportRequest.getReportCode());

        if (status.close()) {
            log.warn("ReportContext [finish] _end_ status not eq, report code => {} status => {}", reportRequest.getReportCode(), status);
            return;
        }

        log.info("ReportContext [finish] data save (dataContext, FileContext), report code => {}", reportRequest.getReportCode());
        this.dataSave();

        this.updateReportStatus(ReportStatusEnum.SUCCESS, null);

        this.status = ReportContextStatusEnum.SUCCESS;

        log.info("ReportContext [finish] call back execute..., report code => {}", reportRequest.getReportCode());
        this.callbackFunction.accept(this);

        log.info("ReportContext [finish] _end_, report code => {}", reportRequest.getReportCode());

        log.info("report context popup, reportCode: {}, reportType: {}, dataTotal: {}, resultFilePath: {}",
                this.reportCode, this.reportExportTypeEnum, this.dataTotal, this.resultFilePath);

    }

    private void dataSave() {

        log.info("ReportContext [finish] reportDateContext execute finish, report code => {}", reportRequest.getReportCode());
        reportDateContext.finish();

        log.info("ReportContext [finish] fileContext execute finish, report code => {}", reportRequest.getReportCode());
        this.resultFilePath = fileContext.finish();
    }

    /**
     * 异常
     */
    public void error(String errorMessage) {

        log.warn("ReportContext [error] _begin_, report code => {}", reportRequest.getReportCode());

        if (status.close()) return;

        log.info("ReportContext [error] reportDateContext execute error, report code => {}", reportRequest.getReportCode());
        reportDateContext.error();

        log.info("ReportContext [error] fileContext execute error, report code => {}", reportRequest.getReportCode());
        fileContext.error();

        this.updateReportStatus(ReportStatusEnum.FAILED, errorMessage);

        status = ReportContextStatusEnum.ERROR;

        log.warn("ReportContext [error] _end_, report code => {}", reportRequest.getReportCode());
        log.error("report context error, reportCode: {}, reportType: {}, dataTotal: {}, resultFilePath: {}",
                this.reportCode, this.reportExportTypeEnum, this.dataTotal, this.resultFilePath);
    }

    public void noData() {

        log.warn("ReportContext [noData] _begin_, report code => {}", reportRequest.getReportCode());

        if (status.close()) return;

        log.info("ReportContext [noData] reportDateContext execute error, report code => {}", reportRequest.getReportCode());
        reportDateContext.error();

        log.info("ReportContext [noData] reportDateContext execute error, report code => {}", reportRequest.getReportCode());
        fileContext.error();

        this.updateReportStatus(ReportStatusEnum.NO_DATA, null);

        status = ReportContextStatusEnum.SUCCESS;

        log.warn("ReportContext [noData] _end_, report code => {}", reportRequest.getReportCode());
    }

    public void appendDate(List<?> list) {

        if (CollectionUtils.isEmpty(list)) return;

        log.info("ReportContext [appendDate] _begin_, report code => {}, data size => {}", reportRequest.getReportCode(), list.size());

        this.dataTotal += list.size();

        reportDateContext.appendData(list);
        fileContext.doFill(list);

        log.info("ReportContext [appendDate] _end_, report code => {}, data size => {}", reportRequest.getReportCode(), list.size());

    }


    @Override
    public void close() throws Exception {

        log.warn("ReportContext [auto close], report code => {}", reportRequest.getReportCode());

        ReportContextHelper.cancel();
    }

}
