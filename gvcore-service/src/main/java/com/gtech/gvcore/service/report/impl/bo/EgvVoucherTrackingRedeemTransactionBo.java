package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName EgvTrackingTransactionBo
 * @Description
 * <AUTHOR>
 * @Date 2023/1/11 20:42
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class EgvVoucherTrackingRedeemTransactionBo implements GroupNewTransactionByVoucherCodeSupport {

    private String voucherCode;

    private String transactionDate;

    private String transactionCode;

    private String transactionType;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

}
