package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class GcBulkOrderQueryData extends PageParam implements ReportQueryParam {

	private Date transactionDateStart;
	private Date transactionDateEnd;
	private List<String> issuerCodeList;
	private List<String> cpgCodeList;
	private String invoiceNo;
	private String customerType;
	private List<String> customerCodeList;
	private String purchaseOrderNumber;
	private List<String> cardNumbers;

	private List<String> customerOrderCodeList;
}

