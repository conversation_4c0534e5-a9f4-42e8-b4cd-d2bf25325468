package com.gtech.gvcore.service.report.impl.support.aging.builder;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.impl.bean.aging.SummaryBean;
import com.gtech.gvcore.service.report.impl.bean.aging.VoucherUsedAgeBean;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheet;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheetBuilder;
import com.gtech.gvcore.service.report.impl.support.aging.bo.VoucherUsedAgeBo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName VoucherUsedAgeBuilder
 * @Description Voucher Used Age
 * <AUTHOR>
 * @Date 2022/11/1 16:59
 * @Version V1.0
 **/
@Component
public class VoucherUsedAgeBuilder implements AgingSheetBuilder, ReportProportionDataFunction {

    private static final int SCALE = 1;
    
    @Autowired
    private SummaryBuilder summaryBuilder;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.AGING_VOUCHER_USED_AGE_REPORT;
    }

    /**
     * 此方法构建并不是最优解
     * 后续后续如有需求 可进行优化
     *
     * @param context
     * @return
     */
    @Override
    public AgingSheet builder(final ReportContext context) {

        final List<SummaryBean> summarySheetDate = summaryBuilder.builderSummary(context);
        final List<VoucherUsedAgeBo> collect = summarySheetDate.stream()
                .filter(e -> StringUtils.isNotBlank(e.getSalesValue()))
                .filter(e -> !e.getSalesValue().contains("%"))
                .map(e -> BeanCopyUtils.jsonCopyBean(e, VoucherUsedAgeBo.class)
                        .setDetail(e.getVoucherDetails()))
                .collect(Collectors.toList());

        return new AgingSheet()
                .setHead(null)
                .setSheetName(exportTypeEnum().getSheetName())
                .addSheetData("vua", exportTypeEnum(), getResult(collect));
    }

    private List<VoucherUsedAgeBean> getResult(List<VoucherUsedAgeBo> collect) {

        return collect
                .stream()
                .map(e ->
                        new VoucherUsedAgeBean().setDetail(e.getDetail())
                                .setUsed(getProportion(e.getUsed(), e.sum(), SCALE))
                                .setAuOne(getProportion(e.getAuOne(), e.getUsed(), SCALE))
                                .setAuTwo(getProportion(e.getAuTwo(), e.getUsed(), SCALE))
                                .setAuThree(getProportion(e.getAuThree(), e.getUsed(), SCALE))
                                .setAuFour(getProportion(e.getAuFour(), e.getUsed(), SCALE))
                                .setAuSix(getProportion(e.getAuSix(), e.getUsed(), SCALE))
                ).collect(Collectors.toList());

    }


    @Override
    public Class<?> getExportDataClass() {
        return VoucherUsedAgeBean.class;
    }


}
