package com.gtech.gvcore.service;

import java.util.List;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.company.CreateCompanyRequest;
import com.gtech.gvcore.common.request.company.DeleteCompanyRequest;
import com.gtech.gvcore.common.request.company.GetCompanyRequest;
import com.gtech.gvcore.common.request.company.QueryCompanyByIssuerCodesRequest;
import com.gtech.gvcore.common.request.company.QueryCompanyRequest;
import com.gtech.gvcore.common.request.company.UpdateCompanyRequest;
import com.gtech.gvcore.common.request.company.UpdateCompanyStatusRequest;
import com.gtech.gvcore.common.response.company.CompanyResponse;
import com.gtech.gvcore.common.response.issuer.PermissionTreeResponse;
import com.gtech.gvcore.dao.model.Company;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
public interface CompanyService {

    Result<String> createCompany(CreateCompanyRequest param);

    Result<Void> updateCompany(UpdateCompanyRequest param);

    Result<Void> updateCompanyStatus(UpdateCompanyStatusRequest param);

    Result<Void> deleteCompany(DeleteCompanyRequest param);

    PageResult<CompanyResponse> queryCompanyList(QueryCompanyRequest param);

    CompanyResponse getCompany(GetCompanyRequest param);

    PageResult<CompanyResponse> queryCompanyByIssuerCodes(QueryCompanyByIssuerCodesRequest request);

	List<CompanyResponse> queryCompanyByCodes(List<String> companyCodeList);

    List<Company> queryAllCompany();

	List<PermissionTreeResponse> queryPermissionTree();
}
