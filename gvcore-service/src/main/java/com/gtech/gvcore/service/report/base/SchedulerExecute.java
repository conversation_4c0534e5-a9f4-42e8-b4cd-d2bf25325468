package com.gtech.gvcore.service.report.base;

import com.alibaba.fastjson.JSON;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.request.flow.SendEmailRequest;
import com.gtech.gvcore.common.utils.SftpUtil;
import com.gtech.gvcore.dao.mapper.SchedulerReportMapper;
import com.gtech.gvcore.dao.model.SchedulerReport;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.RedisLockHelper;
import com.gtech.gvcore.service.impl.MessageComponent;
import com.gtech.gvcore.service.report.SchedulerReportService;
import com.gtech.gvcore.service.report.SchedulerReportTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@EnableScheduling
//@ConditionalOnProperty(name = "gvcore.report", havingValue = "report")
@Component
public class SchedulerExecute implements SchedulerReportTaskService {


    @Autowired
    private GvCodeHelper codeHelper;

    @Autowired
    private SchedulerReportMapper schedulerReportMapper;

    @Autowired
    private SchedulerReportHelper schedulerReportHelper;

    @Autowired
    private MessageComponent messageComponent;

    private final RestTemplate restTemplate = new RestTemplate();

    public static final String SCHEDULER_REPORT_TASK_LOCK_KEY = "SCHEDULER_REPORT_TASK";

    /**
     * 定时报表执行方法
     */
    @Scheduled(cron = "0 * * * * ?")
    @Override
    public void runTask() {

        //锁定业务 30秒
        boolean lock = RedisLockHelper.tryLock(SCHEDULER_REPORT_TASK_LOCK_KEY, 3 * 1000L, 30 * 1000L);
        if (!lock) return;

        try {

            //查询条件
            final Example example = new Example(SchedulerReport.class);
            example.createCriteria()
                    //启用的定时任务
                    .andEqualTo(SchedulerReport.C_STATUS, true)
                    //有效的定时任务
                    .andEqualTo(SchedulerReport.C_EXECUTE_FLAG, 1)
                    //小于等于当前时间
                    .andLessThanOrEqualTo(SchedulerReport.C_EXECUTION_TIME, new Date());
            final List<SchedulerReport> schedulerReportList = this.schedulerReportMapper.selectByCondition(example);

            if (CollectionUtils.isEmpty(schedulerReportList)) {
                log.debug("定时报表正常退出 [没有查询到符合执行条件的定时报表]");
                return;
            }

            schedulerReportList.forEach(this::tryExecute);
        } finally {
            RedisLockHelper.unLock(SCHEDULER_REPORT_TASK_LOCK_KEY);
        }


    }

    @Override
    public String enforce(String code) {

        log.info("enforce [定时报表强制触发] code => {}", code);

        SchedulerReport schedulerReport = this.schedulerReportMapper.selectOne(SchedulerReport.builder().schedulerReportCode(code).build());

        if (null == schedulerReport) {

            log.info("enforce [定时报表强制触发] 找不到对应数据 => {}", code);
            return StringUtils.EMPTY;
        }

        String reportCode = schedulerReportHelper.execute(schedulerReport, new Date());

        log.info("enforce [定时报表强制触发] 执行完毕 => {}, reportCode => {}", code, reportCode);

        return reportCode;
    }


    @Override
    public void rollbackSchedulerReport(SendEmailRequest request, String schedulerReportCode, String createUserEmail, SendEmailRequest.FileVo fileVo) {

        //find exist schedulerReport
        SchedulerReport schedulerReport = schedulerReportMapper.selectOne(SchedulerReport.builder()
                .schedulerReportCode(schedulerReportCode).build());
        if (null == schedulerReport || null == fileVo) return;

        rollbackEmail(request, createUserEmail, schedulerReport, fileVo);
        rollbackUploadFile(schedulerReport, fileVo);
    }

    /**
     *
     * 尝试执行方法
     * 包裹外层防止执行时抛出异常中断后续数据操作
     *
     * @param schedulerReport
     */
    private void tryExecute(SchedulerReport schedulerReport) {
        try {
            schedulerReportHelper.execute(schedulerReport);
        } catch (Exception e) {
            log.error("定时报表执行异常 [执行中途报错] 退出 => " + JSON.toJSONString(schedulerReport), e);
        }

    }
    private void rollbackUploadFile(SchedulerReport schedulerReport, SendEmailRequest.FileVo fileVo) {

        if (StringUtils.isBlank(schedulerReport.getFtpAddress())) return;

        try {
            URL url = new URL(schedulerReport.getFtpAddress());
            SftpUtil sftpUtil = "Password".equals(schedulerReport.getLoginMethod())
                    ? new SftpUtil(schedulerReport.getFtpUsername(), schedulerReport.getFtpPassword(), url.getHost(), url.getPort(), null, SftpUtil.AUTH_TYPE_PAD)
                    : new SftpUtil(schedulerReport.getFtpUsername(), url.getHost(), url.getPort(), readFtpKeyByUrl(schedulerReport.getEncryptionKey()).getBytes(), null, SftpUtil.AUTH_TYPE_PRIVATE_KEY_BYTE);
            sftpUtil.connect();

            sftpUtil.upload(url.getPath(), fileVo.getFilename(), fileVo.getInputStream());

            sftpUtil.disconnect();
        } catch (Exception e) {
            log.error("FTP上传失败 code => " + schedulerReport.getSchedulerReportCode(), e);
        }

    }

    private void rollbackEmail(SendEmailRequest request, String createUserEmail, SchedulerReport schedulerReport, SendEmailRequest.FileVo fileVo) {

        //file
        request.setFileList(Collections.singletonList(fileVo));

        //所有邮件地址
        List<String> emailAddress = new ArrayList<>();

        //追加操作者邮件
        emailAddress.add(createUserEmail);

        //emails
        String emails = schedulerReport.getEmails();
        if (StringUtils.isNotBlank(emails)) emailAddress.addAll(Arrays.stream(emails.split(";")).collect(Collectors.toList()));

        //distinct isNotBlank
        request.setEmails(emailAddress.stream().distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList()));

        //send
        messageComponent.sendEmail(request, MessageEnventEnum.SCHEDULER_REPORT.getCode());
    }


    public String readFtpKeyByUrl(String url) {

        try {

            ResponseEntity<String> key = restTemplate.getForEntity(url, String.class);

            return key.getBody();
        } catch (Exception e) {

            log.error("定时报表 url 获取 ftp 密钥失败, url => " + url, e);

            return StringUtils.EMPTY;
        }
    }

}
