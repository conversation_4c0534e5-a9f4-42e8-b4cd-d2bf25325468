package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.customerproductcategory.*;
import com.gtech.gvcore.common.response.customerproductcategory.CustomerProductCategoryResponse;
import com.gtech.gvcore.dao.mapper.CustomerProductCategoryMapper;
import com.gtech.gvcore.dao.model.CustomerProductCategory;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CustomerProductCategoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
@Service
public class CustomerProductCategoryServiceImpl implements CustomerProductCategoryService {

    @Autowired
    private CustomerProductCategoryMapper customerProductCategoryMapper;

    @Autowired
    private GvCodeHelper codeHelper;

    @Override
    public Result<Void> createCustomerProductCategory(CreateCustomerProductCategoryRequest param) {

        //批量添加
        if (CollectionUtils.isNotEmpty(param.getProductCategoryCodeList())){
            ArrayList<CustomerProductCategory> entities = new ArrayList<>();
            List<String> list = param.getProductCategoryCodeList();
            for (String s : list) {
                CustomerProductCategory entity = new CustomerProductCategory();
                entity.setCustomerProductCategoryCode(codeHelper.generateCustomerProductCategoryCode());
                entity.setCustomerCode(param.getCustomerCode());
                entity.setProductCategoryCode(s);
                entity.setStatus(GvcoreConstants.STATUS_ENABLE);
                entity.setCreateUser(param.getCreateUser());
                entity.setCreateTime(new Date());
                entities.add(entity);
            }
            customerProductCategoryMapper.insertList(entities);
            return Result.ok();
        }

        CustomerProductCategory entity = BeanCopyUtils.jsonCopyBean(param, CustomerProductCategory.class);
        entity.setCustomerProductCategoryCode(codeHelper.generateCustomerProductCategoryCode());
        entity.setCreateTime(new Date());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);

        try {
            customerProductCategoryMapper.insertSelective(entity);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> updateCustomerProductCategory(UpdateCustomerProductCategoryRequest param) {
        CustomerProductCategory entity = BeanCopyUtils.jsonCopyBean(param, CustomerProductCategory.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(CustomerProductCategory.class);
        example.createCriteria()
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_PRODUCT_CATEGORY_CODE,param.getCustomerProductCategoryCode());

        try {
            customerProductCategoryMapper.updateByConditionSelective(entity,example);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> deleteCustomerProductCategory(DeleteCustomerProductCategoryRequest param) {
        Example example = new Example(CustomerProductCategory.class);
        example.createCriteria()
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_PRODUCT_CATEGORY_CODE,param.getCustomerProductCategoryCode());
        customerProductCategoryMapper.deleteByCondition(example);

        return Result.ok();
    }


    @Override
    public Result<Void> deleteCustomerProductCategoryByCustomer(String param) {
        Example example = new Example(CustomerProductCategory.class);
        example.createCriteria()
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_CODE,param);
        customerProductCategoryMapper.deleteByCondition(example);

        return Result.ok();
    }

    @Override
    public PageResult<CustomerProductCategoryResponse> queryCustomerProductCategoryList(QueryCustomerProductCategoryRequest param) {

        PageHelper.startPage(param.getPageNum(),param.getPageSize());

        Example example = new Example(CustomerProductCategory.class);
        example.createCriteria()
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_PRODUCT_CATEGORY_CODE,param.getCustomerProductCategoryCode())
                .andEqualTo(CustomerProductCategory.C_PRODUCT_CATEGORY_CODE,param.getProductCategoryCode())
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_CODE,param.getCustomerCode())
                .andEqualTo(CustomerProductCategory.C_STATUS,param.getStatus());
        //创建时间倒序
        example.orderBy(CustomerProductCategory.C_CREATE_TIME).desc();

        List<CustomerProductCategory> gvCustomerProductCategoryEntities = customerProductCategoryMapper.selectByCondition(example);
        PageInfo<CustomerProductCategory> info = PageInfo.of(gvCustomerProductCategoryEntities);

        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(),CustomerProductCategoryResponse.class),info.getTotal());
    }

    @Override
    public List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomer(QueryCustomerProductCategoryRequest param) {

        Example example = new Example(CustomerProductCategory.class);
        example.createCriteria()
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_PRODUCT_CATEGORY_CODE,param.getCustomerProductCategoryCode())
                .andEqualTo(CustomerProductCategory.C_PRODUCT_CATEGORY_CODE,param.getProductCategoryCode())
                .andEqualTo(CustomerProductCategory.C_CUSTOMER_CODE,param.getCustomerCode())
                .andEqualTo(CustomerProductCategory.C_STATUS,param.getStatus());
        //创建时间倒序
        example.orderBy(CustomerProductCategory.C_CREATE_TIME).desc();

        List<CustomerProductCategory> gvCustomerProductCategoryEntities = customerProductCategoryMapper.selectByCondition(example);

        return BeanCopyUtils.jsonCopyList(gvCustomerProductCategoryEntities,CustomerProductCategoryResponse.class);
    }

    @Override
    public List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomer(String customer) {

        return customerProductCategoryMapper.queryCustomerProductCategoryListByCustomer(customer);

    }

    @Override
    public List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomerList(List<String> customerList) {

        return customerProductCategoryMapper.queryCustomerProductCategoryListByCustomerList(customerList);

    }

    @Override
    public CustomerProductCategoryResponse getCustomerProductCategory(GetCustomerProductCategoryRequest param) {
        CustomerProductCategory entity = BeanCopyUtils.jsonCopyBean(param, CustomerProductCategory.class);
        CustomerProductCategory customerProductCategory = customerProductCategoryMapper.selectOne(entity);

        return BeanCopyUtils.jsonCopyBean(customerProductCategory,CustomerProductCategoryResponse.class);
    }


}
