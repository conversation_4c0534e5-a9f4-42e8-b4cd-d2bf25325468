package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 13:43
 * @Description: Gift Card Expiry Report Summary Bean
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcExpiryForSummaryBean {

    /**
     * Merchant Name
     */
    @ExcelProperty(value = "Merchant Name")
    private String merchant;

    /**
     * Gift Card Program Group
     */
    @ExcelProperty(value = "Gift Card Program Group")
    private String vpg;

    /**
     * Total Cards
     */
    @ExcelProperty(value = "Total Cards", converter = ExportExcelNumberConverter.class)
    private String numberOfVouchers;

    /**
     * Total Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String voucherAmount;

    /**
     * Customer Name
     */
    @ExcelProperty(value = "Customer Name")
    private String customerName;

    /**
     * Expiry Date
     */
    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    /**
     * Notes
     */
    @ExcelProperty(value = "Notes")
    private String notes;
}
