package com.gtech.gvcore.service;


import java.util.List;

import com.gtech.gvcore.common.request.receive.QueryReceiveRecordRequest;
import com.gtech.gvcore.common.request.receive.VoucherReceiveRecordRequest;
import com.gtech.gvcore.dao.dto.VoucherReceiveDto;

public interface VoucherReceiveRecordService {

	void saveVoucherReceiveRecord(List<VoucherReceiveRecordRequest> receiveRecordList);

	List<VoucherReceiveDto> queryReceiveRecord(QueryReceiveRecordRequest request);
}
