package com.gtech.gvcore.service.report.export.snapshoot.label;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.service.report.export.snapshoot.QueryOrderReportDataResponse;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName OrderReportLabelDynamicHelper
 * @Description
 * <AUTHOR>
 * @Date 2023/1/13 19:01
 * @Version V1.0
 **/
@Slf4j
@Component
public class OrderReportLabelDynamicHelper implements ApplicationContextAware {

    private static final String GET_STUFF_PARAM_LABEL_START_DELIMITER = "{";
    private static final String GET_STUFF_PARAM_LABEL_END_DELIMITER = "}";
    private ApplicationContext applicationContext;

    /**
     * impl bean map
     */
    private static final Map<ReportExportTypeEnum, ReportLabelSupport> REPORT_IMPL_BEAN = new EnumMap<>(ReportExportTypeEnum.class);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {

        Collection<ReportLabelSupport> values = applicationContext.getBeansOfType(ReportLabelSupport.class).values();

        //所有报表实例
        REPORT_IMPL_BEAN.putAll(values.stream().collect(Collectors.toMap(ReportLabelSupport::exportTypeEnum, Function.identity()
                , (a, b) -> MapUtils.isNotEmpty(a.getResultDateType()) ? a : b)));

        this.applicationContext = applicationContext;
    }

    /**
     * 获得动态表头
     */
    public List<QueryOrderReportDataResponse.LabelBean> getDynamicLabel(List<QueryOrderReportDataResponse.LabelBean> labelBeans, ReportRequest reportRequest, List<?> orderReportDataList) {

        //复制动态表头信息 以及 基础动态表头配置
        List<QueryOrderReportDataResponse.LabelBean> labelBeanList = labelBeans.stream().map(QueryOrderReportDataResponse.LabelBean::copy).collect(Collectors.toList());

        //获取动态参数
        JSONObject param = getDynamicParam(reportRequest, orderReportDataList);

        //生成动态表头信息
        return generateDynamicLabel(labelBeanList, param, ReportExportTypeEnum.getExportTypeEnumByType(reportRequest.getReportType()));
    }

    /**
     * 获得动态表头参数
     */
    private static JSONObject getDynamicParam(ReportRequest reportRequest, List<?> orderReportDataList) {

        JSONObject orderReportJsonObject = (JSONObject) JSON.toJSON(reportRequest);
        orderReportJsonObject.put(ReportLabelContextBean.REPORT_BEAN_KEY, reportRequest);

        //报表结果中不包含对象则直接取报表生成信息
        if (CollectionUtils.isEmpty(orderReportDataList)) return orderReportJsonObject;

        //报表结果中包含对象 则取报表结果的第一个对象 并被 报表生成信息覆盖成为动态参数
        Object resultFastBean = orderReportDataList.get(0);
        JSONObject param = (JSONObject) JSON.toJSON(resultFastBean);
        param.put(ReportLabelContextBean.RESULT_BEAN_KEY, reportRequest);

        param.putAll(orderReportJsonObject);

        return param;
    }

    /**
     * 生成动态表头
     */

    private List<QueryOrderReportDataResponse.LabelBean> generateDynamicLabel (List<QueryOrderReportDataResponse.LabelBean> labelBeans, JSONObject param, ReportExportTypeEnum exportTypeEnumByType) {

        final ReportLabelContextBean reportLabelContextBean = initReportLabelContextBean(param, exportTypeEnumByType);

        //生成动态表头
        labelBeans.stream()
                //过滤没有配置过@ReportLable的表头
                .filter(e -> Objects.nonNull(e.getReportLabel()))
                //过滤没有动态信息的表头
                .filter(e -> e.getLabel().contains(GET_STUFF_PARAM_LABEL_START_DELIMITER) && e.getLabel().contains(GET_STUFF_PARAM_LABEL_END_DELIMITER) && e.getLabel().indexOf(GET_STUFF_PARAM_LABEL_START_DELIMITER) < e.getLabel().indexOf(GET_STUFF_PARAM_LABEL_END_DELIMITER))
                //循环
                .forEach(e -> {

                    this.updateFieldName(reportLabelContextBean, e.getValue());

                    //获取填充参数列表
                    Map<String, String> stuffParam = getFillParam(reportLabelContextBean, e);

                    //根据获取的填充参数列表替换原表头配置中动态部分数据
                    AtomicReference<String> labelAtomic = new AtomicReference<>(e.getLabel());
                    stuffParam.forEach((k, v) -> labelAtomic.set(labelAtomic.get().replace(GET_STUFF_PARAM_LABEL_START_DELIMITER + k + GET_STUFF_PARAM_LABEL_END_DELIMITER, v)));

                    //设置表头信息
                    e.setLabel(labelAtomic.get());

                    //如果表头下存在子表头则循环子表头生成
                    if (CollectionUtils.isNotEmpty(e.getChildren())) this.generateDynamicLabel(e.getChildren(), param, exportTypeEnumByType);

                });

        return labelBeans;
    }

    private ReportLabelContextBean initReportLabelContextBean(JSONObject param, ReportExportTypeEnum exportTypeEnumByType) {

        return new ReportLabelContextBean()
                .setReportRequest((ReportRequest) param.get(ReportLabelContextBean.REPORT_BEAN_KEY))
                .setResultBean(param.get(ReportLabelContextBean.RESULT_BEAN_KEY))
                .setExportTypeEnumByType(exportTypeEnumByType)
                .setJsonObject(param);
    }

    private void updateFieldName(ReportLabelContextBean contextBean, String fieldName) {

        contextBean.setFieldName(fieldName);
    }

    /**
     * 获得填充参数
     */
    private Map<String, String> getFillParam(ReportLabelContextBean reportLabelContextBean, QueryOrderReportDataResponse.LabelBean labelBean) {

        JSONObject param = reportLabelContextBean.getJsonObject();

        //参数初始化
        final String label = labelBean.getLabel();
        final ReportLabel reportLabel = labelBean.getReportLabel();

        //生成参数key
        String keyLabel = label;
        final List<String> key = new ArrayList<>();
        while (keyLabel.contains(GET_STUFF_PARAM_LABEL_START_DELIMITER)) {
            key.add(keyLabel.substring(keyLabel.indexOf(GET_STUFF_PARAM_LABEL_START_DELIMITER) + 1, keyLabel.indexOf(GET_STUFF_PARAM_LABEL_END_DELIMITER)));
            keyLabel = keyLabel.substring(keyLabel.indexOf(GET_STUFF_PARAM_LABEL_END_DELIMITER) + 1);
        }

        //生成参数value
        // 如果不存在参数序列化配置 则直接取默认列表中的参数 否则带入参数初始化方法执行后获取结果
        final Map<String, String> fillParam = new HashMap<>();
        key.stream().filter(e -> !e.contains(EXECUTE_FUNCTION_METHOD_DELIMITER)).forEach(k -> {

            Object value;
            if (StringUtils.isBlank(reportLabel.dynamicFormat())) value = param.get(k);
            else value = executeFunction(reportLabel.dynamicFormat(), reportLabelContextBean, param.get(k), true);

            fillParam.put(k, ConvertUtils.toString(value));
        });

        key.stream().filter(e -> e.contains(EXECUTE_FUNCTION_METHOD_DELIMITER)).forEach(k -> fillParam.put(k, String.valueOf(executeFunction(k, reportLabelContextBean, null, false))));

        return fillParam;
    }

    private static final String EXECUTE_FUNCTION_METHOD_DELIMITER = "#";
    private static final String EXECUTE_FUNCTION_THIS_BEAN = "this";


    /**
     * 执行参数初始化
     *  执行失败取原始参数
     */
    private Object executeFunction(final String expression, final ReportLabelContextBean reportLabelContextBean, final Object value, boolean formatFunction) {

        //参数初始化
        final Object bean;
        final String methodName;
        final ReportExportTypeEnum exportTypeEnumByType = reportLabelContextBean.getExportTypeEnumByType();

        try {

            //是否存在 #
            if (expression.contains(EXECUTE_FUNCTION_METHOD_DELIMITER)) {

                String beanName = expression.substring(0, expression.indexOf(EXECUTE_FUNCTION_METHOD_DELIMITER));

                //beanName 如果为this 则直接取当前业务对象 否则 通过 application context 获取 spring 对象
                bean = StringUtils.equalsIgnoreCase(EXECUTE_FUNCTION_THIS_BEAN, beanName) ? REPORT_IMPL_BEAN.get(exportTypeEnumByType) : applicationContext.getBean(beanName);
                methodName = expression.substring(expression.indexOf(EXECUTE_FUNCTION_METHOD_DELIMITER) + 1);
            } else {
                //不存在则直接通过报表类型获取对应的生成类寻找对应方法
                bean = REPORT_IMPL_BEAN.get(exportTypeEnumByType);
                methodName = expression;
            }

            if (null == bean) {
                //没有找到对应的执行bean
                log.error("解析label 错误, 没有对应的执行bean, 请确认配置信息是否正确 expression = " + expression + " reportType : " + exportTypeEnumByType.getExportType());
                return value;
            }

            //找到对应的执行方法
            Method declaredMethod = Arrays.stream(bean.getClass().getDeclaredMethods())
                    .filter(m -> m.getName().equals(methodName))
                    .findFirst().orElse(null);

            if (null == declaredMethod) {
                //没有找到执行方法
                log.error("解析label 错误, 没有对应的执行方法, 请确认配置信息是否正确 expression = " + expression + " reportType : " + exportTypeEnumByType.getExportType());
                return value;
            }


            if (formatFunction) {
                //format 方法确定签名 不进行加工

                return declaredMethod.invoke(bean, value);
            } else {
                //非format 方法 进行参数加工

                //如果空参直接执行
                Class<?>[] parameterTypes = declaredMethod.getParameterTypes();
                if (parameterTypes.length == 0) return declaredMethod.invoke(bean);

                //否则尝试根据参数类型寻找参数
                return invoke(reportLabelContextBean, bean, declaredMethod);

            }
        } catch (Exception ex) {

            log.error("解析label 错误", ex);
            return value;
        }
    }

    private static Object invoke (final ReportLabelContextBean reportLabelContextBean, final Object bean, final Method declaredMethod) throws IllegalAccessException, InvocationTargetException {

        final Class<?>[] parameterTypes = declaredMethod.getParameterTypes();
        final Object[] param = new Object[parameterTypes.length];

        for (int i = 0; i < parameterTypes.length; i++) {

            if (ReportLabelContextBean.class == parameterTypes[i]) param[i] = reportLabelContextBean;
            else if (ReportRequest.class == parameterTypes[i]) param[i] = reportLabelContextBean.getReportRequest();
            else if (reportLabelContextBean.getResultBean().getClass() == parameterTypes[i]) param[i] = reportLabelContextBean.getResultBean();
            else if (String.class == parameterTypes[i]) param[i] = reportLabelContextBean.getFieldName();
            else {
                log.warn("没有找到对应参数, 填充空参尝试执行");
                param[i] = null;
            }
        }

        return declaredMethod.invoke(bean, param);
    }


}
