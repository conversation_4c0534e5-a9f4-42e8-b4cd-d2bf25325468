package com.gtech.gvcore.service.report.impl.support.aging.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName AgingMonthSalesAndContributionBo
 * @Description aging month sales and contribution bo
 * <AUTHOR>
 * @Date 2022/11/1 14:30
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class MonthSalesAndContributionBo {

    private String customerCode;

    private String customerName;

    private int jan;

    private int feb;

    private int mar;

    private int apr;

    private int may;

    private int jun;

    private int jul;

    private int aug;

    private int sep;

    private int oct;

    private int nov;

    private int dec;

    public int getTotal() {
        return jan +
                feb +
                mar +
                apr +
                may +
                jun +
                jul +
                aug +
                sep +
                oct +
                nov +
                dec;
    }


    public void addJan() {
        this.jan++;
    }
    public void addFeb() {
        this.feb++;
    }
    public void addMar() {
        this.mar++;
    }
    public void addApr() {
        this.apr++;
    }
    public void addMay() {
        this.may++;
    }
    public void addJun() {
        this.jun++;
    }
    public void addJul() {
        this.jul++;
    }
    public void addAug() {
        this.aug++;
    }
    public void addSep() {
        this.sep++;
    }
    public void addOct() {
        this.oct++;
    }
    public void addNov() {
        this.nov++;
    }
    public void addDec() {
        this.dec++;
    }

    public MonthSalesAndContributionBo addJan (int value) {
        this.jan +=value;
        return this;
    }
    public MonthSalesAndContributionBo addFeb (int value) {
        this.feb +=value;
        return this;
    }
    public MonthSalesAndContributionBo addMar (int value) {
        this.mar +=value;
        return this;
    }
    public MonthSalesAndContributionBo addApr (int value) {
        this.apr +=value;
        return this;
    }
    public MonthSalesAndContributionBo addMay (int value) {
        this.may +=value;
        return this;
    }
    public MonthSalesAndContributionBo addJun (int value) {
        this.jun +=value;
        return this;
    }
    public MonthSalesAndContributionBo addJul (int value) {
        this.jul +=value;
        return this;
    }
    public MonthSalesAndContributionBo addAug (int value) {
        this.aug +=value;
        return this;
    }
    public MonthSalesAndContributionBo addSep (int value) {
        this.sep +=value;
        return this;
    }
    public MonthSalesAndContributionBo addOct (int value) {
        this.oct +=value;
        return this;
    }
    public MonthSalesAndContributionBo addNov (int value) {
        this.nov +=value;
        return this;
    }
    public MonthSalesAndContributionBo addDec (int value) {
        this.dec +=value;
        return this;
    }

}
