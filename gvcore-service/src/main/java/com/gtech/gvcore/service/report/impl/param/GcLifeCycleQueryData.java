package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName GcLifeCycleQueryData
 * @Description Gift Card Life Cycle Query Data
 * <AUTHOR> based on VoucherLifeCycleQueryData
 * @Date 2025年6月19日
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcLifeCycleQueryData extends PageParam implements ReportQueryParam {

    private List<String> cardNumber;
}
