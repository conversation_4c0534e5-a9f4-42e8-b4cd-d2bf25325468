package com.gtech.gvcore.service.report.impl.support.gclife.builder;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.giftcard.application.service.GiftCardApplicationService;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.giftcard.util.GiftCardStatusCalculator;
import com.gtech.gvcore.giftcard.util.PeriodCalculator;
import com.gtech.gvcore.service.report.impl.bean.GcLifeCycleBean;
import com.gtech.gvcore.service.report.impl.param.GcLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.gclife.GcLifeCycleAbstract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Gift Card Life Cycle Card Builder
 *
 * <p>负责构建礼品卡生命周期报表的主要信息，包括：
 * <ul>
 *   <li>礼品卡基本信息（卡号、程序组、状态等）</li>
 *   <li>时间信息（发行日期、激活日期、过期日期等）</li>
 *   <li>金额信息（面额、余额）</li>
 * </ul>
 *
 * <AUTHOR> based on VoucherLifeCycleVoucherBuilder
 * @version V1.0
 * @date 2025年6月19日
 */
@Slf4j
@Service
public class GcLifeCycleCardBuilder extends GcLifeCycleAbstract<GcLifeCycleBean> {

    private final GiftCardApplicationService giftCardApplicationService;

    @Autowired
    public GcLifeCycleCardBuilder(GiftCardApplicationService giftCardApplicationService) {
        this.giftCardApplicationService = giftCardApplicationService;
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_CARD_LIFE_CYCLE_REPORT;
    }

    @Override
    public Class<?> getExportDataClass() {
        return GcLifeCycleBean.class;
    }

    @Override
    public String getFillKey() {
        return "cycle";
    }

    @Override
    public List<GcLifeCycleBean> builder(GcLifeCycleQueryData queryData) {
        List<GcLifeCycleBean> beans = new ArrayList<>();
        // 1. 查询礼品卡信息
        List<GiftCardEntity> giftCardEntities = giftCardApplicationService.queryByCardNumberList(null, queryData.getCardNumber());

        if (null == giftCardEntities) return Collections.emptyList();
        for (GiftCardEntity giftCard : giftCardEntities) {
            // 2. 查询相关主数据
            GcCpg gcCpg = super.nonNullGetByCode(giftCard.getCpgCode(), GcCpg.class);

            // 3. 组装数据
            GcLifeCycleBean cycleBean = buildLifeCycleBean(giftCard, gcCpg);
            beans.add(cycleBean);
        }
        return beans;
    }


    /**
     * 构建生命周期Bean
     */
    private GcLifeCycleBean buildLifeCycleBean(GiftCardEntity giftCard, GcCpg gcCpg) {
        GcLifeCycleBean cycleBean = new GcLifeCycleBean();

        // 基本信息
        setBasicInfo(cycleBean, giftCard, gcCpg);

        // 时间信息
        setTimeInfo(cycleBean, giftCard);

        // 金额信息
        setAmountInfo(cycleBean, giftCard);

        return cycleBean;
    }

    /**
     * 设置基本信息
     */
    private void setBasicInfo(GcLifeCycleBean cycleBean, GiftCardEntity giftCard, GcCpg gcCpg) {
        cycleBean.setGiftCardNumber(giftCard.getCardNumber());
        cycleBean.setGiftCardProgram(gcCpg.getCpgName());
        String cardStatus = GiftCardStatusCalculator.builder().cardStatus(giftCard.getStatus())
                .activationExtensionCount(giftCard.getActivationExtensionCount())
                .activationDeadline(giftCard.getActivationDeadline())
                .managementStatus(giftCard.getManagementStatus())
                .expiryTime(giftCard.getExpiryTime()).build().determineCardStatus();
        cycleBean.setGiftCardStatus(cardStatus);
    }

    /**
     * 设置时间信息
     */
    private void setTimeInfo(GcLifeCycleBean cycleBean, GiftCardEntity giftCard) {
        // 发行日期
        setFormattedDate(cycleBean::setIssuanceDate, giftCard.getCreateTime());

        // 激活日期
        setFormattedDate(cycleBean::setActivationDate, giftCard.getActivationTime());

        // 过期日期
        setFormattedDate(cycleBean::setExpiryDate, giftCard.getExpiryTime());

        String activationPeriodEnded;
        String gracePeriodEnded;
        if (giftCard.getActivationExtensionCount() == 0) {
            activationPeriodEnded = DateUtil.format(giftCard.getActivationDeadline(), DateUtil.FORMAT_YYYYMMDDHHMISS);
            Date start = giftCard.getActivationDeadline();
            gracePeriodEnded = DateUtil.format(PeriodCalculator.calculateDate(giftCard.getActivationGracePeriod(), start), DateUtil.FORMAT_YYYYMMDDHHMISS);
        } else {
            activationPeriodEnded = DateUtil.format(PeriodCalculator.calculateSubDate(giftCard.getActivationGracePeriod(), giftCard.getActivationDeadline()), DateUtil.FORMAT_YYYYMMDDHHMISS);
            gracePeriodEnded = DateUtil.format(giftCard.getActivationDeadline(), DateUtil.FORMAT_YYYYMMDDHHMISS);
        }
        //宽限期结束日期
        cycleBean.setGracePeriodEnded(gracePeriodEnded);
        cycleBean.setActivationEnded(activationPeriodEnded);
    }

    /**
     * 设置金额信息
     */
    private void setAmountInfo(GcLifeCycleBean cycleBean, GiftCardEntity giftCard) {
        cycleBean.setDenomination(super.toAmount(giftCard.getDenomination()));
        cycleBean.setRemainingBalance(super.toAmount(giftCard.getBalance()));
    }

    /**
     * 设置格式化日期的通用方法
     */
    private void setFormattedDate(java.util.function.Consumer<String> setter, Date date) {
        if (date != null) {
            setter.accept(DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS));
        }
    }

}
