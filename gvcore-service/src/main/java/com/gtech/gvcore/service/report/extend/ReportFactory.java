package com.gtech.gvcore.service.report.extend;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName ReportFactory
 * @Description 报表实现类工厂
 * <AUTHOR>
 * @Date 2022/10/20 19:08
 * @Version V1.0
 **/
@Slf4j
@Component(ReportFactory.BEAN_NAME)
public class ReportFactory implements ApplicationContextAware {

    public static final String BEAN_NAME = "reportFactory";

    /**
     * 报表实现类map
     */
    private static final Map<ReportExportTypeEnum, BusinessReport<ReportQueryParam, Object>> REPORT_IMPL_MAP = new EnumMap<>(ReportExportTypeEnum.class);

    /**
     * 初始化
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {

        applicationContext.getBeansOfType(BusinessReport.class)
                .values()
                .forEach(e -> REPORT_IMPL_MAP.put(e.exportTypeEnum(), e));
    }

    public static Set<ReportExportTypeEnum> getAllSupportEnum() {

        return REPORT_IMPL_MAP.keySet();
    }

    public static void register (ReportExportTypeEnum reportExportTypeEnum, BusinessReport<ReportQueryParam, Object> reportImpl) {

        if (REPORT_IMPL_MAP.containsKey(reportExportTypeEnum)) throw new UnsupportedOperationException("尝试注册业务报表实现时异常, 重复注册 ERROR");

        REPORT_IMPL_MAP.put(reportExportTypeEnum, reportImpl);
    }

    /**
     *
     * @param reportType
     * @return
     */
    public static BusinessReport<ReportQueryParam, Object> getReportImpl (Integer reportType) {

        log.trace("ReportFactory.getReportImpl param reportType: {}", reportType);

        if (!exist(reportType)) throw new UnsupportedOperationException("尝试获取业务报表实现时异常, 找不到预期业务实现 ERROR");

        final ReportExportTypeEnum exportTypeEnumByType = ReportExportTypeEnum.getExportTypeEnumByType(reportType);

        final BusinessReport<ReportQueryParam, Object> result = REPORT_IMPL_MAP.get(exportTypeEnumByType);

        log.info("ReportFactory.getReportImpl result reportType: {}, name:{}", reportType, result.exportTypeEnum().name());

        return result;
    }

    public static boolean exist (Integer reportType) {

        return exist(ReportExportTypeEnum.getExportTypeEnumByType(reportType));
    }

    public static boolean exist (ReportExportTypeEnum reportExportTypeEnum) {

        return REPORT_IMPL_MAP.containsKey(reportExportTypeEnum);
    }

}
