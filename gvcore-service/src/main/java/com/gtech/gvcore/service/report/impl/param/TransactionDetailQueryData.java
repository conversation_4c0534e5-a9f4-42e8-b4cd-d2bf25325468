package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class TransactionDetailQueryData extends TransactionDataPageParam implements ReportQueryParam {

    private Date transactionDateStart;

    private Date transactionDateEnd;

    private String issuerCode;

    private List<String> issuerCodes;

    private List<String> merchantCodes;

    private List<String> outletCodes;

    private List<String> cpgCodes;

    private List<String> transactionType;

    private String transactionStatus;

    private List<String> voucherStatusList;

    private String invoiceNumber;

    private String customerType;

    private List<String> customerCodes;

    private String corporate;

    private List<String> bulkOrderStatus;

    private Date expiryStatusStartDate;

    private Date expiryStatusEndDate;

    private Date voucherEffectiveDateStart;

    private Date voucherEffectiveDateEnd;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

    private List<String> purchaseOrderVoucherCodeList;

    private List<String> bulkOrderStatusTransactionIdList;

}
