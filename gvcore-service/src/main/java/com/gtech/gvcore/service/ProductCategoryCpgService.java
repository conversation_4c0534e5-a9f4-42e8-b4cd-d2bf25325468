package com.gtech.gvcore.service;

import java.util.List;

import com.gtech.gvcore.dao.model.ProductCategoryCpg;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
public interface ProductCategoryCpgService {

    /**
     * 根据商品分类编码查询商品分类与促销策略关联关系
     * 
     * @param productCategoryCode
     * @param deleteStatus
     * @return
     */
    List<ProductCategoryCpg> queryByProductCategoryCode(String productCategoryCode, Integer deleteStatus,String cpgType);

    /**
     * 批量插入商品分类与促销策略关联关系
     * 
     * @param list
     * @return
     */
    int insertList(List<ProductCategoryCpg> list);

    /**
     * 更新商品分类与促销策略关联关系
     * 
     * @param productCategoryCpg
     * @return
     */
    int updateByPrimaryKeySelective(ProductCategoryCpg productCategoryCpg);

    /**
     * 修改删除状态
     * 
     * @param productCategoryCpg
     * @return
     */
    int deleteStatusByPrimaryKey(ProductCategoryCpg productCategoryCpg);
    
    /**
     * 根据条件查询商品分类与促销策略关联关系
     * 
     * @param criteria 查询条件
     * @return 符合条件的关联关系列表
     */
    List<ProductCategoryCpg> selectByCriteria(ProductCategoryCpg criteria);

}
