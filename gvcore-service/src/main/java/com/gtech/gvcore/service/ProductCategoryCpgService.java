package com.gtech.gvcore.service;

import java.util.List;

import com.gtech.gvcore.dao.model.ProductCategoryCpg;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
public interface ProductCategoryCpgService {

    /**
     * 
     * <AUTHOR>
     * @param productCategoryCode
     * @param deleteStatus
     * @return
     * @date 2022年2月24日
     */
    List<ProductCategoryCpg> queryByProductCategoryCode(String productCategoryCode, Integer deleteStatus);

    /**
     * 
     * <AUTHOR>
     * @param list
     * @return
     * @date 2022年2月25日
     */
    int insertList(List<ProductCategoryCpg> list);

    /**
     * 
     * <AUTHOR>
     * @param productCategoryCpg
     * @return
     * @date 2022年2月24日
     */
    int updateByPrimaryKeySelective(ProductCategoryCpg productCategoryCpg);

    /**
     * 
     * <AUTHOR>
     * @param id
     * @return
     * @date 2022年2月24日
     */
    int deleteStatusByPrimaryKey(ProductCategoryCpg productCategoryCpg);

}
