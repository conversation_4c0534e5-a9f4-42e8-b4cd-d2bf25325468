package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.InventoryDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.InventoryDetailedBo;
import com.gtech.gvcore.service.report.impl.param.InventoryQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName InventoryDetailedImpl
 * @Description InventoryDetailedImpl
 * <AUTHOR>
 * @Date 2022/9/21 14:07
 * @Version V1.0
 **/
@Service
public class InventoryDetailedImpl extends ReportSupport
        implements BusinessReport<InventoryQueryData, InventoryDetailedBean>, SingleReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.INVENTORY_DETAILED_REPORT;
    }

    @Override
    public InventoryQueryData builderQueryParam(CreateReportRequest reportParam) {

        ReportParamConvertHelper.convertQueryDateMerchantCodeToOutletCode(reportParam);

        InventoryQueryData inventoryQueryData = new InventoryQueryData();

        // issue
        inventoryQueryData.setIssuerCodes(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);

        // outlet
        inventoryQueryData.setOutletCodes(reportParam.getOutletCodes());

        // cpg
        inventoryQueryData.setCpgCodes(reportParam.getCpgCodes());

        // booklet
        inventoryQueryData.setBookletStatus(reportParam.getBookletStatus());

        // booklet no
        inventoryQueryData.setStartBookletNo(reportParam.getBookletStart());
        inventoryQueryData.setEndBookletNo(reportParam.getBookletEnd());

        // voucher no
        inventoryQueryData.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        inventoryQueryData.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());

        // effective date
        inventoryQueryData.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        inventoryQueryData.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        return inventoryQueryData;
    }

    @Override
    public List<InventoryDetailedBean> getExportData(InventoryQueryData queryData) {

        List<InventoryDetailedBo> detailedList = reportBusinessMapper.inventoryDetailed(queryData);
        if (CollectionUtils.isEmpty(detailedList)) return Collections.emptyList();

        final JoinDataMap<Issuer> issuerJoinDataMap = super.getMapByCode(detailedList, InventoryDetailedBo::getIssuerCode, Issuer.class);
        final JoinDataMap<Outlet> outletJoinDataMap = super.getMapByCode(detailedList, InventoryDetailedBo::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantJoinDataMap = super.getMapByCode(outletJoinDataMap.values(), Outlet::getMerchantCode, Merchant.class);
        final JoinDataMap<Cpg> cpgJoinDataMap = super.getMapByCode(detailedList, InventoryDetailedBo::getCpgCode, Cpg.class);

        return detailedList.stream()
                .map(e -> {

                    final Issuer issuer = issuerJoinDataMap.findValue(e.getIssuerCode());
                    final Outlet outlet = outletJoinDataMap.findValue(e.getOutletCode());
                    final Merchant merchant = merchantJoinDataMap.findValue(outlet.getMerchantCode());
                    final Cpg cpg = cpgJoinDataMap.findValue(e.getCpgCode());

                    return new InventoryDetailedBean()
                            .setIssuer(issuer.getIssuerName())
                            .setMerchant(merchant.getMerchantName())
                            .setOutletCode(outlet.getBusinessOutletCode())
                            .setOutlet(outlet.getOutletName())
                            .setVoucherProgramGroup(cpg.getCpgName())
                            .setExpiryDate(DateUtils.formatDate(e.getVoucherEffectiveDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                            .setBookletNumber(e.getBookletNumber())
                            .setCardNumber(e.getCardNumber())
                            .setCardStatus(super.getVoucherStatus(e.getStatus(), e.getVoucherStatus(), e.getVoucherEffectiveDate(), GvcoreConstants.MOP_CODE_VCR).getDesc())
                            .setIssuanceYear(DateUtils.formatDate(e.getCreateTime(), DateUtil.FORMAT_YYYY))
                            .setCreationDate(DateUtils.formatDate(e.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                            .setDenomination(format(e.getDenomination()));

                }).collect(Collectors.toList());
    }

    private static final String[] SUFFIX = new String[]{"", "k", "m", "b", "t"};
    private static final int MAX_LENGTH = 4;

    //k进制转换
    public String format(BigDecimal number) {
        String r = new DecimalFormat("##0E0").format(number);
        r = r.replaceAll("E[0-9]", SUFFIX[Character.getNumericValue(r.charAt(r.length() - 1)) / 3]);
        while (r.length() > MAX_LENGTH || r.matches("[0-9]+\\.[a-z]")) {
            r = r.substring(0, r.length() - 2) + r.substring(r.length() - 1);
        }
        return r;
    }


}
