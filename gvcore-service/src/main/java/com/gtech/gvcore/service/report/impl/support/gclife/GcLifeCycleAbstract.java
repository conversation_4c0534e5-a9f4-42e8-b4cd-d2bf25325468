package com.gtech.gvcore.service.report.impl.support.gclife;

import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.impl.param.GcLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.gclife.excel.GcLifeCycleSheet;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName GcLifeCycleAbstract
 * @Description Gift Card Life Cycle Abstract Class
 * <AUTHOR> based on VoucherLifeCycleAbstract
 * @Date 2025年6月19日
 * @Version V1.0
 **/
public abstract class GcLifeCycleAbstract<T> extends ReportSupport implements GcLifeCycle {

    @Override
    public GcLifeCycleSheet builderSheet(GcLifeCycleQueryData param) {

        return GcLifeCycleSheet.newSheet(getFillKey(), exportTypeEnum(), builderSheetData(param));
    }

    private List<T> builderSheetData(GcLifeCycleQueryData param) {
        if (param.getCardNumber() == null || param.getCardNumber().isEmpty()) return Collections.emptyList();
        return new ArrayList<>(builder(param));
    }

    protected abstract List<T> builder(GcLifeCycleQueryData queryData);

    protected abstract String getFillKey();

}
