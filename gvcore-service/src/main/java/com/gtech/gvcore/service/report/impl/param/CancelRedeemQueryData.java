package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName CancelRedeemQueryData
 * @Description cancel redeem query data
 * <AUTHOR>
 * @Date 2023/4/10 17:31
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class CancelRedeemQueryData extends TransactionDataPageParam implements ReportQueryParam {

    private List<String> issuerCodeList;

    private List<String> outletCodeList;

    private Date transactionDateStart;

    private Date transactionDateEnd;

    private List<String> merchantCodeList;

    private List<String> cpgCodeList;

    private String invoiceNo;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

}
