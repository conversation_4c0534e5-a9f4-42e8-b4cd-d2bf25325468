package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 17:21
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class CardLifeCycleMovementBean {

    @ExcelProperty(index = 0,value = "Merchant")
    private String merchant;

    @ExcelProperty(index = 1,value = "Merchant Outlet")
    private String merchantOutlet;

    @ExcelProperty(index = 2,value = "Merchant Outlet Code")
    private String merchantOutletCode;

    @ExcelProperty(index = 3,value = "Voucher Number")
    private String voucherNumber;

    @ExcelProperty(index = 4,value = "Request Number")
    private String requestNumber;

    @ExcelProperty(index = 5,value = "Voucher Request Status")
    private String voucherRequestStatus;

    @ExcelProperty(index = 6,value = "Time Zone")
    private String timeZone;

    @ExcelProperty(index = 7,value = "Outbound")
    private String outbound;

    @ExcelProperty(index = 8,value = "Inbound")
    private String inbound;

    @ExcelProperty(index = 9,value = "Requested Date")
    private String requestedDate;

    @ExcelProperty(index = 10,value = "Approved Date")
    private String approvedDate;

    @ExcelProperty(index = 11,value = "Allocate Date")
    private String allocateDate;

    @ExcelProperty(index = 12,value = "Receive Date")
    private String receiveDate;
}
