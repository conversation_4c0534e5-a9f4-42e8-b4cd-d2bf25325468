package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.IssueHandlingService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GcSalesDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.GcSalesBo;
import com.gtech.gvcore.service.report.impl.param.GcSalesQueryData;
import com.gtech.gvcore.service.report.impl.support.GcSalesBaseImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:01
 * @Description: 0.24 - 1.02 59.58s,
 */
@Service
public class GcSalesDetailImpl extends GcSalesBaseImpl<GcSalesDetailedBean>
        implements BusinessReport<GcSalesQueryData, GcSalesDetailedBean>, SingleReport {

    @Autowired
    protected IssueHandlingService issueHandlingService;
    @Autowired
    protected CustomerOrderMapper customerOrderMapper;


    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_SALES_DETAILED_REPORT;
    }

    @Override
    protected void addParam(GcSalesQueryData param, CreateReportRequest reportParam) {

    }

    @Override
    public List<GcSalesDetailedBean> getExportData(List<GcSalesBo> boList) {

        final JoinDataMap<GiftCardEntity> voucherMap = super.getMapByCode(boList, GcSalesBo::getVoucherCode, GiftCardEntity.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(boList, GcSalesBo::getOutletCode, Outlet.class);
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(boList, GcSalesBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(boList, GcSalesBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(boList, GcSalesBo::getCustomerCode, Customer.class);


        return boList.stream()
                .map(e -> {
                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final GcCpg cpg = cpgMap.findValue(e.getCpgCode());
                    final GiftCardEntity voucher = voucherMap.findValue(e.getVoucherCode());
                    Customer value = customerMap.findValue(e.getCustomerCode());
                    String customerName;
                    if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                        customerName = value.getCompanyName();
                    } else {
                        customerName = value.getCustomerName();
                    }
                    final String transactionDate = DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS);
                    final String denomination = super.toAmount(e.getDenomination());
                    final String issuerYear = DateUtil.format(voucher.getCreateTime(), DateUtil.FORMAT_YYYY);

                    return new GcSalesDetailedBean()
                            .setCardNumber(e.getVoucherCode())
                            .setCustomerName(customerName)
                            .setMerchant(merchant.getMerchantName())
                            .setOutlet(outlet.getOutletName())
                            .setTransactionDate(transactionDate)
                            .setProgramGroup(cpg.getCpgName())
                            .setTransactionType("GIFT CARD SELL")
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setIssuanceYear(issuerYear)
                            .setDenomination(denomination)
                            .setApprovalCode(e.getApprovalCode());
                }).collect(Collectors.toList());
    }

}
