package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dao.GcCpgMapper;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName CpgQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:46
 * @Version V1.0
 **/
@Component
public class GcCpgQueryImpl implements QuerySupport<GcCpg> {

    public static final GcCpg EMPTY = new GcCpg();

    @Autowired
    private GcCpgMapper cpgMapper;

    @Override
    public List<GcCpg> queryByCode(List<String> codes, String... selectFields) {
        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(GcCpg.class);
        example.createCriteria().andIn(Cpg.C_CPG_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<GcCpg> list = cpgMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

    @Override
    public Function<GcCpg, String> codeMapper() {
        return GcCpg::getCpgCode;
    }

    @Override
    public GcCpg emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<GcCpg> supportType() {
        return GcCpg.class;
    }
}
