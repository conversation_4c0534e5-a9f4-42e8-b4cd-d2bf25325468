package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.merchant.CreateMerchantRequest;
import com.gtech.gvcore.common.request.merchant.DeleteMerchantRequest;
import com.gtech.gvcore.common.request.merchant.GetMerchantRequest;
import com.gtech.gvcore.common.request.merchant.QueryMerchantByCompanyCodesRequest;
import com.gtech.gvcore.common.request.merchant.QueryMerchantRequest;
import com.gtech.gvcore.common.request.merchant.UpdateMerchantRequest;
import com.gtech.gvcore.common.request.merchant.UpdateMerchantStatusRequest;
import com.gtech.gvcore.common.response.merchant.MerchantResponse;
import com.gtech.gvcore.dao.model.Merchant;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
public interface MerchantService {

    Result<String> createMerchant(CreateMerchantRequest param);

    Result<Void> updateMerchant(UpdateMerchantRequest param);

    Result<Void> deleteMerchant(DeleteMerchantRequest param);

    PageResult<MerchantResponse> queryMerchantList(QueryMerchantRequest param);

    /**
     *
     * 根据company code list 查询merchant 不追加分页参数 并返回 merchant 对象
     *
     * @param companyCodes
     * @return
     */
    List<Merchant> queryMerchantByCompanyCodesNotPageParam(List<String> companyCodes);

    MerchantResponse getMerchant(GetMerchantRequest param);


    Result<Void> updateMerchantStatus(UpdateMerchantStatusRequest param);

    PageResult<MerchantResponse> queryMerchantByCompanyCodes(QueryMerchantByCompanyCodesRequest request);

    /**
     * 
     * <AUTHOR>
     * @param merchantCodeList
     * @return
     * @date 2022年6月23日
     */
    Map<String, Merchant> queryMerchantMapByMerchantCodeList(List<String> merchantCodeList);

    List<Merchant> queryMerchantListByCodeList(List<String> merchantCodeList);

	List<Merchant> queryAllMerchant();

}
