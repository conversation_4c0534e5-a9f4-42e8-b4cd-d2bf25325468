package com.gtech.gvcore.service.report.impl.support.aging.builder;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.extend.ReportAmountSupport;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.aging.VoucherUsedAgeBySBUBean;
import com.gtech.gvcore.service.report.impl.bean.aging.VoucherUsedAgeBySBUOneBean;
import com.gtech.gvcore.service.report.impl.bean.aging.VoucherUsedAgeBySBUTwoBean;
import com.gtech.gvcore.service.report.impl.bo.AgingBo;
import com.gtech.gvcore.service.report.impl.bo.AgingVoucherTransactionBoVoucher;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheet;
import com.gtech.gvcore.service.report.impl.support.aging.AgingSheetBuilder;
import com.gtech.gvcore.service.report.impl.support.aging.bo.VoucherUsedAgeBySBUBo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * @ClassName VoucherUsedAgeBySBUBuilder
 * @Description Voucher Used Age by SBU
 * <AUTHOR>
 * @Date 2022/11/3 10:12
 * @Version V1.0
 **/
@Component
public class VoucherUsedAgeBySBUBuilder implements ReportAmountSupport, AgingSheetBuilder, ReportProportionDataFunction {

    private static final int SCALE = 1;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.AGING_VOUCHER_USED_AGE_BY_SBU_REPORT;
    }

    public AgingSheet builder(final ReportContext context) {

        final AgingSheet agingSheet = new AgingSheet()
                .setHead(null)
                .setSheetName(exportTypeEnum().getSheetName());

        // result map
        final Collection<VoucherUsedAgeBySBUBo> boList = this.getResultMap(context);
        if (CollectionUtils.isEmpty(boList)) return agingSheet; // empty

        // sum total
        final VoucherUsedAgeBySBUBo totalBo = new VoucherUsedAgeBySBUBo().setSbuName("Total");
        boList.forEach(e -> totalBo.addOneValue(e.getOneValue())
                .addTwoValue(e.getTwoValue())
                .addThreeValue(e.getThreeValue())
                .addFourValue(e.getFourValue())
                .addSixValue(e.getSixValue()));

        // join data
        final List<VoucherUsedAgeBySBUBo> bos = new ArrayList<>(boList);
        bos.add(totalBo);

        //table
        final List<VoucherUsedAgeBySBUBean> table0 = new ArrayList<>();
        final List<VoucherUsedAgeBySBUOneBean> table1 = new ArrayList<>();
        final List<VoucherUsedAgeBySBUTwoBean> table2 = new ArrayList<>();

        //add data
        bos.forEach(bo -> {

            //table0
            table0.add(new VoucherUsedAgeBySBUBean()
                    .setSbuName(bo.getSbuName())
                    .setOneValue(toAmount(bo.getOneValue()))
                    .setTwoValue(toAmount(bo.getTwoValue()))
                    .setThreeValue(toAmount(bo.getThreeValue()))
                    .setFourValue(toAmount(bo.getFourValue()))
                    .setSixValue(toAmount(bo.getSixValue()))
                    .setTotal(toAmount(bo.getTotal())));

            //table1
            table1.add(new VoucherUsedAgeBySBUOneBean().setSbuName(bo.getSbuName())
                    .setOneValue(getProportion(bo.getOneValue(), totalBo.getOneValue(), SCALE))
                    .setTwoValue(getProportion(bo.getTwoValue(), totalBo.getTwoValue(), SCALE))
                    .setThreeValue(getProportion(bo.getThreeValue(), totalBo.getThreeValue(), SCALE))
                    .setFourValue(getProportion(bo.getFourValue(), totalBo.getFourValue(), SCALE))
                    .setSixValue(getProportion(bo.getSixValue(), totalBo.getSixValue(), SCALE))
                    .setTotal(getProportion(bo.getTotal(), totalBo.getTotal(), SCALE)));

            //table2
            table2.add(new VoucherUsedAgeBySBUTwoBean().setSbuName(bo.getSbuName())
                    .setOneValue(getProportion(bo.getOneValue(), bo.getTotal(), SCALE))
                    .setTwoValue(getProportion(bo.getTwoValue(), bo.getTotal(), SCALE))
                    .setThreeValue(getProportion(bo.getThreeValue(), bo.getTotal(), SCALE))
                    .setFourValue(getProportion(bo.getFourValue(), bo.getTotal(), SCALE))
                    .setSixValue(getProportion(bo.getSixValue(), bo.getTotal(), SCALE))
                    .setTotal(getProportion(bo.getTotal(), bo.getTotal(), SCALE)));

        });

        //setting data
        agingSheet.addSheetData("vau0", ReportExportTypeEnum.AGING_VOUCHER_USED_AGE_BY_SBU_REPORT, table0)
                .addSheetData("vau1", ReportExportTypeEnum.AGING_VOUCHER_USED_AGE_BY_SBU_T_ONE_REPORT, table1)
                .addSheetData("vau2", ReportExportTypeEnum.AGING_VOUCHER_USED_AGE_BY_SBU_T_TWO_REPORT, table2);

        return agingSheet;

    }

    private Collection<VoucherUsedAgeBySBUBo> getResultMap(final ReportContext context) {

        //init
        final AddFunction addFunction = this.getAddFunction();
        final Map<String, VoucherUsedAgeBySBUBo> result = new HashMap<>();

        //get master sales data
        final List<AgingBo> agingSalesData = context.getCacheList(AgingSheetBuilder.SALES_DATA_KEY);

        //get join data
        final JoinDataMap<Voucher> voucherMap = context.getCacheJoinMap(AgingSheetBuilder.VOUCHER_MAP_KEY);
        final List<AgingVoucherTransactionBoVoucher> transactionDataList = context.getCacheList(AgingSheetBuilder.REDEEM_DATA_KEY);

        //init generate
        final Map<String, String> sbuMap = getSbuMap(context); // voucher code -> sbu name
        final Set<String> useVoucher = transactionDataList.stream().map(AgingVoucherTransactionBoVoucher::getVoucherCode).collect(Collectors.toSet()); // voucher code distinct

        //for sales
        agingSalesData.stream()
                // filter voucher code
                .filter(e -> voucherMap.containsKey(e.getVoucherCode()))
                // filter voucher code in redeem
                .filter(e -> useVoucher.contains(e.getVoucherCode()))
                // add
                .forEach(e -> addFunction.add(
                        // voucher create time
                        voucherMap.findValue(e.getVoucherCode()).getCreateTime()
                        // compute if absent
                        , result.computeIfAbsent(sbuMap.get(e.getVoucherCode()), k -> new VoucherUsedAgeBySBUBo().setSbuName(k))
                        // get denomination
                        , voucherMap.findValue(e.getVoucherCode()).getDenomination()));

        //return
        return result.values();
    }

    private Map<String, String> getSbuMap(final ReportContext context) {

        Map<String, String> sbuMap = new HashMap<>();
        final List<AgingVoucherTransactionBoVoucher> transactionDataList = context.getCacheList(AgingSheetBuilder.REDEEM_DATA_KEY);
        final JoinDataMap<Merchant> merchantMap = context.getCacheJoinMap(AgingSheetBuilder.REDEEM_MERCHANT_MAP_KEY);
        final JoinDataMap<Company> companyMap = context.getCacheJoinMap(AgingSheetBuilder.REDEEM_COMPANY_MAP_KEY);

        transactionDataList.forEach(transactionData -> {

            Merchant merchant = merchantMap.findValue(transactionData.getMerchantCode());
            Company company = companyMap.findValue(merchant.getCompanyCode());

            sbuMap.put(transactionData.getVoucherCode(), company.getSbu());
        });
        return sbuMap;
    }

    private AddFunction getAddFunction() {

        final long now = System.currentTimeMillis();

        final BiConsumer<VoucherUsedAgeBySBUBo, BigDecimal> addM30Function = VoucherUsedAgeBySBUBo::addOneValue;
        final BiConsumer<VoucherUsedAgeBySBUBo, BigDecimal> addM60Function = VoucherUsedAgeBySBUBo::addTwoValue;
        final BiConsumer<VoucherUsedAgeBySBUBo, BigDecimal> addM90Function = VoucherUsedAgeBySBUBo::addThreeValue;
        final BiConsumer<VoucherUsedAgeBySBUBo, BigDecimal> addM180Function = VoucherUsedAgeBySBUBo::addFourValue;
        final BiConsumer<VoucherUsedAgeBySBUBo, BigDecimal> addM360Function = VoucherUsedAgeBySBUBo::addSixValue;

        //add function
        return (saleTime, executeObject, value) -> {


            long time = now - saleTime.getTime();
            int day = (int) (time / (24 * 60 * 60 * 1000));

            if (day <= AddFunction.FunctionMethod.M30.getDay()) addM30Function.accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M60.getDay()) addM60Function.accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M90.getDay()) addM90Function.accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M180.getDay()) addM180Function.accept(executeObject, value);
            else if (day <= AddFunction.FunctionMethod.M360.getDay()) addM360Function.accept(executeObject, value);
        };
    }

    public interface AddFunction {

        enum FunctionMethod {
            M30(30),
            M60(60),
            M90(90),
            M180(180),
            M360(360),
            ;
            private final int day;

            FunctionMethod(int day) {
                this.day = day;
            }

            public int getDay() {
                return day;
            }
        }

        void add(Date saleTime, VoucherUsedAgeBySBUBo executeObject, BigDecimal value);
    }

    @Override
    public Map<ReportExportTypeEnum, Class<?>> getResultDateType() {
        EnumMap<ReportExportTypeEnum, Class<?>> typeMap = new EnumMap<>(ReportExportTypeEnum.class);
        typeMap.put(ReportExportTypeEnum.AGING_VOUCHER_USED_AGE_BY_SBU_REPORT, VoucherUsedAgeBySBUBean.class);
        typeMap.put(ReportExportTypeEnum.AGING_VOUCHER_USED_AGE_BY_SBU_T_ONE_REPORT, VoucherUsedAgeBySBUOneBean.class);
        typeMap.put(ReportExportTypeEnum.AGING_VOUCHER_USED_AGE_BY_SBU_T_TWO_REPORT, VoucherUsedAgeBySBUTwoBean.class);
        return typeMap;
    }
}
