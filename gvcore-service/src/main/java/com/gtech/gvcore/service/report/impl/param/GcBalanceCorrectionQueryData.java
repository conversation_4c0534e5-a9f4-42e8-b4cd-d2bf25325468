package com.gtech.gvcore.service.report.impl.param;

import com.gtech.commons.page.PageParam;
import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class GcBalanceCorrectionQueryData extends PageParam implements ReportQueryParam {

    private Date transactionDateStart;
    private Date transactionDateEnd;
    private List<String> cpgCodeList;
    private List<String> outletCodeList;
    private List<String> merchantCodeList;
    private List<String> customerCodeList;
    private List<String> cardNumbers;
    private List<String> transactionType;

}

