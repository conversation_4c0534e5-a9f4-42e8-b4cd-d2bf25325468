package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.businesslogdetail.CreateBusinessLogDetailRequest;
import com.gtech.gvcore.common.request.businesslogdetail.QueryBusinessLogDetailRequest;
import com.gtech.gvcore.common.response.businesslogdetail.BusinessLogDetailResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/11 17:08
 */
public interface BusinessLogDetailService {

    int createBusinessLogDetail(CreateBusinessLogDetailRequest request);

    int createBusinessLogDetailList(List<CreateBusinessLogDetailRequest> request);


    List<BusinessLogDetailResponse> queryBusinessLogDetail(QueryBusinessLogDetailRequest request);


}
