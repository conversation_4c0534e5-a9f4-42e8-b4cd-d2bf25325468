package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcBalanceCorrectionDetailBean;
import com.gtech.gvcore.service.report.impl.bo.GcBalanceCorrectionBo;
import com.gtech.gvcore.service.report.impl.param.GcBalanceCorrectionQueryData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:54
 * @Description:
 */
@Service
public class GcBalanceCorrectionDetailedImpl extends ReportSupport
        implements BusinessReport<GcBalanceCorrectionQueryData, GcBalanceCorrectionDetailBean>, SingleReport, ReportProportionDataFunction {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_BALANCE_CORRECTION_DETAIL_REPORT;
    }

    @Override
    public GcBalanceCorrectionQueryData builderQueryParam(final CreateReportRequest reportParam) {

        final GcBalanceCorrectionQueryData queryData = new GcBalanceCorrectionQueryData();
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());
        queryData.setTransactionType(reportParam.getTransactionTypes());
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setCustomerCodeList(reportParam.getCustomerCodes());
        if (StringUtils.isNotBlank(reportParam.getVoucherCode())) {
            queryData.setCardNumbers((Arrays.asList(reportParam.getVoucherCode().split(","))));
        }
        return queryData;
    }

    @Override
    public List<GcBalanceCorrectionDetailBean> getExportData(final GcBalanceCorrectionQueryData queryData) {
        List<GcBalanceCorrectionBo> list = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectBalanceCorrection, queryData, list::addAll);

        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, GcBalanceCorrectionBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, GcBalanceCorrectionBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcBalanceCorrectionBo::getOutletCode, Outlet.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, GcBalanceCorrectionBo::getCustomerCode, Customer.class);


        return list.stream().map(x -> {

            Customer value = customerMap.findValue(x.getCustomerCode());
            String customerName;
            if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                customerName = value.getCompanyName();
            } else {
                customerName = value.getCustomerName();
            }

            GcBalanceCorrectionDetailBean bean = new GcBalanceCorrectionDetailBean();
            bean.setCardNumber(x.getCardNumber());
            bean.setCpgCode(cpgMap.findValue(x.getCpgCode()).getCpgName());
            bean.setMerchant(merchantMap.findValue(x.getMerchantCode()).getMerchantName());
            bean.setOutlet(outletMap.findValue(x.getOutletCode()).getOutletName());
            bean.setTransactionDate(DateUtil.format(x.getCreateTime(), DateUtil.FORMAT_YYYYMMDDHHMISS));
            bean.setInitialBalance(x.getInitialBalance());
            bean.setCorrectionBalance(super.toAmount(x.getCorrectionBalance()));
            bean.setAfterCorrection(super.toAmount(x.getAfterBalance()));
            bean.setTransactionType(x.getTransactionType());
            bean.setCustomerName(customerName);
            bean.setExpiryDate(DateUtil.format(x.getExpiryDate(), DateUtil.FORMAT_YYYYMMDDHHMISS));
            bean.setNotes(x.getNotes());
            return bean;
        }).collect(Collectors.toList());
    }
}
