package com.gtech.gvcore.service.report.extend.context;

import com.gtech.gvcore.common.enums.ReportStatusEnum;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.export.file.DefaultFileContext;
import com.gtech.gvcore.service.report.export.file.FileContext;
import com.gtech.gvcore.service.report.export.snapshoot.DefaultReportDateContext;
import com.gtech.gvcore.service.report.export.snapshoot.ReportDataHelper;
import com.gtech.gvcore.service.report.export.snapshoot.ReportDateContext;
import lombok.Getter;

import java.util.function.BiPredicate;
import java.util.function.Consumer;
import java.util.function.Supplier;


/**
 * @ClassName ReportContextBuilder
 * @Description 报表上下文构建类
 * <AUTHOR>
 * @Date 2022/10/20 20:03
 * @Version V1.0
 **/
public class ReportContextBuilder {

    private ReportRequest reportRequest;
    private BusinessReport<ReportQueryParam, ?> businessReport;
    private Supplier<ReportDateContext> reportDataContextSupplier;
    private Supplier<FileContext> fileContextSupplier;

    private Consumer<ReportContext> initFunction = a -> {};
    private Consumer<ReportContext> builderReportFunction = a -> {};
    private Consumer<ReportContext> callbackFunction = a -> {};
    private BiPredicate<ReportContext, ReportStatusEnum> updateReportStatusFunction = (a, b) -> false;


    public static ReportContextBuilder builder() {
        return new ReportContextBuilder();
    }

    public ReportContextBuilder builderReportRequest(ReportRequest val) {
        reportRequest = val;
        return this;
    }

    public ReportContextBuilder builderBusinessReport(BusinessReport<ReportQueryParam, ?> val) {
        businessReport = val;
        return this;
    }

    public ReportContextBuilder bindReportDateContext(ReportDateContext val) {
        reportDataContextSupplier = () -> val;
        return this;
    }

    public ReportContextBuilder bindDefaultReportDateContext(ReportDataHelper reportDataHelper) {

        reportDataContextSupplier = () -> new DefaultReportDateContext(reportRequest, reportDataHelper, businessReport.snapshotSize());
        return this;
    }

    public ReportContextBuilder bindFileContext(FileContext val) {
        fileContextSupplier = () -> val;
        return this;
    }

    public ReportContextBuilder bindDefaultFileContext() {

        fileContextSupplier = () -> new DefaultFileContext(reportRequest, businessReport);
        return this;
    }

    public ReportContextBuilder builderInitFunction(Consumer<ReportContext> val) {
        initFunction = val;
        return this;
    }

    public ReportContextBuilder builderReportFunction(Consumer<ReportContext> val) {
        builderReportFunction = val;
        return this;
    }

    public ReportContextBuilder builderCallbackFunction(Consumer<ReportContext> val) {
        callbackFunction = val;
        return this;
    }

    public ReportContextBuilder builderUpdateReportStatusFunction(BiPredicate<ReportContext, ReportStatusEnum> val) {
        updateReportStatusFunction = val;
        return this;
    }

    public ReportContextBuilder customContext() {
        businessReport.customContext(this);
        return this;
    }



    public ReportContext build() {

        return new ReportContext(reportRequest, businessReport, ProcessFunctionBean.newInstance(this), reportDataContextSupplier.get(), fileContextSupplier.get());
    }

    @Getter
    public static class ProcessFunctionBean {

        private final Consumer<ReportContext> initFunction;
        private final Consumer<ReportContext> builderReportFunction;
        private final Consumer<ReportContext> callbackFunction;
        private final BiPredicate<ReportContext, ReportStatusEnum> updateReportStatusFunction;

        public ProcessFunctionBean(Consumer<ReportContext> initFunction, Consumer<ReportContext> builderReportFunction, Consumer<ReportContext> callbackFunction, BiPredicate<ReportContext, ReportStatusEnum> updateReportStatusFunction) {
            this.initFunction = initFunction;
            this.callbackFunction = callbackFunction;
            this.builderReportFunction = builderReportFunction;
            this.updateReportStatusFunction = updateReportStatusFunction;
        }

        public static ProcessFunctionBean newInstance(ReportContextBuilder builder) {

            return new ProcessFunctionBean(builder.initFunction, builder.builderReportFunction, builder.callbackFunction, builder.updateReportStatusFunction);
        }
    }

}
