package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.basic.masterdata.web.entity.MasterDataDdLangEntity;
import com.gtech.basic.masterdata.web.service.MasterDataDdLangService;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.meansofpayment.CreateMeansOfPaymentRequest;
import com.gtech.gvcore.common.request.meansofpayment.QueryMeansOfPaymentsByPageRequest;
import com.gtech.gvcore.common.request.meansofpayment.UpdateMeansOfPaymentRequest;
import com.gtech.gvcore.common.request.meansofpayment.UpdateMeansOfPaymentStatusRequest;
import com.gtech.gvcore.common.response.meansofpayment.OutletInfo;
import com.gtech.gvcore.common.response.meansofpayment.QueryMeansOfPaymentsByPageResponse;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.dto.MeansOfPaymentDto;
import com.gtech.gvcore.dao.dto.OutletDto;
import com.gtech.gvcore.dao.dto.OutletIssuerNameInfo;
import com.gtech.gvcore.dao.mapper.MeansOfPaymentMapper;
import com.gtech.gvcore.dao.model.MeansOfPayment;
import com.gtech.gvcore.dao.model.MeansOfPaymentOutlet;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.MeansOfPaymentOutletService;
import com.gtech.gvcore.service.MeansOfPaymentService;
import com.gtech.gvcore.service.OutletService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
@Service
public class MeansOfPaymentServiceImpl implements MeansOfPaymentService {

    @Value("${gv.masterdata.ddcode.mopgroup.grp:MOP_GROUP_GRP}")
    private String ddCodeMopGroupGrp;

    @Value("${gv.masterdata.ddcode.mopgroup.externalpaymentmode:MOP_GROUP_EXTERNAL_PAYMENT_MODE}")
    private String ddCodeMopGroupExternalPaymentMode;

    @Value("${gv.outlet.outlettype.mvstore:MVStore}")
    private String outletTypeMvStore;

    @Autowired
    private MeansOfPaymentMapper meansOfPaymentMapper;

    @Autowired
    private OutletService outletService;

    @Autowired
    private MasterDataDdLangService masterDataDdLangService;

    @Autowired
    private MeansOfPaymentOutletService meansOfPaymentOutletService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Void> createMeansOfPayment(CreateMeansOfPaymentRequest request) {

        MeansOfPayment meansOfPayment = new MeansOfPayment();
        meansOfPayment.setMopName(request.getMopName());
        int count = meansOfPaymentMapper.selectCount(meansOfPayment);
        if (count > 0) {
            return Result.failed(ResultErrorCodeEnum.NAME_ALREADY_USED.code(),
                    ResultErrorCodeEnum.NAME_ALREADY_USED.desc());
        }

        List<String> outletCodeList = request.getOutletCodeList().stream().distinct().collect(Collectors.toList());
        Result<MeansOfPayment> result = checkData(request.getMopGroup(), outletCodeList);
        if (!result.isSuccess()) {
            return Result.failed(result.getCode(), result.getMessage());
        }

        meansOfPayment = result.getData();
        BeanUtils.copyProperties(request, meansOfPayment);
        meansOfPayment.setMeansOfPaymentCode(UUIDUtils.generateCode());
        meansOfPayment.setStatus(GvcoreConstants.STATUS_ENABLE);
        meansOfPayment.setCreateTime(new Date());
        try {
            meansOfPaymentMapper.insertSelective(meansOfPayment);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }

        meansOfPaymentOutletService.insesrt(meansOfPayment.getMeansOfPaymentCode(), outletCodeList,
                meansOfPayment.getCreateUser());

        return Result.ok();
    }

    /**
     * 
     * @param mopGroup
     * @param outletCodeList
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    private Result<MeansOfPayment> checkData(String mopGroup, List<String> outletCodeList) {

        MeansOfPayment meansOfPayment = new MeansOfPayment();
        MasterDataDdLangEntity mopGroupGrp = getMasterDataDdLangEntity(ddCodeMopGroupGrp, mopGroup);
        MasterDataDdLangEntity externalPaymentMode = getMasterDataDdLangEntity(ddCodeMopGroupExternalPaymentMode,
                mopGroup);
        if (mopGroupGrp == null || externalPaymentMode == null) {
            return Result.failed(ResultErrorCodeEnum.SAP_MOP_GROUP_ERROR.code(),
                    ResultErrorCodeEnum.SAP_MOP_GROUP_ERROR.desc());
        }

        meansOfPayment.setGrp(mopGroupGrp.getDdText());
        meansOfPayment.setExternalPaymentMode(externalPaymentMode.getDdText());


        List<Outlet> outletList = outletService.queryByOutletCodeList(outletCodeList);
        Map<String, Outlet> outletMap = outletList.stream().collect(Collectors.toMap(Outlet::getOutletCode, v -> v));
        for (String outletCode : outletCodeList) {
            Outlet outlet = outletMap.get(outletCode);
            if (outlet == null) {
                return Result.failed(ResultErrorCodeEnum.NO_STORE_DATA_FOUND.code(),
                        outletCode + " " + ResultErrorCodeEnum.NO_STORE_DATA_FOUND.desc());
            }
            if (!outletTypeMvStore.equals(outlet.getOutletType())) {
                return Result.failed(ResultErrorCodeEnum.STORE_TYPE_IS_NOT_MVSTORE.code(),
                        outletCode + " " + ResultErrorCodeEnum.STORE_TYPE_IS_NOT_MVSTORE.desc());
            }
        }
        return Result.ok(meansOfPayment);
    }

    /**
     * 
     * @param ddCode
     * @param ddValue
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    private MasterDataDdLangEntity getMasterDataDdLangEntity(String ddCode, String ddValue) {
        MasterDataDdLangEntity dataEntity = new MasterDataDdLangEntity();
        dataEntity.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        dataEntity.setDdCode(ddCode);
        dataEntity.setDdValue(ddValue);
        dataEntity.setState(1);
        return masterDataDdLangService.getByCode(dataEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Void> updateMeansOfPayment(UpdateMeansOfPaymentRequest request) {

        MeansOfPayment queryMeansOfPayment = new MeansOfPayment();
        queryMeansOfPayment.setId(request.getId());
        queryMeansOfPayment = meansOfPaymentMapper.selectOne(queryMeansOfPayment);
        if (queryMeansOfPayment == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        
        if(!queryMeansOfPayment.getMopName().equals(request.getMopName())) {
            MeansOfPayment meansOfPayment = new MeansOfPayment();
            meansOfPayment.setMopName(request.getMopName());
            int count = meansOfPaymentMapper.selectCount(meansOfPayment);
            if (count > 0) {
                return Result.failed(ResultErrorCodeEnum.NAME_ALREADY_USED.code(),
                        ResultErrorCodeEnum.NAME_ALREADY_USED.desc());
            }
        }
        

        List<String> outletCodeList = request.getOutletCodeList().stream().distinct().collect(Collectors.toList());
        Result<MeansOfPayment> result = checkData(request.getMopGroup(), outletCodeList);
        if (!result.isSuccess()) {
            return Result.failed(result.getCode(), result.getMessage());
        }

        MeansOfPayment meansOfPayment = result.getData();
        List<MeansOfPaymentOutlet> outletList = meansOfPaymentOutletService
                .queryByMeansOfPaymentCode(queryMeansOfPayment.getMeansOfPaymentCode());
        Map<String, MeansOfPaymentOutlet> outletMap = outletList.stream()
                .collect(Collectors.toMap(MeansOfPaymentOutlet::getOutletCode, v -> v));

        BeanUtils.copyProperties(request, meansOfPayment);
        meansOfPayment.setUpdateTime(new Date());
        int i = meansOfPaymentMapper.updateByPrimaryKeySelective(meansOfPayment);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }

        List<String> insertOutletCodeList = new ArrayList<>(outletCodeList.size());
        for (String outletCode : outletCodeList) {
            MeansOfPaymentOutlet outlet = outletMap.remove(outletCode);
            if (outlet == null) {
                insertOutletCodeList.add(outletCode);
                continue;
            }
            if (outlet.getDeleteStatus().equals(GvcoreConstants.DELETE_STATUS_ENABLE)) {
                updateMeansOfPaymentOutletDeleteStatus(meansOfPayment, outlet.getId(),
                        GvcoreConstants.DELETE_STATUS_DISABLE);
            }
        }

        for (Map.Entry<String, MeansOfPaymentOutlet> entry : outletMap.entrySet()) {
            updateMeansOfPaymentOutletDeleteStatus(meansOfPayment, entry.getValue().getId(),
                    GvcoreConstants.DELETE_STATUS_ENABLE);
        }

        if (!insertOutletCodeList.isEmpty()) {
            meansOfPaymentOutletService.insesrt(queryMeansOfPayment.getMeansOfPaymentCode(), insertOutletCodeList,
                    meansOfPayment.getUpdateUser());
        }

        return Result.ok();
    }

    /**
     * 
     * @param meansOfPayment
     * @param id
     * @param deleteStatus
     * <AUTHOR>
     * @date 2022年4月22日
     */
    private void updateMeansOfPaymentOutletDeleteStatus(MeansOfPayment meansOfPayment, Long id, Integer deleteStatus) {
        MeansOfPaymentOutlet updateOutlet = new MeansOfPaymentOutlet();
        updateOutlet.setId(id);
        updateOutlet.setDeleteStatus(deleteStatus);
        updateOutlet.setUpdateUser(meansOfPayment.getUpdateUser());
        updateOutlet.setUpdateTime(meansOfPayment.getUpdateTime());
        meansOfPaymentOutletService.updateByPrimaryKeySelective(updateOutlet);
    }

    @Override
    public Result<Void> updateMeansOfPaymentStatus(UpdateMeansOfPaymentStatusRequest request) {

        MeansOfPayment meansOfPayment = new MeansOfPayment();
        meansOfPayment.setId(request.getId());
        int i = meansOfPaymentMapper.selectCount(meansOfPayment);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        meansOfPayment.setStatus(request.getStatus());
        meansOfPayment.setUpdateUser(request.getUpdateUser());
        meansOfPayment.setUpdateTime(new Date());
        i = meansOfPaymentMapper.updateByPrimaryKeySelective(meansOfPayment);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }

        return Result.ok();
    }

    @Override
    public PageResult<QueryMeansOfPaymentsByPageResponse> queryMeansOfPaymentsByPage(
            QueryMeansOfPaymentsByPageRequest request) {

        MeansOfPaymentDto meansOfPayment = new MeansOfPaymentDto();
        BeanUtils.copyProperties(request, meansOfPayment);
        meansOfPayment.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);

        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<MeansOfPayment> list = meansOfPaymentMapper.selectSelective(meansOfPayment);
        PageInfo<MeansOfPayment> pageInfo = new PageInfo<>(list);

        List<String> meansOfPaymentCodeList = list.stream().map(MeansOfPayment::getMeansOfPaymentCode)
                .collect(Collectors.toList());
        List<MeansOfPaymentOutlet> mpOutletList = meansOfPaymentOutletService
                .queryByMeansOfPaymentCodeList(meansOfPaymentCodeList, GvcoreConstants.DELETE_STATUS_DISABLE);
        List<String> outletCodeList = mpOutletList.stream().map(MeansOfPaymentOutlet::getOutletCode)
                .collect(Collectors.toList());

        OutletDto dto = new OutletDto();
        dto.setOutletCodeList(outletCodeList);
        List<OutletIssuerNameInfo> outletIssuerNameInfoList = outletService.queryOutletIssuerNameInfo(dto);

        Map<String, OutletIssuerNameInfo> outletIssuerNameMap = outletIssuerNameInfoList.stream()
                .collect(Collectors.toMap(OutletIssuerNameInfo::getOutletCode, v -> v));
        Map<String, List<MeansOfPaymentOutlet>> mpOutletMap = mpOutletList.stream()
                .collect(Collectors.groupingBy(MeansOfPaymentOutlet::getMeansOfPaymentCode));

        List<QueryMeansOfPaymentsByPageResponse> responses = new ArrayList<>(request.getPageSize());
        list.forEach(item -> {
            QueryMeansOfPaymentsByPageResponse response = new QueryMeansOfPaymentsByPageResponse();
            BeanUtils.copyProperties(item, response);
            responses.add(response);

            List<MeansOfPaymentOutlet> outlets = mpOutletMap.getOrDefault(item.getMeansOfPaymentCode(),
                    Collections.emptyList());
            List<OutletInfo> outletInfoList = new ArrayList<>(outlets.size());
            response.setOutletInfoList(outletInfoList);
            for (MeansOfPaymentOutlet meansOfPaymentOutlet : outlets) {
                OutletInfo outletInfo = new OutletInfo();
                outletInfo.setOutletCode(meansOfPaymentOutlet.getOutletCode());
                OutletIssuerNameInfo nameInfo = outletIssuerNameMap.get(meansOfPaymentOutlet.getOutletCode());
                if (nameInfo != null) {
                    outletInfo.setOutletName(nameInfo.getOutletName());
                    /*outletInfo.setIssuerCode(nameInfo.getIssuerCode());
                    outletInfo.setIssuerName(nameInfo.getIssuerName());*/
                }
                outletInfoList.add(outletInfo);
            }
        });

        return PageResult.ok(responses, pageInfo.getTotal());
    }

    @Override
    public QueryMeansOfPaymentsByPageResponse getMeansOfPayments(String meansOfPaymentsCode) {
        MeansOfPayment meansOfPayment = new MeansOfPayment();
        meansOfPayment.setMeansOfPaymentCode(meansOfPaymentsCode);
        return BeanCopyUtils.jsonCopyBean(meansOfPaymentMapper.selectOne(meansOfPayment),QueryMeansOfPaymentsByPageResponse.class);
    }

    @Override
    public Map<String, MeansOfPayment> queryByCodeList(List<String> meansOfPaymentCodeList) {

        if (CollectionUtils.isEmpty(meansOfPaymentCodeList)) {
            return Collections.emptyMap();
        }

        List<MeansOfPayment> list = meansOfPaymentMapper.queryByCodeList(meansOfPaymentCodeList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(MeansOfPayment::getMeansOfPaymentCode, v -> v));
    }

    @Override
    public MeansOfPayment getMeansOfPaymentsByName(String meansOfPaymentsName) {

        MeansOfPayment meansOfPayment = new MeansOfPayment();
        meansOfPayment.setMopName(meansOfPaymentsName);
        return meansOfPaymentMapper.selectOne(meansOfPayment);
    }

}
