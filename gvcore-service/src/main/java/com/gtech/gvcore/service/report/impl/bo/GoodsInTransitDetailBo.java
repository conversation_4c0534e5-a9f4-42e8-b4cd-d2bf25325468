package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date 2022年4月26日
 */
@Getter
@Setter
@Accessors(chain = true)
public class GoodsInTransitDetailBo {

    private String requestId;

    private String receiveCode;

    private String outbound;

    private String inbound;

    private String cpgCode;

    private String voucherStartNo;

    private String voucherEndNo;

    private Date allLocationTime;

}


