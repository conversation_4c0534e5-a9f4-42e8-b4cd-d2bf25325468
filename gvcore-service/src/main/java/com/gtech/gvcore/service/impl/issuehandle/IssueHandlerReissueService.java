package com.gtech.gvcore.service.impl.issuehandle;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.gtech.basic.filecloud.api.model.Resp;
import com.gtech.basic.filecloud.commons.PagedData;
import com.gtech.basic.filecloud.commons.PagedDatas;
import com.gtech.basic.filecloud.exports.excel.spec.ExcelExportSpec;
import com.gtech.basic.filecloud.exports.management.FileExport;
import com.gtech.basic.filecloud.exports.management.FileExportManager;
import com.gtech.basic.filecloud.exports.management.FileExportResult;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherCirculationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnableDisableEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest;
import com.gtech.gvcore.common.response.voucherbatch.ExportDigitalVoucherResponse;
import com.gtech.gvcore.dao.dto.IssueHandlingDetailsDto;
import com.gtech.gvcore.dao.mapper.IssueHandlingDetailsMapper;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.helper.FileCompressionHelper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import com.gtech.gvcore.service.IssuerService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.impl.MessageComponent;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Service
@Slf4j
public class IssueHandlerReissueService extends IssueHandlerValidateService implements IssueHandlerBaseService {
	
	@Autowired
	private IssueHandlerReissueService reissueService;
	
	@Autowired
	private VoucherNumberHelper voucherNumberHelper;
	
	@Autowired
	private IssueHandlingDetailsMapper issueHandlingDetailsMapper;
	
	@Autowired
	private GvCodeHelper gvCodeHelper;
	
	@Autowired
	private FileExportManager fileExportManager;
	
	@Autowired
	private MessageComponent messageComponent;
	
	@Autowired
	private CpgService cpgService;

	@Autowired
	private IssuerService issuerService;

	@Value("#{${gv.issuer.businesswarehouse}}")
	private Map<String, String> issuerBusinessWarehouseMap;

	@Autowired
	private OssHelper ossHelper;

	@Autowired
	private OutletService outletService;



	@Override
	public IssueHandlingTypeEnum getIssueHandlingType() {
		return IssueHandlingTypeEnum.BULK_REISSUE;
	}

	@Override
	public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {
		checkIfExist(details, getIssueHandlingType(), issuerCode);
		checkInvoiceNumber(details,null);
		if (CollectionUtils.isEmpty(details)) {
			return details;
		}
		for (IssueHandlingDetails detail : details) {
			if (StringUtil.isEmpty(detail.getReceiverEmail())) {
				String result = "Email can't be empty!";
				if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
					detail.setResult(detail.getResult() + result);
				} else {
					detail.setResult(result);
				}
				detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			}
			//根据voucherCode查询voucher,如果voucher的mop为VCR时newVoucherCode不能为空
			Voucher voucher = voucherService.getVoucherByCode(detail.getVoucherCode());
			if (voucher != null && voucher.getMopCode().equals("VCR")) {
				if (StringUtil.isEmpty(detail.getNewVoucherCode())) {
					String result = "New voucher code can't be empty!";
					if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
						detail.setResult(detail.getResult() + result);
					} else {
						detail.setResult(result);
					}
					detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
				}
			}

		}
		return details;
	}

	@Override
	public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {
		validate(details, issuerCode);
		if (CollectionUtils.isEmpty(details)) {
			return Collections.emptyList();
		}
		CountDownLatch downlatch = new CountDownLatch(details.size());
		List<IssueHandlingDetails> returnList = new CopyOnWriteArrayList<>();
		for (IssueHandlingDetails issueHandlingDetails : details) {
			EXECUTOR.execute(() -> {
				try {
					returnList.add(issueHandlingDetails);
					reissueService.makeIssueHandling(issueHandlingDetails, issuerCode);
				} catch (Exception e) {
					String msg = e.getMessage();
					log.error(e.getMessage(), e);
					issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
					issueHandlingDetails.setResult(msg.length() > 500 ? msg.substring(0, 499) : msg);
				} finally {
					downlatch.countDown();
				}
			});
		}
		try {
			downlatch.await();
		} catch (InterruptedException e) {
			log.error(e.getMessage(), e);
			Thread.currentThread().interrupt();
		}
		return returnList;
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public IssueHandlingDetails makeIssueHandling(IssueHandlingDetails issueHandlingDetails, String issuerCode) {
		if (IssueHandlingProcessStatusEnum.FAILED.code().equals(issueHandlingDetails.getProcessStatus())) {
			return issueHandlingDetails;
		}
		String voucherCode = issueHandlingDetails.getVoucherCode();
		String newVoucherCode = issueHandlingDetails.getNewVoucherCode();


		Voucher voucher = voucherService.getVoucherByCode(voucherCode);
		if (voucher == null || !issuerCode.equals(voucher.getIssuerCode())) {
			issueHandlingDetails.setResult("Voucher number doesn't exist!");
			issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			return issueHandlingDetails;
		}
		if (GvcoreConstants.MOP_CODE_VCR.equals(voucher.getMopCode()) && StringUtil.isEmpty(newVoucherCode)) {
			issueHandlingDetails.setResult("New voucher number is empty");
			issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
			return issueHandlingDetails;
		}

		//查询原来的交易记录 用于继承
		List<TransactionData> oldTransactionData = transactionDataService.queryTransactionDataByVoucherCode(voucherCode);
		//oldTransactionData根据交易类型分组并且获取最新一条交易记录
		Map<String, TransactionData> transactionDataMap = oldTransactionData.stream().collect(Collectors.toMap(TransactionData::getTransactionType, Function.identity(), (k1, k2) -> k2));

		ArrayList<TransactionData> newTransactionData = new ArrayList<>();




		
		if (GvcoreConstants.MOP_CODE_VCR.equals(voucher.getMopCode())) {
			Voucher newVoucher = voucherService.getVoucherByCode(newVoucherCode);
			if (newVoucher == null || !issuerCode.equals(newVoucher.getIssuerCode())) {
				issueHandlingDetails.setResult("New voucher number doesn't exist!");
				issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
				return issueHandlingDetails;
			}
			if (!newVoucher.getCpgCode().equals(voucher.getCpgCode())) {
				issueHandlingDetails.setResult("Vpg mismatch!");
				issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
				return issueHandlingDetails;
			}
			boolean ownerFlag = checkNewVoucherOwner(newVoucher);
			if (Boolean.FALSE.equals(ownerFlag)) {
				issueHandlingDetails.setResult("New voucher owner error!");
				issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
				return issueHandlingDetails;
			}
			// change new voucher outlet
			newVoucher.setVoucherOwnerCode(voucher.getVoucherOwnerCode());
			newVoucher.setVoucherOwnerType(voucher.getVoucherOwnerType());
			newVoucher.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());

			newVoucher.setSalesOutlet(voucher.getSalesOutlet());
			newVoucher.setSalesTime(voucher.getSalesTime());
			newVoucher.setUsedTime(voucher.getUsedTime());
			newVoucher.setUsedOutlet(voucher.getUsedOutlet());
			Example example = new Example(Voucher.class);

			example.createCriteria().andEqualTo("voucherCode", newVoucherCode);
			voucherMapper.updateByConditionSelective(newVoucher,example);


			createTransaction(transactionDataMap, TransactionTypeEnum.GIFT_CARD_ACTIVATE, issueHandlingDetails, newTransactionData);

			createTransaction(transactionDataMap, TransactionTypeEnum.GIFT_CARD_SELL, issueHandlingDetails, newTransactionData);

			
		} else {
			String newCode = voucherNumberHelper.voucherCodeElectronic(voucherCode.substring(0, 3), voucher.getDenomination(), Calendar.getInstance().get(Calendar.YEAR) + "",voucher.getIssuerCode());
			Voucher newVoucher = BeanCopyUtils.jsonCopyBean(voucher, Voucher.class);
			newVoucher.setVoucherCode(newCode);
			newVoucher.setVoucherCodeNum(Long.valueOf(newCode.replaceAll("[a-zA-Z]", "")));
			newVoucher.setCreateTime(new Date());
			newVoucher.setUpdateTime(new Date());
			newVoucher.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode());
			newVoucher.setVoucherPin(voucherNumberHelper.pinCode());
			newVoucher.setVoucherBarcode(voucherNumberHelper.barCode27Bit(newCode));
			boolean automaticActivate = getAutomaticActivate(newVoucher.getCpgCode(), newVoucher.getIssuerCode());
			if (Boolean.TRUE.equals(automaticActivate)) {
				newVoucher.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());

				createTransaction(transactionDataMap, TransactionTypeEnum.GIFT_CARD_ACTIVATE, issueHandlingDetails, newTransactionData);
				
			} else {
				newVoucher.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
				String activationCode = voucherNumberHelper.activationCode();
				newVoucher.setVoucherActiveCode(activationCode);
				newVoucher.setVoucherActiveUrl(activeUrl + activationCode);
			}
			newVoucher.setId(null);
			voucherMapper.insert(newVoucher);
			



			

			issueHandlingDetails.setNewVoucherCode(newCode);




			createTransaction(transactionDataMap, TransactionTypeEnum.GIFT_CARD_NEW_GENERATE, issueHandlingDetails, newTransactionData);
			createTransaction(transactionDataMap, TransactionTypeEnum.GIFT_CARD_SELL, issueHandlingDetails, newTransactionData);
			
			
		}
		// insert transaction data
		transactionDataService.insertList(newTransactionData);


		// insert cpg code
		issueHandlingDetails.setCpgCode(voucher.getCpgCode());
		issueHandlingDetailsMapper.updateByPrimaryKeySelective(issueHandlingDetails);

		issueHandlingDetails.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());

		// change old voucher status to block
		UpdateVoucherStatusRequest updateVoucherStatusRequest = new UpdateVoucherStatusRequest();
		updateVoucherStatusRequest.setVoucherCode(voucherCode);
		updateVoucherStatusRequest.setVoucherStatus(VoucherStatusEnableDisableEnum.STATUS_DESTROY.getCode());
		voucherService.updateVoucherStatus(updateVoucherStatusRequest);

		 return issueHandlingDetails;
	}



	private  void createTransaction(Map<String, TransactionData> transactionDataMap, TransactionTypeEnum giftCardNewGenerate, IssueHandlingDetails issueHandlingDetails, ArrayList<TransactionData> newTransactionData) {
		TransactionData transactionDataCreated = transactionDataMap.get(giftCardNewGenerate.getCode());
		if (null == transactionDataCreated){
			return;
		}
		transactionDataCreated.setVoucherCode(issueHandlingDetails.getNewVoucherCode());
		transactionDataCreated.setInvoiceNumber(issueHandlingDetails.getInvoiceNo());
		transactionDataCreated.setNotes(issueHandlingDetails.getRemarks());
		transactionDataCreated.setEmail(issueHandlingDetails.getReceiverEmail());
		transactionDataCreated.setTransactionDate(new Date());
		transactionDataCreated.setCreateTime(new Date());
		transactionDataCreated.setUpdateTime(new Date());
		newTransactionData.add(transactionDataCreated);
	}

	private boolean getAutomaticActivate(String cpgCode, String issuerCode) {
		List<String> list = cpgService.queryAutomaticActivateCpg(Arrays.asList(cpgCode), issuerCode);
		return !CollectionUtils.isEmpty(list);
	}

	/**
	 * MVStore & HO01
	 * 
	 * @param newVoucher
	 * @return
	 */
	private boolean checkNewVoucherOwner(Voucher newVoucher) {
		String ownerCode = newVoucher.getVoucherOwnerCode();
		if (VoucherOwnerTypeEnum.OUTLET.code().equalsIgnoreCase(newVoucher.getVoucherOwnerType())) {
			Outlet outlet = outletService.queryByOutletCode(ownerCode);
			if (outlet != null && "MVStore".equalsIgnoreCase(outlet.getOutletType())) {
				return true;
			}
		}
		if (VoucherOwnerTypeEnum.WAREHOUSE.code().equalsIgnoreCase(newVoucher.getVoucherOwnerType())) {
			String issuerCode = newVoucher.getIssuerCode();
			if (issuerBusinessWarehouseMap.get(issuerCode).equals(ownerCode)) {
				return true;
			}
		}
		return false;
	}

	@Override
	public void afterExecute(IssueHandling issueHandling) {
		if (issueHandling == null) {
			return;
		}
		IssueHandlingDetailsDto dto = new IssueHandlingDetailsDto();
		dto.setIssueHandlingCode(issueHandling.getIssueHandlingCode());
		List<IssueHandlingDetails> details = issueHandlingDetailsMapper.queryDigitalByIssueHandlingCode(dto);
		if (CollectionUtils.isEmpty(details)) {
			return;
		}
		Map<String, List<IssueHandlingDetails>> map = details.stream().collect(Collectors.groupingBy(IssueHandlingDetails::getReceiverEmail));
		map.forEach((email, v) -> {
			try {
				sendToPic(issueHandling, details, email, v);
			} catch (Exception e) {
				log.error(e.getMessage(), e);
			}
		});
	}

	private void sendToPic(IssueHandling issueHandling, List<IssueHandlingDetails> details, String email, List<IssueHandlingDetails> v) {
		String fileName = gvCodeHelper.generateFileNameCode();
		PageData<ExportDigitalVoucherResponse> pageData = new PageData<>();
		List<String> voucherCodeList = v.stream().map(IssueHandlingDetails::getNewVoucherCode).collect(Collectors.toList());
		List<Voucher> voucherList = voucherService.queryByVoucherCodeList(null, voucherCodeList);
		if (CollectionUtils.isEmpty(voucherList)) {
			return;
		}

		boolean isActivation = false;
		List<String> activationCodes = voucherList.stream().filter(vo -> StringUtil.isNotEmpty(vo.getVoucherActiveCode()))
				.map(Voucher::getVoucherActiveCode).collect(Collectors.toList());
		List<String> activationUrls = voucherList.stream().filter(vo -> StringUtil.isNotEmpty(vo.getVoucherActiveUrl()))
				.map(Voucher::getVoucherActiveUrl).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(activationCodes) && CollectionUtils.isNotEmpty(activationUrls)) {
			isActivation = true;
		}
		List<ExportDigitalVoucherResponse> list = new ArrayList<>();
		for (Voucher voucher : voucherList) {
			ExportDigitalVoucherResponse response = BeanCopyUtils.jsonCopyBean(voucher, ExportDigitalVoucherResponse.class);
			response.setActivationCode(voucher.getVoucherActiveCode());
			response.setActivationUrl(voucher.getVoucherActiveUrl());
			list.add(response);
		}
		pageData.setList(list);
		// 电子券excel
		String secretCode = voucherNumberHelper.randomPassword(10);
		PagedData<ExportDigitalVoucherResponse> voucherData = PagedDatas
		        .<ExportDigitalVoucherResponse, PageData<ExportDigitalVoucherResponse>>builder()
		        .query(() -> pageData)
		        .queryResultConverter(PageData::getList)
		        .hasNextPage(pageResult -> false)
		        .afterQuery(() -> log.info("do nothing"))
		        .build();

		ExcelExportSpec.SheetBuilder<ExportDigitalVoucherResponse> sheet = ExcelExportSpec.builder()
		        .sheet(ExportDigitalVoucherResponse.class, "Digital Voucher")
		        .dataSource(voucherData);

		if (isActivation) {
            sheet.addColumn("ActivationCode","ActivationCode", ExportDigitalVoucherResponse::getActivationCode);
            sheet.addColumn("ActivationUrl","ActivationUrl", ExportDigitalVoucherResponse::getActivationUrl);
        }

		FileExport fileExport = FileExport.builder()
		        .domainCode(GvcoreConstants.SYSTEM_DEFAULT)
		        .tenantCode(GvcoreConstants.SYSTEM_DEFAULT)
		        .userId(details.get(0).getUpdateUser())
				.name(fileName + ".xlsx")
		        .category("voucher.digital")
		        .spec(sheet.build())
		        .build();

		String accessUrl = "";
		Resp<FileExportResult> result = FileExportResult.from(fileExportManager.export(fileExport));
		accessUrl = result.getData().getAccessUrl();
		final FileCompressionHelper.EncryptCompressionParameter parameter = FileCompressionHelper.EncryptCompressionParameter.builder()
        		.fileUrl(accessUrl)
				.fileName(fileName + ".xlsx")
				.password(secretCode)
        		.build();
		accessUrl = ossHelper.compressionUploadToOss(parameter, fileName);

		List<String> cpgCodeList = new ArrayList<>();
		List<String> issuerCodeList = new ArrayList<>();
		BigDecimal voucherAmount = BigDecimal.ZERO;
		for (Voucher voucher : voucherList) {
			cpgCodeList.add(voucher.getCpgCode());
			issuerCodeList.add(voucher.getIssuerCode());
			voucherAmount = voucherAmount.add(voucher.getDenomination());
		}
		List<Issuer> issuerList = issuerService.queryIssuerByCodeList(issuerCodeList);
		Map<String, Cpg> cpgMap = cpgService.queryCpgMapByCpgCodeList(cpgCodeList);

		Map<String, Object> extendParams = new HashMap<>();
		extendParams.put("fileName", fileName + ".zip");
		extendParams.put("businessCode", issueHandling.getIssueHandlingCode());
		extendParams.put(VoucherBatch.C_CREATE_TIME, DateUtil.format(issueHandling.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
		extendParams.put(VoucherBatch.C_UPDATE_TIME, DateUtil.format(issueHandling.getUpdateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
		extendParams.put("voucherAmount", voucherAmount);
		extendParams.put("issuerList", issuerList.stream().map(Issuer::getIssuerName).collect(Collectors.toList()));
		extendParams.put("cpgList", cpgMap.values().stream().collect(Collectors.toList()));
		extendParams.put("email", email);
		//发送EXCEL邮件 TEST
		sendExcelToEmail(fileName, ossHelper.grantAccessUrl(accessUrl), extendParams);

		sendPwdToEmail(secretCode, extendParams);

	}

	private void sendPwdToEmail(String pwd, Map<String, Object> extendParams) {
		JSONObject messageRequest = new JSONObject();
		messageRequest.put("eventCode", MessageEnventEnum.SEND_REISSUE_E_PWD.getCode());
		JSONObject param = new JSONObject();
		param.putAll(extendParams);
		param.put("password", pwd);
		messageRequest.put("param", param);
		messageComponent.send(messageRequest);
	}

	private void sendExcelToEmail(String fileName, String fileUrl, Map<String, Object> extendParams) {
        JSONObject messageRequest = new JSONObject();
        messageRequest.put("eventCode", MessageEnventEnum.SEND_REISSUE_E_VOUCHER.getCode());
        JSONObject param = new JSONObject();
		param.putAll(extendParams);
        messageRequest.put("param", param);
        JSONArray attachments = new JSONArray();
        JSONObject files = new JSONObject();
		files.put("filename", fileName + ".zip");
        files.put("url", fileUrl);
        attachments.add(files);
        param.put("attachments", attachments);
        messageComponent.send(messageRequest);
    }

}
