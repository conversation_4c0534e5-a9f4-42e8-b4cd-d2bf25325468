package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName GcRegenerateActivationCodeBean
 * @Description Gift Card Regenerate Activation Code Bean
 * <AUTHOR> based on RegenerateActivationCodeBean
 * @Date 2025/6/19
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcRegenerateActivationCodeBean {

    @ExcelProperty(value = "Request Number")
    private String issueHandlingCode;

    @ExcelProperty(value = "Gift Card List File name")
    private String giftCardListFileName;

    @ExcelProperty(value = "Request Date")
    private Date requestDate;

    @ExcelProperty(value = "Total Cards")
    private Integer totalCards;

    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String totalAmount;

    @ExcelProperty(value = "Requestor")
    private String requestor;

    @ExcelProperty(value = "Status")
    private String status;
}
