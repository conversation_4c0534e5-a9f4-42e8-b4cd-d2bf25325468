package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.extend.ReportBeanCustomerAutoFull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 17:14
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class CardLifeCycleBean implements ReportBeanCustomerAutoFull {

    @ExcelProperty(index = 0, value = "Merchant")
    private String merchantName;

    @ExcelProperty(index = 1, value = "Voucher Number")
    private String voucherNumber;

    @ExcelProperty(index = 2, value = "Booklet Number")
    private String bookletNumber;

    @ExcelProperty(index = 3, value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(index = 4, value = "Time Zone")
    private String timeZone;

    @ExcelProperty(index = 5, value = "Creation Date")
    private String creationDate;

    @ExcelProperty(index = 6, value = "Selling Date")
    private String sellingDate;

    @ExcelProperty(index = 7, value = "Voucher Status")
    private String voucherStatus;

    @ExcelProperty(index = 8, value = "Base Currency of the Voucher")
    private String baseCurrencyOfTheCard;

    @ReportAmountValue
    @ExcelProperty(index = 9, value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

    @ExcelProperty(index = 10, value = "Expiry Date")
    private String expiryDate;

    @ExcelProperty(index = 11, value = "Expiry Time Zone")
    private String expiryTimeZone;

    @ExcelProperty(index = 12, value = "Voucher Holder Name")
    private String customerFullName;

    @ExcelProperty(index = 13, value = "Voucher Holder Mobile")
    private String customerMobile;

    @ExcelProperty(index = 14, value = "Company")
    private String customerName;
}
