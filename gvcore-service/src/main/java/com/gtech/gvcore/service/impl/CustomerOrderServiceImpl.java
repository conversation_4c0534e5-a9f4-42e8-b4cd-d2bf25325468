package com.gtech.gvcore.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.gtech.basic.idm.common.enums.ResourceTypeEnum;
import com.gtech.basic.idm.dao.entity.ResourceEntity;
import com.gtech.basic.idm.dao.entity.RoleResourceMappingEntity;
import com.gtech.basic.idm.dao.mapper.IResourceMapper;
import com.gtech.basic.idm.dao.mapper.IRoleResourceMappingMapper;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ApproveNodeRecordTypeEnum;
import com.gtech.gvcore.common.enums.ApproveTypeEnum;
import com.gtech.gvcore.common.enums.CustomerOrderStatusEnum;
import com.gtech.gvcore.common.enums.FlowEnum;
import com.gtech.gvcore.common.enums.FlowNodeEnum;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ProductCategoryDiscountTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherAllocationBusinessTypeEnum;
import com.gtech.gvcore.common.enums.VoucherAllocationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherCirculationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherReceiveSourceTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.customer.GetCustomerRequest;
import com.gtech.gvcore.common.request.customerorder.AddSendEmailRequest;
import com.gtech.gvcore.common.request.customerorder.ApproveCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.CancelReceiveRequest;
import com.gtech.gvcore.common.request.customerorder.CancelReleaseRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.DelPaymentVoucherRequest;
import com.gtech.gvcore.common.request.customerorder.DeliverCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.GetCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.IssuanceRequest;
import com.gtech.gvcore.common.request.customerorder.NonSystemCreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.QueryCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.ReceiveRequest;
import com.gtech.gvcore.common.request.customerorder.ReleaseRequest;
import com.gtech.gvcore.common.request.customerorder.SendCustomerOrderEmailRequest;
import com.gtech.gvcore.common.request.customerorder.SubmitCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.UpdateCustomerInfoInCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.UpdateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.request.customerorder.UpdateCustomerOrderRequest;
import com.gtech.gvcore.common.request.customerorder.UploadPaymentVoucherRequest;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.CreateLogRecode;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.senddigitalvoucherexceltoemail.SendDigitalVoucherExcelToEmailRequest;
import com.gtech.gvcore.common.response.allocation.VoucherAllocationBatchResponse;
import com.gtech.gvcore.common.response.customer.CustomerResponse;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderEmailResponse;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderResponse;
import com.gtech.gvcore.common.response.customerorder.QueryCustomerOrderResponse;
import com.gtech.gvcore.common.response.meansofpayment.QueryMeansOfPaymentsByPageResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.releaseapprove.ApproveNodeRecordResponse;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.common.utils.ParamCheckUtils;
import com.gtech.gvcore.components.QueryVoucherComponent;
import com.gtech.gvcore.components.TaskProgressManager;
import com.gtech.gvcore.dao.dto.CustomerOrderDto;
import com.gtech.gvcore.dao.mapper.ApproveNodeRecordMapper;
import com.gtech.gvcore.dao.mapper.BusinessLogDetailMapper;
import com.gtech.gvcore.dao.mapper.BusinessLogMapper;
import com.gtech.gvcore.dao.mapper.CancelVoucherReceiveMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderDetailsMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderEmailMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.CustomerOrderReceiverMapper;
import com.gtech.gvcore.dao.mapper.OutletMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.mapper.VoucherReceiveBatchMapper;
import com.gtech.gvcore.dao.mapper.VoucherReceiveMapper;
import com.gtech.gvcore.dao.mapper.VoucherReceiveRecordMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.BusinessLog;
import com.gtech.gvcore.dao.model.BusinessLogDetails;
import com.gtech.gvcore.dao.model.CancelVoucherReceive;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import com.gtech.gvcore.dao.model.CustomerOrderEmail;
import com.gtech.gvcore.dao.model.CustomerOrderReceiver;
import com.gtech.gvcore.dao.model.GvOperateLogEntity;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.dao.model.VoucherAllocationBatch;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.dao.model.VoucherReceive;
import com.gtech.gvcore.dao.model.VoucherReceiveBatch;
import com.gtech.gvcore.dao.model.VoucherReceiveRecord;
import com.gtech.gvcore.dto.GetCpgQuantityNumParam;
import com.gtech.gvcore.dto.QueryCustomerOrderPermissionRequest;
import com.gtech.gvcore.dto.TaskProgress;
import com.gtech.gvcore.dto.salespostingxml.SumCustomerOrderGroupByArticle;
import com.gtech.gvcore.helper.CustomerOrderPdfHelper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.PermissionHelper;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.CustomerService;
import com.gtech.gvcore.service.FlowNoticeService;
import com.gtech.gvcore.service.GvOperateLogService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.MeansOfPaymentService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.ReleaseApproveService;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.VoucherAllocationBatchService;
import com.gtech.gvcore.service.VoucherAllocationService;
import com.gtech.gvcore.service.VoucherBatchService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.threadpool.ThreadPoolCenter;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.Sqls;
import tk.mybatis.mapper.util.StringUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/18 14:46
 */

@Slf4j
@Service
public class CustomerOrderServiceImpl implements CustomerOrderService {
    @Autowired
    private CustomerOrderMapper customerOrderMapper;
    @Autowired
    private GvCodeHelper codeHelper;
    @Autowired
    private CustomerOrderDetailsMapper customerOrderDetailsMapper;
    @Autowired
    private CustomerOrderReceiverMapper customerOrderReceiverMapper;
    @Autowired
    private ReleaseApproveService releaseApproveService;
    @Autowired
    private ApproveNodeRecordMapper approveNodeRecordMapper;
    @Autowired
    private VoucherAllocationService voucherAllocationService;
    @Autowired
    private VoucherBatchService voucherBatchService;

    @Autowired
    private VoucherReceiveRecordMapper voucherReceiveRecordMapper;
    @Autowired
    private OutletMapper outletMapper;
    @Lazy
    @Autowired
    private VoucherService voucherService;
    @Autowired
    private VoucherMapper voucherMapper;
    @Lazy
    @Autowired
    private FlowNoticeService flowNoticeService;
    @Autowired
    private GvUserAccountService userAccountService;
    @Autowired
    private CustomerOrderPdfHelper customerOrderPdfHelper;
    @Autowired
    private MessageComponent messageComponent;
    @Autowired
    private VoucherAllocationBatchService voucherAllocationBatchService;
    @Autowired
    private GvOperateLogService operateLogService;

    @Value("#{'${gv.quotation.email.list:<EMAIL>,<EMAIL>}'.split(',')}")
    private List<String> quotationEmails;


    @Autowired
    private MeansOfPaymentService mopService;

    @Autowired
    private CpgService cpgService;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private TransactionDataService transactionDataService;

    @Autowired
    private OutletService outletService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private PermissionHelper permissionHelper;

    @Autowired
    private CustomerOrderEmailMapper customerOrderEmailMapper;


    @Autowired
    private IResourceMapper resourceMapper;

    @Autowired
    private IRoleResourceMappingMapper roleResourceMapper;

    @Autowired
    private VoucherReceiveMapper voucherReceiveMapper;
    @Autowired
    private VoucherReceiveBatchMapper voucherReceiveBatchMapper;

    @Autowired
    private CancelVoucherReceiveMapper cancelVoucherReceiveMapper;

    @Value("#{${gv.issuer.warehouse:}}")
    private Map<String, String> issuerWarehouseMap;

    @Autowired
    private BusinessLogMapper businessLogMapper;

    @Autowired
    private BusinessLogDetailMapper businessLogDetailMapper;

    @Autowired
    @Qualifier("verifyVoucher")
    private Executor executor;


    @Autowired
    private QueryVoucherComponent queryVoucherComponent;


    @Autowired
    private TransactionExecute transactionExecute;


    @Autowired
    private TaskProgressManager taskProgressManager;

    private static final String NO_INPUT = "NO_INPUT";

    private static final ThreadPoolExecutor EXECUTOR =
            new ThreadPoolExecutor(20, 50, 100, TimeUnit.SECONDS, new LinkedBlockingDeque<>(1000),
                    new ThreadPoolExecutor.CallerRunsPolicy()) {
                @Override
                protected void afterExecute(Runnable r, Throwable t) {
                    super.afterExecute(r, t);

                    if (t != null) {
                        // 在主线程中打印异常
                        log.error("Exception occurred in custom thread pool:");
                        t.printStackTrace();
                    }
                }
            };

    private static final ThreadPoolExecutor CANCEL_EXECUTOR =
            new ThreadPoolExecutor(20, 50, 100, TimeUnit.SECONDS, new LinkedBlockingDeque<>(1000),
                    new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    @Transactional
    public Result<String> createCustomerOrder(CreateCustomerOrderRequest createCustomerOrderRequest) {

        if (!checkAmount(createCustomerOrderRequest)) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_AMOUNT_DIFFERENT.code(), ResultErrorCodeEnum.VOUCHER_AMOUNT_DIFFERENT.desc());
        }

        if (createCustomerOrderRequest.getAmount() != null && createCustomerOrderRequest.getAmount().compareTo(createCustomerOrderRequest.getVoucherAmount()) > 0) {
            createCustomerOrderRequest.setAmount(createCustomerOrderRequest.getVoucherAmount());
        }
        //检查是否有相同面额的数据
        //Check to see if there are values of the same denomination
        if (!checkDetailRepeatForCreate(createCustomerOrderRequest.getCreateCustomerOrderDetailsRequests())) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_DETAIL_DENOMINATION_REPEAT.code(), ResultErrorCodeEnum.VOUCHER_DETAIL_DENOMINATION_REPEAT.desc());
        }

        CustomerOrder customerOrder = BeanCopyUtils.jsonCopyBean(createCustomerOrderRequest, CustomerOrder.class);
        if (StringUtil.isEmpty(customerOrder.getCustomerOrderCode())) {
            customerOrder.setCustomerOrderCode(codeHelper.generateCustomerOrderCode());
        }
        customerOrder.setInvoiceNo(createCustomerOrderRequest.getInvoiceNo());
        customerOrder.setCurrencyCode("IDR");
        customerOrder.setCreateTime(new Date());
        if (StringUtils.isNotBlank(createCustomerOrderRequest.getStatus())) {
            customerOrder.setStatus(createCustomerOrderRequest.getStatus());
        } else {
            customerOrder.setStatus(CustomerOrderStatusEnum.CREATED.getStatus());
        }

        if (null != createCustomerOrderRequest.getReleaseTime()) {
            customerOrder.setReleaseTime(createCustomerOrderRequest.getReleaseTime());
        }

        customerOrderMapper.insertSelective(customerOrder);

        List<CustomerOrderDetails> insertCustomerOrderDetails = this.insertCustomerOrderDetails(createCustomerOrderRequest, customerOrder);

        CustomerOrderReceiver customerOrderReceiver = BeanCopyUtils.jsonCopyBean(createCustomerOrderRequest, CustomerOrderReceiver.class);

        if (!createCustomerOrderRequest.getOutletCode().equals(NO_INPUT)
            &&  !CustomerOrderStatusEnum.API.getStatus().equals(createCustomerOrderRequest.getStatus())
        ) {
            executor.execute(()->this.processingHtml(customerOrder, insertCustomerOrderDetails, customerOrderReceiver));
        }

        customerOrderReceiver.setCustomerOrderCode(customerOrder.getCustomerOrderCode());
        customerOrderReceiverMapper.insertSelective(customerOrderReceiver);
        return Result.ok(customerOrder.getCustomerOrderCode());
    }

    public List<CustomerOrderDetails> insertCustomerOrderDetails(CreateCustomerOrderRequest createCustomerOrderRequest, CustomerOrder customerOrder) {
        List<CustomerOrderDetails> insertCustomerOrderDetails = new ArrayList<>(createCustomerOrderRequest.getCreateCustomerOrderDetailsRequests().size());
        for (CreateCustomerOrderDetailsRequest createCustomerOrderDetailsRequest : createCustomerOrderRequest.getCreateCustomerOrderDetailsRequests()) {
            CustomerOrderDetails customerOrderDetails = BeanCopyUtils.jsonCopyBean(createCustomerOrderDetailsRequest, CustomerOrderDetails.class);
            customerOrderDetails.setCustomerOrderCode(customerOrder.getCustomerOrderCode());
            customerOrderDetails.setCustomerOrderDetailsCode(codeHelper.generateCustomerOrderDetailCode());
            customerOrderDetails.setCreateUser(createCustomerOrderRequest.getCreateUser());
            if (null == customerOrderDetails.getDeleteStatus()) {
                customerOrderDetails.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
            }
            insertCustomerOrderDetails.add(customerOrderDetails);
        }
        calculateDetails(customerOrder, insertCustomerOrderDetails);

        customerOrderDetailsMapper.insertList(insertCustomerOrderDetails);

        return insertCustomerOrderDetails;
    }

    private void calculateDetails(CustomerOrder customerOrder, List<CustomerOrderDetails> customerOrderDetails) {
        if (customerOrder == null || CollectionUtils.isEmpty(customerOrderDetails)) {
            return;
        }
        BigDecimal totalAmount = customerOrder.getAmount();
        if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            customerOrderDetails.forEach(vo -> {
                vo.setVoucherAmount(vo.getDenomination().multiply(BigDecimal.valueOf(vo.getVoucherNum())));
                vo.setDiscount(BigDecimal.ZERO);
                vo.setAmount(vo.getVoucherAmount());
            });
            return;
        }
        BigDecimal totalVoucherAmount = customerOrder.getVoucherAmount();
        BigDecimal discountDiv = BigDecimal.ZERO;
        for (int i = 0; i < customerOrderDetails.size(); i++) {
            CustomerOrderDetails detail = customerOrderDetails.get(i);
            Integer voucherNum = detail.getVoucherNum();
            BigDecimal denomination = detail.getDenomination();
            BigDecimal voucherAmount = denomination.multiply(BigDecimal.valueOf(voucherNum));
            BigDecimal discount;
            if (i == customerOrderDetails.size() - 1) {
                discount = totalAmount.subtract(discountDiv);
            } else {
                discount = totalAmount.multiply(voucherAmount).divide(totalVoucherAmount, 0, RoundingMode.HALF_DOWN);
                discountDiv = discountDiv.add(discount);
            }
            detail.setVoucherAmount(voucherAmount);
            detail.setDiscount(discount);
            BigDecimal amount = voucherAmount.subtract(discount);
            detail.setAmount(amount.compareTo(BigDecimal.ZERO) > -1 ? amount : BigDecimal.ZERO);
        }
    }


    private boolean checkDetailRepeatForCreate(List<CreateCustomerOrderDetailsRequest> createCustomerOrderDetailsRequests) {
        Set<BigDecimal> collect = createCustomerOrderDetailsRequests.stream()
                .map(CreateCustomerOrderDetailsRequest::getDenomination)
                .collect(Collectors.toSet());
        return collect.size() == createCustomerOrderDetailsRequests.size();
    }

    private boolean checkAmount(CreateCustomerOrderRequest createCustomerOrderRequest) {
        BigDecimal amount = BigDecimal.ZERO;
        int num = 0;
        for (CreateCustomerOrderDetailsRequest createCustomerOrderDetailsRequest : createCustomerOrderRequest.getCreateCustomerOrderDetailsRequests()) {
            num += createCustomerOrderDetailsRequest.getVoucherNum();
            amount = amount.add(createCustomerOrderDetailsRequest.getDenomination().multiply(new BigDecimal(createCustomerOrderDetailsRequest.getVoucherNum())));
        }
        return num == createCustomerOrderRequest.getVoucherNum() && amount.compareTo(createCustomerOrderRequest.getVoucherAmount()) == 0;

    }

    @Override
    public PageResult<QueryCustomerOrderResponse> queryCustomerOrder(QueryCustomerOrderRequest queryCustomerOrderRequest) {

        final QueryCustomerOrderPermissionRequest queryCondition = BeanCopyUtils.jsonCopyBean(queryCustomerOrderRequest, QueryCustomerOrderPermissionRequest.class);

        if (null == queryCustomerOrderRequest.getNoSystem() || !Boolean.TRUE.equals(queryCustomerOrderRequest.getNoSystem())) {
            final PermissionCodeResponse permission = this.permissionHelper.getPermission(queryCustomerOrderRequest.getUserCode(), queryCustomerOrderRequest.getIssuerCode());
            if (!PermissionCodeResponse.hasOutlet(permission)) {
                return new PageResult<>(Collections.emptyList(), 0L);
            }
            permission.getOutletCodeList().add(NO_INPUT);
            queryCondition.setOutletCodeRangeList(permission.getOutletCodeList());

        }


        PageHelper.startPage(queryCustomerOrderRequest.getPageNum(), queryCustomerOrderRequest.getPageSize());
        List<QueryCustomerOrderResponse> queryCustomerOrderResponses = customerOrderMapper.queryOrder(queryCondition);
        PageInfo<QueryCustomerOrderResponse> of = PageInfo.of(queryCustomerOrderResponses);

        List<QueryCustomerOrderResponse> queryCustomerOrderResponseList = new ArrayList<>(queryCustomerOrderRequest.getPageSize());
        List<String> collect = of.getList().stream().map(QueryCustomerOrderResponse::getCreateUser).collect(Collectors.toList());
        Map<String, String> stringStringMap = userAccountService.queryFullNameByCodeList(collect);
        for (QueryCustomerOrderResponse queryCustomerOrderResponse : of.getList()) {
            Example example = new Example(CustomerOrderDetails.class);
            example.createCriteria()
                    .andEqualTo(CustomerOrderDetails.C_CUSTOMER_ORDER_CODE, queryCustomerOrderResponse.getCustomerOrderCode())
                    .andEqualTo(CustomerOrderDetails.C_DELETE_STATUS, 0);
            List<CustomerOrderDetails> customerOrderDetails = customerOrderDetailsMapper.selectByCondition(example);
            StringBuilder stringBuilder = new StringBuilder();
            for (CustomerOrderDetails customerOrderDetail : customerOrderDetails) {
                stringBuilder.append(customerOrderDetail.getDenomination().setScale(0, RoundingMode.DOWN)).append("|");
            }
            if (stringBuilder.length() > 0) {
                stringBuilder.deleteCharAt(stringBuilder.lastIndexOf("|"));
            }
            VoucherAllocation voucherAllocationByCode = voucherAllocationService.getAllocationBySourceDataCode(queryCustomerOrderResponse.getCustomerOrderCode(), VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
            if (voucherAllocationByCode != null) {
                queryCustomerOrderResponse.setVoucherAllocationCode(voucherAllocationByCode.getVoucherAllocationCode());
            }
            queryCustomerOrderResponse.setDenomination(stringBuilder.toString());
            queryCustomerOrderResponse.setStatus(queryCustomerOrderResponse.getStatus());
            queryCustomerOrderResponse.setCancelAble(checkCancelAble(BeanCopyUtils.jsonCopyBean(queryCustomerOrderResponse, CustomerOrder.class)));
            if (stringStringMap.get(queryCustomerOrderResponse.getCreateUser()) != null) {
                queryCustomerOrderResponse.setCreateUser(stringStringMap.get(queryCustomerOrderResponse.getCreateUser()));
            }
            queryCustomerOrderResponse.setReleaseAble(false);
            if (queryCustomerOrderResponse.getStatus().equals(CustomerOrderStatusEnum.ISSUANCE.getStatus())) {
                ReleaseApproveAbleRequest build = ReleaseApproveAbleRequest
                        .builder()
                        .releaseType(ApproveNodeRecordTypeEnum.RELEASE.getType())
                        .approveRoleCode(queryCustomerOrderRequest.getRoleList())
                        .approveUser(queryCustomerOrderRequest.getUserCode())
                        .voucherAmount(queryCustomerOrderResponse.getVoucherAmount())
                        .releaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_01.getType())
                        .businessCode(queryCustomerOrderResponse.getCustomerOrderCode())
                        .issuerCode(queryCustomerOrderRequest.getIssuerCode())
                        .build();
                Result<Integer> booleanResult = releaseApproveService.approveAble(build);
                queryCustomerOrderResponse.setReleaseAble(booleanResult.isSuccess());
                if (booleanResult.getData() > 0) {
                    ApproveNodeRecordRequest approveNodeRecordRequest = new ApproveNodeRecordRequest();
                    approveNodeRecordRequest.setBusinessCode(queryCustomerOrderResponse.getCustomerOrderCode());
                    approveNodeRecordRequest.setReleaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_01.getType());
                    approveNodeRecordRequest.setVoucherAmount(queryCustomerOrderResponse.getVoucherAmount());
                    approveNodeRecordRequest.setReleaseType(ApproveNodeRecordTypeEnum.RELEASE.getType());
                    approveNodeRecordRequest.setIssuerCode(queryCustomerOrderRequest.getIssuerCode());
                    ThreadPoolCenter.commonThreadPoolExecute(
                            () -> releaseApproveService.automaticApproveAndNoticeNextNode(approveNodeRecordRequest,
                                    this::approveCustomerOrderRelease));

                }
            }

            //判断Completed 状态是否可以cancel D+1 00:00:00前可以cancel MER-1957
            if (queryCustomerOrderResponse.getStatus().equals(CustomerOrderStatusEnum.COMPLETED.getStatus())) {
                queryCustomerOrderResponse.setCancelAble(isCancel(queryCustomerOrderResponse.getUpdateTime()));
            }


            queryCustomerOrderResponseList.add(queryCustomerOrderResponse);
        }
        return new PageResult<>(queryCustomerOrderResponseList, of.getTotal());
    }

    private static Boolean isCancel(Date updateTime) {
        Date date = DateUtils.addDays(updateTime, 1);
        Date date1 = DateUtils.truncate(date, Calendar.DATE);
        Date date2 = DateUtils.addSeconds(date1, -1);
        Boolean before = new Date().before(date2);
        return before;
    }

    @SuppressWarnings("unchecked")
    public Map<String, Object> getExtendsParams(String customerOrderCode) {
        GetCustomerOrderRequest getCustomerOrderRequest = new GetCustomerOrderRequest();
        getCustomerOrderRequest.setCustomerOrderCode(customerOrderCode);
        Result<GetCustomerOrderResponse> customerOrderResult = getCustomerOrder(getCustomerOrderRequest);
        GetCustomerOrderResponse customerOrder = customerOrderResult.getData();
        if (StringUtil.isEmpty(customerOrder.getDiscountType())) {
            customerOrder.setDiscountType(null);
        }
        String meansOfPaymentsCode = customerOrder.getMeansOfPaymentCode();
        Map<String, Object> params = new HashMap<>();
        if (!StringUtil.isEmpty(meansOfPaymentsCode)) {
            QueryMeansOfPaymentsByPageResponse meansOfPaymentsByPageResponse = mopService.getMeansOfPayments(meansOfPaymentsCode);
            params.putAll(JSON.parseObject(JSON.toJSONString(meansOfPaymentsByPageResponse), Map.class));
        }
        params.putAll(JSON.parseObject(JSON.toJSONString(customerOrder), Map.class));
        params.put(VoucherBatch.C_CREATE_TIME, DateUtil.format(customerOrder.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
        params.put(VoucherBatch.C_UPDATE_TIME, DateUtil.format(customerOrder.getUpdateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
        GvOperateLogEntity submitLog = getOperateLog(customerOrderCode, GvcoreConstants.OPERATE_METHOD_ISSUANCE);
        if (submitLog != null) {
            params.put("submitBy", submitLog.getFirstName() + " " + submitLog.getLastName());
        }
        GvOperateLogEntity issuanceLog = getOperateLog(customerOrderCode, GvcoreConstants.OPERATE_METHOD_ISSUANCE);
        if (issuanceLog != null) {
            params.put("issuanceTime", DateUtil.format(issuanceLog.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
        }
        String discountType = customerOrder.getDiscountType();
        if (StringUtil.isNotEmpty(discountType) && ProductCategoryDiscountTypeEnum.PERCENTAGE.code().equals(discountType)) {
            params.put("discount", customerOrder.getDiscount().setScale(1) + "%");
        }
        return params;
    }

    private GvOperateLogEntity getOperateLog(String customerOrderCode, String method) {
        List<GvOperateLogEntity> list = operateLogService.queryLogByBusiness(customerOrderCode);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        for (GvOperateLogEntity entity : list) {
            if (method.equals(entity.getMethod())) {
                return entity;
            }
        }
        return null;
    }

    /**
     * @param customerOrderCode
     * @param method
     * @param flag              标识是否取一条
     * @return
     */
    private List<String> getOperateEmail(String customerOrderCode, String method, boolean flag) {
        List<String> emails = new ArrayList<>();
        List<GvOperateLogEntity> list = operateLogService.queryLogByBusiness(customerOrderCode);
        if (CollectionUtils.isEmpty(list)) {
            return emails;
        }
        for (GvOperateLogEntity entity : list) {
            if (method.equals(entity.getMethod())) {
                String email = userAccountService.getUserEmail(entity.getOperateUser());
                emails.add(email);
                if (Boolean.TRUE.equals(flag)) {
                    break;
                }
            }
        }
        return emails;
    }

    @Override
    public Result<GetCustomerOrderResponse> getCustomerOrder(GetCustomerOrderRequest getCustomerOrderRequest) {
        CustomerOrder customerOrder = customerOrderMapper.selectOne(new CustomerOrder(getCustomerOrderRequest.getCustomerOrderCode()));
        GetCustomerOrderResponse getCustomerOrderResponse = BeanCopyUtils.jsonCopyBean(customerOrder, GetCustomerOrderResponse.class);
        getCustomerOrderResponse.setCancelAble(checkCancelAble(customerOrder));
        getCustomerOrderResponse.setReleaseAble(false);
        if (CustomerOrderStatusEnum.ISSUANCE.getStatus().equals(customerOrder.getStatus()) && !StringUtil.isEmpty(getCustomerOrderRequest.getRoleList())) {
            ReleaseApproveAbleRequest build = ReleaseApproveAbleRequest
                    .builder()
                    .releaseType(ApproveNodeRecordTypeEnum.RELEASE.getType())
                    .approveRoleCode(getCustomerOrderRequest.getRoleList())
                    .approveUser(getCustomerOrderRequest.getUserCode())
                    .voucherAmount(customerOrder.getVoucherAmount())
                    .releaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_01.getType())
                    .businessCode(customerOrder.getCustomerOrderCode())
                    .issuerCode(customerOrder.getIssuerCode())
                    .build();
            Result<Integer> booleanResult = releaseApproveService.approveAble(build);
            getCustomerOrderResponse.setReleaseAble(booleanResult.isSuccess());
        }
        List<GetCustomerOrderDetailsResponse> getCustomerOrderDetailsResponses = customerOrderDetailsMapper
                .selectCustomerDetails(getCustomerOrderResponse.getCustomerOrderCode(), GvcoreConstants.DELETE_STATUS_DISABLE);
        getCustomerOrderResponse.setGetCustomerOrderDetailsResponses(getCustomerOrderDetailsResponses);
        VoucherAllocation voucherAllocationByCode = voucherAllocationService.getAllocationBySourceDataCode(customerOrder.getCustomerOrderCode(), VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
        if (voucherAllocationByCode != null) {
            getCustomerOrderResponse.setVoucherAllocationCode(voucherAllocationByCode.getVoucherAllocationCode());
        }
        CustomerOrderReceiver customerOrderReceiver = customerOrderReceiverMapper.selectOne(new CustomerOrderReceiver(getCustomerOrderRequest.getCustomerOrderCode()));
        Result<List<ApproveNodeRecordResponse>> listResult = releaseApproveService.queryLogByBusinessCode(customerOrder.getCustomerOrderCode(), null);
        if (listResult == null) {
            return Result.ok(getCustomerOrderResponse);
        }
        getCustomerOrderResponse.setNotes(listResult.getData());
        BeanCopyUtils.copyProps(customerOrderReceiver, getCustomerOrderResponse);
        getCustomerOrderResponse.setQuotationUrl(customerOrderReceiver.getQuotation());
        getCustomerOrderResponse.setInvoiceUrl(customerOrderReceiver.getInvoice());
        getCustomerOrderResponse.setSalesOrderUrl(customerOrderReceiver.getSalesOrder());

        if (StringUtil.isNotEmpty(customerOrderReceiver.getPaymentVoucher())) {
            getCustomerOrderResponse.setPaymentVoucherUrl(customerOrderReceiver.getPaymentVoucher());
            Map<String, String> stringStringMap = userAccountService.queryFullNameByCodeList(Collections.singletonList(customerOrderReceiver.getUpdateUser()));
            if (stringStringMap.get(customerOrderReceiver.getUpdateUser()) != null) {
                getCustomerOrderResponse.setPaymentVoucherUploader(stringStringMap.get(customerOrderReceiver.getUpdateUser()));
            } else {
                getCustomerOrderResponse.setPaymentVoucherUploader(customerOrderReceiver.getUpdateUser());
            }
        }

        //cancelAble
        if (customerOrder.getStatus().equals(CustomerOrderStatusEnum.COMPLETED.getStatus())) {
            getCustomerOrderResponse.setCancelAble(isCancel(customerOrder.getUpdateTime()));
        }
        getAlloctionBatchList(getCustomerOrderResponse);

        this.getSendEmailList(getCustomerOrderResponse);

        return Result.ok(getCustomerOrderResponse);
    }

    /**
     * 该方法获得 客户订单所关联的邮件发送记录
     *
     * @param customerOrder
     */
    private void getSendEmailList(GetCustomerOrderResponse customerOrder) {

        //null
        if (null == customerOrder) return;

        //find
        List<CustomerOrderEmail> result = customerOrderEmailMapper.select(new CustomerOrderEmail()
                .setCustomerOrderCode(ConvertUtils.toString(customerOrder.getCustomerOrderCode(), "")));

        //is empty
        if (CollectionUtils.isEmpty(result)) return;

        //setting result
        customerOrder.setSendEmailList(BeanCopyUtils.jsonCopyList(result, GetCustomerOrderEmailResponse.class));
    }

    private void getAlloctionBatchList(GetCustomerOrderResponse getCustomerOrderResponse) {
        String allocationCode = getCustomerOrderResponse.getVoucherAllocationCode();
        if (StringUtil.isEmpty(allocationCode)) {
            return;
        }
        List<VoucherAllocationBatch> alloctionBatchList = voucherAllocationBatchService.queryByVoucherAllocationCode(allocationCode);
        if (CollectionUtils.isEmpty(alloctionBatchList)) {
            return;
        }
        List<String> createUsers = alloctionBatchList.stream().map(VoucherAllocationBatch::getCreateUser).distinct().collect(Collectors.toList());
        List<String> cpgCodeList = alloctionBatchList.stream().map(VoucherAllocationBatch::getCpgCode).collect(Collectors.toList());
        Map<String, Cpg> cpgMap = cpgService.queryCpgMapByCpgCodeList(cpgCodeList);
        Map<String, String> userMap = userAccountService.queryFullNameByCodeList(createUsers);
        List<VoucherAllocationBatchResponse> alloctionBatchRespList = BeanCopyUtils.jsonCopyList(alloctionBatchList, VoucherAllocationBatchResponse.class);
        for (VoucherAllocationBatchResponse voucherAllocationBatchResponse : alloctionBatchRespList) {
            String createUser = voucherAllocationBatchResponse.getCreateUser();
            createUser = StringUtil.isEmpty(userMap.get(createUser)) ? createUser : userMap.get(createUser);
            voucherAllocationBatchResponse.setCreateUser(createUser);
            String cpgCode = voucherAllocationBatchResponse.getCpgCode();
            Cpg cpg = cpgMap.get(cpgCode);
            if (cpg != null) {
                voucherAllocationBatchResponse.setCpgName(cpg.getCpgName());
            }
        }
        getCustomerOrderResponse.setAlloctionBatchList(alloctionBatchRespList);

    }

    @Override
    @Transactional
    public Result<Void> updateCustomerOrder(UpdateCustomerOrderRequest updateCustomerOrderRequest) {

        //检查详情的条数和金额总和是否和请求传入的相同
        //Check that the number and sum of details are the same as those passed in by the request
        if (!checkAmount(updateCustomerOrderRequest)) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_AMOUNT_DIFFERENT.code(), ResultErrorCodeEnum.VOUCHER_AMOUNT_DIFFERENT.desc());
        }

        if (updateCustomerOrderRequest.getAmount() != null && updateCustomerOrderRequest.getAmount().compareTo(updateCustomerOrderRequest.getVoucherAmount()) > 0) {
            updateCustomerOrderRequest.setAmount(updateCustomerOrderRequest.getVoucherAmount());
        }

        //检查是否有相同面额的数据
        //Check to see if there are values of the same denomination
        if (!checkDetailRepeatForUpdate(updateCustomerOrderRequest.getUpdateCustomerOrderDetailsRequests())) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_DETAIL_DENOMINATION_REPEAT.code(), ResultErrorCodeEnum.VOUCHER_DETAIL_DENOMINATION_REPEAT.desc());
        }

        List<UpdateCustomerOrderDetailsRequest> updateCustomerOrderDetailsRequests = updateCustomerOrderRequest.getUpdateCustomerOrderDetailsRequests();
        if (CollectionUtils.isEmpty(updateCustomerOrderDetailsRequests)) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_DETAIL_BLANK.code(), ResultErrorCodeEnum.VOUCHER_DETAIL_BLANK.desc());
        }

        CustomerOrder customerOrder = customerOrderMapper.selectOne(new CustomerOrder(updateCustomerOrderRequest.getCustomerOrderCode()));
        if (!customerOrder.getPurchaseOrderNo().equals(updateCustomerOrderRequest.getPurchaseOrderNo())
                && Boolean.TRUE.equals(customerOrderMapper.exists(updateCustomerOrderRequest.getPurchaseOrderNo()))) {
            return Result.failed(ResultErrorCodeEnum.PURCHASE_ORDER_NUMBER_EXISTS.code(), ResultErrorCodeEnum.PURCHASE_ORDER_NUMBER_EXISTS.desc());
        }
        //BeanCopyUtils.copyProps(updateCustomerOrderRequest, customerOrder);
        CustomerOrder customerOrder1 = BeanCopyUtils.jsonCopyBean(updateCustomerOrderRequest, CustomerOrder.class);
        customerOrder1.setId(customerOrder.getId());
        customerOrder1.setUpdateTime(new Date());
        customerOrder1.setCurrencyCode(customerOrder.getCurrencyCode());
        customerOrder1.setStatus(CustomerOrderStatusEnum.CREATED.getStatus());
        customerOrder1.setCreateUser(customerOrder.getCreateUser());
        customerOrderMapper.updateByPrimaryKey(customerOrder1);

        List<CustomerOrderDetails> customerOrderDetails = customerOrderDetailsMapper.select(CustomerOrderDetails.builder().customerOrderCode(customerOrder.getCustomerOrderCode()).deleteStatus(0).build());
        if (!checkAndUpdateData(customerOrderDetails, updateCustomerOrderDetailsRequests)) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DETAILS_DATA_FOUND.code(), ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DETAILS_DATA_FOUND.desc());
        }
        List<CustomerOrderDetails> detailsList = BeanCopyUtils.jsonCopyList(updateCustomerOrderDetailsRequests, CustomerOrderDetails.class);
        // calculate detail
        calculateDetails(customerOrder1, detailsList);
        for (CustomerOrderDetails detail : detailsList) {
            if (detail.getCustomerOrderDetailsCode() != null) {
                CustomerOrderDetails customerOrderDetails1 = customerOrderDetailsMapper
                        .selectOne(CustomerOrderDetails.builder().customerOrderDetailsCode(detail.getCustomerOrderDetailsCode()).build());
                BeanCopyUtils.copyProps(detail, customerOrderDetails1);
                customerOrderDetails1.setUpdateUser(updateCustomerOrderRequest.getUpdateUser());
                customerOrderDetails1.setUpdateTime(new Date());
                customerOrderDetailsMapper.updateByPrimaryKeySelective(customerOrderDetails1);
            } else {
                String customerOrderDetailCode = codeHelper.generateCustomerOrderDetailCode();
                detail.setCustomerOrderDetailsCode(customerOrderDetailCode);
                detail.setCustomerOrderCode(updateCustomerOrderRequest.getCustomerOrderCode());
                detail.setCreateUser(updateCustomerOrderRequest.getUpdateUser());
                detail.setUpdateUser(updateCustomerOrderRequest.getUpdateUser());
                detail.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
                customerOrderDetailsMapper.insertSelective(detail);
            }
        }

        List<CustomerOrderDetails> customerOrderDetails1 = customerOrderDetailsMapper.select(CustomerOrderDetails.builder().customerOrderCode(customerOrder.getCustomerOrderCode()).deleteStatus(0).build());

        CustomerOrderReceiver customerOrderReceiver1 = BeanCopyUtils.jsonCopyBean(updateCustomerOrderRequest, CustomerOrderReceiver.class);
        CustomerOrderReceiver customerOrderReceiver = customerOrderReceiverMapper.selectOne(new CustomerOrderReceiver(updateCustomerOrderRequest.getCustomerOrderCode()));
        BeanCopyUtils.copyProps(customerOrderReceiver1, customerOrderReceiver);

        executor.execute(()->this.processingHtml(customerOrder1, customerOrderDetails1, customerOrderReceiver));

        customerOrderReceiverMapper.updateByPrimaryKey(customerOrderReceiver);

        return Result.ok();
    }

    private void processingHtml(final CustomerOrder customerOrder, final List<CustomerOrderDetails> customerOrderDetails, final CustomerOrderReceiver customerOrderReceiver) {

        CustomerOrderReceiver updateEntity = new CustomerOrderReceiver();

        CompletableFuture<Void> quotationFuture = CompletableFuture.runAsync(() -> {
            updateEntity.setQuotation(customerOrderPdfHelper.quotation(customerOrder, customerOrderDetails, customerOrderReceiver));
        });

        CompletableFuture<Void> salesOrderFuture = CompletableFuture.runAsync(() -> {
            updateEntity.setSalesOrder(customerOrderPdfHelper.salesOrder(customerOrder, customerOrderDetails));
        });

        CompletableFuture<Void> invoiceFuture = CompletableFuture.runAsync(() -> {
            updateEntity.setInvoice(customerOrderPdfHelper.proformaInvoice(customerOrder, customerOrderDetails, customerOrderReceiver));
        });

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(quotationFuture, salesOrderFuture, invoiceFuture);

        allFutures.thenRun(() -> {
            // 执行数据库更新操作
            Example example = new Example(CustomerOrderReceiver.class);
            example.createCriteria().andEqualTo("customerOrderCode", customerOrderReceiver.getCustomerOrderCode());
            customerOrderReceiverMapper.updateByConditionSelective(updateEntity, example);
            System.out.println("Database updated with new values.");
        });

    }


    private boolean checkAndUpdateData(List<CustomerOrderDetails> customerOrderDetails, List<UpdateCustomerOrderDetailsRequest> updateCustomerOrderDetailsRequests) {

        List<UpdateCustomerOrderDetailsRequest> collect = updateCustomerOrderDetailsRequests.stream()
                .filter(updateCustomerOrderDetailsRequest -> updateCustomerOrderDetailsRequest.getCustomerOrderDetailsCode() != null).collect(Collectors.toList());

        Iterator<CustomerOrderDetails> iterator = customerOrderDetails.iterator();
        while (iterator.hasNext()) {
            CustomerOrderDetails next = iterator.next();
            Iterator<UpdateCustomerOrderDetailsRequest> iterator1 = collect.iterator();
            while (iterator1.hasNext()) {
                UpdateCustomerOrderDetailsRequest next1 = iterator1.next();
                if (next.getCustomerOrderDetailsCode().equals(next1.getCustomerOrderDetailsCode())) {
                    iterator.remove();
                    iterator1.remove();
                }
            }
        }
        if (!CollectionUtils.isEmpty(collect)) {
            return false;
        }

        if (!CollectionUtils.isEmpty(customerOrderDetails)) {
            for (CustomerOrderDetails customerOrderDetails1 : customerOrderDetails) {
                customerOrderDetails1.setDeleteStatus(1);
                customerOrderDetailsMapper.updateByPrimaryKey(customerOrderDetails1);
            }
        }
        return true;
    }

    private boolean checkDetailRepeatForUpdate(List<UpdateCustomerOrderDetailsRequest> updateCustomerOrderDetailsRequests) {
        Set<BigDecimal> collect = updateCustomerOrderDetailsRequests.stream()
                .map(UpdateCustomerOrderDetailsRequest::getDenomination)
                .collect(Collectors.toSet());
        return collect.size() == updateCustomerOrderDetailsRequests.size();
    }

    private boolean checkAmount(UpdateCustomerOrderRequest updateCustomerOrderRequest) {
        BigDecimal amount = BigDecimal.ZERO;
        int num = 0;
        for (UpdateCustomerOrderDetailsRequest updateCustomerOrderDetailsRequest : updateCustomerOrderRequest.getUpdateCustomerOrderDetailsRequests()) {
            num += updateCustomerOrderDetailsRequest.getVoucherNum();
            amount = amount.add(updateCustomerOrderDetailsRequest.getDenomination().multiply(new BigDecimal(updateCustomerOrderDetailsRequest.getVoucherNum())));
        }
        return num == updateCustomerOrderRequest.getVoucherNum() && amount.compareTo(updateCustomerOrderRequest.getVoucherAmount()) == 0;

    }

    @Override
    public Result<String> generatePurchaseOrderNumber(String storeCode) {

        return Result.ok(codeHelper.generateOrderNumber(storeCode));
    }

    @Override
    public Result<String> updateRedisOrderNumber(String storeCode, String poNumber) {
        Boolean aBoolean = codeHelper.updateRedisOrderNumber(storeCode, poNumber);
        if (Boolean.TRUE.equals(aBoolean)) {
            return Result.ok();
        } else {
            return Result.failed("Update failed");
        }
    }

    @Override
    @Transactional
    public Result<Void> submitCustomerOrder(SubmitCustomerOrderRequest submitCustomerOrderRequest) {
        CustomerOrder customerOrder = customerOrderMapper.selectOne(new CustomerOrder(submitCustomerOrderRequest.getCustomerOrderCode()));

        customerOrder.setInvoiceNo(gvCodeHelper.generateInvoiceNumber());

        customerOrder.setStatus(CustomerOrderStatusEnum.SUBMIT.getStatus());
        customerOrder.setUpdateUser(submitCustomerOrderRequest.getUserCode());
        customerOrder.setUpdateTime(new Date());
        customerOrderMapper.updateByPrimaryKeySelective(customerOrder);
        sendEmail(customerOrder.getCustomerOrderCode(), FlowNodeEnum.SUBMIT.getCode(), null);
        return Result.ok();
    }

    private void sendEmail(String customerOrderCode, String flowNodeCode, List<String> emails) {
        Map<String, Object> params = getExtendsParams(customerOrderCode);
        SendNoticeRequest sendNoticeRequest = new SendNoticeRequest();
        sendNoticeRequest.setBusinessCode(customerOrderCode);
        sendNoticeRequest.setFlowCode(FlowEnum.CUSTOMER_ORDER_FLOW.getCode());
        sendNoticeRequest.setFlowNodeCode(flowNodeCode);
        sendNoticeRequest.setExtendParams(params);
        if (!CollectionUtils.isEmpty(emails)) {
            sendNoticeRequest.setEmails(emails);
        }
        flowNoticeService.send(sendNoticeRequest);
    }

    @Override
    @Transactional
    public Result<String> approveCustomerOrder(ApproveCustomerOrderRequest approveCustomerOrderRequest) {
        CustomerOrder customerOrder = customerOrderMapper.selectOne(new CustomerOrder(approveCustomerOrderRequest.getCustomerOrderCode().trim()));
        if (customerOrder == null || !customerOrder.getStatus().equals(CustomerOrderStatusEnum.SUBMIT.getStatus())) {
            return Result.failed(ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_APPROVED.code(), ResultErrorCodeEnum.CURRENT_STATUS_CANNOT_APPROVED.desc());
        }
        releaseApproveService.createLogRecord(CreateLogRecode.builder()
                .approveRoleCode(com.gtech.commons.utils.StringUtil.EMPTY)
                .status(true)
                .approveType(ApproveNodeRecordTypeEnum.APPROVE.getType())
                .approveUser(approveCustomerOrderRequest.getUserCode())
                .note(approveCustomerOrderRequest.getNote())
                .businessCode(approveCustomerOrderRequest.getCustomerOrderCode())
                .build());
        if (Boolean.TRUE.equals(approveCustomerOrderRequest.getStatus())) {
            customerOrder.setStatus(CustomerOrderStatusEnum.APPROVAL.getStatus());
            if (GvcoreConstants.MOP_CODE_VCR.equals(customerOrder.getMopCode())) {
                int orUpdate = voucherAllocationService.createOrUpdate(customerOrder);
                if (orUpdate == 0) {
                    log.error("approvalCustomerOrder 创建Voucher allocation 失败");
                    return Result.failed("approvalCustomerOrder 创建Voucher allocation 失败");
                } else {
                    log.error("approvalCustomerOrder 创建Voucher allocation 成功");
                }
            }
        } else {
            customerOrder.setStatus(CustomerOrderStatusEnum.REJECTED.getStatus());
        }
        customerOrder.setUpdateTime(new Date());
        customerOrder.setUpdateUser(approveCustomerOrderRequest.getUserCode());
        customerOrderMapper.updateByPrimaryKey(customerOrder);
        if (customerOrder.getStatus().equals(CustomerOrderStatusEnum.APPROVAL.getStatus())) {
            sendEmail(customerOrder.getCustomerOrderCode(), FlowNodeEnum.APPROVE.getCode(), null);
        } else if (customerOrder.getStatus().equals(CustomerOrderStatusEnum.REJECTED.getStatus())) {
            List<String> emails = getOperateEmail(customerOrder.getCustomerOrderCode(), GvcoreConstants.OPERATE_METHOD_SUBMIT, true);
            sendEmail(customerOrder.getCustomerOrderCode(), FlowNodeEnum.REJECTED.getCode(), emails);
        }
        return Result.ok();
    }


    @Override
    public Result<String> issuance(IssuanceRequest request) {

        CustomerOrder customerOrder = customerOrderMapper.selectOne(new CustomerOrder(request.getCustomerOrderCode()));
        Result<String> result = checkIssuanceRequest(request, customerOrder);
        if (!result.isSuccess()) {
            return result;
        }

        CustomerOrderDetails orderDetail = new CustomerOrderDetails(customerOrder.getCustomerOrderCode());
        orderDetail.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        List<CustomerOrderDetails> details = customerOrderDetailsMapper.select(orderDetail);
        if (CollectionUtils.isEmpty(details)) {
            return Result.failed(ResultErrorCodeEnum.NO_ORDER_DETAILS_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_ORDER_DETAILS_DATA_FOUND.desc());
        }

        int i = loadingOrder(request.getCustomerOrderCode(), CustomerOrderStatusEnum.APPROVAL);

        if (i == 0) {
            log.error("{}修改订单状态失败---------------------------------------issuance",request.getCustomerOrderCode());
            return Result.failed(ResultErrorCodeEnum.ORDER_HAS_UPDATE_FOUND.code(),
                    ResultErrorCodeEnum.ORDER_HAS_UPDATE_FOUND.desc());
        }
        executor.execute(() -> {
            try {
                transactionExecute.issuanceExecute(request, customerOrder, details);
            } catch (Exception e) {
                //订单回滚为APPROVAL
                updateStatus(customerOrder.getCustomerOrderCode(),
                        CustomerOrderStatusEnum.APPROVAL.getStatus(),
                        CustomerOrderStatusEnum.LOADING.getStatus(),
                        request.getUpdateUser());
            }
        });


        return Result.ok(customerOrder.getCustomerOrderCode());
    }


    //@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)


    @Override
    public Result<String> issuanceAndNoticeNextNode(IssuanceRequest request) {


        Result<String> result = this.issuance(request);
        log.info("----发行结束，准备往下个节点-----");
        if (result.isSuccess()) {
            ThreadPoolCenter.commonThreadPoolExecute(() -> {

                CustomerOrder customerOrder = queryByCustomerOrderCode(result.getData());
                if (customerOrder != null) {
                    ApproveNodeRecordRequest approveNodeRecordRequest = new ApproveNodeRecordRequest();
                    approveNodeRecordRequest.setIssuerCode(customerOrder.getIssuerCode());
                    approveNodeRecordRequest.setBusinessCode(customerOrder.getCustomerOrderCode());
                    approveNodeRecordRequest.setReleaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_01.getType());
                    approveNodeRecordRequest.setVoucherAmount(customerOrder.getVoucherAmount());
                    approveNodeRecordRequest.setReleaseType(ApproveNodeRecordTypeEnum.RELEASE.getType());
                    approveNodeRecordRequest.setExtendParams(getExtendsParams(customerOrder.getCustomerOrderCode()));
                    releaseApproveService.automaticApproveAndNoticeNextNode(approveNodeRecordRequest, this::approveCustomerOrderRelease);
                }
            });

        }


        return result;
    }

    private Result<String> checkIssuanceRequest(IssuanceRequest request, CustomerOrder customerOrder) {

        if (customerOrder == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        /*
            添加进行中状态
            if (!CustomerOrderStatusEnum.APPROVAL.getStatus().equals(customerOrder.getStatus())) {
                return Result.failed(ResultErrorCodeEnum.CUSTOMERORDER_NOT_APPROVAL.code(), ResultErrorCodeEnum.CUSTOMERORDER_NOT_APPROVAL.desc());
            }
           */

        if (GvcoreConstants.MOP_CODE_VCR.equals(customerOrder.getMopCode())) {
            if (CollectionUtils.isEmpty(request.getVoucherBatchList())) {
                return Result.failed("voucherBatchList can not be empty");
            }
            Result<String> result = ParamCheckUtils.listValidate(request.getVoucherBatchList());
            if (!result.isSuccess()) {
                return result;
            }
        }
        return Result.ok();
    }

    @Override
    public int updateStatus(String customerOrderCode, String status, String oldStatus, String updateUser) {

        CustomerOrderDto dto = new CustomerOrderDto();
        dto.setCustomerOrderCode(customerOrderCode);
        dto.setStatus(status);
        dto.setOldStatus(oldStatus);
        dto.setUpdateTime(new Date());
        dto.setUpdateUser(updateUser);
        return customerOrderMapper.updateStatus(dto);
    }

    @Override
    public List<CustomerOrderDetails> queryCustomerOrderDetails(String customerOrderCode) {
       return customerOrderDetailsMapper.select(new CustomerOrderDetails(customerOrderCode));
    }

    @Override
    public List<GetCustomerOrderDetailsResponse> selectCustomerDetails(String customerOrderCode, Integer deleteStatus) {
        return customerOrderDetailsMapper.selectCustomerDetails(customerOrderCode,deleteStatus);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> approveCustomerOrderRelease(ApproveNodeRecordRequest approveNodeRecordRequest) {

        CustomerOrder customerOrder = customerOrderMapper
                .selectOne(new CustomerOrder(approveNodeRecordRequest.getBusinessCode()));
        if (customerOrder == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        if (approveNodeRecordRequest.getExtendParams() == null) {
            approveNodeRecordRequest.setExtendParams(getExtendsParams(customerOrder.getCustomerOrderCode()));
        }

        if (!CustomerOrderStatusEnum.ISSUANCE.getStatus().equals(customerOrder.getStatus())) {
            return Result.failed(ResultErrorCodeEnum.CUSTOMERORDER_NOT_ISSUANCE.code(),
                    ResultErrorCodeEnum.CUSTOMERORDER_NOT_ISSUANCE.desc());
        }

        approveNodeRecordRequest.setReleaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_01.getType());
        approveNodeRecordRequest.setVoucherAmount(customerOrder.getVoucherAmount());
        approveNodeRecordRequest.setReleaseType(ApproveNodeRecordTypeEnum.RELEASE.getType());
        approveNodeRecordRequest.setIssuerCode(customerOrder.getIssuerCode());
        Result<ApproveNodeRecord> approve = releaseApproveService.approve(approveNodeRecordRequest);
        ApproveNodeRecord approveNodeRecord = approve.getData();
        if (approveNodeRecord == null) {
            return Result.failed(approve.getCode(), approve.getMessage());
        }
        if (approveNodeRecord.getNextRoleCode() == null) {
            ReleaseRequest request = new ReleaseRequest();
            request.setCustomerOrderCode(approveNodeRecordRequest.getBusinessCode());
            request.setStatus(approveNodeRecordRequest.getStatus());
            request.setNotes(approveNodeRecordRequest.getNote());
            request.setUpdateUser(approveNodeRecordRequest.getApproveUser());
            Result<String> result = release(request);
            if (!result.isSuccess()) {
                throw new GTechBaseException(result.getCode(), "release failed, {0}", result.getMessage());
            }
        }
        return Result.ok(approveNodeRecord.getApproveNodeRecordCode());
    }

    //@Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> release(ReleaseRequest request) {

        CustomerOrder customerOrder = customerOrderMapper.selectOne(new CustomerOrder(request.getCustomerOrderCode()));
        if (customerOrder == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        if (!CustomerOrderStatusEnum.ISSUANCE.getStatus().equals(customerOrder.getStatus())) {
            return Result.failed(ResultErrorCodeEnum.CUSTOMERORDER_NOT_ISSUANCE.code(), ResultErrorCodeEnum.CUSTOMERORDER_NOT_ISSUANCE.desc());
        }


        //检查issuance进度条
        List<TaskProgress> allSubtaskProgress = taskProgressManager.getAllSubtaskProgress(request.getCustomerOrderCode());
        if (CollectionUtils.isNotEmpty(allSubtaskProgress)){
            log.info("ISSUANCE 未完成{}",JSON.toJSONString(allSubtaskProgress));
            return Result.failed(ResultErrorCodeEnum.ISSUANCE_NOT_COMPLETED.code(), ResultErrorCodeEnum.ISSUANCE_NOT_COMPLETED.desc());
        }


        //进行中
        //修改customerOrder的状态
        int updateLoading = loadingOrder(customerOrder.getCustomerOrderCode(), CustomerOrderStatusEnum.ISSUANCE);
        if (updateLoading == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }


        EXECUTOR.execute(() -> {
            try {
                transactionExecute.releaseExecute(request, customerOrder);
            } catch (Exception e) {
                this.rollbackOrder(customerOrder.getCustomerOrderCode(), CustomerOrderStatusEnum.ISSUANCE);
                //删除approval记录
                ApproveNodeRecord approveNodeRecord = new ApproveNodeRecord();
                approveNodeRecord.setBusinessCode(customerOrder.getCustomerOrderCode());
                approveNodeRecord.setReleaseApproveNodeName(1);
                approveNodeRecord.setReleaseApproveAmountType(ApproveNodeRecordTypeEnum.RELEASE.getType());
                approveNodeRecordMapper.deleteByCondition(approveNodeRecord);
                throw e;
            }
        });
        return Result.ok(customerOrder.getCustomerOrderCode());
    }


    public int loadingOrder(String customerOrderCode, CustomerOrderStatusEnum statusEnum) {
        Example example = new Example(CustomerOrder.class);
        example.createCriteria()
                .andEqualTo(CustomerOrder.C_CUSTOMER_ORDER_CODE, customerOrderCode)
                .andEqualTo(CustomerOrder.C_STATUS, statusEnum.getStatus());
        CustomerOrder order = new CustomerOrder();
        order.setStatus(CustomerOrderStatusEnum.LOADING.getStatus());
        return customerOrderMapper.updateByConditionSelective(order, example);
    }

    @Override
    public int rollbackOrder(String customerOrderCode, CustomerOrderStatusEnum statusEnum) {
        Example example = new Example(CustomerOrder.class);
        example.createCriteria()
                .andEqualTo(CustomerOrder.C_CUSTOMER_ORDER_CODE, customerOrderCode);
        CustomerOrder order = new CustomerOrder();
        order.setStatus(statusEnum.getStatus());
        return customerOrderMapper.updateByConditionSelective(order, example);
    }

    @Override
    public int updateCustomerBatchCode(String customerOrderCode, String batchCode) {
        CustomerOrder updateCustomerOrder = new CustomerOrder();
        updateCustomerOrder.setVoucherBatchCode(batchCode);
        Example updateCustomerExample = new Example(CustomerOrder.class);
        updateCustomerExample.createCriteria().andEqualTo("customerOrderCode",customerOrderCode);
        return customerOrderMapper.updateByConditionSelective(updateCustomerOrder,updateCustomerExample);

    }


    /**
     * @param customerOrder
     * @param request
     * @param isRelease
     * <AUTHOR>
     * @date 2022年4月22日
     */
    private void sendDigitalVoucherExcelToEmail(CustomerOrder customerOrder, ReleaseRequest request,
                                                boolean isRelease) {

        if (GvcoreConstants.MOP_CODE_VCE.equals(customerOrder.getMopCode()) && isRelease) {
            SendDigitalVoucherExcelToEmailRequest emailRequest = new SendDigitalVoucherExcelToEmailRequest();
            emailRequest.setVoucherBatchCode(customerOrder.getVoucherBatchCode());
            emailRequest.setInvoiceNumber(customerOrder.getInvoiceNo());
            emailRequest.setEmail(customerOrder.getContactEmail());
            emailRequest.setCustomerOrderCode(customerOrder.getCustomerOrderCode());
            emailRequest.setCreateUser(request.getUpdateUser());
            try {
                voucherBatchService.sendDigitalVoucherExcelToEmail(emailRequest);
            } catch (Exception e) {
                log.warn("sendDigitalVoucherExcelToEmailException voucherBatchCode="
                        + customerOrder.getVoucherBatchCode() + ", Exception=" + e.getMessage(), e);
            }
        }
    }

    //@Transactional
    @Override
    public Result<String> receive(ReceiveRequest request) {

        CustomerOrder customerOrder = customerOrderMapper.selectOne(new CustomerOrder(request.getCustomerOrderCode()));
        if (customerOrder == null) {
            return Result.failed("A partial voucher does not exist.");
        }
        if (!CustomerOrderStatusEnum.DELIVER.getStatus().equals(customerOrder.getStatus())) {
            return Result.failed(ResultErrorCodeEnum.CUSTOMERORDER_NOT_DELIVER.code(), ResultErrorCodeEnum.CUSTOMERORDER_NOT_DELIVER.desc());
        }
		int i = loadingOrder(request.getCustomerOrderCode(), CustomerOrderStatusEnum.DELIVER);
		if (i == 0) {
			log.error("修改订单状态失败---------------------------------------receive");
			return Result.ok(customerOrder.getCustomerOrderCode());
		}

        if (GvcoreConstants.MOP_CODE_VCR.equals(customerOrder.getMopCode())) {
            EXECUTOR.execute(() -> {
                transactionExecute.receiveExecute(request, customerOrder);
            });
        }

        return Result.ok(customerOrder.getCustomerOrderCode());
    }


    private void sendEmail(CustomerOrder customerOrder) {
        if (customerOrder == null) {
            return;
        }
        List<String> emails = getOperateEmail(customerOrder.getCustomerOrderCode(), GvcoreConstants.OPERATE_METHOD_SUBMIT, false);
        SendNoticeRequest sendNoticeRequest = new SendNoticeRequest();
        sendNoticeRequest.setFlowCode(FlowEnum.CUSTOMER_ORDER_FLOW.getCode());
        sendNoticeRequest.setFlowNodeCode(FlowNodeEnum.DELIVER.getCode());
        sendNoticeRequest.setEmails(emails);
        sendNoticeRequest.setBusinessCode(customerOrder.getCustomerOrderCode());
        sendNoticeRequest.setExtendParams(getExtendsParams(customerOrder.getCustomerOrderCode()));
        flowNoticeService.send(sendNoticeRequest);
    }

    @Override
    public Result<Boolean> approveCustomerOrderAble(ReleaseApproveAbleRequest releaseApproveAbleRequest) {
        CustomerOrder customerOrder = customerOrderMapper.selectOne(new CustomerOrder(releaseApproveAbleRequest.getBusinessCode().trim()));
        releaseApproveAbleRequest.setReleaseApproveAmountType(ApproveTypeEnum.APPROVE_TYPE_01.getType());
        releaseApproveAbleRequest.setVoucherAmount(customerOrder.getVoucherAmount());
        releaseApproveAbleRequest.setIssuerCode(customerOrder.getIssuerCode());
        releaseApproveAbleRequest.setReleaseType(ApproveNodeRecordTypeEnum.APPROVE.getType());
        Result<Integer> result = releaseApproveService.approveAble(releaseApproveAbleRequest);
        return Result.ok(result.isSuccess());
    }

    @Override
    @Transactional
    public Result<String> nonSystemCreateCustomerOrder(NonSystemCreateCustomerOrderRequest nonSystemCreateCustomerOrderRequest) {
        CreateCustomerOrderRequest createCustomerOrderRequest = BeanCopyUtils.jsonCopyBean(nonSystemCreateCustomerOrderRequest, CreateCustomerOrderRequest.class);
        String stringResult = gvCodeHelper.generateCustomerOrderCode();
        createCustomerOrderRequest.setPurchaseOrderNo(stringResult);
        if (StringUtils.isBlank(nonSystemCreateCustomerOrderRequest.getCustomerCode())) {
            createCustomerOrderRequest.setCustomerCode(NO_INPUT);
        }
        createCustomerOrderRequest.setOutletCode(NO_INPUT);
        createCustomerOrderRequest.setDiscount(BigDecimal.ZERO);
        createCustomerOrderRequest.setAmount(BigDecimal.ZERO);
        createCustomerOrderRequest.setCreateUser("customer");
        return createCustomerOrder(createCustomerOrderRequest);
    }

    @Override
    public void deliverCustomerOrder(DeliverCustomerOrderRequest deliverCustomerOrderRequest) {
        if (deliverCustomerOrderRequest == null) {
            return;
        }
        checkDeliverCustomer(deliverCustomerOrderRequest);
        CustomerOrder customerOrder = customerOrderMapper.selectOne(new CustomerOrder(deliverCustomerOrderRequest.getCustomerOrderCode()));
        if (customerOrder == null) {
            throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        if (!CustomerOrderStatusEnum.RELEASE.getStatus().equals(customerOrder.getStatus())) {
            throw new GTechBaseException(ResultErrorCodeEnum.CUSTOMERORDER_NOT_RELEASE.code(), ResultErrorCodeEnum.CUSTOMERORDER_NOT_RELEASE.desc());
        }
        customerOrder.setTrackNo(deliverCustomerOrderRequest.getTrackNo());
        customerOrder.setLogisticsName(deliverCustomerOrderRequest.getLogisticsName());
        customerOrder.setAwb(deliverCustomerOrderRequest.getAwb());
        customerOrder.setScanOfReceipt(deliverCustomerOrderRequest.getScanOfReceipt());
        customerOrder.setDeliveType(deliverCustomerOrderRequest.getDeliveType());
        customerOrder.setUpdateUser(deliverCustomerOrderRequest.getUserCode());
        customerOrder.setStatus(CustomerOrderStatusEnum.DELIVER.getStatus());
        customerOrderMapper.updateByPrimaryKeySelective(customerOrder);
        sendEmail(customerOrder);
    }

    private void checkDeliverCustomer(DeliverCustomerOrderRequest deliverCustomerOrderRequest) {
        /*if (DeliveTypeEnum.LOGISTICS.getCode() != deliverCustomerOrderRequest.getDeliveType()) {
            return;
        }*/
        /*if (StringUtil.isEmpty(deliverCustomerOrderRequest.getLogisticsName())) {
            throw new GTechBaseException(ResultErrorCodeEnum.PARAMTER_NULL_ERROR.code(),
                    ResultErrorCodeEnum.PARAMTER_NULL_ERROR.desc() + ":" + CustomerOrder.C_LOGISTICS_NAME);
        }*/
        /*if (StringUtil.isEmpty(deliverCustomerOrderRequest.getTrackNo())) {
            throw new GTechBaseException(ResultErrorCodeEnum.PARAMTER_NULL_ERROR.code(),
                    ResultErrorCodeEnum.PARAMTER_NULL_ERROR.desc() + ":" + CustomerOrder.C_TRACK_NO);
        }*/
    }

    /**
     * 用来取消某个客户订单的，首先根据订单号查询客户订单，然后检查能否取消，如果当前订单状态处于发布、接收、发货或者补发状态，
     * 则根据支付方式不同进行不同的取消操作，如果是VCR支付方式，则将所有的凭证状态设置为已取消；如果是VCE支付方
     *
     * @param cancelReleaseRequest 取消发布请求
     * @return 返回结果
     */
    @Override
    public Result<Void> cancelRelease(CancelReleaseRequest cancelReleaseRequest) {

        CustomerOrder customerOrder = customerOrderMapper.selectOne(CustomerOrder.builder().customerOrderCode(cancelReleaseRequest.getCustomerOrderCode()).build());
        //检查能否cancel
        if (!checkCancelAble(customerOrder)) {
            return Result.failed(ResultErrorCodeEnum.NOT_CANCEL_NOW_ORDER.code(), ResultErrorCodeEnum.NOT_CANCEL_NOW_ORDER.desc());
        }

        if (customerOrder.getStatus().equals(CustomerOrderStatusEnum.RELEASE.getStatus())
                || customerOrder.getStatus().equals(CustomerOrderStatusEnum.RECEIVE.getStatus())
                || customerOrder.getStatus().equals(CustomerOrderStatusEnum.DELIVER.getStatus())
                || customerOrder.getStatus().equals(CustomerOrderStatusEnum.ISSUANCE.getStatus())
                || customerOrder.getStatus().equals(CustomerOrderStatusEnum.COMPLETED.getStatus())) {


            String approveCode = gvCodeHelper.generateApproveCode();


            if (customerOrder.getMopCode().equals(GvcoreConstants.MOP_CODE_VCR)) {
                VoucherAllocation voucherAllocationByCode = voucherAllocationService.getAllocationBySourceDataCode(cancelReleaseRequest.getCustomerOrderCode(),
                        VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
                Outlet ownerOutlet = outletMapper.selectOne(Outlet.builder().outletCode(voucherAllocationByCode.getVoucherOwnerCode()).build());
                List<VoucherAllocationBatch> voucherAllocationBatches = voucherAllocationBatchService.queryByVoucherAllocationCode(voucherAllocationByCode.getVoucherAllocationCode());
                List<Voucher> vouchers = new ArrayList<>();

                voucherAllocationBatches.forEach(x -> {
                    List<Voucher> voucher = voucherMapper.queryVoucherListByAllocateCode(x.getVoucherStartNo(), x.getVoucherEndNo());
                    vouchers.addAll(voucher);
                });
                List<String> collect = vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList());

                List<Voucher> usedVoucher = vouchers.stream().filter(x -> x.getStatus().equals(VoucherStatusEnum.VOUCHER_USED.getCode())).collect(Collectors.toList());
                if (usedVoucher.size() > 0) {
                    return Result.failed(ResultErrorCodeEnum.VOUCHER_ALREADY_USED.code(), ResultErrorCodeEnum.VOUCHER_ALREADY_USED.desc());
                }
                loadingOrder(customerOrder.getCustomerOrderCode(), CustomerOrderStatusEnum.valueOfCode(customerOrder.getStatus()));

                EXECUTOR.execute(() -> {
                    //https://jira.gtech.asia/browse/MER-1936
                    if (customerOrder.getStatus().equals(CustomerOrderStatusEnum.RECEIVE.getStatus())
                            || customerOrder.getStatus().equals(CustomerOrderStatusEnum.COMPLETED.getStatus())) {

                        //ListUtils.partition(vouchers, voucherReceiveBatchSize).forEach(x->{
                            /*voucherService.cancelSales(ownerOutlet.getOutletCode(),"outlet",collect, null,
                                    null, VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());*/

                        log.info("开始修改券,待修改券数量:{}", collect.size());
                        voucherAllocationBatches.forEach(x -> {
                            Integer count = voucherService.cancelSalesByStartAndEnd(ownerOutlet.getOutletCode(), "outlet", x.getVoucherStartNo(), x.getVoucherEndNo(), null,
                                    null, VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
                            log.info("修改券成功,修改券数量:{}", count);
                        });


                            /*queryVoucherComponent.queryVoucherAndUpdateVoucherStatus(ownerOutlet.getOutletCode(),"outlet", collect, null,
                                    null, VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());*/

                        //addTransactionData(cancelReleaseRequest, x, customerOrder, TransactionTypeEnum.GIFT_CARD_CANCEL_ACTIVATE);
                        queryVoucherComponent.insertTransactionDataByVoucherList(vouchers, customerOrder, approveCode, TransactionTypeEnum.GIFT_CARD_CANCEL_ACTIVATE);
                        //});
                    } else {

                        queryVoucherComponent.queryVoucherAndUpdateVoucherStatus(ownerOutlet.getOutletCode(), "outlet", collect, null, VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode(), null);
                        //voucherService.cancelSales(ownerOutlet.getOutletCode(), "outlet", collect, null, VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode(),null );
                        /*ListUtils.partition(collect, voucherReceiveBatchSize).forEach(x->{

                        });*/
                    }
                    //voucherService.cancelSales(ownerOutlet.getOutletCode(), ownerOutlet.getOutletType(), collect, null, null);
                    /*ListUtils.partition(vouchers, voucherReceiveBatchSize).forEach(x-> {
                        addTransactionData(cancelReleaseRequest, x, customerOrder, TransactionTypeEnum.GIFT_CARD_CANCEL_SELL);
                    });*/
                    if (customerOrder.getStatus().equals(CustomerOrderStatusEnum.RELEASE.getStatus())
                            || customerOrder.getStatus().equals(CustomerOrderStatusEnum.RECEIVE.getStatus())
                            || customerOrder.getStatus().equals(CustomerOrderStatusEnum.DELIVER.getStatus())
                            || customerOrder.getStatus().equals(CustomerOrderStatusEnum.COMPLETED.getStatus())) {
                        queryVoucherComponent.insertTransactionDataByVoucherList(vouchers, customerOrder, approveCode, TransactionTypeEnum.GIFT_CARD_CANCEL_SELL);
                    }

                    customerOrder.setStatus(CustomerOrderStatusEnum.CANCELED.getStatus());
                    customerOrderMapper.updateByPrimaryKeySelective(customerOrder);
                });
            } else if (customerOrder.getMopCode().equals(GvcoreConstants.MOP_CODE_VCE)) {
                if (customerOrder.getVoucherBatchCode() == null) {
                    return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
                }

                //voucherMapper.delete(Voucher.builder().voucherBatchCode(customerOrder.getVoucherBatchCode()).build());

                List<Voucher> vouchers = voucherService.queryVoucherListByVoucherBatchCode(customerOrder.getVoucherBatchCode());
                List<Voucher> usedVoucher = vouchers.stream().filter(x -> x.getStatus().equals(VoucherStatusEnum.VOUCHER_USED.getCode())).collect(Collectors.toList());
                if (usedVoucher.size() > 0) {
                    return Result.failed(ResultErrorCodeEnum.VOUCHER_ALREADY_USED.code(), ResultErrorCodeEnum.VOUCHER_ALREADY_USED.desc());
                }
                loadingOrder(customerOrder.getCustomerOrderCode(), CustomerOrderStatusEnum.valueOfCode(customerOrder.getStatus()));

                EXECUTOR.execute(() -> {
                    List<String> voucherCodeList = vouchers.stream().map(Voucher::getVoucherCode).collect(Collectors.toList());
                    //https://jira.gtech.asia/browse/MER-1936
                    voucherService.disableVoucherBatch(customerOrder.getVoucherBatchCode());

                    if (CollectionUtils.isNotEmpty(vouchers)) {
                        /*ListUtils.partition(vouchers, voucherReceiveBatchSize).forEach(x-> {
                            addTransactionData(cancelReleaseRequest, x, customerOrder, TransactionTypeEnum.GIFT_CARD_CANCEL_SELL);
                            addTransactionData(cancelReleaseRequest, x, customerOrder, TransactionTypeEnum.GIFT_CARD_DEACTIVATE);
                        });*/
                        if (customerOrder.getStatus().equals(CustomerOrderStatusEnum.RELEASE.getStatus())
                                || customerOrder.getStatus().equals(CustomerOrderStatusEnum.RECEIVE.getStatus())
                                || customerOrder.getStatus().equals(CustomerOrderStatusEnum.DELIVER.getStatus())
                                || customerOrder.getStatus().equals(CustomerOrderStatusEnum.COMPLETED.getStatus())) {
                            queryVoucherComponent.insertTransactionDataByVoucherList(vouchers, customerOrder, approveCode, TransactionTypeEnum.GIFT_CARD_CANCEL_SELL);
                        }
                        queryVoucherComponent.insertTransactionDataByVoucherList(vouchers, customerOrder, approveCode, TransactionTypeEnum.GIFT_CARD_DEACTIVATE);


                    }

                    customerOrder.setStatus(CustomerOrderStatusEnum.CANCELED.getStatus());
                    customerOrderMapper.updateByPrimaryKeySelective(customerOrder);
                });


            } else {
                return Result.failed("mop code not exist");
            }

        } else {
            customerOrder.setStatus(CustomerOrderStatusEnum.CANCELED.getStatus());
            customerOrderMapper.updateByPrimaryKeySelective(customerOrder);
        }

        return Result.ok();
    }


    private void addTransactionData(CancelReleaseRequest cancelReleaseRequest, List<Voucher> vouchers, CustomerOrder customerOrder, TransactionTypeEnum typeEnum) {

        if (CollectionUtils.isEmpty(vouchers)) return;

        transactionDataService.insertList(vouchers.stream().map(e -> {

            TransactionData request = new TransactionData();
            request.setMopCode(e.getMopCode());
            request.setTransactionId(cancelReleaseRequest.getCustomerOrderCode());
            request.setTransactionType(typeEnum.getCode());
            request.setMerchantCode(outletService.getOutlet(GetOutletRequest.builder().outletCode(customerOrder.getOutletCode()).build()).getMerchantCode());
            request.setIssuerCode(customerOrder.getIssuerCode());
            request.setOutletCode(customerOrder.getOutletCode());
            request.setCpgCode(e.getCpgCode());
            request.setTransactionDate(new Date());
            request.setVoucherCode(e.getVoucherCode());
            request.setVoucherCodeNum(Long.valueOf(e.getVoucherCode().replaceAll("[a-zA-Z]", "")));
            request.setInitiatedBy("");
            request.setPosCode("");
            request.setBatchCode("");
            request.setLoginSource("");
            request.setDenomination(e.getDenomination());
            request.setActualOutlet("");
            request.setCardEntryMode("GV POS");
            request.setForwardingEntityId("");
            request.setResponseMessage("Transaction successful.");
            request.setTransactionMode("");
            request.setCorporateName("");
            request.setCustomerSalutation("");
            request.setCustomerFirstName("");
            request.setCustomerLastName("");
            request.setMobile("");
            request.setInvoiceNumber(customerOrder.getInvoiceNo());
            request.setOtherInputParameter("{}");
            request.setSuccessOrFailure("0");
            request.setCreateUser("");
            request.setCreateTime(new Date());
            request.setCustomerCode(customerOrder.getCustomerCode());
            request.setCardEntryMode("Swiped");
            request.setApproveCode(gvCodeHelper.generateApproveCode());
            request.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
            request.setVoucherEffectiveDate(e.getVoucherEffectiveDate());
            return request;
        }).collect(Collectors.toList()));

    }


    /**
     * 检查客户订单是否可以取消。
     * 首先判断客户订单的状态是否等于CREATED、SUBMIT、APPROVAL、ISSUANCE、REJECTED中的一种，如果是，则返回true，表示可以取消；
     * 如果客户订单的状态是RELEASE、RECEIVE、DELIVER中的一种，则查询最新的审批节点记录，如果记录存在且创建时间和今天相同
     * MER-1957 新增 Completed 状态订单，增加Cancel按钮
     *
     * @param customerOrder 客户订单
     * @return true：可以取消；false：不可以取消
     */
    private boolean checkCancelAble(CustomerOrder customerOrder) {
        if (CustomerOrderStatusEnum.CREATED.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.SUBMIT.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.APPROVAL.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.ISSUANCE.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.REJECTED.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.COMPLETED.getStatus().equals(customerOrder.getStatus())
        ) {
            return true;
        }
        if (CustomerOrderStatusEnum.RELEASE.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.RECEIVE.getStatus().equals(customerOrder.getStatus())
                || CustomerOrderStatusEnum.DELIVER.getStatus().equals(customerOrder.getStatus())) {
            ApproveNodeRecord approveNodeRecord = approveNodeRecordMapper.selectNewNote(customerOrder.getCustomerOrderCode(), ApproveNodeRecordTypeEnum.RELEASE.getType());
            if (approveNodeRecord == null || approveNodeRecord.getCreateTime() == null) {
                return false;
            }
            return Integer.parseInt(DateUtil.compareDateWithToday(approveNodeRecord.getCreateTime())) == 0;
        }
        return false;
    }

    @Override
    public Result<Void> uploadPaymentVoucher(UploadPaymentVoucherRequest uploadPaymentVoucherRequest) {
        CustomerOrderReceiver customerOrderReceiver = customerOrderReceiverMapper.selectOne(CustomerOrderReceiver.builder().customerOrderCode(uploadPaymentVoucherRequest.getCustomerOrderCode()).build());
        customerOrderReceiver.setPaymentVoucher(uploadPaymentVoucherRequest.getPaymentVoucherUrl());
        customerOrderReceiver.setUpdateUser(uploadPaymentVoucherRequest.getUserCode());
        customerOrderReceiverMapper.updateByPrimaryKeySelective(customerOrderReceiver);
        return Result.ok();
    }

    @Override
    public Result<Void> delPaymentVoucher(DelPaymentVoucherRequest delPaymentVoucherRequest) {
        CustomerOrderReceiver customerOrderReceiver = customerOrderReceiverMapper.selectOne(CustomerOrderReceiver.builder().customerOrderCode(delPaymentVoucherRequest.getCustomerOrderCode()).build());
        customerOrderReceiver.setPaymentVoucher(com.gtech.commons.utils.StringUtil.EMPTY);
        customerOrderReceiver.setUpdateUser(delPaymentVoucherRequest.getUserCode());
        customerOrderReceiverMapper.updateByPrimaryKeySelective(customerOrderReceiver);
        return Result.ok();
    }

    @Override
    public Result<Void> sendEmail(SendCustomerOrderEmailRequest sendCustomerOrderEmailRequest) {
        CustomerOrder customerOrder = customerOrderMapper.selectOne(CustomerOrder.builder().customerOrderCode(sendCustomerOrderEmailRequest.getCustomerOrderCode()).build());
        JSONObject messageRequest = new JSONObject();
        JSONObject param = new JSONObject();
        if (sendCustomerOrderEmailRequest.getFileName().contains("Quotation")) {
            messageRequest.put("eventCode", MessageEnventEnum.CUSTOMER_SEND_QUOTATION.getCode());
            Map<String, Object> extendParams = getExtendsParams(customerOrder.getCustomerOrderCode());
            param.putAll(extendParams);
            param.put("quotationEmails", quotationEmails);
        } else if (sendCustomerOrderEmailRequest.getFileName().contains("Invoice")) {
            messageRequest.put("eventCode", MessageEnventEnum.CUSTOMER_SEND_INVOICE.getCode());
        } else {
            return Result.failed(ResultErrorCodeEnum.SEND_EMAIL_FAIL.code(), ResultErrorCodeEnum.SEND_EMAIL_FAIL.desc());
        }
        param.put("email", customerOrder.getContactEmail());
        JSONArray attachments = new JSONArray();
        JSONObject files = new JSONObject();
        files.put("filename", sendCustomerOrderEmailRequest.getFileName());
        files.put("url", sendCustomerOrderEmailRequest.getFileUrl());
        attachments.add(files);
        param.put("attachments", attachments);
        param.put("purchaseOrderNo", customerOrder.getPurchaseOrderNo());
        messageRequest.put("param", param);
        messageComponent.send(messageRequest);
        return Result.ok();
    }

    @Override
    public CustomerOrder getCustomerByCustomerOrderDetailCode(String customerOrderDetailCode) {

        CustomerOrderDetails entity = new CustomerOrderDetails();
        entity.setCustomerOrderDetailsCode(customerOrderDetailCode);

        CustomerOrderDetails detail = customerOrderDetailsMapper.selectOne(entity);


        CustomerOrder orderEntity = new CustomerOrder();
        orderEntity.setCustomerOrderCode(detail.getCustomerOrderCode());

        return customerOrderMapper.selectOne(orderEntity);
    }

    @Override
    public CustomerOrder queryByCustomerOrderCode(String customerOrderCode) {

        if (StringUtils.isBlank(customerOrderCode)) {
            return null;
        }

        CustomerOrder customerOrder = new CustomerOrder();
        customerOrder.setCustomerOrderCode(customerOrderCode);
        return customerOrderMapper.selectOne(customerOrder);
    }

    @Override
    public Result<Void> updateCustomerInfoInCustomerOrder(UpdateCustomerInfoInCustomerOrderRequest updateCustomerInfoInCustomerOrderRequest) {
        CustomerOrder customerOrder = customerOrderMapper.selectOne(CustomerOrder.builder().customerOrderCode(updateCustomerInfoInCustomerOrderRequest.getCustomerOrderCode()).build());
        BeanCopyUtils.copyProps(updateCustomerInfoInCustomerOrderRequest, customerOrder);
        customerOrderMapper.updateByPrimaryKeySelective(customerOrder);
        return Result.ok();
    }

    @Override
    public List<SumCustomerOrderGroupByArticle> sumCustomerOrderGroupByArticle(CustomerOrderDto dto) {

        List<SumCustomerOrderGroupByArticle> sumList = customerOrderMapper.sumCustomerOrderGroupByArticle(dto);
        if (CollectionUtils.isEmpty(sumList)) {
            return Collections.emptyList();
        }
        return sumList;
    }

    @Override
    public List<SumCustomerOrderGroupByArticle> sumCancelCustomerOrderGroupByArticle(CustomerOrderDto dto) {

        List<SumCustomerOrderGroupByArticle> sumCancelList = customerOrderMapper
                .sumCancelCustomerOrderGroupByArticle(dto);
        if (CollectionUtils.isEmpty(sumCancelList)) {
            return Collections.emptyList();
        }
        return sumCancelList;
    }


    @Override
    public List<CustomerOrder> sumGroupByMeansOfPaymentCode(CustomerOrderDto dto) {

        // 真确的应该是means_of_payment_code, IFNULL(SUM(amount), 0) amount
        // 目前这样计算是因为保存的数据是错的，其他人不想修改，只能配合错误的数据修改, 会产生负数需要再程序内修改
        List<CustomerOrder> list = customerOrderMapper.sumGroupByMeansOfPaymentCode(dto);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public List<CustomerOrder> sumCancelGroupByMeansOfPaymentCode(CustomerOrderDto dto) {

        List<CustomerOrder> list = customerOrderMapper.sumCancelGroupByMeansOfPaymentCode(dto);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public CustomerOrderReceiver getOrderReceiver(final String customerOrderCode) {
        if (StringUtils.isBlank(customerOrderCode)) {
            return null;
        }
        return this.customerOrderReceiverMapper.selectOne(CustomerOrderReceiver.builder().customerOrderCode(customerOrderCode).build());
    }

    @Override
    public void resendVoucherEmail(String emailCode) {

        this.voucherBatchService.resendVoucherExcelEmail(emailCode);
    }

    @Override
    public void addSendVoucherEmail(AddSendEmailRequest request) {

        if (StringUtils.isBlank(request.getCustomerOrderCode())) return;

        CustomerOrder selectParam = new CustomerOrder();
        selectParam.setCustomerOrderCode(request.getCustomerOrderCode());
        CustomerOrder customerOrder = this.customerOrderMapper.selectOne(selectParam);

        if (null == customerOrder) return;

        SendDigitalVoucherExcelToEmailRequest sendRequest = new SendDigitalVoucherExcelToEmailRequest();
        sendRequest.setEmail(request.getEmailAddress());
        sendRequest.setCreateUser(request.getUserCode());
        sendRequest.setCustomerOrderCode(request.getCustomerOrderCode());
        sendRequest.setVoucherBatchCode(customerOrder.getVoucherBatchCode());
        sendRequest.setInvoiceNumber(customerOrder.getInvoiceNo());

        try {
            this.voucherBatchService.sendDigitalVoucherExcelToEmail(sendRequest);
        } catch (IOException e) {
            log.info("修改邮件并重发失败", e);
        }
    }

    @Override
    public int getCpgQuantityNum(final GetCpgQuantityNumParam param) {

        List<CustomerOrder> customerOrderList = this.customerOrderMapper
                .select(CustomerOrder.builder().customerCode(param.getCustomerCode()).status(CustomerOrderStatusEnum.COMPLETED.getStatus()).build());

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(customerOrderList)) return 0;

        Set<String> voucherBatchCodeSet = customerOrderList.stream().map(CustomerOrder::getVoucherBatchCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        final Example queryExample = new Example(Voucher.class);
        queryExample.createCriteria()
                .andIn(Voucher.C_VOUCHER_BATCH_CODE, voucherBatchCodeSet)
                .andNotEqualTo(Voucher.C_VOUCHER_STATUS, 0)
                .andEqualTo(Voucher.C_CPG_CODE, GvConvertUtils.toString(param.getCpgCode(), ""))
                .andEqualTo(Voucher.C_VOUCHER_OWNER_TYPE, "customer")
                .andEqualTo(Voucher.C_VOUCHER_OWNER_CODE, GvConvertUtils.toString(param.getCustomerCode(), ""));


        // 基于订单号集和cpg_code统计券合计数量,该数量即为cpg数量
        return this.voucherMapper.selectCountByCondition(queryExample);
    }

    @Override
    public CustomerOrder getCustomerOrder(String customerOrderCode, String batchCode) {

        if (StringUtils.isBlank(customerOrderCode) && StringUtils.isBlank(batchCode)) return null;

        List<CustomerOrder> select = this.customerOrderMapper.selectByRowBounds(CustomerOrder.builder().customerOrderCode(customerOrderCode)
                .voucherBatchCode(batchCode).build(), new RowBounds(0, 1));

        if (CollectionUtils.isEmpty(select)) return null;

        return select.get(0);
    }

    @Override
    public CustomerOrder getCustomerOrder(String customerOrderCode) {
       return customerOrderMapper.selectOne(new CustomerOrder(customerOrderCode));

    }

    /**
     * delivery 定时发送邮件,默认每天四点发送
     *
     * @param
     * @return
     */
    @Override
    public Result<String> delivery() {
        //查询状态为release和receive的订单
        List<CustomerOrder> customerOrders = customerOrderMapper.selectByCondition(Example.builder(CustomerOrder.class)
                .where(Sqls.custom()
                        .andEqualTo(CustomerOrder.C_STATUS, CustomerOrderStatusEnum.RECEIVE.getStatus())
                ).build());

        //异步生成excel并且发送邮件
        for (CustomerOrder customerOrder : customerOrders) {
            EXECUTOR.execute(() -> {
                        try {
                            deliverySendEmail(customerOrder);
                        } catch (Exception e) {
                            log.error("生成excel并且发送邮件失败", e);
                        }
                    }
            );
        }
        return Result.ok();
    }

    @Transactional
    @Override
    public Result<String> cancelReceive(CancelReceiveRequest request) throws Exception {
        //查询receive数据
        VoucherReceive selectReceive = new VoucherReceive();
        selectReceive.setVoucherReceiveCode(request.getReceiveCode());
        VoucherReceive voucherReceive = voucherReceiveMapper.selectOne(selectReceive);
        List<VoucherReceiveBatch> voucherReceiveCode = voucherReceiveBatchMapper.selectByCondition(Example.builder(VoucherReceiveBatch.class)
                .where(Sqls.custom()
                        .andEqualTo("voucherReceiveCode", request.getReceiveCode())
                ).build());


        List<Future<CancelVoucherReceive>> futures = new ArrayList<>();
        for (VoucherReceiveBatch batch : voucherReceiveCode) {
            //Future<CancelVoucherReceive> future = CANCEL_EXECUTOR.submit(() -> {
            Future<CancelVoucherReceive> future =
                    cancelVoucher(request, voucherReceive, batch);
            //});
            futures.add(future);
        }

        List<CancelVoucherReceive> cancelVoucherReceives = new ArrayList<>();
        for (Future<CancelVoucherReceive> future : futures) {
            try {
                CancelVoucherReceive cancelVoucher = future.get();
                if (null != cancelVoucher) {
                    cancelVoucherReceives.add(cancelVoucher);
                }
            } catch (ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof GTechBaseException) {
                    throw (GTechBaseException) cause;
                } else {
                    throw new GTechBaseException(cause);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new GTechBaseException(e);
            }
        }

        if (CollectionUtils.isEmpty(cancelVoucherReceives)) {
            throw new GTechBaseException(ResultErrorCodeEnum.LOCATION_CHANGE_ERROR.code(), ResultErrorCodeEnum.LOCATION_CHANGE_ERROR.desc());
        }


        int i = cancelVoucherReceiveMapper.insertList(cancelVoucherReceives);
        if (i == 0) {
            throw new GTechBaseException();
        }
        //cancel订单
        Example receiveEx = Example.builder(VoucherReceive.class)
                .where(Sqls.custom()
                        .andEqualTo("voucherReceiveCode", request.getReceiveCode())
                ).build();
        VoucherReceive receiveEntity = VoucherReceive.builder()
                .status(0)
                .receivedNum(0)
                .build();
        i = voucherReceiveMapper.updateByConditionSelective(receiveEntity, receiveEx);
        if (i == 0) {
            throw new GTechBaseException();
        }


        //删除business log 记录
        List<BusinessLog> contentCode = businessLogMapper.selectByCondition(Example.builder(BusinessLog.class)
                .where(Sqls.custom()
                        .andEqualTo("contentCode", request.getReceiveCode())
                ).build());

        if (CollectionUtils.isNotEmpty(contentCode)) {
            i = businessLogMapper.deleteByCondition(Example.builder(BusinessLog.class)
                    .where(Sqls.custom()
                            .andEqualTo("contentCode", request.getReceiveCode())
                    ).build());
            i = businessLogDetailMapper.deleteByCondition(Example.builder(BusinessLogDetails.class)
                    .where(Sqls.custom()
                            .andIn("businessCode", contentCode.stream().map(BusinessLog::getBusinessCode).collect(Collectors.toList()))
                    ).build());
        }


        //HO01向WH01请求 回滚allocation
        if (VoucherReceiveSourceTypeEnum.SALES.getCode().equals(voucherReceive.getSourceType())) {
            VoucherAllocation allocationByCode = voucherAllocationService.getAllocationByCode(voucherReceive.getSourceDataCode());
            i = voucherAllocationService.updateVoucherAllocationStatusByAllocationCode(allocationByCode.getVoucherAllocationCode(), VoucherAllocationStatusEnum.PENDING_RECEIPT.code());
            if (i == 0) {
                throw new GTechBaseException();
            }
        }


        return Result.ok();
    }

    @Override
    public int updateByRelease(CustomerOrderDto dto) {
        return customerOrderMapper.updateByRelease(dto);
    }


    @Async
    @Transactional(rollbackFor = Exception.class)
    public Future<CancelVoucherReceive> cancelVoucher(CancelReceiveRequest request, VoucherReceive voucherReceive, VoucherReceiveBatch batch) throws Exception {
        String startCode = batch.getVoucherStartNo();
        String endCode = batch.getVoucherEndNo();
        Voucher voucher = new Voucher();
        Example example = new Example(Voucher.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andBetween(Voucher.C_VOUCHER_CODE, startCode, endCode);
        List<Voucher> allVoucherList = voucherMapper.selectByCondition(example);

        List<Voucher> vouchers = new ArrayList<>();
        //只有WH01会变更所属人
        if (VoucherReceiveSourceTypeEnum.GENERATE.getCode().equals(voucherReceive.getSourceType())) {
            //criteria.andEqualTo("voucherOwnerCode", issuerWarehouseMap.get(voucherReceive.getIssuerCode()));
            vouchers = allVoucherList.stream().filter(x -> x.getVoucherOwnerCode().equals(issuerWarehouseMap.get(voucherReceive.getIssuerCode()))).collect(Collectors.toList());
        } else {
            //criteria.andEqualTo("voucherOwnerCode", voucherReceive.getInboundCode());
            vouchers = allVoucherList.stream().filter(x -> x.getVoucherOwnerCode().equals(voucherReceive.getInboundCode())).collect(Collectors.toList());
        }
        //MER-1833 添加状态校验和数量校验
        checkStatus(allVoucherList);
        //校验已接收的券是否已经被使用
        Example voucherReceiveRecordExample = new Example(VoucherReceiveRecord.class);
        voucherReceiveRecordExample.createCriteria().andEqualTo("voucherReceiveCode", voucherReceive.getVoucherReceiveCode());
        List<VoucherReceiveRecord> query = voucherReceiveRecordMapper.selectByCondition(voucherReceiveRecordExample);
        query.forEach(
                x -> {
                    Example recordExample = new Example(Voucher.class);
                    Example.Criteria recordCriteria = recordExample.createCriteria();
                    recordCriteria.andBetween(Voucher.C_VOUCHER_CODE, startCode, endCode);
                    List<Voucher> recordList = voucherMapper.selectByCondition(recordExample);
                    //MER-2005
                    checkCirculationStatus(recordList);
                }
        );
        checkChange(request, batch, allVoucherList, vouchers);

        //回滚券

        //根据cpg判断禁用券或者回滚券状态
        //https://jira.gtech.asia/browse/MER-1936
        /*Result<GetCpgResponse> cpg = cpgService.getCpg(GetCpgRequest.builder().cpgCode(batch.getCpgCode()).build());
        if(cpg.getData().getMopCode().equals(GvcoreConstants.MOP_CODE_VCE)){
            voucher.setVoucherStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        }else {
            //实体券需要变更回原所属人
            VoucherAllocation voucherAllocationByCode = voucherAllocationService.getAllocationBySourceDataCode(cancelReleaseRequest.getCustomerOrderCode(),
                    VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
            Outlet ownerOutlet = outletMapper.selectOne(Outlet.builder().outletCode(voucherAllocationByCode.getVoucherOwnerCode()).build());
            voucher.setVoucherOwnerCode(ownerOutlet.getOutletCode());
            voucher.setVoucherOwnerType(ownerOutlet.getOutletType());
            voucher.setStatus(VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        }*/
        voucher.setCirculationStatus(2);
        log.info("待修改券状态数量：{}", allVoucherList.size());
        //TODO 修改券状态
        ListUtils.partition(allVoucherList, 1000).forEach(
                x -> {
                    Example updateVoucherExample = new Example(Voucher.class);
                    Example.Criteria voucherExampleCriteria = updateVoucherExample.createCriteria();
                    voucherExampleCriteria.andIn(Voucher.C_VOUCHER_CODE, x.stream().map(Voucher::getVoucherCode).collect(Collectors.toList()));

                    int i = voucherMapper.updateByConditionSelective(voucher, updateVoucherExample);
                    if (i == 0 || i != batch.getVoucherNum()) {
                        throw new GTechBaseException(ResultErrorCodeEnum.LOCATION_CHANGE_ERROR.code()
                                , "The quantity of received vouchers is incorrect.");
                    }
                    log.info("已修改券状态数量：{}", i);
                });


        /*int i = voucherMapper.updateByConditionSelective(voucher, example);
        if (i == 0 || i != batch.getVoucherNum()) {
            throw new GTechBaseException(ResultErrorCodeEnum.LOCATION_CHANGE_ERROR.code()
                    , "The quantity of received vouchers is incorrect.");
        }*/

        //删除receive 记录 (这里不删除, 保留记录,如果删除记录,会导致生成xml的时候缺失记录)
        /*i = voucherReceiveRecordMapper.deleteByCondition(Example.builder(VoucherReceiveRecord.class)
                .where(Sqls.custom()
                        .andEqualTo("voucherReceiveCode", request.getReceiveCode())
                ).build());
        if (i == 0) {
            throw new Exception("cancel voucher error, voucher a location change occurred");
        }
        */
        CancelVoucherReceive cancelVoucherReceive = new CancelVoucherReceive();
        cancelVoucherReceive.setCancelReceiveCode(gvCodeHelper.generateCancelVoucherReceiveCode());
        cancelVoucherReceive.setReceiveCode(request.getReceiveCode());
        cancelVoucherReceive.setCpgCode(batch.getCpgCode());
        cancelVoucherReceive.setVoucherStartNo(batch.getVoucherStartNo());
        cancelVoucherReceive.setVoucherEndNo(batch.getVoucherEndNo());
        cancelVoucherReceive.setBookletStartNo(batch.getBookletStartNo());
        cancelVoucherReceive.setBookletEndNo(batch.getBookletEndNo());
        cancelVoucherReceive.setOutboundCode(voucherReceive.getOutboundCode());
        cancelVoucherReceive.setInboundCode(voucherReceive.getInboundCode());
        cancelVoucherReceive.setDenomination(batch.getDenomination());
        cancelVoucherReceive.setReceivedNum(batch.getVoucherNum());
        cancelVoucherReceive.setCancelDate(new Date());

        return new AsyncResult<>(cancelVoucherReceive);
    }

    private static void checkStatus(List<Voucher> allVoucherList) {
        List<Voucher> activateVoucher = allVoucherList.stream().filter(x -> x.getStatus().equals(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode())).collect(Collectors.toList());
        List<Voucher> redeemedVoucher = allVoucherList.stream().filter(x -> x.getStatus().equals(VoucherStatusEnum.VOUCHER_USED.getCode())).collect(Collectors.toList());
        List<Voucher> expiredVoucher = allVoucherList.stream().filter(x -> x.getStatus().equals(VoucherStatusEnum.VOUCHER_EXPIRED.getCode())).collect(Collectors.toList());
        List<Voucher> deactivatedVoucher = allVoucherList.stream().filter(x -> x.getVoucherStatus().equals(GvcoreConstants.STATUS_DISABLE)).collect(Collectors.toList());

        String errorMessage = "";

        if (CollectionUtils.isNotEmpty(activateVoucher)) {
            errorMessage += " activated ";
        }
        if (CollectionUtils.isNotEmpty(redeemedVoucher)) {
            errorMessage += " redeemed ";
        }
        if (CollectionUtils.isNotEmpty(expiredVoucher)) {
            errorMessage += " expired ";
        }
        if (CollectionUtils.isNotEmpty(deactivatedVoucher)) {
            errorMessage += " deactivated ";
        }

        if (StringUtils.isEmpty(errorMessage)) {
            return;
        }

        log.error("The partial vouchers are already :{0}", errorMessage);
        throw new GTechBaseException(ResultErrorCodeEnum.LOCATION_CHANGE_ERROR.code()
                , "The partial vouchers are already :{实际状态}.".replace("{实际状态}", errorMessage));
    }


    private static void checkCirculationStatus(List<Voucher> allVoucherList) {
        List<Voucher> toReceived = allVoucherList.stream().filter(x -> x.getCirculationStatus().equals(VoucherCirculationStatusEnum.VOUCHER_TO_BE_RECEIVED.getCode())).collect(Collectors.toList());
        //allVoucherList的ownerType不能是customer
        List<Voucher> customer = allVoucherList.stream().filter(x -> x.getVoucherOwnerType().equals("customer")).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(toReceived) || CollectionUtils.isNotEmpty(customer)) {
            String errorMessage = "Vouchers have been sold";
            log.error(errorMessage);
            throw new GTechBaseException(ResultErrorCodeEnum.LOCATION_CHANGE_ERROR.code()
                    , errorMessage);
        }
    }

    private void checkChange(CancelReceiveRequest request, VoucherReceiveBatch batch, List<Voucher> allVoucherList, List<Voucher> vouchers) {
        //检查券是变更
        if (vouchers.size() != batch.getVoucherNum()) {
            String errorMessage = "";
            //过滤出不在vouchers中的券,并且按照券的所属人进行分组
            List<Voucher> finalVouchers = vouchers;
            Map<String, List<Voucher>> collect = allVoucherList.stream()
                    .filter(x -> !finalVouchers.contains(x)).collect(Collectors.groupingBy(Voucher::getVoucherOwnerType));


            Set<Map.Entry<String, List<Voucher>>> entries = collect.entrySet();
            for (Map.Entry<String, List<Voucher>> entry : entries) {
                if (entry.getKey().equals("outlet")) {
                    Map<String, List<Voucher>> ownerCodes = entry.getValue().stream().collect(Collectors.groupingBy(Voucher::getVoucherOwnerCode));
                    Set<Map.Entry<String, List<Voucher>>> ownerMap = ownerCodes.entrySet();
                    for (Map.Entry<String, List<Voucher>> stringListEntry : ownerMap) {

                        OutletResponse outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(stringListEntry.getKey()).build());
                        errorMessage += (" " + outlet.getOutletName());
                    }

                } else if (entry.getKey().equals("warehouse")) {
                    Map<String, List<Voucher>> ownerCodes = entry.getValue().stream().collect(Collectors.groupingBy(Voucher::getVoucherOwnerCode));
                    Set<Map.Entry<String, List<Voucher>>> ownerMap = ownerCodes.entrySet();
                    for (Map.Entry<String, List<Voucher>> stringListEntry : ownerMap) {
                        OutletResponse outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(stringListEntry.getKey()).build());
                        errorMessage += (" " + outlet.getOutletName());
                    }
                } else if (entry.getKey().equals("customer")) {
                    Map<String, List<Voucher>> ownerCodes = entry.getValue().stream().collect(Collectors.groupingBy(Voucher::getVoucherOwnerCode));
                    Set<Map.Entry<String, List<Voucher>>> ownerMap = ownerCodes.entrySet();
                    for (Map.Entry<String, List<Voucher>> stringListEntry : ownerMap) {
                        CustomerResponse customer = customerService.getCustomer(GetCustomerRequest.builder().customerCode(stringListEntry.getKey()).build());

                        String companyName = customer.getCompanyName();
                        String customerName = customer.getCustomerName();

                        if (StringUtils.isNotEmpty(companyName) && StringUtils.isNotEmpty(customerName)) {
                            customer.setCompanyAndBranchName(companyName + "(" + customerName + ")");
                        } else if (StringUtils.isNotEmpty(companyName)) {
                            customer.setCompanyAndBranchName(companyName);
                        } else if (StringUtils.isNotEmpty(customerName)) {
                            customer.setCompanyAndBranchName(customerName);
                        }
                        errorMessage += (" " + customer.getCompanyAndBranchName());
                    }
                }
            }


            log.error("cancel voucher error, voucher a location change occurred, receiveCode:{}", request.getReceiveCode());
            throw new GTechBaseException(ResultErrorCodeEnum.LOCATION_CHANGE_ERROR.code()
                    , "The gift voucher inventory is incorrect, and it is in the {实际存在的位置} now.".replace("{实际存在的位置}", errorMessage));
        }
    }

    public void deliverySendEmail(CustomerOrder customerOrder) {

        if (customerOrder == null) {
            return;
        }

        Map<String, Object> extendsParams = getExtendsParams(customerOrder.getCustomerOrderCode());
        String enventCode = MessageEnventEnum.CUSTOMER_ORDER_DELIVERY_REMINDER.getCode();

        //查询流程通知人
        Set<String> sendEmails = getUserByRole(customerOrder.getIssuerCode());
        if (CollectionUtils.isEmpty(sendEmails)) {
            log.info("send -> emails empty");
            return;
        }

        //装填参数
        JSONObject messageRequest = new JSONObject();
        messageRequest.put("eventCode", enventCode);
        JSONObject param = new JSONObject();
        param.put("emails", sendEmails);
        param.put("code", customerOrder.getCustomerOrderCode());
        if (extendsParams != null) {
            param.putAll(extendsParams);
        }
        /*if (userCodeList.length() > 0) {
            param.put("userCodeList", userCodeList.substring(0, userCodeList.length() - 1));
        }*/
        messageRequest.put("param", param);
        messageComponent.send(messageRequest);
    }

    public Set<String> getUserByRole(String permissionCode) {
        //查询按钮通知人
        // default by resource
        List<String> roleCodeList = getRoleCodeListByResource(Lists.newArrayList("deliverCustomerOrder"));
        List<UserAccount> userAccountList = userAccountService.queryUserByRolesAndDataPermissions(roleCodeList, permissionCode);
        return userAccountList.stream().filter(userAccount -> StringUtils.isNotBlank(userAccount.getEmail())).map(UserAccount::getEmail).collect(Collectors.toSet());
    }

    private List<String> getRoleCodeListByResource(List<String> resourceList) {

        Example example = new Example(ResourceEntity.class);
        example.createCriteria().andIn(ResourceEntity.C_RESOURCE_CODE, resourceList);
        List<ResourceEntity> list = resourceMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> resourceCodeList = new ArrayList<>();
        for (ResourceEntity vo : list) {
            Integer resourceType = vo.getResourceType();
            if (ResourceTypeEnum.BUTTON.number() == resourceType) {
                resourceCodeList.add(vo.getResourceCode());
            } else if (ResourceTypeEnum.PAGE.number() == resourceType) {
                resourceCodeList.add(vo.getResourceParent());
            }
        }
        return queryRolesByResource(resourceCodeList);
    }


    private List<String> queryRolesByResource(List<String> resourceCodeList) {
        if (CollectionUtils.isEmpty(resourceCodeList)) {
            return Collections.emptyList();
        }
        Example example = new Example(RoleResourceMappingEntity.class);
        example.createCriteria().andIn(RoleResourceMappingEntity.C_RESOURCE_CODE, resourceCodeList);
        List<RoleResourceMappingEntity> list = roleResourceMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(resourceCodeList)) {
            return Collections.emptyList();
        }
        return list.stream().map(RoleResourceMappingEntity::getRoleCode).distinct().collect(Collectors.toList());
    }


}

