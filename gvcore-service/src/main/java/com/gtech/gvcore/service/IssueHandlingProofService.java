package com.gtech.gvcore.service;

import java.util.List;

import com.gtech.gvcore.dao.model.IssueHandlingProof;

public interface IssueHandlingProofService {
	
	/**
	 * 
	 * @param proofList
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月5日
	 */
	int insertList(List<IssueHandlingProof> proofList);
	
	/**
	 * 
	 * @param issueHandlingCode
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	List<IssueHandlingProof> queryByIssueHandlingCode(String issueHandlingCode);
	
	/**
	 * 
	 * @param issueHandlingCode
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月16日
	 */
	int deleteByIssueHandlingCode(String issueHandlingCode);

}
