package com.gtech.gvcore.service.report.impl.bean.aging;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName AgingUsedAndUnredeemedReportBo
 * @Description Aging Used And Unredeemed Report Bean
 * <AUTHOR>
 * @Date 2022/10/31 20:16
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class UsedAndUnredeemedBean {

    @ExcelProperty(value = "Details", index = 0)
    private String customerName;

    @ExcelProperty(value = "Used", index = 1)
    private String useScale;

    @ExcelProperty(value = "Unredeemed", index = 2)
    private String unUseScale;

}