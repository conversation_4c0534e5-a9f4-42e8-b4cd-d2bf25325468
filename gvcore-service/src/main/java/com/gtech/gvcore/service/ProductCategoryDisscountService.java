package com.gtech.gvcore.service;

import com.gtech.gvcore.dao.model.ProductCategoryDisscount;

/**
 * <AUTHOR>
 * @date 2022年2月23日
 */
public interface ProductCategoryDisscountService {

    /**
     * 
     * <AUTHOR>
     * @param productCategoryCode
     * @return
     * @date 2022年2月24日
     */
    ProductCategoryDisscount queryByProductCategoryCode(String productCategoryCode);

    /**
     * 
     * <AUTHOR>
     * @param productCategoryDisscount
     * @return
     * @date 2022年2月24日
     */
    int insertOrUpdate(ProductCategoryDisscount productCategoryDisscount);

}
