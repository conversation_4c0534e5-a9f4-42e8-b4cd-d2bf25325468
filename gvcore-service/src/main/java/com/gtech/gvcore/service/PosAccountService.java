package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.posaccount.CreatePosAccountRequest;
import com.gtech.gvcore.common.request.posaccount.DeletePosAccountRequest;
import com.gtech.gvcore.common.request.posaccount.QueryPosAccountListRequest;
import com.gtech.gvcore.common.request.posaccount.UpdatePosAccountRequest;
import com.gtech.gvcore.common.response.posaccount.PosAccountResponse;

public interface PosAccountService {


    Result<String> createPosAccount(CreatePosAccountRequest request);

    Result<String> updatePosAccount(UpdatePosAccountRequest request);

    Result<String> deletePosAccount(DeletePosAccountRequest request);

    PageResult<PosAccountResponse> queryPosAccountList(QueryPosAccountListRequest request);

    Result<PosAccountResponse> getPosAccount(QueryPosAccountListRequest request);

}
