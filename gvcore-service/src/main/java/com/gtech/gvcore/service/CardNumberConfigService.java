package com.gtech.gvcore.service;

import com.gtech.commons.page.PageData;
import com.gtech.gvcore.dto.CardNumberConfigDto;

import java.util.List;

public interface CardNumberConfigService {

    String createCardNumberConfig(CardNumberConfigDto cardNumberConfig);

    String updateCardNumberConfig(CardNumberConfigDto cardNumberConfig);

    CardNumberConfigDto getCardNumberConfig(String configCode);

    PageData<CardNumberConfigDto> queryCardNumberConfigList(CardNumberConfigDto cardNumberConfigDto);


   List<CardNumberConfigDto> queryAllDenominationConfigList();

}
