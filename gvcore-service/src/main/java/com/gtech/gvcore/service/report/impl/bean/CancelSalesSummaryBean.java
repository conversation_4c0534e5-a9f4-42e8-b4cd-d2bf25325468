package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 16:52
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class CancelSalesSummaryBean {

    /**
     * Merchant
     */
    @ExcelProperty(value = "Merchant")
    private String merchant;

    /**
     * Outlet
     */
    @ExcelProperty(value = "Outlet")
    private String outlet;

    /**
     * Voucher Program Group
     */
    @ExcelProperty(value = "Voucher Program Group")
    private String voucherProgramGroup;

    /**
     * Number of Vouchers
     */
    @ExcelProperty(value = "Number of Vouchers", converter = ExportExcelNumberConverter.class)
    private String numberOfVouchers;

    /**
     * Total Amount
     */
    @ReportAmountValue
    @ExcelProperty(value = "Total Amount", converter = ExportExcelNumberConverter.class)
    private String totalAmount;

}
