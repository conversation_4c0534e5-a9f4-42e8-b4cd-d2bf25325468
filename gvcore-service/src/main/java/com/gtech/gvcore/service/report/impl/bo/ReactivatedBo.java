package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName DeactivatedForSummaryBo
 * @Description blocked deactivated for summary bo
 * <AUTHOR>
 * @Date 2022/9/22 19:30
 * @Version V1.0
 **/
@Setter
@Getter
@Accessors(chain = true)
public class ReactivatedBo implements GroupNewTransactionByVoucherCodeSupport {


    private String transactionCode;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }

    /**
     * Merchant code.
     */
    private String merchantCode;

    /**
     * Outlet code.
     */
    private String outletCode;

    /**
     * Cpg code.
     */
    private String cpgCode;

    /**
     * Voucher Code.
     */
    private String voucherCode;

    /**
     * Voucher Effective Date.
     */
    private String voucherEffectiveDate;

    /**
     * Invoice Number.
     */
    private String invoiceNumber;

    /**
     * Denomination.
     */
    private BigDecimal denomination;

    /**
     * Create Time.
     */
    private String transactionDate;

    /**
     * Customer Code.
     */
    private String customerCode;

    public Date getVoucherEffectiveDate() {

        return DateUtil.parseDate(voucherEffectiveDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }


    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }


}
