package com.gtech.gvcore.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.constants.RedisConstants;
import com.gtech.gvcore.common.enums.ApproveTypeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.flow.SendEmailRequest;
import com.gtech.gvcore.common.request.releaseapprove.ApproveNodeRecordRequest;
import com.gtech.gvcore.common.request.releaseapprove.CreateLogRecode;
import com.gtech.gvcore.common.request.releaseapprove.GetNextAmountRequest;
import com.gtech.gvcore.common.request.releaseapprove.QueryApproveNodeRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAbleRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveAmountRequest;
import com.gtech.gvcore.common.request.releaseapprove.ReleaseApproveNodeRequest;
import com.gtech.gvcore.common.response.releaseapprove.ApproveNodeRecordResponse;
import com.gtech.gvcore.common.response.releaseapprove.GetNextAmountResponse;
import com.gtech.gvcore.common.response.releaseapprove.ReleaseApproveAmountResponse;
import com.gtech.gvcore.common.response.releaseapprove.ReleaseApproveNodeResponse;
import com.gtech.gvcore.dao.mapper.ApproveNodeRecordMapper;
import com.gtech.gvcore.dao.mapper.ReleaseApproveAmountMapper;
import com.gtech.gvcore.dao.mapper.ReleaseApproveNodeMapper;
import com.gtech.gvcore.dao.model.ApproveNodeRecord;
import com.gtech.gvcore.dao.model.ReleaseApproveAmount;
import com.gtech.gvcore.dao.model.ReleaseApproveNode;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.ReleaseApproveService;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 16:11
 */
@Slf4j
@Service
public class ReleaseApproveServiceImpl implements ReleaseApproveService {

    @Autowired
    private ReleaseApproveAmountMapper releaseApproveAmountMapper;
    @Autowired
    private ReleaseApproveNodeMapper releaseApproveNodeMapper;
    @Autowired
    private ApproveNodeRecordMapper approveNodeRecordMapper;
    @Autowired
    private GvCodeHelper gvCodeHelper;
    @Autowired
    private GvUserAccountService gvUserAccountService;
    @Autowired
    private MessageComponent messageComponent;

    @Autowired
    private GTechRedisTemplate gTechRedisTemplate;

    @Override
    @Transactional
    public Result<List<String>> settingApproveConfig(List<ReleaseApproveAmountRequest> releaseApproveAmountRequests) {

        List<String> returnList = new ArrayList<>();
        Long typeCount = releaseApproveAmountRequests.stream().map(ReleaseApproveAmountRequest::getType).distinct()
                .count();
        if (typeCount > 1) {
            return Result.failed(ResultErrorCodeEnum.TYPE_INCONSISTENCY_ERROR.code(), ResultErrorCodeEnum.TYPE_INCONSISTENCY_ERROR.desc());
        }

        releaseApproveAmountRequests.sort(Comparator.comparing(ReleaseApproveAmountRequest::getRangeName));

        Result<Void> result = checkRange(releaseApproveAmountRequests);
        if (!result.isSuccess()) {
            return Result.failed(result.getCode(), result.getMessage());
        }

        List<ReleaseApproveAmount> releaseApproveAmounts = new ArrayList<>(releaseApproveAmountRequests.size());
        List<ReleaseApproveNode> releaseApproveNodes = new ArrayList<>();
        for (ReleaseApproveAmountRequest releaseApproveAmountRequest : releaseApproveAmountRequests) {
            ReleaseApproveAmount releaseApproveAmount = BeanCopyUtils.jsonCopyBean(releaseApproveAmountRequest, ReleaseApproveAmount.class);
            releaseApproveAmount.setReleaseApproveAmountCode(gvCodeHelper.generateReleaseApproveAmountCode());
            releaseApproveAmount.setCreateTime(new Date());
            releaseApproveAmount.setUpdateTime(new Date());
            returnList.add(releaseApproveAmount.getReleaseApproveAmountCode());
            releaseApproveAmounts.add(releaseApproveAmount);
            for (ReleaseApproveNodeRequest releaseApproveNodeRequest : releaseApproveAmountRequest.getReleaseApproveNodeRequests()) {
                ReleaseApproveNode releaseApproveNode = BeanCopyUtils.jsonCopyBean(releaseApproveNodeRequest, ReleaseApproveNode.class);
                releaseApproveNode.setReleaseApproveAmountCode(releaseApproveAmount.getReleaseApproveAmountCode());
                releaseApproveNode.setReleaseApproveNodeCode(gvCodeHelper.generateReleaseApproveNodeCode());
				releaseApproveNode.setIssuerCode(releaseApproveAmountRequest.getIssuerCode());
                releaseApproveNode.setCreateTime(new Date());
                releaseApproveNode.setUpdateTime(new Date());
                releaseApproveNodes.add(releaseApproveNode);
            }

        }
        Example example = new Example(ReleaseApproveAmount.class);
		example.createCriteria().andEqualTo(ReleaseApproveAmount.C_TYPE, releaseApproveAmountRequests.get(0).getType()).andEqualTo("issuerCode",
				releaseApproveAmountRequests.get(0).getIssuerCode());
        List<ReleaseApproveAmount> releaseApproveAmounts1 = releaseApproveAmountMapper.selectByCondition(example);
        List<String> collect = releaseApproveAmounts1.stream().map(ReleaseApproveAmount::getReleaseApproveAmountCode).collect(Collectors.toList());
        releaseApproveAmountMapper.deleteByCondition(example);
        releaseApproveAmountMapper.insertList(releaseApproveAmounts);

        if (!CollectionUtils.isEmpty(collect)) {
            Example example1 = new Example(ReleaseApproveNode.class);
            example1.createCriteria().andIn(ReleaseApproveNode.C_RELEASE_APPROVE_AMOUNT_CODE, collect);
            releaseApproveNodeMapper.deleteByCondition(example1);
        }
        releaseApproveNodeMapper.insertList(releaseApproveNodes);
        return Result.ok(returnList);

    }

    private Result<Void> checkRange(List<ReleaseApproveAmountRequest> releaseApproveAmountRequests) {

        if (BigDecimal.ONE.compareTo(releaseApproveAmountRequests.get(0).getStartNum()) != 0) {
            return Result.failed(ResultErrorCodeEnum.RELEASE_CONFIG_RANGE_ERROR.code(),
                    "The starting amount on the first line of Approval Config must be equal to 1");
        }

        Integer line = 1;
        int listSize = releaseApproveAmountRequests.size();
        for (int i = 0; i < listSize; i++) {

            ReleaseApproveAmountRequest approveAmount = releaseApproveAmountRequests.get(i);
            Result<Void> checkResult = checkApprove(releaseApproveAmountRequests, line, i, approveAmount);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }

            approveAmount.getReleaseApproveNodeRequests()
                    .sort(Comparator.comparing(ReleaseApproveNodeRequest::getNodeName));

            Integer node = 1;
            for (ReleaseApproveNodeRequest approveNode : approveAmount.getReleaseApproveNodeRequests()) {
                if (!node.equals(approveNode.getNodeName())) {
                    return Result.failed(ResultErrorCodeEnum.APPROVE_CONFIG_NODE_MISS.code(),
                            "Approval node " + node + " missing from Approval Config line " + line);
                }
                node++;
            }

            line++;
        }

        return Result.ok();
    }

    private Result<Void> checkApprove(List<ReleaseApproveAmountRequest> releaseApproveAmountRequests, Integer line,
            int index, ReleaseApproveAmountRequest approveAmount) {

        if (!line.equals(approveAmount.getRangeName())) {
            return Result.failed(ResultErrorCodeEnum.MISS_APPROVE_CONFIG_LINE.code(),
                    ResultErrorCodeEnum.MISS_APPROVE_CONFIG_LINE.desc() + line);
        }

        if (approveAmount.getStartNum().compareTo(approveAmount.getEndNum()) > 0) {
            return Result.failed(ResultErrorCodeEnum.RELEASE_CONFIG_RANGE_ERROR.code(),
                    "Start amount must be greater than equal end amount, line " + line);
        }

        if (index > 0) {
            ReleaseApproveAmountRequest previousApprove = releaseApproveAmountRequests.get(index - 1);
            if (previousApprove.getEndNum().add(BigDecimal.ONE).compareTo(approveAmount.getStartNum()) != 0) {
                return Result.failed(ResultErrorCodeEnum.RELEASE_CONFIG_RANGE_ERROR.code(),
                        ResultErrorCodeEnum.RELEASE_CONFIG_RANGE_ERROR.desc() + ", line " + line);
            }
        }

        if (CollectionUtils.isEmpty(approveAmount.getReleaseApproveNodeRequests())) {
            return Result.failed(ResultErrorCodeEnum.APPROVAL_NODE_CANNOT_BE_EMPTY.code(),
                    ResultErrorCodeEnum.APPROVAL_NODE_CANNOT_BE_EMPTY.desc() + line);
        }

        return Result.ok();
    }

    @Override
	public Result<List<ReleaseApproveAmountResponse>> queryApproveConfig(String issuerCode) {
		List<ReleaseApproveAmount> releaseApproveAmounts = releaseApproveAmountMapper.selectAllAndSort(issuerCode);
        if (CollectionUtils.isEmpty(releaseApproveAmounts)) {
            return Result.ok();
        }
        List<ReleaseApproveAmountResponse> releaseApproveAmountResponses = new ArrayList<>(releaseApproveAmounts.size());
        for (ReleaseApproveAmount releaseApproveAmount : releaseApproveAmounts) {
            List<ReleaseApproveNode> releaseApproveNodes = releaseApproveNodeMapper.selectByAmountCodeAndSort(releaseApproveAmount.getReleaseApproveAmountCode());
            ReleaseApproveAmountResponse releaseApproveAmountResponse = BeanCopyUtils.jsonCopyBean(releaseApproveAmount, ReleaseApproveAmountResponse.class);
            releaseApproveAmountResponse.setReleaseApproveNodeResponseList(BeanCopyUtils.jsonCopyList(releaseApproveNodes, ReleaseApproveNodeResponse.class));
            releaseApproveAmountResponses.add(releaseApproveAmountResponse);
        }
        return Result.ok(releaseApproveAmountResponses);
    }

	@Override
	public List<UserAccount> queryUserByApproveNode(QueryApproveNodeRequest queryApproveNodeRequest) {
		List<ReleaseApproveNode> releaseApproveNodes = releaseApproveNodeMapper.selectNodesByAmountAndType(queryApproveNodeRequest.getIssuerCode(),
				queryApproveNodeRequest.getVoucherAmount(),
				queryApproveNodeRequest.getConfigType());
		if (releaseApproveNodes == null) {
			return Collections.emptyList();
		}
		Map<Integer, String> roleMap = releaseApproveNodes.stream().collect(Collectors.toMap(ReleaseApproveNode::getNodeName, ReleaseApproveNode::getRoleCode));
		String roleCode = roleMap.get(1);// 默认取第一个
		String configType = queryApproveNodeRequest.getConfigType();
		String approve = ApproveTypeEnum.getByType(configType);
		List<ApproveNodeRecord> approveNodeRecords = approveNodeRecordMapper.selectByBusinessAndType(queryApproveNodeRequest.getBusinessCode(),
				approve, GvcoreConstants.DELETE_STATUS_DISABLE);
		
		if (!CollectionUtils.isEmpty(approveNodeRecords)) {
			Integer nodeName = approveNodeRecords.get(0).getReleaseApproveNodeName();
			roleCode = roleMap.get(nodeName + 1);
		}
		if (!StringUtil.isEmpty(roleCode)) {
			return gvUserAccountService.queryUserByRolesAndDataPermissions(Arrays.asList(roleCode), queryApproveNodeRequest.getPermissionCode());
		}
		return Collections.emptyList();
	}

    @Override
	public Result<Integer> approveAble(ReleaseApproveAbleRequest approveAbleRequest) {

        Result<Integer> result = Result.failed(null);
        result.setData(0);
        //如果这个金额未配置
        //If it is not configured, it cannot be approved
		List<ReleaseApproveNode> releaseApproveNodes = releaseApproveNodeMapper.selectNodesByAmountAndType(approveAbleRequest.getIssuerCode(),
				approveAbleRequest.getVoucherAmount(), approveAbleRequest.getReleaseApproveAmountType());
        if (CollectionUtils.isEmpty(releaseApproveNodes)) {
            log.warn("This voucher amount it is not configured, it cannot be approved");
            return result;
        }

        Map<Integer, ApproveNodeRecord> recordMap = queryApproveNodeRecordMap(approveAbleRequest.getBusinessCode(),
                approveAbleRequest.getReleaseType());

        int listSize = releaseApproveNodes.size();
        ReleaseApproveNode lastAutomaticApproveNode = null;

        for (int i = 0; i < listSize; i++) {

            ReleaseApproveNode releaseApproveNode = releaseApproveNodes.get(i);
            if (!recordMap.containsKey(releaseApproveNode.getNodeName())) {
                if (GvcoreConstants.AUTOMATIC_APPROVE.equals(releaseApproveNode.getRoleCode())) {
                    lastAutomaticApproveNode = releaseApproveNode;
                } else {
                    String[] split = approveAbleRequest.getApproveRoleCode().split(",");
                    if (containsRoleCode(split, releaseApproveNode.getRoleCode())) {
                        result = Result.ok(0);
                    }
                    break;
                }
            }
        }

        if (lastAutomaticApproveNode != null) {
            result.setData(1);
        }

        return result;
    }

    /**
     * 
     * @param releaseApproveNodes
     * @param index
     * @param listSize
     * @return
     * <AUTHOR>
     * @date 2022年5月16日
     */
    private String getNextRoleCode(List<ReleaseApproveNode> releaseApproveNodes, int index, int listSize) {

        ReleaseApproveNode node = getNextNode(releaseApproveNodes, index, listSize);
        if (node == null) {
            return null;
        }
        return node.getRoleCode();
    }

    /**
     * 
     * @param releaseApproveNodes
     * @param index
     * @param listSize
     * @return
     * <AUTHOR>
     * @date 2022年5月18日
     */
    private ReleaseApproveNode getNextNode(List<ReleaseApproveNode> releaseApproveNodes, int index, int listSize) {

        int nextIndex = index + 1;
        if (nextIndex < listSize) {
            return releaseApproveNodes.get(nextIndex);
        }
        return null;
    }

    private Map<Integer, ApproveNodeRecord> queryApproveNodeRecordMap(String businessCode,
            String releaseApproveAmountType) {

        List<ApproveNodeRecord> approveNodeRecords = approveNodeRecordMapper.selectByBusinessAndType(businessCode,
                releaseApproveAmountType, GvcoreConstants.DELETE_STATUS_DISABLE);
        if (!CollectionUtils.isEmpty(approveNodeRecords)) {
            return approveNodeRecords.stream()
                    .collect(Collectors.toMap(ApproveNodeRecord::getReleaseApproveNodeName, v -> v, (v1, v2) -> v2));
        }

        return Collections.emptyMap();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ApproveNodeRecord> approve(ApproveNodeRecordRequest approveNodeRecordRequest) {

		List<ReleaseApproveNode> releaseApproveNodes = releaseApproveNodeMapper.selectNodesByAmountAndType(approveNodeRecordRequest.getIssuerCode(),
				approveNodeRecordRequest.getVoucherAmount(), approveNodeRecordRequest.getReleaseApproveAmountType());
        if (CollectionUtils.isEmpty(releaseApproveNodes)) {
            return Result.failed(ResultErrorCodeEnum.NO_CONFIG_AMOUNT.code(), ResultErrorCodeEnum.NO_CONFIG_AMOUNT.desc());
        }

        String key = RedisConstants.LOCK_KEY_APPROVE + approveNodeRecordRequest.getBusinessCode();
        boolean lockFlag = gTechRedisTemplate.opsValueSetIfAbsent(RedisConstants.APPKEY, key, key,
                RedisConstants.CACHE_TIMEOUT);
        log.info("approveLock BusinessCode={}, lockFlag={}", approveNodeRecordRequest.getBusinessCode(), lockFlag);
        if (!lockFlag) {
            return Result.failed(ResultErrorCodeEnum.ALREADY_IN_APPROVAL.code(),
                    ResultErrorCodeEnum.ALREADY_IN_APPROVAL.desc());
        }

        Map<Integer, ApproveNodeRecord> recordMap = queryApproveNodeRecordMap(
                approveNodeRecordRequest.getBusinessCode(), approveNodeRecordRequest.getReleaseType());

        int listSize = releaseApproveNodes.size();
        String[] roleCodeArr = approveNodeRecordRequest.getRoleCode().split(",");
        ReleaseApproveNode nowReleaseApproveNode = null;
        ReleaseApproveNode nextNode = null;
        int index = 0;
        for (; index < listSize; index++) {
            ReleaseApproveNode releaseApproveNode = releaseApproveNodes.get(index);
            if (!recordMap.containsKey(releaseApproveNode.getNodeName())) {
                if (containsRoleCode(roleCodeArr, releaseApproveNode.getRoleCode())) {
                    nowReleaseApproveNode = releaseApproveNode;
                    nextNode = getNextNode(releaseApproveNodes, index, listSize);
                }
                index++;
                break;
            }
        }

        if (nowReleaseApproveNode == null) {

            gTechRedisTemplate.delete(RedisConstants.APPKEY, key);

            ReleaseApproveNode lastNode = releaseApproveNodes.get(listSize - 1);
            ApproveNodeRecord record = recordMap.get(lastNode.getNodeName());
            if (record != null && GvcoreConstants.AUTOMATIC_APPROVE.equals(lastNode.getRoleCode())) {
                record.setNextRoleCode(null);
                return Result.ok(record);
            }

            return Result.failed(ResultErrorCodeEnum.NO_AUDIT_PERMISSION.code(),
                    ResultErrorCodeEnum.NO_AUDIT_PERMISSION.desc());
        }

        List<ApproveNodeRecord> approveList = new ArrayList<>((listSize - index) + 1);

        String approveNodeRecordCode = gvCodeHelper.generateApproveNodeRecordCode();
        ApproveNodeRecord approveNodeRecord = BeanCopyUtils.jsonCopyBean(approveNodeRecordRequest,
                ApproveNodeRecord.class);
        approveNodeRecord.setApproveNodeRecordCode(approveNodeRecordCode);
        approveNodeRecord.setReleaseApproveAmountType(approveNodeRecordRequest.getReleaseType());
        approveNodeRecord.setApproveRoleCode(nowReleaseApproveNode.getRoleCode());
        approveNodeRecord.setCreateUser(approveNodeRecordRequest.getApproveUser());
        approveNodeRecord.setCreateTime(new Date());
        approveNodeRecord.setUpdateTime(approveNodeRecord.getCreateTime());
        approveNodeRecord.setReleaseApproveNodeName(nowReleaseApproveNode.getNodeName());
        approveNodeRecord.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        // reject or approve data process, 拒绝没有下个节点、不用通知、改为删除状态
        // reject情况下，业务处理程序必须成功，不成功要抛出异常，回滚数据
        setNextRoleCode(approveNodeRecordRequest, nextNode, approveNodeRecord);
        approveList.add(approveNodeRecord);

        ReleaseApproveNode noticeNode = addAutomaticApproveAndNoticeNextNode(approveNodeRecordRequest,
                releaseApproveNodes, listSize, nextNode, index, approveList);

        approveNodeRecordMapper.insertList(approveList);

        if (noticeNode != null) {
            processApproveEmail(approveNodeRecordRequest, noticeNode);
        }

        gTechRedisTemplate.delete(RedisConstants.APPKEY, key);
        return Result.ok(approveList.get(approveList.size() - 1));
    }

    /**
     * 
     * @param approveNodeRecordRequest
     * @param releaseApproveNodes
     * @param listSize
     * @param nextNode
     * @param index
     * @param approveList
     * @return
     * <AUTHOR>
     * @date 2022年5月19日
     */
    private ReleaseApproveNode addAutomaticApproveAndNoticeNextNode(ApproveNodeRecordRequest approveNodeRecordRequest,
            List<ReleaseApproveNode> releaseApproveNodes, int listSize, ReleaseApproveNode nextNode, int index,
            List<ApproveNodeRecord> approveList) {

        ReleaseApproveNode noticeNode = null;
        if (Boolean.TRUE.equals(approveNodeRecordRequest.getStatus()) && nextNode != null) {
            Date nowDate = new Date();
            for (; index < listSize; index++) {
                ReleaseApproveNode releaseApproveNode = releaseApproveNodes.get(index);
                if (GvcoreConstants.AUTOMATIC_APPROVE.equals(releaseApproveNode.getRoleCode())) {
                    ApproveNodeRecord record = createAutomaticApproveNodeRecord(approveNodeRecordRequest,
                            releaseApproveNodes, listSize, nowDate, index, releaseApproveNode);
                    approveList.add(record);
                } else {
                    noticeNode = releaseApproveNode;
                    break;
                }
            }
        }
        return noticeNode;
    }

    /**
     * 
     * @param approveNodeRecordRequest
     * @param nextNode
     * @param approveNodeRecord
     * <AUTHOR>
     * @date 2022年5月18日
     */
    private void setNextRoleCode(ApproveNodeRecordRequest approveNodeRecordRequest, ReleaseApproveNode nextNode,
            ApproveNodeRecord approveNodeRecord) {
        if (Boolean.TRUE.equals(approveNodeRecordRequest.getStatus()) && nextNode != null) {
            approveNodeRecord.setNextRoleCode(nextNode.getRoleCode());
        }
    }

    /**
     * 
     * @param userRoleCodeArr
     * @param approvalConfigRoleCode
     * @return
     * <AUTHOR>
     * @date 2022年5月13日
     */
    private boolean containsRoleCode(String[] userRoleCodeArr, String approvalConfigRoleCode) {

        for (String roleCode : userRoleCodeArr) {
            if (approvalConfigRoleCode.equals(roleCode)) {
                return true;
            }
        }
        return false;
    }

    private void processApproveEmail(ApproveNodeRecordRequest approveNodeRecordRequest, ReleaseApproveNode noticeNode) {

        List<String> roleCodeList = new ArrayList<>();
        roleCodeList.add(noticeNode.getRoleCode());
		List<UserAccount> accountList = gvUserAccountService.queryUserByRolesAndDataPermissions(roleCodeList, approveNodeRecordRequest.getIssuerCode());
		if (CollectionUtils.isEmpty(accountList)) {
            log.info("approveNoticeEmail account isEmpty BusinessCode={}, ReleaseType={}, ReleaseType={}, NodeName={}",
                    approveNodeRecordRequest.getBusinessCode(), approveNodeRecordRequest.getReleaseApproveAmountType(),
                    approveNodeRecordRequest.getReleaseType(), noticeNode.getNodeName());
            return;
        }

        log.info("approveNoticeEmail BusinessCode={}, ReleaseType={}, ReleaseType={}, NodeName={}",
                approveNodeRecordRequest.getBusinessCode(), approveNodeRecordRequest.getReleaseApproveAmountType(),
                approveNodeRecordRequest.getReleaseType(), noticeNode.getNodeName());

        SendEmailRequest request = new SendEmailRequest();
        request.setBusinessCode(approveNodeRecordRequest.getBusinessCode());
        request.setFlow(approveNodeRecordRequest.getReleaseApproveAmountType());
        request.setNode(approveNodeRecordRequest.getReleaseType());
        request.setEmails(accountList.stream().map(UserAccount::getEmail).filter(StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList()));
		request.setExtendParams(approveNodeRecordRequest.getExtendParams());
        messageComponent.sendApproveEmail(request);
    }


    @Override
    public Result<String> createLogRecord(CreateLogRecode createLogRecode) {
        ApproveNodeRecord approveNodeRecord = BeanCopyUtils.jsonCopyBean(createLogRecode, ApproveNodeRecord.class);
        approveNodeRecord.setApproveNodeRecordCode(gvCodeHelper.generateApproveNodeRecordCode());
        approveNodeRecord.setReleaseApproveAmountType(createLogRecode.getApproveType());
        approveNodeRecord.setReleaseApproveNodeName(0);
        approveNodeRecord.setCreateUser(createLogRecode.getApproveUser());
        approveNodeRecord.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        approveNodeRecordMapper.insertSelective(approveNodeRecord);
        return Result.ok(approveNodeRecord.getApproveNodeRecordCode());
    }

    @Override
    public Result<List<ApproveNodeRecordResponse>> queryLogByBusinessCode(String businessCode, String approveType) {
        Example example = new Example(ApproveNodeRecord.class);
        example.createCriteria().andEqualTo(ApproveNodeRecord.C_BUSINESS_CODE, businessCode).andEqualTo(ApproveNodeRecord.C_APPROVE_TYPE, approveType);
        example.orderBy(ApproveNodeRecord.C_CREATE_TIME).desc().orderBy(ApproveNodeRecord.C_ID).desc();
        List<ApproveNodeRecord> approveNodeRecords = approveNodeRecordMapper.selectByCondition(example);
        List<ApproveNodeRecordResponse> approveNodeRecordResponses = new ArrayList<>(approveNodeRecords.size());
        for (ApproveNodeRecord approveNodeRecord : approveNodeRecords) {
            if (StringUtil.isNotBlank(approveNodeRecord.getNote())) {
                ApproveNodeRecordResponse approveNodeRecordResponse = BeanCopyUtils.jsonCopyBean(approveNodeRecord, ApproveNodeRecordResponse.class);
                approveNodeRecordResponse.setApproveType(approveNodeRecord.getReleaseApproveAmountType());
                approveNodeRecordResponse.setApproveTime(approveNodeRecord.getCreateTime());
                approveNodeRecordResponses.add(approveNodeRecordResponse);
            }
        }
        return Result.ok(approveNodeRecordResponses);
    }

    @Override
    public Result<GetNextAmountResponse> getNextAmount(GetNextAmountRequest getNextAmountRequest) {
        GetNextAmountResponse getNextAmountResponse = new GetNextAmountResponse();
        getNextAmountResponse.setRangeName(getNextAmountRequest.getRangeName() + 1);
        getNextAmountResponse.setStartNum(getNextAmountRequest.getEndNum().add(BigDecimal.ONE));
        return Result.ok(getNextAmountResponse);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByDeleted(String businessCode, String updateUser, String releaseApproveAmountType) {

        Objects.requireNonNull(businessCode);
        Objects.requireNonNull(updateUser);
        Objects.requireNonNull(releaseApproveAmountType);

        ApproveNodeRecord approveNodeRecord = new ApproveNodeRecord();
        approveNodeRecord.setBusinessCode(businessCode);
        approveNodeRecord.setDeleteStatus(GvcoreConstants.DELETE_STATUS_ENABLE);
        approveNodeRecord.setUpdateUser(updateUser);
        approveNodeRecord.setUpdateTime(new Date());
        approveNodeRecord.setReleaseApproveAmountType(releaseApproveAmountType);
        approveNodeRecordMapper.updateByDeleted(approveNodeRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public <T> void automaticApproveAndNoticeNextNode(ApproveNodeRecordRequest request,
            Function<ApproveNodeRecordRequest, Result<T>> handlMethod) {
        Objects.requireNonNull(request);
		Objects.requireNonNull(request.getIssuerCode());
        Objects.requireNonNull(request.getBusinessCode());
        Objects.requireNonNull(request.getReleaseApproveAmountType());
        Objects.requireNonNull(request.getReleaseType());
        Objects.requireNonNull(request.getVoucherAmount());

        request.setRoleCode(GvcoreConstants.AUTOMATIC_APPROVE);
        request.setApproveUser(GvcoreConstants.AUTOMATIC_APPROVE);
        request.setStatus(true);
        
        String key = RedisConstants.LOCK_KEY_AUTOMATIC_APPROVE + request.getBusinessCode();
        boolean lockFlag = gTechRedisTemplate.opsValueSetIfAbsent(RedisConstants.APPKEY, key, key,
                RedisConstants.CACHE_TIMEOUT);
        log.info("automaticApproveAndNoticeNextNode BusinessCode={}, lockFlag={}", request.getBusinessCode(), lockFlag);
        if (!lockFlag) {
            return;
        }

        List<ReleaseApproveNode> releaseApproveNodes = releaseApproveNodeMapper
				.selectNodesByAmountAndType(request.getIssuerCode(), request.getVoucherAmount(), request.getReleaseApproveAmountType());
        if (CollectionUtils.isEmpty(releaseApproveNodes)) {
            log.warn("automaticApproveAndNoticeNextNode This voucherAmount not configured, cannot be approved, BusinessCode={}, releaseApproveType={}",
                    request.getBusinessCode(), request.getReleaseApproveAmountType());
            gTechRedisTemplate.delete(RedisConstants.APPKEY, key);
            return;
        }

        Map<Integer, ApproveNodeRecord> recordMap = queryApproveNodeRecordMap(request.getBusinessCode(),
                request.getReleaseType());

        int listSize = releaseApproveNodes.size();
        List<ApproveNodeRecord> automaticApproveList = new ArrayList<>(listSize);
        Date nowDate = new Date();
        ReleaseApproveNode noticeNextNode = null;
        for (int i = 0; i < listSize; i++) {

            ReleaseApproveNode releaseApproveNode = releaseApproveNodes.get(i);
            if (!recordMap.containsKey(releaseApproveNode.getNodeName())) {
                if (GvcoreConstants.AUTOMATIC_APPROVE.equals(releaseApproveNode.getRoleCode())) {
                    ApproveNodeRecord approveNodeRecord = createAutomaticApproveNodeRecord(request, releaseApproveNodes,
                            listSize, nowDate, i, releaseApproveNode);
                    automaticApproveList.add(approveNodeRecord);
                } else {
                    noticeNextNode = releaseApproveNode;
                    break;
                }
            }
        }

        if (automaticApproveList.isEmpty() && noticeNextNode == null) {
            gTechRedisTemplate.delete(RedisConstants.APPKEY, key);
            log.info("automaticApproveAndNoticeNextNode automaticApproveList isEmpty && nextNode is null, BusinessCode={}", request.getBusinessCode());
            return;
        }

        if (!automaticApproveList.isEmpty()) {
            approveNodeRecordMapper.insertList(automaticApproveList);
        }


        if (noticeNextNode != null) {
            processApproveEmail(request, noticeNextNode);
            return;
        }

        Result<T> result = handlMethod.apply(request);
        if (result == null || !Boolean.TRUE.equals(result.isSuccess())) {
            gTechRedisTemplate.delete(RedisConstants.APPKEY, key);
            throw new GTechBaseException(ResultErrorCodeEnum.FAILED.code(),
                    " automatic Approve failed, BusinessCode={0}, result={1}", request.getBusinessCode(),
                    JSON.toJSONString(result));
        }

    }

    /**
     * 
     * @param request
     * @param releaseApproveNodes
     * @param listSize
     * @param nowDate
     * @param i
     * @param releaseApproveNode
     * @return
     * <AUTHOR>
     * @date 2022年5月19日
     */
    private ApproveNodeRecord createAutomaticApproveNodeRecord(ApproveNodeRecordRequest request,
            List<ReleaseApproveNode> releaseApproveNodes, int listSize, Date nowDate, int i,
            ReleaseApproveNode releaseApproveNode) {

        ApproveNodeRecord approveNodeRecord = BeanCopyUtils.jsonCopyBean(request, ApproveNodeRecord.class);
        approveNodeRecord.setCreateUser(request.getApproveUser());
        approveNodeRecord.setReleaseApproveAmountType(request.getReleaseType());
        approveNodeRecord.setApproveNodeRecordCode(gvCodeHelper.generateApproveNodeRecordCode());
        approveNodeRecord.setReleaseApproveNodeName(releaseApproveNode.getNodeName());
        String nextRoleCode = getNextRoleCode(releaseApproveNodes, i, listSize);
        approveNodeRecord.setNextRoleCode(nextRoleCode);
        approveNodeRecord.setApproveRoleCode(GvcoreConstants.AUTOMATIC_APPROVE);
        approveNodeRecord.setNote(GvcoreConstants.AUTOMATIC_APPROVE);
        approveNodeRecord.setStatus(true);
        approveNodeRecord.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        approveNodeRecord.setCreateTime(nowDate);
        approveNodeRecord.setUpdateTime(nowDate);
        return approveNodeRecord;
    }

}
