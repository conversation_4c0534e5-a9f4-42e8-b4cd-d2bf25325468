package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.reportmanagement.CreateReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.DelReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.QueryReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.UpdateReportManagementRequest;
import com.gtech.gvcore.common.response.reportmanagement.QueryReportManagementResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/11 16:57
 */


public interface ReportManagementService {
    /**
     * Create a Report Management permission data
     *
     * @param createReportManagementRequest request data
     * @return result
     */
    Result<List<String>> createReportManagements(List<CreateReportManagementRequest> createReportManagementRequest);

    Result<Void> updatePermissions(UpdateReportManagementRequest updateReportManagementRequest);

    Result<Void> delReportManagement(DelReportManagementRequest delReportManagementRequest);

    PageResult<QueryReportManagementResponse> queryReportManagements(QueryReportManagementRequest reportManagementRequest);
}
