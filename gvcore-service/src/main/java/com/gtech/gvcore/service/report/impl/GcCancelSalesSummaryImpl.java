package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GcCancelSalesSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GcCancelSalesBo;
import com.gtech.gvcore.service.report.impl.param.GcCancelSalesQueryData;
import com.gtech.gvcore.service.report.impl.support.GcCancelSalesBaseImpl;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:58
 * @Description:
 */
@Service
public class GcCancelSalesSummaryImpl extends GcCancelSalesBaseImpl<GcCancelSalesSummaryBean>
        implements BusinessReport<GcCancelSalesQueryData, GcCancelSalesSummaryBean>, SingleReport {

    @Override
    public List<GcCancelSalesSummaryBean> getExportData(GcCancelSalesQueryData queryData) {

        Collection<CancelSalesSummaryBo> list = getList(queryData);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, CancelSalesSummaryBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, CancelSalesSummaryBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, CancelSalesSummaryBo::getCustomerCode, Customer.class);

        //convert result
        List<GcCancelSalesSummaryBean> collect = list.stream()
                .map(e -> {
                    Customer value = customerMap.findValue(e.getCustomerCode());
                    String customerName;
                    if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                        customerName = value.getCompanyName();
                    } else {
                        customerName = value.getCustomerName();
                    }
                    GcCancelSalesSummaryBean gcCancelSalesSummaryBean = new GcCancelSalesSummaryBean();
                    gcCancelSalesSummaryBean.setTotalAmount(toAmount(ConvertUtils.toBigDecimal(e.getTotalAmount(), BigDecimal.ZERO)));
                    gcCancelSalesSummaryBean.setCpgCode(cpgMap.findValue(e.getCpgCode()).getCpgName());
                    gcCancelSalesSummaryBean.setTotalCards(e.getNumberOfVouchers());
                    gcCancelSalesSummaryBean.setMerchantCode(merchantMap.findValue(e.getMerchantCode()).getMerchantName());
                    gcCancelSalesSummaryBean.setCustomerName(customerName);
                    gcCancelSalesSummaryBean.setInvoiceNumber(e.getInvoiceNumber());
                    gcCancelSalesSummaryBean.setNotes(e.getNotes());
                    return gcCancelSalesSummaryBean;
                })
                .collect(Collectors.toList());
        GcCancelSalesSummaryBean bean = new GcCancelSalesSummaryBean();
        bean.setMerchantCode("Total");
        bean.setTotalCards(list.stream().map(CancelSalesSummaryBo::getNumberOfVouchers).reduce(0, Integer::sum));
        bean.setTotalAmount(list.stream().map(CancelSalesSummaryBo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
        collect.add(bean);
        return collect;
    }

    private Collection<CancelSalesSummaryBo> getList(GcCancelSalesQueryData queryData) {

        //find
        List<GcCancelSalesBo> boList = super.getBoList(queryData);

        return boList.stream()
                .map(CancelSalesSummaryBo::convert)
                .collect(Collectors.toMap(
                                // key
                                CancelSalesSummaryBo::getGroupKey,
                                // value
                                CancelSalesSummaryBo::newInstance,
                                // merge
                                CancelSalesSummaryBo::merge)
                        //to map
                ).values();
    }


    @Override
    public ReportExportTypeEnum exportTypeEnum() {

        //ENUM
        return ReportExportTypeEnum.GC_CANCEL_SALES_SUMMARY_REPORT;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class CancelSalesSummaryBo {
        private String merchantCode;
        private String cpgCode;
        private int numberOfVouchers = 1;
        private BigDecimal totalAmount = BigDecimal.ZERO;
        private String customerCode;
        private String invoiceNumber;
        private String notes;


        public static CancelSalesSummaryBo convert(GcCancelSalesBo bo) {
            return new CancelSalesSummaryBo()
                    .setMerchantCode(bo.getMerchantCode())
                    .setCpgCode(bo.getCpgCode())
                    .setCustomerCode(bo.getCustomerCode())
                    .setTotalAmount(bo.getDenomination())
                    .setInvoiceNumber(bo.getInvoiceNumber())
                    .setNotes(bo.getNotes());
        }

        public static String getGroupKey(CancelSalesSummaryBo bean) {

            return StringUtils.join("_", bean.getMerchantCode(), bean.getCpgCode(), bean.getInvoiceNumber(), bean.getCustomerCode());
        }

        public static CancelSalesSummaryBo newInstance(CancelSalesSummaryBo bean) {

            return new CancelSalesSummaryBo()
                    .setMerchantCode(bean.getMerchantCode())
                    .setCpgCode(bean.getCpgCode())
                    .setCustomerCode(bean.getCustomerCode())
                    .setTotalAmount(bean.getTotalAmount())
                    .setInvoiceNumber(bean.getInvoiceNumber())
                    .setNotes(bean.getNotes());
        }

        public CancelSalesSummaryBo merge(CancelSalesSummaryBo bo) {
            numberOfVouchers += ConvertUtils.toInteger(bo.getNumberOfVouchers(), 0);
            totalAmount = totalAmount.add(bo.getTotalAmount());
            return this;
        }

    }
}
