package com.gtech.gvcore.service.report.impl.support.life.excel;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class VoucherLifeCycleSheet {

    private String sheetName;

    private String fillKey;

    private List<?> data;

    public static VoucherLifeCycleSheet newSheet(String key, ReportExportTypeEnum reportExportTypeEnum, List<?> data) {

        return new VoucherLifeCycleSheet()
                .setFillKey(key)
                .setSheetName(reportExportTypeEnum.getSheetName())
                .setData(data);
    }


}