package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;

/**
 * @ClassName UserQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/2/13 11:14
 * @Version V1.0
 **/
@Component
public class UserQueryImpl implements QuerySupport<UserAccount> {

    private static final UserAccount EMPTY = new UserAccount();

    @Autowired
    private GvUserAccountService userAccountService;

    @Override
    public List<UserAccount> queryByCode(List<String> codes, String... selectFields) {

        return userAccountService.queryUserByCodes(codes, selectFields);
    }

    @Override
    public Function<UserAccount, String> codeMapper() {
        return UserAccount::getUserCode;
    }

    @Override
    public UserAccount emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<UserAccount> supportType() {
        return UserAccount.class;
    }


}
