package com.gtech.gvcore.service;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.customerproductcategory.*;
import com.gtech.gvcore.common.response.customerproductcategory.CustomerProductCategoryResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:27
 */
public interface CustomerProductCategoryService {

    Result<Void> createCustomerProductCategory(CreateCustomerProductCategoryRequest param);

    Result<Void> updateCustomerProductCategory(UpdateCustomerProductCategoryRequest param);

    Result<Void> deleteCustomerProductCategory(DeleteCustomerProductCategoryRequest param);

    Result<Void> deleteCustomerProductCategoryByCustomer(String customer);

    PageResult<CustomerProductCategoryResponse> queryCustomerProductCategoryList(QueryCustomerProductCategoryRequest param);

    List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomer(QueryCustomerProductCategoryRequest param);

    List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomer(String customer);
    List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomerList(List<String> customerList);

    CustomerProductCategoryResponse getCustomerProductCategory(GetCustomerProductCategoryRequest param);



}
