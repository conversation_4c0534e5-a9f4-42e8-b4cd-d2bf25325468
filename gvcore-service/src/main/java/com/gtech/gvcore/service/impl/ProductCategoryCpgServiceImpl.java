package com.gtech.gvcore.service.impl;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.mapper.ProductCategoryCpgMapper;
import com.gtech.gvcore.dao.model.ProductCategoryCpg;
import com.gtech.gvcore.service.ProductCategoryCpgService;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
@Service
public class ProductCategoryCpgServiceImpl implements ProductCategoryCpgService {

    @Autowired
    private ProductCategoryCpgMapper productCategoryCpgMapper;

    @Override
    public List<ProductCategoryCpg> queryByProductCategoryCode(String productCategoryCode, Integer deleteStatus) {
        if (StringUtils.isBlank(productCategoryCode)) {
            return Collections.emptyList();
        }

        ProductCategoryCpg categoryCpg = new ProductCategoryCpg();
        categoryCpg.setProductCategoryCode(productCategoryCode);
        categoryCpg.setDeleteStatus(deleteStatus);
        List<ProductCategoryCpg> categoryCpgs = productCategoryCpgMapper.select(categoryCpg);
        if (CollectionUtils.isEmpty(categoryCpgs)) {
            return Collections.emptyList();
        }
        return categoryCpgs;
    }

    @Transactional
    @Override
    public int insertList(List<ProductCategoryCpg> list) {

        for (ProductCategoryCpg productCategoryCpg : list) {
            productCategoryCpg.setProductCategoryCpgCode(UUIDUtils.generateCode());
            productCategoryCpg.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        }

        return productCategoryCpgMapper.insertList(list);
    }

    @Transactional
    @Override
    public int updateByPrimaryKeySelective(ProductCategoryCpg productCategoryCpg) {
        productCategoryCpg.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
        return productCategoryCpgMapper.updateByPrimaryKeySelective(productCategoryCpg);
    }

    @Transactional
    @Override
    public int deleteStatusByPrimaryKey(ProductCategoryCpg productCategoryCpg) {

        productCategoryCpg.setStatus(GvcoreConstants.STATUS_DISABLE);
        productCategoryCpg.setDeleteStatus(GvcoreConstants.DELETE_STATUS_ENABLE);
        return productCategoryCpgMapper.updateByPrimaryKeySelective(productCategoryCpg);
    }

}
