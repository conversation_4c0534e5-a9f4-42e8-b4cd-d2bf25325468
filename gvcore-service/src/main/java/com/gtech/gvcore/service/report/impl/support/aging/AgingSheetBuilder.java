package com.gtech.gvcore.service.report.impl.support.aging;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.service.report.export.snapshoot.label.ReportLabelSupport;
import com.gtech.gvcore.service.report.extend.context.ReportContext;

/**
 * @ClassName AgingReportSheet
 * @Description aging report sheet interface
 * <AUTHOR>
 * @Date 2022/10/31 20:02
 * @Version V1.0
 **/
public interface AgingSheetBuilder extends ReportLabelSupport {

    String CUSTOMER_MAP_KEY = "CUSTOMER_MAP_KEY";
    String MERCHANT_MAP_KEY = "MERCHANT_MAP_KEY";
    String VOUCHER_MAP_KEY = "VOUCHER_MAP_KEY";
    String SALES_DATA_KEY = "SALES_DATA_KEY";
    String REDEEM_DATA_KEY = "REDEEM_DATA_KEY";
    String REDEEM_MERCHANT_MAP_KEY = "REDEEM_MERCHANT_MAP_KEY";
    String REDEEM_COMPANY_MAP_KEY = "REDEEM_COMPANY_MAP_KEY";

    ReportExportTypeEnum exportTypeEnum();

    AgingSheet builder(final ReportContext context);

}
