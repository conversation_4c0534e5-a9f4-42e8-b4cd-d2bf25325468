package com.gtech.gvcore.service;

import java.util.List;

import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.common.request.operatelog.CreateOperationLogRequest;
import com.gtech.gvcore.common.request.operatelog.QueryOperationLogRequest;
import com.gtech.gvcore.common.response.operatelog.GvOperateLogByPageResponse;
import com.gtech.gvcore.dao.model.GvOperateLogEntity;

/**
 * (GvOperateLog)表服务接口
 *
 * <AUTHOR>
 * @since 2022-02-28 10:22:13
 */
public interface GvOperateLogService {

    Result<String> createOperationLog(CreateOperationLogRequest createCpgTypeRequest);

    PageResult<GvOperateLogByPageResponse> queryOperationLog(QueryOperationLogRequest logRequest);
    
    /**
     * 
     * @param businessCode
     * @param method
     * @param operateUserCode
     * @param remarkDesc
     * <AUTHOR>
     * @date 2022年5月18日
     */
    void createSuccessLog(String businessCode, String method, String operateUserCode, String remarkDesc);
    
	List<GvOperateLogEntity> queryLogByBusiness(String businessCode);
}
