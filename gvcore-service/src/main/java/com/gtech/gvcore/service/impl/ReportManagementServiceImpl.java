package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.reportmanagement.CreateReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.DelReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.QueryReportManagementRequest;
import com.gtech.gvcore.common.request.reportmanagement.UpdateReportManagementRequest;
import com.gtech.gvcore.common.response.reportmanagement.QueryReportManagementResponse;
import com.gtech.gvcore.dao.mapper.ReportManagementMapper;
import com.gtech.gvcore.dao.model.ReportManagement;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.ReportManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/11 17:33
 */

@Service
public class ReportManagementServiceImpl implements ReportManagementService {

    @Autowired
    private ReportManagementMapper reportManagementMapper;
    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Override
    public Result<Void> updatePermissions(UpdateReportManagementRequest updateReportManagementRequest) {
        ReportManagement reportManagement;
        try {
            reportManagement = reportManagementMapper.selectByCode(updateReportManagementRequest.getReportManagementCode());
            if (reportManagement == null) {
                return Result.failed(ResultErrorCodeEnum.FIND_MORE_THAN_ONE_DATA.code(), ResultErrorCodeEnum.FIND_MORE_THAN_ONE_DATA.desc());
            }
        } catch (Exception e) {
            return Result.failed(ResultErrorCodeEnum.FIND_MORE_THAN_ONE_DATA.code(), ResultErrorCodeEnum.FIND_MORE_THAN_ONE_DATA.desc());
        }
        BeanCopyUtils.copyProps(updateReportManagementRequest, reportManagement);
        reportManagement.setUpdateTime(new Date());
        try {
            reportManagementMapper.updateByPrimaryKeySelective(reportManagement);
        } catch (Exception e) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }
        return Result.ok();
    }

    @Override
    @Transactional
    public Result<List<String>> createReportManagements(List<CreateReportManagementRequest> createReportManagementRequests) {
        List<String> returnList = new ArrayList<>();
        List<ReportManagement> reportManagements = BeanCopyUtils.jsonCopyList(createReportManagementRequests, ReportManagement.class);
        List<ReportManagement> reportManagementList = new ArrayList<>(reportManagements.size());
        for (ReportManagement reportManagement : reportManagements) {
            Example example = new Example(ReportManagement.class);
            example.createCriteria()
                    .andEqualTo(ReportManagement.C_MERCHANT_CODE, reportManagement.getMerchantCode())
                    .andEqualTo(ReportManagement.C_ROLE_CODE, reportManagement.getRoleCode())
                    .andEqualTo(ReportManagement.C_REPORT_TYPE, reportManagement.getReportType());
            reportManagementMapper.deleteByCondition(example);
            reportManagement.setReportManagementCode(gvCodeHelper.generateReportManagementCode());
            reportManagement.setCreateTime(new Date());
            reportManagement.setUpdateTime(new Date());
            returnList.add(reportManagement.getReportManagementCode());
            reportManagementList.add(reportManagement);
        }
        reportManagementMapper.insertList(reportManagementList);
        return Result.ok(returnList);
    }

    @Override
    public Result<Void> delReportManagement(DelReportManagementRequest delReportManagementRequest) {
        try {
            Example example = new Example(ReportManagement.class);
            example.createCriteria().andEqualTo(ReportManagement.C_REPORT_MANAGEMENT_CODE, delReportManagementRequest.getReportManagementCode());
            int i = reportManagementMapper.deleteByCondition(example);
            if (i < 1) {
                return Result.failed(ResultErrorCodeEnum.FAILED_TO_DELETE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_DELETE_DATA.desc());
            }
        } catch (Exception e) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_DELETE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_DELETE_DATA.desc());
        }
        return Result.ok();
    }

    @Override
    public PageResult<QueryReportManagementResponse> queryReportManagements(QueryReportManagementRequest reportManagementRequest) {
        PageMethod.startPage(reportManagementRequest.getPageNum(), reportManagementRequest.getPageSize());
        List<QueryReportManagementResponse> queryReportManagementResponses = reportManagementMapper.queryReportManagements(reportManagementRequest);
        PageInfo<QueryReportManagementResponse> responsePageInfo = PageInfo.of(queryReportManagementResponses);
        return new PageResult<>(responsePageInfo.getList(), responsePageInfo.getTotal());
    }
}
