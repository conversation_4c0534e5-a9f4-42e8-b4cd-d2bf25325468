package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportParamConvertHelper;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.RedemptionSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.RedemptionBo;
import com.gtech.gvcore.service.report.impl.bo.RedemptionSummaryStatisticalBo;
import com.gtech.gvcore.service.report.impl.param.RedemptionQueryData;
import com.gtech.gvcore.service.report.impl.support.RedemptionHelper;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:00
 * @Description:
 * @Version 1.1 3/
 */
@Service
public class RedemptionSummaryImpl extends ReportSupport
        implements BusinessReport<RedemptionQueryData, RedemptionSummaryBean>, SingleReport {

    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.REDEMPTION_SUMMARY_REPORT;
    }

    @Override
    public RedemptionQueryData builderQueryParam(CreateReportRequest reportParam) {

        ReportParamConvertHelper.convertQueryDateCompanyCodeToMerchantCode(reportParam);

        return RedemptionHelper.getQueryData(reportParam);
    }

    @Override
    public List<RedemptionSummaryBean> getExportData(RedemptionQueryData queryData) {

        Collection<RedemptionSummaryStatisticalBo> boList = this.queryBoList(queryData);

        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(boList, RedemptionSummaryStatisticalBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(boList, RedemptionSummaryStatisticalBo::getOutletCode, Outlet.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(boList, RedemptionSummaryStatisticalBo::getCpgCode, Cpg.class);

        return boList.stream()
                .map(e -> new RedemptionSummaryBean()
                        .setDate(e.getTransactionDateDay())
                        .setMerchantName(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setMerchantOutletName(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setMerchantOutletCode(outletMap.findValue(e.getOutletCode()).getBusinessOutletCode())
                        .setBillNumber(e.getInvoiceNumber())
                        .setNotes(e.getNotes())
                        .setVoucherProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setRedemptionCount(String.valueOf(e.getRedemptionCount()))
                        .setRedemptionAmount(super.toAmount(e.getDenomination()))

                ).collect(Collectors.toList());
    }

    private Collection<RedemptionSummaryStatisticalBo> queryBoList(RedemptionQueryData queryData) {

        // 查询最新并且是兑换的交易
        List<RedemptionBo> redemptionBoList = Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectRedemption, queryData))
                .map(e -> e.stream().filter(d -> TransactionTypeEnum.GIFT_CARD_REDEEM.equalsCode(d.getTransactionType())).collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        //根据条件分组
        return redemptionBoList.stream()
                .map(RedemptionSummaryStatisticalBo::convert)
                .collect(Collectors.toMap(
                                // key
                                RedemptionSummaryStatisticalBo::getGroupKey,
                                // value
                                RedemptionSummaryStatisticalBo::newInstance,
                                // merge
                                RedemptionSummaryStatisticalBo::merge)
                        //to map
                ).values();

    }


}
