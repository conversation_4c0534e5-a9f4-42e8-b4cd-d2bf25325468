package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.VoucherMovementSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.VoucherMovementBo;
import com.gtech.gvcore.service.report.impl.param.VoucherMovementSummaryQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * @ClassName PerformanceImpl
 * @Description Performance report
 * <AUTHOR>
 * @Date 2022/9/20 16:20
 * @Version V1.0
 **/
@Slf4j
@Service
public class VoucherMovementSummaryImpl extends ReportSupport
        implements BusinessReport<VoucherMovementSummaryQueryData, VoucherMovementSummaryBean>, SingleReport {

    public static final String CACHE_CUSTOMER_KEY = "CUSTOMER";

    public static final String HEAD_TEMPLATE =
                    "<div class=\"gt-view-report-result__highTitle\"> " +
                    "   SUMMARY REPORT %s " +
                    "</div>";

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.VOUCHER_MOVEMENT_SUMMARY_REPORT;
    }

    @Override
    public VoucherMovementSummaryQueryData builderQueryParam(CreateReportRequest reportParam) {

        VoucherMovementSummaryQueryData voucherMovementSummaryQueryData = new VoucherMovementSummaryQueryData();

        voucherMovementSummaryQueryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        voucherMovementSummaryQueryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        voucherMovementSummaryQueryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        voucherMovementSummaryQueryData.setMerchantCodeList(reportParam.getMerchantCodes());
        voucherMovementSummaryQueryData.setOutletCodeList(reportParam.getOutletCodes());

        if (CollectionUtils.isNotEmpty(reportParam.getCustomerCodes())) {
            voucherMovementSummaryQueryData.setCustomerCode(reportParam.getCustomerCodes().get(0));
        }

        voucherMovementSummaryQueryData.setCpgCodeList(reportParam.getCpgCodes());
        voucherMovementSummaryQueryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        voucherMovementSummaryQueryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        voucherMovementSummaryQueryData.setTransactionType(TransactionTypeEnum.GIFT_CARD_SELL.getCode());

        voucherMovementSummaryQueryData.setCustomerCode(CollectionUtils.isNotEmpty(reportParam.getCustomerCodes()) ? reportParam.getCustomerCodes().get(0) : null);

        ReportContextHelper.findContext().putCache(CACHE_CUSTOMER_KEY, super.nonNullGetByCode(voucherMovementSummaryQueryData.getCustomerCode(), Customer.class));

        return voucherMovementSummaryQueryData;
    }

    @Override
    public List<VoucherMovementSummaryBean> getExportData(VoucherMovementSummaryQueryData queryData) {

        // init container
        final Map<Cpg, VoucherMovementSummaryBean> vceSummary = new HashMap<>();
        final Map<Cpg, VoucherMovementSummaryBean> vcrSummary = new HashMap<>();

        //find data
        final List<String> salesArray = Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectVoucherMovement, queryData))
                // filter gift voucher sell
                .map(e -> e.stream().filter(s -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(s.getTransactionType())).map(VoucherMovementBo::getVoucherCode).collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        if (CollectionUtils.isEmpty(salesArray)) return new ArrayList<>();

        this.groupVoucher(vceSummary, vcrSummary, salesArray);

        //package
        return packageSummaryBeanList(vceSummary, vcrSummary);
    }

    private List<VoucherMovementSummaryBean> packageSummaryBeanList(Map<Cpg, VoucherMovementSummaryBean> vceSummary, Map<Cpg, VoucherMovementSummaryBean> vcrSummary) {


        //init result
        final List<VoucherMovementSummaryBean> summaryBeanList = new ArrayList<>();

        //setting product name
        vceSummary.forEach((k, v) -> v.setProduct(k.getCpgName()));
        vcrSummary.forEach((k, v) -> v.setProduct(k.getCpgName()));

        //vce
        Collection<VoucherMovementSummaryBean> vceList = vceSummary.values();
        summaryBeanList.addAll(summaryDetailTotal(vceList, "Sub Total Digital voucher"));
        //vcr
        Collection<VoucherMovementSummaryBean> vcrList = vcrSummary.values();
        summaryBeanList.addAll(summaryDetailTotal(vcrList, "Sub Total Paper Voucher"));
        //total
        List<VoucherMovementSummaryBean> all = new ArrayList<>();
        all.addAll(vceList);
        all.addAll(vcrList);
        summaryBeanList.add(summaryTotal(all, "Total"));

        //setting balance
        settingBalance(summaryBeanList);

        //setting format
        settingAmountFormat(summaryBeanList);

        return summaryBeanList;
    }

    private void settingAmountFormat(List<VoucherMovementSummaryBean> array) {

        //is null
        if (CollectionUtils.isEmpty(array)) return;

        array.forEach(e -> e.setBalanceAmount(super.toAmount(new BigDecimal(e.getBalanceAmount())))
                .setActivationAmount(super.toAmount(new BigDecimal(e.getActivationAmount())))
                .setSalesAmount(super.toAmount(new BigDecimal(e.getSalesAmount())))
                .setRedemptionAmount(super.toAmount(new BigDecimal(e.getRedemptionAmount())))
                .setExpiredAmount(super.toAmount(new BigDecimal(e.getExpiredAmount()))));
    }

    private List<VoucherMovementSummaryBean> summaryDetailTotal(Collection<VoucherMovementSummaryBean> array, String title){

        VoucherMovementSummaryBean voucherMovementSummaryBean = this.summaryTotal(array, title);

        if (null == voucherMovementSummaryBean) return new ArrayList<>();

        ArrayList<VoucherMovementSummaryBean> result = new ArrayList<>(array);
        result.add(voucherMovementSummaryBean);

        return result;
    }

    private VoucherMovementSummaryBean summaryTotal(Collection<VoucherMovementSummaryBean> array, String title) {

        //is null
        if (CollectionUtils.isEmpty(array)) return null;

        //init
        VoucherMovementSummaryBean total = new VoucherMovementSummaryBean().setProduct(title);

        //foreach add
        array.forEach(b -> total.addSalesQuantity(b.getSalesQuantity()).addSalesAmount(ConvertUtils.toBigDecimal(b.getSalesAmount(), BigDecimal.ZERO))
                .addActivationQuantity(b.getActivationQuantity()).addActivationAmount(ConvertUtils.toBigDecimal(b.getActivationAmount(), BigDecimal.ZERO))
                .addRedemptionQuantity(b.getRedemptionQuantity()).addRedemptionAmount(ConvertUtils.toBigDecimal(b.getRedemptionAmount(), BigDecimal.ZERO))
                .addExpiredQuantity(b.getExpiredQuantity()).addExpiredAmount(ConvertUtils.toBigDecimal(b.getExpiredAmount(), BigDecimal.ZERO)));

        //return
        return total;
    }

    public void settingBalance(List<VoucherMovementSummaryBean> array) {

        //is null
        if (CollectionUtils.isEmpty(array)) return;

        //setting balance
        array.forEach(b -> b.setBalanceQuantity(String.valueOf(ConvertUtils.toInteger(b.getSalesQuantity(), 0) - ConvertUtils.toInteger(b.getRedemptionQuantity(), 0)))
                .setBalanceAmount(String.valueOf(ConvertUtils.toBigDecimal(b.getSalesAmount(), BigDecimal.ZERO).subtract(ConvertUtils.toBigDecimal(b.getRedemptionAmount(), BigDecimal.ZERO)))));
    }

    private void groupVoucher(final Map<Cpg, VoucherMovementSummaryBean> vceSummary,
                              final Map<Cpg, VoucherMovementSummaryBean> vcrSummary,
                              final List<String> salesArray) {

        Date now = new Date();

        //find
        final List<Voucher> vouchers = new ArrayList<>(super.getMapByCode(salesArray, Voucher.class).values());
        final Map<String, Cpg> cpgMap = super.getMapByCode(vouchers, Voucher::getCpgCode, Cpg.class);

        BiConsumer<VoucherMovementSummaryBean, Voucher> saleFunction = (b, v) -> b.addSalesQuantity().addSalesAmount(v.getDenomination());
        BiConsumer<VoucherMovementSummaryBean, Voucher> activationFunction = (b, v) -> b.addActivationQuantity().addActivationAmount(v.getDenomination());
        BiConsumer<VoucherMovementSummaryBean, Voucher> redemptionFunction = (b, v) -> b.addRedemptionQuantity().addRedemptionAmount(v.getDenomination());
        BiConsumer<VoucherMovementSummaryBean, Voucher> expiredFunction = (b, v) -> b.addExpiredQuantity().addExpiredAmount(v.getDenomination());

        //for group
        vouchers.stream()
                .filter(v -> cpgMap.containsKey(v.getCpgCode()))
                .filter(v -> GvcoreConstants.MOP_CODE_VCE.equals(v.getMopCode()) || GvcoreConstants.MOP_CODE_VCR.equals(v.getMopCode()))
                .filter(v -> VoucherStatusEnum.valueOfCode(v.getStatus()) != null)
                .forEach(v -> {

            Cpg cpg = cpgMap.get(v.getCpgCode());

            //summary
            Map<Cpg, VoucherMovementSummaryBean> nowMap = GvcoreConstants.MOP_CODE_VCE.equals(v.getMopCode()) ? vceSummary : vcrSummary;
            VoucherMovementSummaryBean voucherMovementSummaryBean = nowMap.computeIfAbsent(cpg, k -> new VoucherMovementSummaryBean());

            saleFunction.accept(voucherMovementSummaryBean, v);

            VoucherStatusEnum voucherStatus = VoucherStatusEnum.valueOfCode(v.getStatus());
            switch (Objects.requireNonNull(voucherStatus)) {//此处需要利用 switch 特性执行
                case VOUCHER_USED: redemptionFunction.accept(voucherMovementSummaryBean, v);//NOSONAR
                case VOUCHER_ACTIVATED: activationFunction.accept(voucherMovementSummaryBean, v);//NOSONAR
                default:
                    break;
            }

            if (null != v.getVoucherEffectiveDate() && now.compareTo(v.getVoucherEffectiveDate()) >= 0) expiredFunction.accept(voucherMovementSummaryBean, v);
        });

    }

    @Override
    public Object getHeadObject(ReportContext context) {

        final VoucherMovementSummaryQueryData queryData = (VoucherMovementSummaryQueryData) context.getQueryParam();

        return new Head()
                .setCustomerName(this.getCustomerName(context.getCache(CACHE_CUSTOMER_KEY, Customer.class)))
                .setSelectTime(DateUtil.format(null == queryData.getTransactionDateEnd() ? new Date() : queryData.getTransactionDateEnd(), DateUtil.FORMAT_DEFAULT));
    }

    @Override
    public String headHtml(ReportContext context) {

        return String.format(HEAD_TEMPLATE, this.getCustomerName(context.getCache(CACHE_CUSTOMER_KEY, Customer.class)));
    }

    private String getCustomerName (Customer customer) {

        return CustomerTypeEnum.CORPORATE.equalsCode(customer.getCustomerType()) ? customer.getCompanyName() : customer.getCustomerName();
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Head {

        private String customerName;

        private String selectTime;

    }
}
