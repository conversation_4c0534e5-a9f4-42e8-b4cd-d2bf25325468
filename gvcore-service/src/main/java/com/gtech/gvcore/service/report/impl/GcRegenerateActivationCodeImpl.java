package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GiftCardMapper;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GcRegenerateActivationCodeBean;
import com.gtech.gvcore.service.report.impl.param.GcRegenerateActivationCodeQueryData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName GcRegenerateActivationCodeImpl
 * @Description Gift Card Regenerate Activation Code Report Implementation
 * <AUTHOR> based on RegenerateActivationCodeImpl
 * @Date 2025/6/19
 * @Version V1.0
 **/
@Service
@Slf4j
public class GcRegenerateActivationCodeImpl
        extends ReportSupport
        implements BusinessReport<GcRegenerateActivationCodeQueryData, GcRegenerateActivationCodeBean>, PollReport {

    @Autowired
    private IssueHandlingDetailsService issueHandlingDetailsService;

    @Autowired
    private GiftCardMapper giftCardMapper;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_REGENERATE_ACTIVATION_CODE_REPORT;
    }

    @Override
    public GcRegenerateActivationCodeQueryData builderQueryParam(CreateReportRequest reportParam) {
        GcRegenerateActivationCodeQueryData param = new GcRegenerateActivationCodeQueryData();

        // 设置时间范围
        param.setStartDate(reportParam.getTransactionDateStart());
        param.setEndDate(reportParam.getTransactionDateEnd());

        // 设置请求ID
        param.setIssueHandlingCode(reportParam.getVoucherRequestId());
        param.setFileName(reportParam.getUploadedFileName());

        return param;
    }

    @Override
    public List<GcRegenerateActivationCodeBean> getExportData(GcRegenerateActivationCodeQueryData param) {
        // 获取数据
        List<GcRegenerateActivationCodeBean> boList = getBoList(param);

        // 如果为空直接返回
        if (CollectionUtils.isEmpty(boList)) {
            return new ArrayList<>();
        }

        // 查询关联的礼品卡信息
        final JoinDataMap<GiftCardEntity> giftCardJoinDataMap = getGiftCardJoinDataMap(boList);
        final JoinDataMap<GcCpg> gcCpgJoinDataMap = getGcCpgJoinDataMap(giftCardJoinDataMap.values());

        // 填充关联数据
        boList.forEach(bo -> fillRelatedData(bo, giftCardJoinDataMap, gcCpgJoinDataMap));

        return boList;
    }

    /**
     * 获取基础数据列表
     */
    private List<GcRegenerateActivationCodeBean> getBoList(GcRegenerateActivationCodeQueryData param) {
        // 查询 Issue Handling Details 数据，使用 GC_RESET_ACTIVATION 类型
        List<IssueHandlingDetails> issueHandlingDetails = issueHandlingDetailsService.exportIssueHandlingDetails(
                IssueHandlingTypeEnum.GC_RESET_ACTIVATION,  // 替换为 GC_RESET_ACTIVATION
                param.getIssueHandlingCode(),
                param.getStartDate(),
                param.getEndDate(),
                param.getStartCardNumber(),
                param.getEndCardNumber(),
                param.getCustomerEmail(),
                param.getCardNumbers(),
                param.getPageSize(),
                param.getPageNum());

        // 按请求号分组并聚合数据
        Map<String, List<IssueHandlingDetails>> groupedByRequestNumber = issueHandlingDetails.stream()
                .collect(Collectors.groupingBy(IssueHandlingDetails::getIssueHandlingCode));

        // 转换为报表Bean
        return groupedByRequestNumber.entrySet().stream()
                .map(entry -> createSummaryBean(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * 创建汇总Bean
     */
    private GcRegenerateActivationCodeBean createSummaryBean(String requestNumber, List<IssueHandlingDetails> details) {
        GcRegenerateActivationCodeBean bean = new GcRegenerateActivationCodeBean();

        // 设置请求号
        bean.setIssueHandlingCode(requestNumber);

        // 设置总卡数
        bean.setTotalCards(details.size());

        // 设置请求日期（取第一条记录的创建时间）
        if (!details.isEmpty()) {
            bean.setRequestDate(details.get(0).getCreateTime());
            bean.setRequestor(details.get(0).getReceiverEmail()); // 使用接收邮箱作为请求者
        }

        // 设置状态（可以根据实际业务逻辑确定）
        bean.setStatus(determineStatus(details));

        return bean;
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String requestNumber) {
        return "list_noviyanti@csid.org_" + requestNumber + ".csv";
    }

    /**
     * 确定状态
     */
    private String determineStatus(List<IssueHandlingDetails> details) {
        // 根据业务逻辑确定状态，这里简化处理
        return Objects.requireNonNull(IssueHandlingProcessStatusEnum.valueOfCode(details.get(0).getProcessStatus())).desc(); // 可以是 Completed, Rejected, Processing 等
    }

    /**
     * 获取礼品卡关联数据
     */
    private JoinDataMap<GiftCardEntity> getGiftCardJoinDataMap(List<GcRegenerateActivationCodeBean> boList) {
        List<String> cardNumbers = boList.stream()
                .map(GcRegenerateActivationCodeBean::getIssueHandlingCode)
                .collect(Collectors.toList());

        // 这里需要根据实际的查询逻辑来获取礼品卡信息
        // 暂时返回空的 JoinDataMap
        return new JoinDataMap<>();
    }

    /**
     * 获取 GC CPG 关联数据
     */
    private JoinDataMap<GcCpg> getGcCpgJoinDataMap(Iterable<GiftCardEntity> giftCards) {
//        return super.getMapByCode(giftCards, GiftCardEntity::getCpgCode, GcCpg.class);
        return null;
    }

    /**
     * 填充关联数据
     */
    private void fillRelatedData(GcRegenerateActivationCodeBean bo,
                                 JoinDataMap<GiftCardEntity> giftCardJoinDataMap,
                                 JoinDataMap<GcCpg> gcCpgJoinDataMap) {

        // 根据实际业务逻辑填充总金额等信息
        // 这里需要根据礼品卡信息计算总金额
        bo.setTotalAmount(calculateTotalAmount(bo.getIssueHandlingCode()));
    }

    /**
     * 计算总金额
     */
    private BigDecimal calculateTotalAmount(String requestNumber) {
        // 根据请求号查询相关礼品卡并计算总金额
        // 这里简化处理，返回示例金额
        return new BigDecimal("2500000"); // 示例金额
    }
}
