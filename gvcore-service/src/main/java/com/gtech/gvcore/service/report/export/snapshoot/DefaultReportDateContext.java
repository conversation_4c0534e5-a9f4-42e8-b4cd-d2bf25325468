package com.gtech.gvcore.service.report.export.snapshoot;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.dao.model.ReportRequest;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据快照上下文
 */
@Slf4j
public class DefaultReportDateContext implements ReportDateContext {

    /**
     * 构造
     */
    public DefaultReportDateContext(ReportRequest orderReport,
                                    ReportDataHelper reportDataHelper,
                                    int pageSize) {
        this.reportDataHelper = reportDataHelper;
        this.pageSize = pageSize;
        this.reportCode = orderReport.getReportCode();
        this.reportExportTypeEnum = ReportExportTypeEnum.getExportTypeEnumByType(orderReport.getReportType());
    }

    //服务支持类
    private final ReportDataHelper reportDataHelper;

    private final String reportCode;

    private final ReportExportTypeEnum reportExportTypeEnum;

    //缓存
    private final List<Object> cacheList = new ArrayList<>();

    //碎片大小
    private final int pageSize;

    //起始索引
    private int pageIndex = 0;

    /**
     * 追加数据
     */
    @Override
    public void appendData(List<?> list) {

        cacheList.addAll(list);

        while (cacheList.size() >= pageSize) saveReportData();
    }

    /**
     * 保存数据
     */
    private void saveReportData() {

        List<Object> saveList = new ArrayList<>(cacheList.subList(0, pageSize));

        cacheList.removeAll(saveList);

        reportDataHelper.savePieceReportData(reportCode, reportExportTypeEnum, saveList, pageIndex);

        pageIndex += pageSize;
    }

    @Override
    public void init() {

        ReportContext context = ReportContextHelper.findContext();

        String headHtml = context.getBusinessReport().headHtml(context);
        if (StringUtils.isNotBlank(headHtml)) {
            reportDataHelper.saveReportHeadHtml(reportCode, headHtml);
        }
    }

    /**
     * 结束
     */
    @Override
    public void finish() {

        if (notUse()) return;

        //实际存储的数据total
        int realTotal = pageIndex + cacheList.size();

        reportDataHelper.finishPieceReportData(reportCode, reportExportTypeEnum, cacheList, pageIndex, pageSize, realTotal);

    }

    /**
     * 是否没有使用
     */
    private boolean notUse() {
        return pageIndex == 0 && cacheList.isEmpty();
    }

    /**
     * 错误处理
     */
    @Override
    public void error() {

        if (notUse()) return;

        reportDataHelper.errorRemovePieceReportData(reportCode);
    }

    @Override
    public void fastSaveAll(Map<ReportExportTypeEnum, List<?>> reportData) {

        if (!notUse()) {
            log.error("ReportDateContext 使用错误, 不应在分片存储的模式中调用 ReportDateContext#fastSaveAll");
            return;
        }

        this.reportDataHelper.saveReportData(reportCode, reportData);
    }
}