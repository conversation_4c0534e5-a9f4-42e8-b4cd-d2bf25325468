package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.giftcard.util.PeriodCalculator;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcExtendExpiryForDetailBean;
import com.gtech.gvcore.service.report.impl.bo.GcExtendExpiryBo;
import com.gtech.gvcore.service.report.impl.param.GcExtendExpiryQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description: Gift Card Extend Expiry Report Detail Implementation
 */
@Service
public class GcExtendExpiryForDetailImpl extends ReportSupport
        implements BusinessReport<GcExtendExpiryQueryData, GcExtendExpiryForDetailBean>, SingleReport {

    @Override
    public GcExtendExpiryQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcExtendExpiryQueryData queryData = new GcExtendExpiryQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());

        // 交易时间范围对应延长时间
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());

        return queryData;
    }

    @Override
    public List<GcExtendExpiryForDetailBean> getExportData(GcExtendExpiryQueryData queryData) {
        List<GcExtendExpiryBo> boList = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectGcExtendExpiry, queryData, boList::addAll);
        //find
        final Collection<GcExtendExpiryBo> list =
                Optional.of(boList)
                        .orElse(Collections.emptyList());

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, GcExtendExpiryBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcExtendExpiryBo::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(outletMap.values(), Outlet::getMerchantCode, Merchant.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, GcExtendExpiryBo::getOwnerCustomer, Customer.class);

        //convert result
        return list.stream()
                .map(e -> {
                    String activationPeriodEnded = DateUtil.format(PeriodCalculator.calculateSubDate(e.getActivationGracePeriod(), DateUtil.parseDate(e.getActivationDeadline(), DateUtil.FORMAT_YYYYMMDDHHMISS)), DateUtil.FORMAT_YYYYMMDDHHMISS);
                    String gracePeriodEnded = e.getActivationDeadline();
                    String customerName;
                    Customer value = customerMap.findValue(e.getOwnerCustomer());
                    if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                        customerName = value.getCompanyName();
                    } else {
                        customerName = value.getCustomerName();
                    }
                    Outlet outlet = outletMap.findValue(e.getOutletCode());
                    return new GcExtendExpiryForDetailBean()
                            .setGiftCardNumber(e.getCardNumber())
                            .setMerchant(merchantMap.findValue(outlet.getMerchantCode()).getMerchantName())
                            .setMerchantOutlet(outlet.getOutletName())
                            .setTransactionType("Expiry Extension") // 固定为延长过期类型
                            .setGiftCardProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                            .setTransactionDate(e.getExtensionTime())
                            .setIssuanceDate(e.getSalesTime())
                            .setActivationPeriodEnded(activationPeriodEnded)
                            .setCustomerName(customerName)
                            .setGracePeriodEnded(gracePeriodEnded)
                            .setSource(e.getSource());
                })
                .collect(Collectors.toList());
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_EXTEND_EXPIRY_DETAILED;
    }


}
