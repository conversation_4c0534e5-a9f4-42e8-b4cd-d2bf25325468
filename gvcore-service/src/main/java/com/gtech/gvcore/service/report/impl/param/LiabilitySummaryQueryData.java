package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName LiabilitySummaryQueryData
 * @Description 责任概况报表查询条件模型
 * <AUTHOR>
 * @Date 2022/7/12 15:58
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class LiabilitySummaryQueryData implements ReportQueryParam {

    //issuer code
    private String issuerCode;

    //merchant code list
    private List<String> merchantCodeList;

    //outlet code list
    private List<String> outletCodeList;

    //cpg code list
    private List<String> cpgCodeList;

    //create user 创建人
    private String createUser;

    //table code 表名
    private String tableCode;

}
