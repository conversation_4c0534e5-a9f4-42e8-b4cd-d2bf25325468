package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.basic.filecloud.exports.core.annotation.FileColumn;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CardsInventorySummaryResponse")
public class BookletInventorySummaryBo implements Serializable {

    private static final long serialVersionUID = 3504421824830522592L;

    @FileColumn(name = "Issuer")
    private String issuer;

    @FileColumn(name = "Merchant")
    private String merchant;

    @FileColumn(name = "OutletCode")
    private String outletCode;

    @FileColumn(name = "OutletName")
    private String outletName;

    @FileColumn(name = "Voucher Program Group")
    private String voucherProgramGroup;

    @FileColumn(name = "Card Program Group Type")
    private String cardProgramGroupType;

    @FileColumn(name = "expiryDate")
    private String expiryDate;

    @FileColumn(name = "Booklet Count")
    private Integer bookletCount;

    @FileColumn(name = "Booklet Status")
    private String bookletStatus;


}
