package com.gtech.gvcore.service.report.impl.bean.aging;

import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportLabel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName AgeBySbuDetailsOneBean
 * @Description Age By Sbu Details One Bean
 * <AUTHOR>
 * @Date 2022/11/4 15:43
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class AgeBySbuDetailsTwoBean {

    @ReportLabel(value = {"Sum of Amount", "Company_Grouping"})
    private String customerName;

    @ReportLabel(value = {"Sum of Amount", "classification"})
    private String sbuName;

    @ReportLabel(value = {"Sum of Amount", "MERCHANTNAME"})
    private String merchantName;

    @ReportLabel(value = {"Usage_AgeBracket", "1 month"})
    private String oneValue;

    @ReportLabel(value = {"Usage_AgeBracket", "2 month"})
    private String twoValue;

    @ReportLabel(value = {"Usage_AgeBracket", "3 month"})
    private String threeValue;

    @ReportLabel(value = {"Usage_AgeBracket", "4 to 6 month"})
    private String fourValue;

    @ReportLabel(value = {"Usage_AgeBracket", "6 to 12 month"})
    private String sixValue;

    @ReportLabel(value = {"Usage_AgeBracket", "Grand Total"})
    private String total;

}
