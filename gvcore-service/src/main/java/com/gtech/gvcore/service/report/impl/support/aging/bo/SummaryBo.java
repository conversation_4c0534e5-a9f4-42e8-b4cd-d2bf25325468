package com.gtech.gvcore.service.report.impl.support.aging.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class SummaryBo {

    private String customerCode;

    private String customerName;

    private String sbuName;

    private Integer auOne;

    private Integer auTwo;

    private Integer auThree;

    private Integer auFour;

    private Integer auSix;

    private Integer auYear;

    private Integer anuOne;

    private Integer anuTwo;

    private Integer anuThree;

    private Integer anuFour;

    private Integer anuSix;

    private Integer anuYear;

    public static SummaryBo newInstance() {

        return new SummaryBo()
                .setAuOne(0)
                .setAuTwo(0)
                .setAuThree(0)
                .setAuFour(0)
                .setAuSix(0)
                .setAuYear(0)
                .setAnuOne(0)
                .setAnuTwo(0)
                .setAnuThree(0)
                .setAnuFour(0)
                .setAnuSix(0)
                .setAnuYear(0);
    }

    //bean copy
    public Integer getAuUsageValue() {

        return auOne
                + auTwo
                + auThree
                + auFour
                + auSix
                + auYear;
    }

    //bean copy
    public Integer getAnuUnredeemValue() {
        
        return anuOne
                + anuTwo
                + anuThree
                + anuFour
                + anuSix
                + anuYear;
    }


    public SummaryBo addAuOne() {
        this.auOne++;
        return this;
    }

    public SummaryBo addAuTwo() {
        this.auTwo++;
        return this;
    }

    public SummaryBo addAuThree() {
        this.auThree++;
        return this;
    }

    public SummaryBo addAuFour() {
        this.auFour++;
        return this;
    }

    public SummaryBo addAuSix() {
        this.auSix++;
        return this;
    }

    public SummaryBo addAuYear() {
        this.auYear++;
        return this;
    }

    public SummaryBo addAnuOne() {
        this.anuOne++;
        return this;
    }

    public SummaryBo addAnuTwo() {
        this.anuTwo++;
        return this;
    }

    public SummaryBo addAnuThree() {
        this.anuThree++;
        return this;
    }

    public SummaryBo addAnuFour() {
        this.anuFour++;
        return this;
    }

    public SummaryBo addAnuSix() {
        this.anuSix++;
        return this;
    }

    public SummaryBo addAnuYear() {
        this.anuYear++;
        return this;
    }

    public SummaryBo addAuOne(int number) {
        this.auOne += number;
        return this;
    }

    public SummaryBo addAuTwo(int number) {
        this.auTwo += number;
        return this;
    }

    public SummaryBo addAuThree(int number) {
        this.auThree += number;
        return this;
    }

    public SummaryBo addAuFour(int number) {
        this.auFour += number;
        return this;
    }

    public SummaryBo addAuSix(int number) {
        this.auSix += number;
        return this;
    }

    public SummaryBo addAuYear(int number) {
        this.auYear += number;
        return this;
    }


    public SummaryBo addAnuOne(int number) {
        this.anuOne += number;
        return this;
    }

    public SummaryBo addAnuTwo(int number) {
        this.anuTwo += number;
        return this;
    }

    public SummaryBo addAnuThree(int number) {
        this.anuThree += number;
        return this;
    }

    public SummaryBo addAnuFour(int number) {
        this.anuFour += number;
        return this;
    }

    public SummaryBo addAnuSix(int number) {
        this.anuSix += number;
        return this;
    }

    public SummaryBo addAnuYear(int number) {
        this.anuYear += number;
        return this;
    }

}


