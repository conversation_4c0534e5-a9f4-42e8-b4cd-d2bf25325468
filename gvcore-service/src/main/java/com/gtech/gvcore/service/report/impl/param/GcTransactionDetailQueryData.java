package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName GcTransactionDetailQueryData
 * @Description Gift Card Transaction Detail Query Data
 * <AUTHOR>
 * @Date 2025-06-18
 * @Version V1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcTransactionDetailQueryData extends TransactionDataPageParam implements ReportQueryParam {

    /**
     * Transaction date start
     */
    private Date transactionDateStart;

    /**
     * Transaction date end
     */
    private Date transactionDateEnd;

    /**
     * Issuer codes list
     */
    private List<String> issuerCodes;

    /**
     * Merchant codes list
     */
    private List<String> merchantCodes;

    /**
     * Outlet codes list
     */
    private List<String> outletCodes;

    /**
     * CPG codes list
     */
    private List<String> cpgCodes;

    /**
     * Transaction types list
     */
    private List<String> transactionType;

    /**
     * Gift card status list
     */
    private List<String> cardStatusList;

    /**
     * Invoice number
     */
    private String invoiceNumber;

    /**
     * Customer codes list
     */
    private List<String> customerCodeList;

    /**
     * Expiry status start date
     */
    private Date expiryDateStart;
    /**
     * Expiry status end date
     */
    private Date expiryDateEnd;

    /**
     * Card number start
     */
    private List<String> cardNumberList;


}
