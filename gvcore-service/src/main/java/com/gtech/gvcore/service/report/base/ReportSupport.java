package com.gtech.gvcore.service.report.base;

import com.gtech.gvcore.dao.mapper.ReportBusinessMapper;
import com.gtech.gvcore.service.report.extend.ReportAmountSupport;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.extend.joindate.GetJoinDateMapSupport;
import com.gtech.gvcore.service.report.extend.voucherstatus.ReportVoucherStatusConvertSupport;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName BusinessReportSupport
 * @Description 业务报表支持类
 * <AUTHOR>
 * @Date 2022/10/24 17:09
 * @Version V1.0
 **/
public abstract class ReportSupport implements
        // join 数据
        GetJoinDateMapSupport,
        // 百分比处理
        ReportProportionDataFunction,
        // 卡券状态
        ReportVoucherStatusConvertSupport,
        // 金额处理
        ReportAmountSupport {

    //mapper
    @Autowired protected ReportBusinessMapper reportBusinessMapper;
}
