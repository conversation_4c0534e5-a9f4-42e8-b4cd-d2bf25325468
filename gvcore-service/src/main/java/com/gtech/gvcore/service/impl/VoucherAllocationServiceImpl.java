package com.gtech.gvcore.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.basic.masterdata.web.entity.MasterDataDistrictEntity;
import com.gtech.basic.masterdata.web.service.MasterDataDistrictService;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.BusinessLogStatusEnum;
import com.gtech.gvcore.common.enums.FlowEnum;
import com.gtech.gvcore.common.enums.FlowNodeEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherAllocationBusinessTypeEnum;
import com.gtech.gvcore.common.enums.VoucherAllocationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherCirculationStatusEnum;
import com.gtech.gvcore.common.enums.VoucherOwnerTypeEnum;
import com.gtech.gvcore.common.enums.VoucherRequestStatusEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.allocation.AllocateRequest;
import com.gtech.gvcore.common.request.allocation.AllocateVoucherBatch;
import com.gtech.gvcore.common.request.allocation.GetVoucherAllocationRequest;
import com.gtech.gvcore.common.request.allocation.QueryVoucherAllocationByPageRequest;
import com.gtech.gvcore.common.request.businesslog.CreateBusinessLogRequest;
import com.gtech.gvcore.common.request.businesslogdetail.CreateBusinessLogDetailRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.cpg.QueryCpgVoucherInventory;
import com.gtech.gvcore.common.request.customerorder.IssuanceRequest;
import com.gtech.gvcore.common.request.customerorder.IssuanceVoucherBatch;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.receive.CreateVoucherReceiveRequest;
import com.gtech.gvcore.common.request.receive.QueryVoucherReceiveBatchRequest;
import com.gtech.gvcore.common.request.receive.VoucherReceiveBatchRequest;
import com.gtech.gvcore.common.response.allocation.AllocationRequestDetails;
import com.gtech.gvcore.common.response.allocation.GetVoucherAllocationResponse;
import com.gtech.gvcore.common.response.allocation.QueryVoucherAllocationByPageResponse;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.common.response.useraccount.PermissionCodeResponse;
import com.gtech.gvcore.common.response.voucherreceive.VoucherReceiveBatchResponse;
import com.gtech.gvcore.components.QueryVoucherComponent;
import com.gtech.gvcore.dao.dto.CpgVoucherCodeNumDto;
import com.gtech.gvcore.dao.dto.VoucherAllocationDto;
import com.gtech.gvcore.dao.dto.VoucherCodeNumDto;
import com.gtech.gvcore.dao.dto.VoucherDto;
import com.gtech.gvcore.dao.mapper.VoucherAllocationMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherAllocation;
import com.gtech.gvcore.dao.model.VoucherAllocationBatch;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.dao.model.VoucherReceive;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.dao.model.VoucherRequestDetails;
import com.gtech.gvcore.dto.VoucherAllocationPermissionDto;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.PermissionHelper;
import com.gtech.gvcore.service.BusinessLogService;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.FlowNoticeService;
import com.gtech.gvcore.service.GvOperateLogService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.service.TransactionDataService;
import com.gtech.gvcore.service.VoucherAllocationBatchService;
import com.gtech.gvcore.service.VoucherAllocationService;
import com.gtech.gvcore.service.VoucherBookletService;
import com.gtech.gvcore.service.VoucherReceiveBatchService;
import com.gtech.gvcore.service.VoucherReceiveService;
import com.gtech.gvcore.service.VoucherRequestDetailsService;
import com.gtech.gvcore.service.VoucherRequestService;
import com.gtech.gvcore.service.VoucherService;
import com.gtech.gvcore.threadpool.ThreadPoolCenter;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2022年3月8日
 */
@Slf4j
@Service
public class VoucherAllocationServiceImpl implements VoucherAllocationService {

	@Value("#{${gv.issuer.warehouse}}")
	private Map<String, String> issuerWarehouseMap;

	@Value("#{${gv.issuer.businesswarehouse}}")
	private Map<String, String> issuerBusinessWarehouseMap;

    @Autowired
    private VoucherAllocationMapper voucherAllocationMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private VoucherRequestService voucherRequestService;

    @Lazy
    @Autowired
    private VoucherService voucherService;

    @Autowired
    private VoucherBookletService bookletService;

    @Autowired
    private VoucherRequestDetailsService voucherRequestDetailsService;

    @Autowired
    private VoucherAllocationBatchService voucherAllocationBatchService;

	@Autowired
	private VoucherReceiveService voucherReceiveService;



	@Autowired
	private BusinessLogService businessLogService;

	@Autowired
	private MasterDataDistrictService masterDataDistrictService;

	@Autowired
	private OutletService outletService;

	@Autowired
	private GvOperateLogService gvOperateLogService;

    @Autowired
    private FlowNoticeService flowNoticeService;

	@Autowired
	private GvUserAccountService userAccountService;

	@Autowired
	private VoucherReceiveBatchService voucherReceiveBatchService;

    @Autowired
    private PermissionHelper permissionHelper;

    @Autowired
    private TransactionDataService transactionDataService;

    @Autowired
    private VoucherMapper voucherMapper;

    @Autowired
    private CpgService cpgService;

    @Autowired
    private QueryVoucherComponent queryVoucherComponent;


    private String bookletMsgPrefix = "Booklet Number=";

    @Transactional
    @Override
	public String createByVoucherRequest(VoucherRequest voucherRequest) {

        VoucherAllocation voucherAllocation = new VoucherAllocation();
        BeanUtils.copyProperties(voucherRequest, voucherAllocation);
        voucherAllocation.setId(null);
        voucherAllocation.setSourceDataCode(voucherRequest.getVoucherRequestCode());
        voucherAllocation.setVoucherAllocationCode(gvCodeHelper.voucherAllocationCode());
        voucherAllocation.setStatus(VoucherAllocationStatusEnum.PENDING_ALLOCATION.code());
        voucherAllocation.setCreateTime(new Date());
        voucherAllocation.setCreateUser(voucherRequest.getUpdateUser());
		voucherAllocationMapper.insertSelective(voucherAllocation);
		return voucherAllocation.getVoucherAllocationCode();
    }

    //@Transactional(rollbackFor = Exception.class)
    @Override
    public int createOrUpdate(CustomerOrder customerOrder) {

    	VoucherAllocation voucherAllocation = new VoucherAllocation();
    	voucherAllocation.setSourceDataCode(customerOrder.getCustomerOrderCode());
    	voucherAllocation.setIssuerCode(customerOrder.getIssuerCode());
    	voucherAllocation.setBusinessType(VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
        VoucherAllocation allocation = voucherAllocationMapper.selectOne(voucherAllocation);

    	voucherAllocation.setVoucherOwnerCode(customerOrder.getOutletCode());
    	String voucherOwnerName = StringUtils.EMPTY;
    	Outlet outlet = outletService.queryByOutletCode(customerOrder.getOutletCode());
    	if(outlet != null) {
    		voucherOwnerName = outlet.getOutletName();
    	}
    	voucherAllocation.setVoucherOwnerName(voucherOwnerName);
    	voucherAllocation.setReceiverCode(customerOrder.getCustomerCode());
    	voucherAllocation.setReceiverName(customerOrder.getCustomerName());
    	voucherAllocation.setVoucherAllocationCode(gvCodeHelper.voucherAllocationCode());
        voucherAllocation.setStatus(VoucherAllocationStatusEnum.PENDING_ALLOCATION.code());
        voucherAllocation.setCreateTime(new Date());
        voucherAllocation.setCreateUser(customerOrder.getUpdateUser());

        if (allocation != null) {
            voucherAllocation.setId(allocation.getId());
            voucherAllocation.setUpdateTime(voucherAllocation.getCreateTime());
            return voucherAllocationMapper.updateByPrimaryKey(voucherAllocation);
        } else {
            return voucherAllocationMapper.insertSelective(voucherAllocation);
        }
	}

	//@Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Void> allocate(AllocateRequest request) {
		log.info("allocate AllocateRequest={}", JSON.toJSONString(request));
        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setVoucherAllocationCode(request.getVoucherAllocationCode());
        voucherAllocation = voucherAllocationMapper.selectOne(voucherAllocation);
        if (voucherAllocation == null) {
            log.error("allocate voucherAllocation is null, voucherAllocationCode={}", request.getVoucherAllocationCode());
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        Result<List<VoucherCodeNumDto>> checkResult = checkAllocateRequest(request, voucherAllocation);
        if (!checkResult.isSuccess()) {
            log.error("allocate checkAllocateRequest fail, voucherAllocationCode={}, error={}",
                    request.getVoucherAllocationCode(), checkResult.getMessage());
            return Result.failed(checkResult.getCode(), checkResult.getMessage());
        }

        VoucherAllocation finalVoucherAllocation = voucherAllocation;
        ThreadPoolCenter.commonThreadPoolExecute(()->{

            log.info("开始分配");
            List<VoucherCodeNumDto> voucherCodeNumDtoList = checkResult.getData();

            int total = allocateVoucher(request.getUpdateUser(), finalVoucherAllocation, voucherCodeNumDtoList);

            log.info("已经分配{}",total);
            Result<Void> result = voucherRequestService.updateRequestStatus(finalVoucherAllocation.getSourceDataCode(),
                    VoucherRequestStatusEnum.PENDING_RECEIPT.getCode(), request.getUpdateUser());
            if (!result.isSuccess()) {
                throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                        "{0} voucherRequest updateRequestStatus fail", finalVoucherAllocation.getSourceDataCode());
            }

            CreateBusinessLogRequest logRequest = buildBusinessLogRequest(request);
            logRequest.setStatus(BusinessLogStatusEnum.SUCCESS.code());
            logRequest.setSuccess(total);
            logRequest.setFailed(0);
            businessLogService.createBusinessLog(logRequest);

            if (VoucherAllocationBusinessTypeEnum.SALES.code().equals(finalVoucherAllocation.getBusinessType())) {

                gvOperateLogService.createSuccessLog(finalVoucherAllocation.getSourceDataCode(), "Allocate",
                        request.getUpdateUser(), "Allocate");
            }

            sendEmail(finalVoucherAllocation);

        });


        return Result.ok();
    }

	@SuppressWarnings("unchecked")
	private void sendEmail(VoucherAllocation voucherAllocation) {
		VoucherReceive voucherReceive = voucherReceiveService.getVoucherReceiveBySourceDataCode(voucherAllocation.getVoucherAllocationCode());
		if (voucherReceive == null) {
			return;
		}
		QueryVoucherReceiveBatchRequest queryVoucherReceiveBatchRequest = new QueryVoucherReceiveBatchRequest();
		queryVoucherReceiveBatchRequest.setVoucherReceiveCode(voucherReceive.getVoucherReceiveCode());
		List<VoucherReceiveBatchResponse> receiveBatchList = voucherReceiveBatchService.queryReceiveBatchList(queryVoucherReceiveBatchRequest);
		if (CollectionUtils.isEmpty(receiveBatchList)) {
			return;
		}
		String vpgNames = "";
		BigDecimal voucherAmount = BigDecimal.ZERO;
		for (VoucherReceiveBatchResponse voucherReceiveBatchResponse : receiveBatchList) {
			String cpgName = voucherReceiveBatchResponse.getCpgName();
			if (StringUtils.isNotEmpty(cpgName)) {
				vpgNames = StringUtils.isEmpty(vpgNames) ? cpgName : vpgNames + "," + cpgName;
			}
			Integer voucherNum = voucherReceiveBatchResponse.getVoucherNum();
			BigDecimal denomination = voucherReceiveBatchResponse.getDenomination();
			voucherAmount = denomination.multiply(BigDecimal.valueOf(voucherNum)).add(voucherAmount);
		}
		Map<String, Object> map = new HashMap<>();
		map.putAll(JSONObject.parseObject(JSON.toJSONString(voucherReceive), Map.class));
		map.put("cpgName", vpgNames);
		map.put("receiveBatchList", receiveBatchList);
		map.put("voucherAmount", voucherAmount);

		String createUser = voucherReceive.getCreateUser();
		UserAccount userAccount = userAccountService.getUserNameInfo(createUser);
		if (userAccount != null) {
			createUser = userAccount.getFullName();
		}
		map.put("createUser", createUser);
        String flowCode = getFlowCode(voucherAllocation);
        SendNoticeRequest sendNoticeRequest = new SendNoticeRequest();
        sendNoticeRequest.setFlowCode(flowCode);
		String type = "";
		if (VoucherAllocationBusinessTypeEnum.RETURN.code().equals(voucherAllocation.getBusinessType())) {
			type = VoucherAllocationBusinessTypeEnum.RETURN.desc();
		} else if (VoucherAllocationBusinessTypeEnum.TRANSFER.code().equals(voucherAllocation.getBusinessType())) {
			type = VoucherAllocationBusinessTypeEnum.TRANSFER.desc();
		}
		map.put(VoucherRequest.C_VOUCHER_REQUEST_CODE, voucherAllocation.getSourceDataCode());
		VoucherRequest voucherRequest = voucherRequestService.queryByVoucherRequestCode(voucherAllocation.getSourceDataCode());
		if (voucherRequest != null) {
			map.put(VoucherBatch.C_CREATE_TIME, DateUtil.format(voucherRequest.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
		}
		map.put(VoucherBatch.C_UPDATE_TIME, DateUtil.format(voucherReceive.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
		map.put("type", type);
		sendNoticeRequest.setExtendParams(map);

        sendNoticeRequest.setFlowNodeCode(FlowNodeEnum.ALLOCATION.getCode());
		sendNoticeRequest.setBusinessCode(voucherAllocation.getSourceDataCode());
        flowNoticeService.send(sendNoticeRequest);
	}

    private String getFlowCode(VoucherAllocation voucherAllocation) {
        String flowCode = FlowEnum.SALES_VOUCHER_FLOW.getCode();
        if (VoucherAllocationBusinessTypeEnum.RETURN.code().equals(voucherAllocation.getBusinessType())) {
            flowCode = FlowEnum.RETURN_VOUCHER_FLOW.getCode();
        } else if (VoucherAllocationBusinessTypeEnum.TRANSFER.code().equals(voucherAllocation.getBusinessType())) {
            flowCode = FlowEnum.TRANSFER_ORDER_FLOW.getCode();
        }
        return flowCode;
    }

	private CreateBusinessLogRequest buildBusinessLogRequest(AllocateRequest request) {
		CreateBusinessLogRequest logRequest = new CreateBusinessLogRequest();
    	logRequest.setContentCode(request.getVoucherAllocationCode());
    	logRequest.setCreateUser(request.getUpdateUser());
    	logRequest.setContent(JSON.toJSONString(request.getVoucherBatchList()));
		return logRequest;
	}

	private int allocateVoucher(String updateUser, VoucherAllocation voucherAllocation,
			List<VoucherCodeNumDto> voucherCodeNumDtoList) {
        log.info("allocate voucher start voucherAllocationCode:{},voucherCodeNumDtoList:{}", voucherAllocation.getVoucherAllocationCode(),
                JSON.toJSONString(voucherCodeNumDtoList));
		List<CpgVoucherCodeNumDto> cpgNumDtos = voucherService
                .groupByDenominationAndCpg(voucherAllocation.getIssuerCode(), voucherCodeNumDtoList);
        List<CpgVoucherCodeNumDto> mergeCpgNumList = new ArrayList<>(cpgNumDtos.size());
        CpgVoucherCodeNumDto numDto = null;
		List<String> codeList = new ArrayList<>(cpgNumDtos.size() * 2);
        for (CpgVoucherCodeNumDto dto : cpgNumDtos) {
        	boolean addFlag = true;
			if (isMergeLine(numDto, dto)) {
				if(numDto.getVoucherCodeNumEnd() < dto.getVoucherCodeNumEnd()) {
					numDto.setVoucherCodeNumEnd(dto.getVoucherCodeNumEnd());
				}
        		addFlag = false;
            }
            if(addFlag) {
            	numDto = dto;
                mergeCpgNumList.add(dto);
            }
            codeList.add(dto.getVoucherCodeNumStart().toString());
			codeList.add(dto.getVoucherCodeNumEnd().toString());
        }

        Map<Long, Voucher> numCodeMap = voucherService.queryByVoucherCodeNumList(voucherAllocation.getIssuerCode(),
        		codeList);
        int listSize = mergeCpgNumList.size();
        List<VoucherAllocationBatch> allocationBatchList = new ArrayList<>(listSize);
        //List<VoucherLogTask> logTaskList = new ArrayList<>(listSize);
        Date now = new Date();
        int total = 0;
        String changeVoucherOwnerCode = voucherAllocation.getReceiverCode();
        String changeVoucherOwnerType = VoucherOwnerTypeEnum.OUTLET.code();
        if (VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code().equals(voucherAllocation.getBusinessType())) {
            changeVoucherOwnerCode = voucherAllocation.getVoucherOwnerCode();
        }

        if (changeVoucherOwnerCode.equals(issuerBusinessWarehouseMap.get(voucherAllocation.getIssuerCode()))) {
        	changeVoucherOwnerType = VoucherOwnerTypeEnum.WAREHOUSE.code();
        }
        for (CpgVoucherCodeNumDto dto : mergeCpgNumList) {
            VoucherAllocationBatch allocationBatch = new VoucherAllocationBatch();
            allocationBatch.setVoucherAllocationCode(voucherAllocation.getVoucherAllocationCode());
            allocationBatch.setCpgCode(dto.getCpgCode());
            Voucher startVoucher = numCodeMap.get(dto.getVoucherCodeNumStart());
            Voucher endVoucher = numCodeMap.get(dto.getVoucherCodeNumEnd());
            allocationBatch.setVoucherStartNo(startVoucher.getVoucherCode());
            allocationBatch.setBookletStartNo(startVoucher.getBookletCode());
            allocationBatch.setVoucherEndNo(endVoucher.getVoucherCode());
            allocationBatch.setBookletEndNo(endVoucher.getBookletCode());
            allocationBatch.setVoucherNum((int) (dto.getVoucherCodeNumEnd() - dto.getVoucherCodeNumStart() + 1));
            allocationBatch.setDenomination(dto.getDenomination());
            allocationBatch.setReceivedNum(0);
            allocationBatch.setCreateUser(updateUser);
            allocationBatch.setCreateTime(now);
            allocationBatchList.add(allocationBatch);
            total += allocationBatch.getVoucherNum();


        }

		int i = updateStatus(voucherAllocation.getVoucherAllocationCode(),
				VoucherAllocationStatusEnum.PENDING_RECEIPT.code(),
				VoucherAllocationStatusEnum.PENDING_ALLOCATION.code(), updateUser, now);
		if (i == 0) {
            log.error("Failed to update voucher allocation status by voucherAllocationCode:{}", voucherAllocation.getVoucherAllocationCode());
            throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_ALLOCATION_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_ALLOCATION_DATA.desc() + " {0}",
                    voucherAllocation.getVoucherAllocationCode());
        }
        log.info("Success to update voucher allocation status by voucherAllocationCode:{}", voucherAllocation.getVoucherAllocationCode());
        int i1 = voucherAllocationBatchService.insertList(allocationBatchList);
        if (i1 == 0) {
            log.error("Failed to insert voucher allocation batch data by voucherAllocationCode:{}", voucherAllocation.getVoucherAllocationCode());
            throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_INSERT_ALLOCATION_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_INSERT_ALLOCATION_DATA.desc() + " {0}",
                    voucherAllocation.getVoucherAllocationCode());
        }
        log.info("Success to insert voucher allocation batch data by voucherAllocationCode:{}", voucherAllocation.getVoucherAllocationCode());

        if(VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code().equals(voucherAllocation.getBusinessType())) {
        	changeVoucherOwnerCode = null;
        	changeVoucherOwnerType = null;
        }else {
            log.info("订单分配,创建receive记录");
        	// create receive request
            createVoucherReceive(updateUser, voucherAllocation, allocationBatchList, total);
        }

		i = voucherService.allocateVoucher(voucherAllocation.getIssuerCode(), voucherAllocation.getVoucherOwnerCode(),
				mergeCpgNumList, changeVoucherOwnerCode, changeVoucherOwnerType);
		if (i != total) {

            log.error("Failed to voucher data by allocate voucherAllocationCode:{}", voucherAllocation.getVoucherAllocationCode());
            throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_VOUCHER_DATA_BY_ALLOCATE.code(),
                    ResultErrorCodeEnum.FAILED_TO_VOUCHER_DATA_BY_ALLOCATE.desc() + " {0} ",
                    voucherAllocation.getVoucherAllocationCode());
        }
        log.info("Success to voucher data by allocate voucherAllocationCode:{}", voucherAllocation.getVoucherAllocationCode());

        return total;
	}

	private boolean isMergeLine(CpgVoucherCodeNumDto numDto, CpgVoucherCodeNumDto dto) {
		return numDto != null && numDto.getCpgCode().equals(dto.getCpgCode())
				&& numDto.getDenomination().doubleValue() == dto.getDenomination().doubleValue()
				&& dto.getVoucherCodeNumStart() <= (numDto.getVoucherCodeNumEnd() + 1);
	}

	private int updateStatus(String voucherAllocationCode, Integer status, Integer oldStatus, String updateUser, Date updateTime) {

		VoucherAllocationDto dto = new VoucherAllocationDto();
        dto.setVoucherAllocationCode(voucherAllocationCode);
        dto.setStatus(status);
        dto.setUpdateUser(updateUser);
        dto.setUpdateTime(updateTime);
        dto.setOldStatus(oldStatus);
		return voucherAllocationMapper.updateStatus(dto);
	}

	private void createVoucherReceive(String updateUser, VoucherAllocation voucherAllocation,
			List<VoucherAllocationBatch> allocationBatchList, int total) {
		CreateVoucherReceiveRequest createVoucherReceiveRequest = BeanCopyUtils.jsonCopyBean(voucherAllocation,
                CreateVoucherReceiveRequest.class);
        createVoucherReceiveRequest.setCreateUser(updateUser);
        createVoucherReceiveRequest.setSourceDataCode(voucherAllocation.getVoucherAllocationCode());
        createVoucherReceiveRequest.setSourceType(voucherAllocation.getBusinessType());
        createVoucherReceiveRequest.setIssuerCode(voucherAllocation.getIssuerCode());
        createVoucherReceiveRequest.setOutboundCode(voucherAllocation.getVoucherOwnerCode());
        createVoucherReceiveRequest.setOutbound(voucherAllocation.getVoucherOwnerName());
        createVoucherReceiveRequest.setInboundCode(voucherAllocation.getReceiverCode());
        createVoucherReceiveRequest.setInbound(voucherAllocation.getReceiverName());
        createVoucherReceiveRequest.setVoucherNum(total);
        createVoucherReceiveRequest.setReceivedNum(0);
        createVoucherReceiveRequest
                .setReceiveBatchList(BeanCopyUtils.jsonCopyList(allocationBatchList, VoucherReceiveBatchRequest.class));
        int voucherReceive = voucherReceiveService.createVoucherReceive(createVoucherReceiveRequest);
        if (voucherReceive == 0) {
            log.error("receive 记录插入数据失败");

        }else {
            log.info("receive 记录插入数据成功");
        }
        log.info("createVoucherReceive success, voucherAllocationCode:{}", voucherAllocation.getVoucherAllocationCode());
	}

	@Override
	public Result<String> allocateByCustomerOrder(IssuanceRequest request, CustomerOrder customerOrder,
			List<CustomerOrderDetails> details) {

		VoucherAllocation voucherAllocation = selectOneByCustomerOrder(customerOrder, VoucherAllocationStatusEnum.PENDING_ALLOCATION.code());
        if (voucherAllocation == null) {
            log.error("No allocation data found by customerOrderCode:{}", customerOrder.getCustomerOrderCode());
            return Result.failed(ResultErrorCodeEnum.NO_ALLOCATION_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_ALLOCATION_DATA_FOUND.desc());
        }
		log.info("allocateByCustomerOrder voucherAllocation:{}", JSON.toJSONString(voucherAllocation));

        if (!VoucherAllocationStatusEnum.PENDING_ALLOCATION.equalsCode(voucherAllocation.getStatus())) {
            log.error("CustomerOrder already allocated by customerOrderCode:{}", customerOrder.getCustomerOrderCode());
            return Result.failed(ResultErrorCodeEnum.CUSTOMERORDER_ALREADY_ALLOCATED.code(),
                    ResultErrorCodeEnum.CUSTOMERORDER_ALREADY_ALLOCATED.desc());
        }

        Result<List<VoucherCodeNumDto>> result = checkAllocateAndGetUpdateData(request, details,
				voucherAllocation);
        if(!result.isSuccess()) {
            log.error("Failed to check allocate data by customerOrderCode:{}", customerOrder.getCustomerOrderCode());
        	return Result.failed(result.getCode(), result.getMessage());
        }

        List<VoucherCodeNumDto> voucherCodeNumDtoList = result.getData();

        allocateVoucher(request.getUpdateUser(), voucherAllocation, voucherCodeNumDtoList);

        return Result.ok();
	}

	private Result<List<VoucherCodeNumDto>> checkAllocateAndGetUpdateData(IssuanceRequest request,
			List<CustomerOrderDetails> details, VoucherAllocation voucherAllocation) {

		Map<String, CustomerOrderDetails> cpgMap = details.stream()
				.collect(Collectors.toMap(CustomerOrderDetails::getCpgCode, v -> v));
		Result<List<VoucherCodeNumDto>> result = checkRequestAndGetCodeNum(request, cpgMap,
				voucherAllocation);
        if(!result.isSuccess()) {
        	return Result.failed(result.getCode(), result.getMessage());
        }

        List<VoucherCodeNumDto> voucherCodeNumDtoList = result.getData();
        List<VoucherDto> countDtos = voucherService.countGroupByCustomerOrderAllocation(voucherCodeNumDtoList);
        String voucherOwnerCode = voucherAllocation.getVoucherOwnerCode();
        String voucherOwnerType = VoucherOwnerTypeEnum.OUTLET.code();
        String issuerCode = voucherAllocation.getIssuerCode();

        List<VoucherDto> errorCirculationStatus = countDtos.stream()
                .filter(v -> !VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode().equals(v.getStatus())
                        || !voucherOwnerCode.equals(v.getVoucherOwnerCode())
                        || !voucherOwnerType.equals(v.getVoucherOwnerType())
                        || VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode() != v.getCirculationStatus()
                        || !GvcoreConstants.STATUS_ENABLE.equals(v.getVoucherStatus())
                        || !issuerCode.equals(v.getIssuerCode())
                        || !cpgMap.containsKey(v.getCpgCode()))
                .collect(Collectors.toList());
        if (!errorCirculationStatus.isEmpty()) {
            return Result.failed(ResultErrorCodeEnum.SOME_VOUCHER_CANNOT_ALLOCATE.code(),
                    ResultErrorCodeEnum.SOME_VOUCHER_CANNOT_ALLOCATE.desc());
        }

        Map<String, Integer> cpgQuantityMap = countDtos.stream().collect(Collectors.toMap(VoucherDto :: getCpgCode, VoucherDto::getCount));
        for (CustomerOrderDetails detail : details) {
            Integer quantity = cpgQuantityMap.get(detail.getCpgCode());
            if (quantity == null) {
                return Result.failed(ResultErrorCodeEnum.NO_VOUCHERS_ALLOCATED.code(),
                        ResultErrorCodeEnum.NO_VOUCHERS_ALLOCATED.desc() + " vpgCode="
                                + detail.getCpgCode());
            }
            if (!quantity.equals(detail.getVoucherNum())) {
                return Result.failed(ResultErrorCodeEnum.NUMBER_ARE_NOT_EQUAL.code(),
                        ResultErrorCodeEnum.NUMBER_ARE_NOT_EQUAL.desc() + " vpgCode="
                                + detail.getCpgCode());
            }
        }
		return Result.ok(voucherCodeNumDtoList);
	}

	private Result<List<VoucherCodeNumDto>> checkRequestAndGetCodeNum(IssuanceRequest request,
			Map<String, CustomerOrderDetails> cpgMap, VoucherAllocation voucherAllocation){

		List<String> voucherCodeList = new ArrayList<>(request.getVoucherBatchList().size() * 2);
        for (IssuanceVoucherBatch voucherBatch : request.getVoucherBatchList()) {
            voucherCodeList.add(voucherBatch.getVoucherStartNo());
            voucherCodeList.add(voucherBatch.getVoucherEndNo());
        }

        List<Voucher> voucherList = voucherService.queryByVoucherCodeList(null, voucherCodeList);
        Map<String, Voucher> voucherMap = voucherList.stream()
                .collect(Collectors.toMap(Voucher::getVoucherCode, v -> v));

        List<VoucherCodeNumDto> voucherCodeNumDtoList = new ArrayList<>(request.getVoucherBatchList().size());

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.SECOND, 1);
        Date nowDate = calendar.getTime();
        for (IssuanceVoucherBatch voucherBatch : request.getVoucherBatchList()) {
            Voucher voucherStart = voucherMap.get(voucherBatch.getVoucherStartNo());
            Voucher voucherEnd = voucherMap.get(voucherBatch.getVoucherEndNo());

            if (voucherStart == null) {
                return Result.failed(ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS.code(),
                        ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS.desc() + voucherBatch.getVoucherStartNo());
            }
            if (voucherEnd == null) {
                return Result.failed(ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS.code(),
                        ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS.desc() + voucherBatch.getVoucherEndNo());
            }

            ResultErrorCodeEnum codeEnum = checkVoucher(voucherStart, voucherAllocation, nowDate);
            if (codeEnum != null) {
                return Result.failed(codeEnum.code(), codeEnum.desc() + voucherBatch.getVoucherStartNo());
            }

            codeEnum = checkVoucher(voucherEnd, voucherAllocation, nowDate);
            if (codeEnum != null) {
                return Result.failed(codeEnum.code(), codeEnum.desc() + voucherBatch.getVoucherEndNo());
            }

            Result<List<VoucherCodeNumDto>> result = checkAllocateVoucherByCustomerOrder(cpgMap, voucherStart,
                    voucherEnd);
            if (!result.isSuccess()) {
                return result;
            }

            VoucherCodeNumDto dto = new VoucherCodeNumDto();
            dto.setVoucherCodeNumStart(voucherStart.getVoucherCodeNum());
            dto.setVoucherCodeNumEnd(voucherEnd.getVoucherCodeNum());
			dto.setVoucherCodeStart(voucherStart.getVoucherCode());
			dto.setVoucherCodeEnd(voucherEnd.getVoucherCode());
			voucherCodeNumDtoList.add(dto);
        }
		return Result.ok(voucherCodeNumDtoList);
	}

    private <T> Result<T> checkAllocateVoucherByCustomerOrder(Map<String, CustomerOrderDetails> cpgMap,
            Voucher voucherStart, Voucher voucherEnd) {

        if (!GvcoreConstants.MOP_CODE_VCR.equals(voucherStart.getMopCode())
                || !GvcoreConstants.MOP_CODE_VCR.equals(voucherEnd.getMopCode())) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_MOP_CODE_NOT_VCR.code(),
                    ResultErrorCodeEnum.VOUCHER_MOP_CODE_NOT_VCR.desc());
        }
        if (!voucherStart.getCpgCode().equals(voucherEnd.getCpgCode())) {
        	return Result.failed(ResultErrorCodeEnum.VOUCHER_START_END_DIFFERENT_CPG.code(),
        			ResultErrorCodeEnum.VOUCHER_START_END_DIFFERENT_CPG.desc());
        }

        CustomerOrderDetails detail = cpgMap.get(voucherStart.getCpgCode());
        if(detail == null) {
        	return Result.failed(ResultErrorCodeEnum.VOUCHER_CPG_ERROR.code(), ResultErrorCodeEnum.VOUCHER_CPG_ERROR.desc());
        }
        if (voucherStart.getDenomination().compareTo(detail.getDenomination()) != 0) {
        	return Result.failed(ResultErrorCodeEnum.VOUCHER_DENOMINATION_ERROR.code(),
                    ResultErrorCodeEnum.VOUCHER_DENOMINATION_ERROR.desc());
        }

        return Result.ok();
    }

	private VoucherAllocation selectOneByCustomerOrder(CustomerOrder customerOrder, Integer status) {
		VoucherAllocation voucherAllocation = new VoucherAllocation();
    	voucherAllocation.setSourceDataCode(customerOrder.getCustomerOrderCode());
    	voucherAllocation.setIssuerCode(customerOrder.getIssuerCode());
    	voucherAllocation.setBusinessType(VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code());
        voucherAllocation.setVoucherOwnerCode(customerOrder.getOutletCode());
    	voucherAllocation.setReceiverCode(customerOrder.getCustomerCode());
    	voucherAllocation.setStatus(status);
        voucherAllocation = voucherAllocationMapper.selectOne(voucherAllocation);
		return voucherAllocation;
	}

    private Result<List<VoucherCodeNumDto>> checkAllocateRequest(AllocateRequest request,
            VoucherAllocation voucherAllocation) {

        if (!VoucherAllocationStatusEnum.PENDING_ALLOCATION.equalsCode(voucherAllocation.getStatus())) {
            return Result.failed(ResultErrorCodeEnum.ALREADY_ALLOCATED.code(), ResultErrorCodeEnum.ALREADY_ALLOCATED.desc());
        }

        VoucherRequest voucherRequest = voucherRequestService
                .queryByVoucherRequestCode(voucherAllocation.getSourceDataCode());
        if (voucherRequest == null) {
            return Result.failed(ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DATA_FOUND.desc());
        }
        if (VoucherRequestStatusEnum.PENDING_ALLOCATION.getCode() != voucherRequest.getStatus().intValue()) {
            return Result.failed(ResultErrorCodeEnum.VOUCHER_REQUEST_STATUS_ERROR.code(),
                    ResultErrorCodeEnum.VOUCHER_REQUEST_STATUS_ERROR.desc());
        }

        List<VoucherRequestDetails> requestDetails = voucherRequestDetailsService
                .queryByVoucherRequestCode(voucherAllocation.getSourceDataCode());
        if (CollectionUtils.isEmpty(requestDetails)) {
            return Result.failed(ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DETAILS_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DETAILS_DATA_FOUND.desc());
        }

        Map<Double, VoucherRequestDetails> denominationMap = requestDetails.stream()
                .collect(Collectors.toMap(k -> Double.valueOf(k.getDenomination().doubleValue()), v -> v));

        //
        Result<List<VoucherCodeNumDto>> result = checkVoucherBatchList(request, denominationMap, voucherAllocation);
        if(!result.isSuccess()) {
            return Result.failed(result.getCode(), result.getMessage());
        }

        String issuerCode = voucherAllocation.getIssuerCode();
        String voucherOwnerCode = voucherAllocation.getVoucherOwnerCode();
        String voucherOwnerType = getVoucherOwnerType(voucherAllocation.getIssuerCode(), voucherOwnerCode);

        List<VoucherCodeNumDto> voucherCodeNumDtoList = result.getData();
        List<VoucherDto> countDtos = voucherService.countGroupByDenominationAndCirculationStatus(voucherCodeNumDtoList);
        List<VoucherDto> errorCirculationStatus = countDtos.stream()
                .filter(v -> !VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode().equals(v.getStatus())
                        || !voucherOwnerCode.equals(v.getVoucherOwnerCode())
                        || !voucherOwnerType.equals(v.getVoucherOwnerType())
                        || VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode() != v.getCirculationStatus()
                        || !GvcoreConstants.STATUS_ENABLE.equals(v.getVoucherStatus())
                        || !issuerCode.equals(v.getIssuerCode())
                        || !denominationMap.containsKey(v.getDenomination().doubleValue()))
                .collect(Collectors.toList());
        if (!errorCirculationStatus.isEmpty()) {
        	int countFailed = errorCirculationStatus.stream().mapToInt(VoucherDto :: getCount).sum();
        	List<Voucher> mismatchList = voucherService.queryMismatchByAllocationCountDto(errorCirculationStatus, 100);
            createCreateBusinessLog(request, mismatchList, ResultErrorCodeEnum.SOME_VOUCHER_CANNOT_ALLOCATE.desc(),
                    countFailed);
            return Result.failed(ResultErrorCodeEnum.SOME_VOUCHER_CANNOT_ALLOCATE.code(),
                    ResultErrorCodeEnum.SOME_VOUCHER_CANNOT_ALLOCATE.desc());
        }

        Map<Double, Integer> allocateQuantityMap = countDtos.stream().collect(
                Collectors.toMap(k -> Double.valueOf(k.getDenomination().doubleValue()), VoucherDto::getCount));
        for (VoucherRequestDetails voucherRequestDetails : requestDetails) {
            Integer quantity = allocateQuantityMap.get(voucherRequestDetails.getDenomination().doubleValue());
            if (quantity == null) {
                return Result.failed(ResultErrorCodeEnum.NO_VOUCHERS_ALLOCATED.code(),
                        ResultErrorCodeEnum.NO_VOUCHERS_ALLOCATED.desc() + " Denomination="
                                + voucherRequestDetails.getDenomination().intValue());
            }
            if (!quantity.equals(voucherRequestDetails.getVoucherNum())) {
                return Result.failed(ResultErrorCodeEnum.NUMBER_ARE_NOT_EQUAL.code(),
                        ResultErrorCodeEnum.NUMBER_ARE_NOT_EQUAL.desc() + " Denomination="
                                + voucherRequestDetails.getDenomination().intValue());
            }
        }
        return Result.ok(voucherCodeNumDtoList);
    }

    private String getVoucherOwnerType(String issuerCode, String voucherOwnerCode) {

        String voucherOwnerType = VoucherOwnerTypeEnum.OUTLET.code();
        if (voucherOwnerCode.equals(issuerWarehouseMap.get(issuerCode))
                || voucherOwnerCode.equals(issuerBusinessWarehouseMap.get(issuerCode))) {
            voucherOwnerType = VoucherOwnerTypeEnum.WAREHOUSE.code();
        }
        return voucherOwnerType;
    }

    private Result<List<VoucherCodeNumDto>> checkVoucherBatchList(AllocateRequest request,
            Map<Double, VoucherRequestDetails> denominationMap, VoucherAllocation voucherAllocation) {

        List<String> voucherCodeList = new ArrayList<>(request.getVoucherBatchList().size() * 2);
        for (AllocateVoucherBatch voucherBatch : request.getVoucherBatchList()) {
            voucherCodeList.add(voucherBatch.getVoucherStartNo());
            voucherCodeList.add(voucherBatch.getVoucherEndNo());
        }

        List<Voucher> voucherList = voucherService.queryByVoucherCodeList(null, voucherCodeList);
        Map<String, Voucher> voucherMap = voucherList.stream()
                .collect(Collectors.toMap(Voucher::getVoucherCode, v -> v));

        List<VoucherCodeNumDto> voucherCodeNumDtoList = new ArrayList<>(request.getVoucherBatchList().size());
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.SECOND, 1);
        Date nowDate = calendar.getTime();
        for (AllocateVoucherBatch voucherBatch : request.getVoucherBatchList()) {
            Voucher voucherStart = voucherMap.get(voucherBatch.getVoucherStartNo());
            Voucher voucherEnd = voucherMap.get(voucherBatch.getVoucherEndNo());

            if (voucherStart == null) {
                createCreateBusinessLog(request, voucherBatch.getVoucherStartNo(),
                        ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS.desc());
                return Result.failed(ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS.code(),
                        ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS.desc() + voucherBatch.getVoucherStartNo());
            }

            if (voucherEnd == null) {
                createCreateBusinessLog(request, voucherBatch.getVoucherEndNo(),
                        ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS.desc());
                return Result.failed(ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS.code(),
                        ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS.desc() + voucherBatch.getVoucherEndNo());
            }

            ResultErrorCodeEnum codeEnum = checkVoucher(voucherStart, voucherAllocation, nowDate);
            if (codeEnum != null) {
                createCreateBusinessLog(request, voucherBatch.getVoucherStartNo(), codeEnum.desc());
                return Result.failed(codeEnum.code(), codeEnum.desc() + voucherBatch.getVoucherStartNo());
            }

            codeEnum = checkVoucher(voucherEnd, voucherAllocation, nowDate);
            if (codeEnum != null) {
                createCreateBusinessLog(request, voucherBatch.getVoucherEndNo(), codeEnum.desc());
                return Result.failed(codeEnum.code(), codeEnum.desc() + voucherBatch.getVoucherEndNo());
            }

            Result<List<VoucherCodeNumDto>> result = checkAllocateVoucher(request, denominationMap, voucherBatch,
                    voucherStart, voucherEnd);
            if (!result.isSuccess()) {
                return result;
            }

            VoucherCodeNumDto dto = new VoucherCodeNumDto();
            dto.setVoucherCodeStart(String.valueOf(voucherStart.getVoucherCodeNum()));
            dto.setVoucherCodeEnd(String.valueOf(voucherEnd.getVoucherCodeNum()));
            voucherCodeNumDtoList.add(dto);
        }
        return Result.ok(voucherCodeNumDtoList);
    }

    private <T> Result<T> checkAllocateVoucher(AllocateRequest request,
            Map<Double, VoucherRequestDetails> denominationMap, AllocateVoucherBatch voucherBatch, Voucher voucherStart,
            Voucher voucherEnd) {

        if (!GvcoreConstants.MOP_CODE_VCR.equals(voucherStart.getMopCode())) {
        	createCreateBusinessLog(request, voucherBatch.getVoucherStartNo(), ResultErrorCodeEnum.VOUCHER_MOP_CODE_NOT_VCR.desc() + bookletMsgPrefix + voucherStart.getBookletCode());
            return Result.failed(ResultErrorCodeEnum.VOUCHER_MOP_CODE_NOT_VCR.code(),
                    ResultErrorCodeEnum.VOUCHER_MOP_CODE_NOT_VCR.desc());
        }
        if (!GvcoreConstants.MOP_CODE_VCR.equals(voucherEnd.getMopCode())) {
        	createCreateBusinessLog(request, voucherBatch.getVoucherEndNo(), ResultErrorCodeEnum.VOUCHER_MOP_CODE_NOT_VCR.desc() + bookletMsgPrefix + voucherEnd.getBookletCode());
            return Result.failed(ResultErrorCodeEnum.VOUCHER_MOP_CODE_NOT_VCR.code(),
                    ResultErrorCodeEnum.VOUCHER_MOP_CODE_NOT_VCR.desc());
        }
        if (!denominationMap.containsKey(voucherStart.getDenomination().doubleValue())) {
        	createCreateBusinessLog(request, voucherBatch.getVoucherStartNo(), ResultErrorCodeEnum.VOUCHER_DENOMINATION_ERROR.desc() + bookletMsgPrefix + voucherStart.getBookletCode());
            return Result.failed(ResultErrorCodeEnum.VOUCHER_DENOMINATION_ERROR.code(),
                    ResultErrorCodeEnum.VOUCHER_DENOMINATION_ERROR.desc());
        }
        if (voucherStart.getDenomination().compareTo(voucherEnd.getDenomination()) != 0) {
        	createCreateBusinessLog(request, voucherBatch.getVoucherEndNo(), ResultErrorCodeEnum.VOUCHER_DENOMINATION_ERROR.desc() + bookletMsgPrefix + voucherEnd.getBookletCode());
            return Result.failed(ResultErrorCodeEnum.VOUCHER_START_END_DIFFERENT_DENOMINATION.code(),
                    ResultErrorCodeEnum.VOUCHER_START_END_DIFFERENT_DENOMINATION.desc());
        }
        return Result.ok();
    }

    /**
     * 
     * @param voucher
     * @param voucherAllocation
     * @param nowDate
     * @return
     * <AUTHOR>
     * @date 2022年5月12日
     */
    private ResultErrorCodeEnum checkVoucher(Voucher voucher, VoucherAllocation voucherAllocation, Date nowDate) {

        if (voucher == null) {
            return ResultErrorCodeEnum.VOUCHER_NO_DATA_MISS;
        }
        if (!voucherAllocation.getIssuerCode().equals(voucher.getIssuerCode())) {
            return ResultErrorCodeEnum.VOUCHER_ISSUER_CODE_MISMATCH;
        }
        if (!voucherAllocation.getVoucherOwnerCode().equals(voucher.getVoucherOwnerCode())) {
            return ResultErrorCodeEnum.VOUCHER_OWNER_CODE_MISMATCH;
        }
        if (nowDate.compareTo(voucher.getVoucherEffectiveDate()) >= 0) {
            return ResultErrorCodeEnum.VOUCHER_EFFECTIVE_DATE_TIMEOUT;
        }
        return null;
    }

    private void createCreateBusinessLog(AllocateRequest request, String voucherCode, String msg) {

    	CreateBusinessLogRequest logRequest = buildBusinessLogRequest(request);
    	logRequest.setStatus(BusinessLogStatusEnum.FAILED.code());
    	logRequest.setSuccess(0);
    	logRequest.setFailed(1);

    	List<CreateBusinessLogDetailRequest> logDetailList = new ArrayList<>();
    	CreateBusinessLogDetailRequest detailRequest = new CreateBusinessLogDetailRequest();
    	detailRequest.setDetailContentCode(voucherCode);
    	JSONObject json = new JSONObject();
    	json.put("desc", msg);
    	detailRequest.setReason(json.toJSONString());
    	logDetailList.add(detailRequest);
    	logRequest.setBusinessLogDetailList(logDetailList);
    	businessLogService.createBusinessLog(logRequest);
    }

    private void createCreateBusinessLog(AllocateRequest request, List<Voucher> mismatchList, String msg, int countFailed) {

    	CreateBusinessLogRequest logRequest = buildBusinessLogRequest(request);
    	logRequest.setStatus(BusinessLogStatusEnum.FAILED.code());
    	logRequest.setSuccess(0);
    	logRequest.setFailed(countFailed);

    	List<CreateBusinessLogDetailRequest> logDetailList = new ArrayList<>(mismatchList.size());
    	JSONObject json = new JSONObject();
    	for (Voucher voucher : mismatchList) {
    		CreateBusinessLogDetailRequest detailRequest = new CreateBusinessLogDetailRequest();
        	detailRequest.setDetailContentCode(voucher.getVoucherCode());
        	json.put("desc", msg + bookletMsgPrefix + voucher.getBookletCode());
        	detailRequest.setReason(json.toJSONString());
        	logDetailList.add(detailRequest);
		 }
    	logRequest.setBusinessLogDetailList(logDetailList);
    	businessLogService.createBusinessLog(logRequest);
    }

    //@Transactional(rollbackFor = Exception.class)
	@Override
	public void customerOrderRelease(CustomerOrder customerOrder, boolean isRelease, String updateUser, String approvalCode) {

		VoucherAllocation voucherAllocation = selectOneByCustomerOrder(customerOrder, VoucherAllocationStatusEnum.PENDING_RECEIPT.code());
        if (voucherAllocation == null) {
            log.error("CustomerOrderCode= {} voucherAllocation data not found", customerOrder.getCustomerOrderCode());
        	throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    "CustomerOrderCode= {0} voucherAllocation data not found", customerOrder.getCustomerOrderCode());
        }
        if (!VoucherAllocationStatusEnum.PENDING_RECEIPT.equalsCode(voucherAllocation.getStatus())) {
            log.error("CustomerOrderCode= {} voucherAllocation Status not {}", customerOrder.getCustomerOrderCode(),
                    VoucherAllocationStatusEnum.PENDING_RECEIPT.name());
            throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    "{0} voucherAllocation Status not {1}", voucherAllocation.getVoucherAllocationCode(),
                    VoucherAllocationStatusEnum.PENDING_RECEIPT.name());
        }

        List<VoucherAllocationBatch> batchList = voucherAllocationBatchService.queryByVoucherAllocationCode(voucherAllocation.getVoucherAllocationCode());

        int listSize = batchList.size();
    	/*List<String> voucherCodeList = new ArrayList<>(listSize);
    	for (VoucherAllocationBatch batch : batchList) {
    		voucherCodeList.add(batch.getVoucherStartNo());
    		voucherCodeList.add(batch.getVoucherEndNo());
		}

    	List<Voucher> voucherList = voucherService.queryByVoucherCodeList(voucherAllocation.getIssuerCode(),
                voucherAllocation.getVoucherOwnerCode(), voucherCodeList);*/

        /*if (CollectionUtils.isEmpty(voucherList)) {
            log.error("CustomerOrderCode= {} voucher data not found", customerOrder.getCustomerOrderCode());
        	throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    "CustomerOrderCode= {0} voucher data not found", customerOrder.getCustomerOrderCode());
        }*/

        /*Map<String, Voucher> voucherMap = voucherList.stream()
                .collect(Collectors.toMap(Voucher::getVoucherCode, v -> v));*/

    	List<CpgVoucherCodeNumDto> voucherCodeNumLlist = new ArrayList<>(listSize);

    	int total = 0;
    	Date now = new Date();
    	String voucherOwnerCode = null;
        String voucherOwnerType = null;
        String logType = null;
        Integer status = null;
        if(isRelease) {
            //logType = VoucherLogTypeEnum.ACTIVATION.code();
        	voucherOwnerCode = voucherAllocation.getReceiverCode();
        	voucherOwnerType = VoucherOwnerTypeEnum.CUSTOMER.code();
            status = VoucherStatusEnum.VOUCHER_ACTIVATED.getCode();
        }else {
        	//logType = VoucherLogTypeEnum.ALLOCATION_CANCEL.code();
        	voucherOwnerCode = voucherAllocation.getVoucherOwnerCode();
        	voucherOwnerType = VoucherOwnerTypeEnum.OUTLET.code();
            status = VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode();
        }
    	for (VoucherAllocationBatch batch : batchList) {
    		CpgVoucherCodeNumDto numDto = new CpgVoucherCodeNumDto();
    		numDto.setVoucherCodeNumStart(Long.valueOf(batch.getVoucherStartNo()));
    		numDto.setVoucherCodeNumEnd(Long.valueOf(batch.getVoucherEndNo()));
			numDto.setVoucherCodeStart(batch.getVoucherStartNo());
			numDto.setVoucherCodeEnd(batch.getVoucherEndNo());
    		voucherCodeNumLlist.add(numDto);
    		total += batch.getVoucherNum();

    		/*VoucherLogTask logTask = new VoucherLogTask();
            logTask.setIssuerCode(voucherAllocation.getIssuerCode());
            logTask.setCpgCode(batch.getCpgCode());
            logTask.setVoucherCodeNumStart(numDto.getVoucherCodeNumStart());
            logTask.setVoucherCodeNumEnd(numDto.getVoucherCodeNumEnd());
            logTask.setVoucherStatus(GvcoreConstants.STATUS_ENABLE);
            logTask.setStatus(status);
            logTask.setCirculationStatus(VoucherCirculationStatusEnum.VOUCHER_TO_BE_RECEIVED.getCode());
            logTask.setVoucherOwnerCode(voucherOwnerCode);
            logTask.setVoucherOwnerType(voucherOwnerType);
    		logTask.setLogType(logType);
            logTask.setSourceDataCode(voucherAllocation.getVoucherAllocationCode());
    		logTask.setCreateUser(updateUser);
    		logTask.setCreateTime(now);
    		logTaskList.add(logTask);*/
		}


        if(isRelease) {
            //release不激活实体券
			int i = voucherService.activatPhysicalVoucherByCustomerOrder(voucherAllocation.getIssuerCode(),
					voucherAllocation.getVoucherOwnerCode(), voucherCodeNumLlist, voucherOwnerCode, voucherOwnerType,customerOrder);
        	if (i != total) {
    			throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                        "{0} failed to update voucher owner", voucherAllocation.getVoucherAllocationCode());
    		}

			createVoucherReceive(updateUser, voucherAllocation, batchList, total);

			addTransactionData(customerOrder, voucherCodeNumLlist, voucherOwnerCode, voucherOwnerType, approvalCode);
        }else {
        	int i = updateStatus(voucherAllocation.getVoucherAllocationCode(), VoucherAllocationStatusEnum.REJECTED.code(),
    				VoucherAllocationStatusEnum.PENDING_RECEIPT.code(), updateUser, now);
    		if (i == 0) {
                log.error("{0} voucherAllocation updateStatusToRejected failed", voucherAllocation.getVoucherAllocationCode());
                throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                        "{0} voucherAllocation updateStatusToRejected failed", voucherAllocation.getVoucherAllocationCode());
            }

    		i = voucherService.updateByCustomerOrderReleaseReject(voucherAllocation.getIssuerCode(), voucherAllocation.getVoucherOwnerCode(), voucherCodeNumLlist);
    		if (i != total) {
                log.error("{0} Some voucher cannot cancel allocate", voucherAllocation.getVoucherAllocationCode());
    			throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                        "{0} Some voucher cannot cancel allocate", voucherAllocation.getVoucherAllocationCode());
    		}
        }

        /*voucherLogTaskService.insertBatchLogTask(logTaskList,false);*/

	}


    private void addTransactionData(CustomerOrder customerOrder, List<CpgVoucherCodeNumDto> voucherCodeNumLlist, String ownerCode, String ownerType, String approvalCode) {
		log.info("RELEASE---------插入交易数据开始");
        List<String> voucherCodes = new ArrayList<>();
        for (CpgVoucherCodeNumDto cpgVoucherCodeNumDto : voucherCodeNumLlist) {
            while (cpgVoucherCodeNumDto.getVoucherCodeStart().compareTo(cpgVoucherCodeNumDto.getVoucherCodeEnd()) <= 0) {
                voucherCodes.add(cpgVoucherCodeNumDto.getVoucherCodeStart());
                BigInteger startNum = new BigInteger(cpgVoucherCodeNumDto.getVoucherCodeStart());
                startNum = startNum.add(BigInteger.ONE);
                cpgVoucherCodeNumDto.setVoucherCodeStart(startNum.toString());
            }
        }
        log.info("RELEASE----------待添加交易记录的券码数量为：{}",voucherCodes.size());


        queryVoucherComponent.queryVoucherByVoucherCodesAndInsertTransactionData( voucherCodes, customerOrder, approvalCode,TransactionTypeEnum.GIFT_CARD_SELL );


        /*List<Voucher> vouchers = new ArrayList<>();
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(1);
        pageParam.setPageSize(5000);
        do {

            //循环查询直到查询完所有的券
            Example example = new Example(Voucher.class);
            Example.Criteria criteria = example.createCriteria();
                    *//*criteria.andEqualTo(Voucher.C_VOUCHER_OWNER_CODE,ownerCode);
                    criteria.andEqualTo(Voucher.C_VOUCHER_OWNER_TYPE,ownerType);*//*
            criteria.andIn(Voucher.C_VOUCHER_CODE,voucherCodes);
            vouchers = voucherMapper.selectByExampleAndRowBounds(example, GvPageHelper.getRowBounds(pageParam));
            if (CollectionUtils.isNotEmpty(vouchers)){
                createTransactionData(customerOrder, vouchers, approvalCode);
                log.info("RELEASE---------已经插入数据条数：{}",vouchers.size());
                pageParam.setPageNum(pageParam.getPageNum() + 1);
            }
        } while (vouchers.size() == pageParam.getPageSize());*/

        log.info("RELEASE---------插入交易数据结束");




        /*if (CollectionUtils.isNotEmpty(vouchers)){
            bookletService.voucherActivateUpdateBookletStatus(VoucherActivateUpdateBookletStatusDto.builder()
                    .voucherCode(vouchers.stream().map(x->x.getVoucherCode()).collect(Collectors.toList()))
                    .statusEnum(BookletStatusEnum.PARTIALLY_ACTIVATED)
                    .type("0")
                    .build());
        }*/



    }




    private void createTransactionData(CustomerOrder customerOrder, List<Voucher> voucherList, String approvalCode) {
        ArrayList<TransactionData> transactionDatas = new ArrayList<>();


        voucherList.forEach(x->{
            //插入transactionData
            TransactionData transactionData = new TransactionData();
            transactionData.setTransactionId(customerOrder.getCustomerOrderCode());
            transactionData.setApproveCode(approvalCode);
            transactionData.setTransactionType(TransactionTypeEnum.GIFT_CARD_SELL.getCode());
            transactionData.setMerchantCode(outletService.getOutlet(GetOutletRequest.builder().outletCode(customerOrder.getOutletCode()).build()).getMerchantCode());
            transactionData.setIssuerCode(customerOrder.getIssuerCode());
            transactionData.setBatchId("");
            transactionData.setBillNumber("");
            transactionData.setOutletCode(customerOrder.getOutletCode());
            transactionData.setCpgCode(x.getCpgCode());
            transactionData.setTransactionDate(new Date());
            transactionData.setVoucherCode(x.getVoucherCode());
            transactionData.setVoucherCodeNum(Long.valueOf(x.getVoucherCode().replaceAll("[a-zA-Z]","")));
            transactionData.setInitiatedBy("");
            transactionData.setPosCode("");
            transactionData.setBatchCode("");
            transactionData.setLoginSource("");
            transactionData.setDenomination(x.getDenomination());
            transactionData.setPaidAmount(new BigDecimal("0"));
            transactionData.setPaymentMethod("");
            transactionData.setDiscountAmount(new BigDecimal("0"));
            transactionData.setActualOutlet("");
            transactionData.setCardEntryMode("GV POS");
            transactionData.setMopCode(x.getMopCode());
            transactionData.setForwardingEntityId("");
            transactionData.setResponseMessage("Transaction successful.");
            transactionData.setTransactionMode("");
            transactionData.setCorporateName("");
            transactionData.setDepartmentDivisionBranch("");
            transactionData.setCustomerSalutation("");
            transactionData.setCustomerFirstName("");
            transactionData.setCustomerLastName("");
            transactionData.setMobile("");
            transactionData.setEmail("");
            transactionData.setInvoiceNumber(customerOrder.getInvoiceNo());
            transactionData.setOtherInputParameter("{}");
            transactionData.setCustomerType("");
            transactionData.setSuccessOrFailure("0");
            transactionData.setPurchaseOrderNo(customerOrder.getPurchaseOrderNo());
            transactionData.setVoucherEffectiveDate(x.getVoucherEffectiveDate());
            transactionData.setCreateUser("");
            transactionData.setCreateTime(new Date());
            transactionData.setUpdateUser("");
            transactionData.setUpdateTime(new Date());
            transactionData.setCustomerCode(customerOrder.getCustomerCode());
            transactionData.setReferenceNumber(customerOrder.getCustomerOrderCode());
            transactionData.setReferenceNumber(gvCodeHelper.generateReferenceNumber());
            transactionDatas.add(transactionData);
        });

//        List<TransactionData> salesTransactionList = BeanCopyUtils.jsonCopyList(transactionDatas, TransactionData.class);
//        salesTransactionList.forEach(t -> t.setTransactionType(TransactionTypeEnum.GIFT_CARD_SELL.getCode()));
//
//        transactionDatas.addAll(salesTransactionList);

        transactionDataService.insertList(transactionDatas);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void completed(String voucherAllocationCode, String updateUser) {

        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setVoucherAllocationCode(voucherAllocationCode);
        voucherAllocation = voucherAllocationMapper.selectOne(voucherAllocation);
        if (voucherAllocation == null) {
            throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    "{0} voucherAllocation data not found", voucherAllocationCode);
        }
        if (!VoucherAllocationStatusEnum.PENDING_RECEIPT.equalsCode(voucherAllocation.getStatus())) {
            throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    "{0} voucherAllocation Status not {1}", voucherAllocationCode,
                    VoucherAllocationStatusEnum.PENDING_RECEIPT.name());
        }

		int i = updateStatus(voucherAllocationCode, VoucherAllocationStatusEnum.COMPLETED.code(),
				VoucherAllocationStatusEnum.PENDING_RECEIPT.code(), updateUser, new Date());
		if (i == 0) {
            throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    "{0} voucherAllocation updateStatusToCompleted failed", voucherAllocationCode);
        }

        if(!VoucherAllocationBusinessTypeEnum.CUSTOMERORDER.code().equals(voucherAllocation.getBusinessType())) {
        	Result<Void> result = voucherRequestService.updateRequestStatus(voucherAllocation.getSourceDataCode(),
                    VoucherRequestStatusEnum.COMPLETED.getCode(), updateUser);
            if (!result.isSuccess()) {
                throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                        "{0} voucherRequest updateRequestStatus fail", voucherAllocation.getSourceDataCode());
            }

            gvOperateLogService.createSuccessLog(voucherAllocation.getSourceDataCode(), "Receive", updateUser,
                    "Completed");
        }

    }

    @Override
    public PageResult<QueryVoucherAllocationByPageResponse> queryVoucherAllocationByPage(
            QueryVoucherAllocationByPageRequest request) {

        VoucherAllocationDto dto = new VoucherAllocationDto();
        dto.setIssuerCode(request.getIssuerCode());
        dto.setReceiverCode(request.getOutletCode());
        dto.setStatus(request.getStatus());
        dto.setDenomination(request.getDenomination());
        dto.setCpgCode(request.getCpgCode());
        dto.setBusinessType(VoucherAllocationBusinessTypeEnum.SALES.code());
        dto.setReceiverCode(request.getOutletCode());

        final PermissionCodeResponse permission = this.permissionHelper.getPermission(request.getUserCode(), request.getIssuerCode());
        if (!PermissionCodeResponse.hasOutlet(permission)) {
            return PageResult.ok(Collections.emptyList(), 0L);
        }

        final VoucherAllocationPermissionDto queryCondition = BeanCopyUtils.jsonCopyBean(dto, VoucherAllocationPermissionDto.class);
        queryCondition.setOutletCodeRangeList(permission.getOutletCodeList());

        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VoucherAllocation> allocationList = voucherAllocationMapper.queryVoucherAllocationByPage(queryCondition);
        if (CollectionUtils.isEmpty(allocationList)) {
            return PageResult.ok(Collections.emptyList(), 0L);
        }

        PageInfo<VoucherAllocation> pageInfo = new PageInfo<>(allocationList);

        PageMethod.clearPage();
        List<String> voucherAllocationCodeList = new ArrayList<>(request.getPageSize());
        List<String> voucherRequestCodeList = new ArrayList<>(request.getPageSize());
        for (VoucherAllocation voucherAllocation : allocationList) {
            voucherAllocationCodeList.add(voucherAllocation.getVoucherAllocationCode());
            voucherRequestCodeList.add(voucherAllocation.getSourceDataCode());
        }

        List<String> allocationCodes = allocationList.stream().map(x -> x.getVoucherAllocationCode()).collect(Collectors.toList());

        allocationList = voucherAllocationMapper.queryByVoucherAllocationCodeList(voucherAllocationCodeList);
        Map<String, List<String>> denominationMap = voucherRequestDetailsService
                .queryDenominationByVoucherRequestCodeList(voucherRequestCodeList);




        List<VoucherRequest> voucherRequests = voucherRequestService
                .queryByVoucherRequestCodeList(voucherRequestCodeList);
        Map<String, VoucherRequest> requestMap = voucherRequests.stream()
                .collect(Collectors.toMap(VoucherRequest::getVoucherRequestCode, v -> v));
        List<QueryVoucherAllocationByPageResponse> responseList = new ArrayList<>(request.getPageSize());
        for (VoucherAllocation allocation : allocationList) {
            QueryVoucherAllocationByPageResponse response = new QueryVoucherAllocationByPageResponse();
            BeanUtils.copyProperties(allocation, response);
            VoucherRequest voucherRequest = requestMap.get(allocation.getSourceDataCode());
            if(voucherRequest != null) {
            	response.setVoucherNum(voucherRequest.getVoucherNum());
                response.setVoucherAmount(voucherRequest.getVoucherAmount());
            }
            response.setCpgList(denominationMap.get(allocation.getSourceDataCode()));
            responseList.add(response);
        }

        return PageResult.ok(responseList, pageInfo.getTotal());
    }

    @Override
    public Result<GetVoucherAllocationResponse> getVoucherAllocation(GetVoucherAllocationRequest request) {

        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setVoucherAllocationCode(request.getVoucherAllocationCode());
        voucherAllocation = voucherAllocationMapper.selectOne(voucherAllocation);
        if (voucherAllocation == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        VoucherRequest voucherRequest = voucherRequestService
                .queryByVoucherRequestCode(voucherAllocation.getSourceDataCode());
        if (voucherRequest == null) {
            return Result.failed(ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DATA_FOUND.desc());
        }

        List<VoucherRequestDetails> requestDetails = voucherRequestDetailsService
                .queryByVoucherRequestCode(voucherAllocation.getSourceDataCode());
        if (CollectionUtils.isEmpty(requestDetails)) {
            return Result.failed(ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DETAILS_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DETAILS_DATA_FOUND.desc());
        }

        String voucherOwnerType = VoucherOwnerTypeEnum.OUTLET.code();
		if (voucherAllocation.getVoucherOwnerCode().equals(issuerWarehouseMap.get(voucherAllocation.getIssuerCode()))
				|| voucherAllocation.getVoucherOwnerCode().equals(issuerBusinessWarehouseMap.get(voucherAllocation.getIssuerCode()))) {
			voucherOwnerType = VoucherOwnerTypeEnum.WAREHOUSE.code();
		}

        Integer status = VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode();
        List<String> cpgCodes = requestDetails.stream().map(VoucherRequestDetails::getCpgCode)
                .collect(Collectors.toList());

        //outletCode
        String voucherOwnerCode = voucherRequest.getVoucherOwnerCode();
        //outletType
        OutletResponse outlet = outletService.getOutlet(GetOutletRequest.builder().outletCode(voucherOwnerCode).build());
        //判断是否是warehouse
        QueryCpgVoucherInventory queryCpgVoucherInventory = new QueryCpgVoucherInventory();
        queryCpgVoucherInventory.setCpgCodes(cpgCodes);

        //if(true)  -- WH01
        if (outlet.getOutletType().equals("Warehouse")){
            queryCpgVoucherInventory.setIssuerCode(issuerWarehouseMap.get(voucherAllocation.getIssuerCode()));
        }else {//else ---HO01
            queryCpgVoucherInventory.setIssuerCode(issuerBusinessWarehouseMap.get(voucherAllocation.getIssuerCode()));
        }

        //TODO 性能问题
        List<Voucher> voucherInventory = cpgService.getVoucherInventory(queryCpgVoucherInventory);

        Map<String, List<Voucher>> cpgInventory = voucherInventory.stream().collect(Collectors.groupingBy(Voucher::getCpgCode));


        /*Map<Double, Integer> denominationMap = voucherService.countGroupByDenomination(
                voucherAllocation.getIssuerCode(), status, voucherAllocation.getVoucherOwnerCode(), voucherOwnerType,
                VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode(), denominationList);*/

        GetVoucherAllocationResponse response = new GetVoucherAllocationResponse();
        BeanUtils.copyProperties(voucherRequest, response);

        response.setSourceDataCode(voucherAllocation.getSourceDataCode());
        response.setBusinessType(voucherAllocation.getBusinessType());
        response.setStateName(getDistrictName(response.getStateCode()));
        response.setCityName(getDistrictName(response.getCityCode()));
        response.setDistrictName(getDistrictName(response.getDistrictCode()));

        response.setVoucherAllocationCode(voucherAllocation.getVoucherAllocationCode());
        List<AllocationRequestDetails> detailsList = new ArrayList<>(requestDetails.size());
        for (VoucherRequestDetails voucherRequestDetails : requestDetails) {
            AllocationRequestDetails details = new AllocationRequestDetails();
            BeanUtils.copyProperties(voucherRequestDetails, details);
            Integer inventoryQuantity = 0;
            if (!cpgInventory.isEmpty()){
                List<Voucher> vouchers = cpgInventory.get(voucherRequestDetails.getCpgCode());
                if (CollectionUtils.isNotEmpty(vouchers)){
                    inventoryQuantity = vouchers.size();
                }
            }


            if (StringUtils.isNotBlank(voucherRequestDetails.getCpgCode())){
                GetCpgResponse data = cpgService.getCpg(GetCpgRequest.builder().cpgCode(voucherRequestDetails.getCpgCode()).build()).getData();
                if ( null != data){
                    details.setCpgName(data.getCpgName());
                }
            }

            details.setInventoryQuantity(inventoryQuantity);
            detailsList.add(details);
        }
        response.setDetailsList(detailsList);
        return Result.ok(response);
    }

    @Override
    public Result<GetVoucherAllocationResponse> getVoucherAllocationByReceive(GetVoucherAllocationRequest request) {
        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setVoucherAllocationCode(request.getVoucherAllocationCode());
        voucherAllocation = voucherAllocationMapper.selectOne(voucherAllocation);
        if (voucherAllocation == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        VoucherRequest voucherRequest = voucherRequestService
                .queryByVoucherRequestCode(voucherAllocation.getSourceDataCode());
        if (voucherRequest == null) {
            return Result.failed(ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DATA_FOUND.desc());
        }

        List<VoucherRequestDetails> requestDetails = voucherRequestDetailsService
                .queryByVoucherRequestCode(voucherAllocation.getSourceDataCode());
        if (CollectionUtils.isEmpty(requestDetails)) {
            return Result.failed(ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DETAILS_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_VOUCHER_REQUEST_DETAILS_DATA_FOUND.desc());
        }

        /*Map<Double, Integer> denominationMap = voucherService.countGroupByDenomination(
                voucherAllocation.getIssuerCode(), status, voucherAllocation.getVoucherOwnerCode(), voucherOwnerType,
                VoucherCirculationStatusEnum.VOUCHER_RECEIVED.getCode(), denominationList);*/

        GetVoucherAllocationResponse response = new GetVoucherAllocationResponse();
        BeanUtils.copyProperties(voucherRequest, response);

        response.setSourceDataCode(voucherAllocation.getSourceDataCode());
        response.setBusinessType(voucherAllocation.getBusinessType());
        response.setStateName(getDistrictName(response.getStateCode()));
        response.setCityName(getDistrictName(response.getCityCode()));
        response.setDistrictName(getDistrictName(response.getDistrictCode()));

        response.setVoucherAllocationCode(voucherAllocation.getVoucherAllocationCode());
        List<AllocationRequestDetails> detailsList = new ArrayList<>(requestDetails.size());
        for (VoucherRequestDetails voucherRequestDetails : requestDetails) {
            AllocationRequestDetails details = new AllocationRequestDetails();
            BeanUtils.copyProperties(voucherRequestDetails, details);



            if (StringUtils.isNotBlank(voucherRequestDetails.getCpgCode())){
                GetCpgResponse data = cpgService.getCpg(GetCpgRequest.builder().cpgCode(voucherRequestDetails.getCpgCode()).build()).getData();
                if ( null != data){
                    details.setCpgName(data.getCpgName());
                }
            }

            detailsList.add(details);
        }
        response.setDetailsList(detailsList);
        return Result.ok(response);

    }

    private String getDistrictName(String districtCode) {

        if (StringUtils.isEmpty(districtCode)) return null;
    	MasterDataDistrictEntity districtEntity = new MasterDataDistrictEntity();
        districtEntity.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        districtEntity.setDistrictCode(districtCode);
        districtEntity = masterDataDistrictService.getByCode(districtEntity);
        if(districtEntity == null) {
        	return null;
        }
        return districtEntity.getDistrictName();
    }

    @Override
    public VoucherAllocation getAllocationBySourceDataCode(String sourceDataCode, String businessType) {
    	VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setSourceDataCode(sourceDataCode);
        voucherAllocation.setBusinessType(businessType);
        return voucherAllocationMapper.selectOne(voucherAllocation);
    }

	@Override
	public VoucherAllocation getAllocationByCode(String voucherAllocationCode) {
		VoucherAllocation voucherAllocation = new VoucherAllocation();
		voucherAllocation.setVoucherAllocationCode(voucherAllocationCode);
		return voucherAllocationMapper.selectOne(voucherAllocation);
	}

    @Override
    public VoucherAllocation queryByVoucherAllocationCode(String voucherAllocationCode) {

        if (StringUtils.isBlank(voucherAllocationCode)) {
            return null;
        }
        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setVoucherAllocationCode(voucherAllocationCode);
        return voucherAllocationMapper.selectOne(voucherAllocation);
    }

    @Override
    public List<VoucherAllocation> queryByVoucherAllocationCodeList(List<String> voucherAllocationCode) {
        if (CollectionUtils.isEmpty(voucherAllocationCode)) {
			return new ArrayList<>();
        }
        Example example = new Example(VoucherAllocation.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("voucherAllocationCode",voucherAllocationCode);


        return voucherAllocationMapper.selectByCondition(example);
    }

    @Override
    public Set<String> querySourceDataCodeVoucherAllocation(final String voucherNumberStart, final String voucherNumberEnd) {

        final List<VoucherAllocationBatch> voucherAllocationBatchList = this.voucherAllocationBatchService.queryByVoucherNum(voucherNumberStart, voucherNumberEnd);
        if (CollectionUtils.isEmpty(voucherAllocationBatchList)) {
            return Collections.emptySet();
        }

        final Set<String> voucherAllocationCodeSet = voucherAllocationBatchList.stream().map(VoucherAllocationBatch::getVoucherAllocationCode).collect(Collectors.toSet());

        final Example example = new Example(VoucherAllocation.class);
        example.createCriteria()
                .andIn(VoucherAllocation.C_VOUCHER_ALLOCATION_CODE, voucherAllocationCodeSet);

        return this.voucherAllocationMapper.selectByCondition(example).stream().map(VoucherAllocation::getSourceDataCode).collect(Collectors.toSet());
    }

    @Override
    public int updateVoucherAllocationStatusByAllocationCode(String allocationCode, Integer status) {

        VoucherAllocation voucherAllocation = new VoucherAllocation();
        voucherAllocation.setStatus(status);

        Example example = new Example(VoucherAllocation.class);
        example.createCriteria()
                .andEqualTo(VoucherAllocation.C_VOUCHER_ALLOCATION_CODE, allocationCode);
        int i = voucherAllocationMapper.updateByConditionSelective(voucherAllocation, example);


        return i;
    }


}
