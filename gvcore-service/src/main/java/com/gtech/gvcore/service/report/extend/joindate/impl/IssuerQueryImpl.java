package com.gtech.gvcore.service.report.extend.joindate.impl;

import com.gtech.gvcore.dao.mapper.IssuerMapper;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.service.report.extend.joindate.QuerySupport;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * @ClassName IssuerQueryImpl
 * @Description
 * <AUTHOR>
 * @Date 2023/1/16 15:44
 * @Version V1.0
 **/
@Component
public class IssuerQueryImpl implements QuerySupport<Issuer> {

    private static final Issuer EMPTY = new Issuer();

    @Autowired
    private IssuerMapper issuerMapper;

    @Override
    public List<Issuer> queryByCode(List<String> codes, String... selectFields) {

        if (CollectionUtils.isEmpty(codes)) return Collections.emptyList();

        Example example = new Example(Issuer.class);
        example.createCriteria().andIn(Issuer.C_ISSUER_CODE, codes);

        if (ArrayUtils.isNotEmpty(selectFields)) example.selectProperties(selectFields);

        List<Issuer> list = issuerMapper.selectByCondition(example);
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        return list;
    }

    @Override
    public Function<Issuer, String> codeMapper() {
        return Issuer::getIssuerCode;
    }

    @Override
    public Issuer emptyObject() {
        return EMPTY;
    }

    @Override
    public Class<Issuer> supportType() {
        return Issuer.class;
    }
}
