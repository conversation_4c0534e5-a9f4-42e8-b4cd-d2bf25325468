package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.giftcard.application.service.GiftCardApplicationService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.export.snapshoot.ReportDateContext;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.context.ReportContextBuilder;
import com.gtech.gvcore.service.report.impl.bean.GcLifeCycleBean;
import com.gtech.gvcore.service.report.impl.param.GcLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.gclife.GcLifeCycle;
import com.gtech.gvcore.service.report.impl.support.gclife.excel.GcLifeCycleFileContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> based on VoucherLifeCycleImpl
 * @date 2025年6月19日
 * @description Gift Card Life Cycle Report Implementation
 */
@Slf4j
@Service
public class GcLifeCycleImpl extends ReportSupport
        implements BusinessReport<GcLifeCycleQueryData, GcLifeCycleBean>, SingleReport {

    @Autowired
    private List<GcLifeCycle> gcLifeCycleList;

    @Autowired
    GiftCardApplicationService giftCardApplicationService;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_CARD_LIFE_CYCLE_REPORT;
    }

    @Override
    public void customContext(ReportContextBuilder builder) {
        builder.bindFileContext(new GcLifeCycleFileContext());
    }

    @Override
    public GcLifeCycleQueryData builderQueryParam(CreateReportRequest reportParam) {
        GcLifeCycleQueryData reportBasicQueryData = new GcLifeCycleQueryData();
        if (reportParam.getVoucherCode() != null) {
            reportBasicQueryData.setCardNumber(Arrays.asList(reportParam.getVoucherCode().split(",")));
        }

        return reportBasicQueryData;
    }

    @Override
    public List<GcLifeCycleBean> getExportData(GcLifeCycleQueryData param) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void builder(ReportContext context) {

        //data
        GcLifeCycleFileContext excelContext = context.getFileContext(GcLifeCycleFileContext.class);
        ReportDateContext reportDateContext = context.getReportDateContext(ReportDateContext.class);

        //param
        GcLifeCycleQueryData queryParam = (GcLifeCycleQueryData) context.getQueryParam();
        queryParam.setPageNum(1);

        //no data
        if (!isExist(queryParam)) {
            context.noData();
            return;
        }

        //builder
        Map<ReportExportTypeEnum, List<?>> reportData = new EnumMap<>(ReportExportTypeEnum.class);
        gcLifeCycleList.forEach(e -> e.builder(excelContext, reportData, queryParam));

        //fast save
        reportDateContext.fastSaveAll(reportData);

    }

    private boolean isExist(final GcLifeCycleQueryData queryParam) {
        //结束不存在值
        List<String> cardNumber = queryParam.getCardNumber();

        if (null == cardNumber || cardNumber.isEmpty()) return false;

        return !giftCardApplicationService.queryByCardNumberList(null, cardNumber).isEmpty();
    }


    @Override
    public Map<ReportExportTypeEnum, Class<?>> getResultDateType() {

        return new EnumMap<>(ReportExportTypeEnum.class);
    }

}
