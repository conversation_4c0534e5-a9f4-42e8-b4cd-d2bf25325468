package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.enums.CpgTypeAutomaticActivateEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.ReportVoucherStatusEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.common.utils.GvDateUtil;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.CpgType;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.ReportProportionDataFunction;
import com.gtech.gvcore.service.report.extend.context.ReportContext;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.EgvTrackingBean;
import com.gtech.gvcore.service.report.impl.bo.EgvTrackingActivateTransactionBo;
import com.gtech.gvcore.service.report.impl.bo.EgvTrackingBo;
import com.gtech.gvcore.service.report.impl.bo.EgvVoucherTrackingRedeemTransactionBo;
import com.gtech.gvcore.service.report.impl.param.EgvTrackingQueryData;
import com.gtech.message.util.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:59
 * @Description:
 */
@Service
public class EgvTrackingImpl extends ReportSupport
        implements BusinessReport<EgvTrackingQueryData, EgvTrackingBean>, ReportProportionDataFunction, SingleReport {

    public static final String DIGITAL_VOUCHER_URL = "Digital Voucher - URL";
    public static final String DIGITAL_VOUCHER_NO_URL =  "e-GV Ultra Voucher";

    @Override
    public EgvTrackingQueryData builderQueryParam(CreateReportRequest reportParam) {

        EgvTrackingQueryData queryData = new EgvTrackingQueryData();
        queryData
                .setTransactionDateStart(reportParam.getTransactionDateStart())
                .setTransactionDateEnd(reportParam.getTransactionDateEnd())
                .setCpgCodeList(reportParam.getCpgCodes())
                .setMerchantCodeList(reportParam.getMerchantCodes())
                .setOutletCodeList(reportParam.getOutletCodes());

        return queryData;
    }

    private EgvTrackingQueryData getQueryParam(EgvTrackingQueryData queryData) {

        EgvTrackingQueryData param = BeanCopyUtils.jsonCopyBean(queryData, EgvTrackingQueryData.class);

        Date wtdBegin = queryData.getTransactionDateStart();
        Date cumulativeBegin = DateUtil.parseDate(DateUtil.format(queryData.getTransactionDateEnd(), DateUtil.FORMAT_YYYY), DateUtil.FORMAT_YYYY);

        param.setTransactionDateStart(wtdBegin.compareTo(cumulativeBegin) > 0 ? cumulativeBegin : wtdBegin);
        param.setTransactionDateEnd( DateUtils.addYears(DateUtil.parseDate(DateUtil.format(queryData.getTransactionDateEnd(), DateUtil.FORMAT_YYYY), DateUtil.FORMAT_YYYY), 1));
        return param;
    }

    @Override
    public List<EgvTrackingBean> getExportData(EgvTrackingQueryData queryData) {

        return group(queryData,
                        Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectEgvTrackingVoucher, this.getQueryParam(queryData)))
                                // filter gift voucher sell
                                .map(e -> e.stream().filter(s -> TransactionTypeEnum.GIFT_CARD_SELL.equalsCode(s.getTransactionType()))
                                        .collect(Collectors.toList())
                ).orElse(Collections.emptyList())
        );

    }

    private List<EgvTrackingBean> group(EgvTrackingQueryData queryData, Collection<EgvTrackingBo> trackingList) {

        //result
        final Map<String, EgvTrackingBean> resultMap = new HashMap<>();

        //init function
        final AddFunction addFunction = getAddFunction(queryData);
        final AddTypeSwitchFunction saleFunction = (b, v, d) -> addFunction.execute(v, d, b::addSalesWTD, b::addSalesMTD, b::addSalesYTD);
        final AddTypeSwitchFunction activationFunction = (b, v, d) -> addFunction.execute(v, d, b::addActivationWTD, b::addActivationMTD, b::addActivationYTD);
        final AddTypeSwitchFunction redemptionFunction = (b, v, d) -> addFunction.execute(v, d, b::addRedemptionWTD, b::addRedemptionMTD, b::addRedemptionYTD);


        //init map
        final JoinDataMap<Outlet> outletMap = getMapByCode(trackingList, EgvTrackingBo::getOutletCode, Outlet.class);
        final JoinDataMap<Cpg> cpgMap = getMapByCode(trackingList, EgvTrackingBo::getCpgCode, Cpg.class);
        final JoinDataMap<Voucher> voucherMap = getMapByCode(trackingList, EgvTrackingBo::getVoucherCode, Voucher.class);
        final JoinDataMap<CpgType> cpgTypeMap = getMapByCode(cpgMap.values(), Cpg::getCpgTypeCode, CpgType.class);
        final Map<String, Date> voucherActivation = getActivateTransactionDate(voucherMap);
        final Map<String, Date> voucherRedemption = getRedeemTransactionDate(voucherMap);

        //statistical
        trackingList
                .stream()
                .filter(t -> outletMap.containsKey(t.getOutletCode()))
                .filter(t -> cpgMap.containsKey(t.getCpgCode()))
                .forEach(t -> {

                    Cpg cpg = cpgMap.get(t.getCpgCode());
                    CpgType cpgType = cpgTypeMap.get(cpg.getCpgTypeCode());
                    if (null == cpgType) return;

                    //egv tracking bean
                    EgvTrackingBean egvTrackingBean = resultMap.computeIfAbsent(cpgType.getAutomaticActivate() + "_" + t.getOutletCode(), k -> EgvTrackingBean.newInstance()
                            .setChannel(outletMap.findValue(t.getOutletCode()).getOutletName())
                            .setTypeOfeGV(CpgTypeAutomaticActivateEnum.NO.equalsCode(cpgType.getAutomaticActivate()) ? DIGITAL_VOUCHER_URL : DIGITAL_VOUCHER_NO_URL));

                    //获得卡券
                    Voucher voucher = voucherMap.findValue(t.getVoucherCode());

                    //统计
                    saleFunction.apply(egvTrackingBean, t.getTransactionDate(), voucher.getDenomination());
                    VoucherStatusEnum voucherStatus = VoucherStatusEnum.valueOfCode(voucher.getStatus());
                    if (null == voucherStatus) return;
                    switch (voucherStatus) {//此处需要利用 switch 特性执行
                        case VOUCHER_USED: redemptionFunction.apply(egvTrackingBean, voucherRedemption.get(t.getVoucherCode()), voucher.getDenomination());//NOSONAR
                        case VOUCHER_ACTIVATED: activationFunction.apply(egvTrackingBean, voucherActivation.get(t.getVoucherCode()), voucher.getDenomination());//NOSONAR
                        default: break;
                    }
                });

        if (MapUtils.isEmpty(resultMap)) return new ArrayList<>();

        return addTotal(resultMap.values());
    }

    private AddFunction getAddFunction(EgvTrackingQueryData queryData) {
        //init
        final Date wtdBegin = queryData.getTransactionDateStart();
        final Date wtdEnd   = queryData.getTransactionDateEnd();

        final Date mtdBegin = DateUtil.getStartDayOfMonth(wtdEnd);
        final Date mtdEnd   = DateUtil.addMonth(mtdBegin, 1);

        final Date ytdBegin = DateUtil.parseDate(DateUtil.format(wtdEnd, DateUtil.FORMAT_YYYY), DateUtil.FORMAT_YYYY);
        final Date ytdEnd   = DateUtils.addYears(ytdBegin, 1);

        //add function
        return (time, denomination, wtdRunnable, mtdRunnable, ytdRunnable) -> {
            if (null == time) return;
            if (wtdBegin.compareTo(time) < 0 && wtdEnd.compareTo(time) >= 0) wtdRunnable.apply(denomination);
            if (mtdBegin.compareTo(time) < 0 && mtdEnd.compareTo(time) > 0) mtdRunnable.apply(denomination);
            if (ytdBegin.compareTo(time) < 0 && ytdEnd.compareTo(time) > 0) ytdRunnable.apply(denomination);
        };
    }

    public interface AddTypeSwitchFunction {

        void apply (EgvTrackingBean egvTrackingBean, Date date, BigDecimal denomination);
    }

    public interface AddFunction {

        void execute(Date time, BigDecimal denomination, Function<BigDecimal, EgvTrackingBean> wtdRunnable, Function<BigDecimal, EgvTrackingBean> mtdRunnable, Function<BigDecimal, EgvTrackingBean> ytdRunnable);

    }

    private List<EgvTrackingBean> addTotal(Collection<EgvTrackingBean> list) {

        List<EgvTrackingBean> beanList = storeBean(list);

        EgvTrackingBean total = EgvTrackingBean.newInstance().setTypeOfeGV("Total MAP e-GV");
        beanList.forEach(e -> {

            total.addSalesWTD(e.getSalesWTD())
                    .addSalesMTD(e.getSalesMTD())
                    .addSalesYTD(e.getSalesYTD())
                    .addRedemptionWTD(e.getRedemptionWTD())
                    .addRedemptionMTD(e.getRedemptionMTD())
                    .addRedemptionYTD(e.getRedemptionYTD());

            if (DIGITAL_VOUCHER_NO_URL.equals(e.getTypeOfeGV())) {
                e.setActivationWTD(null)
                    .setActivationMTD(null)
                    .setActivationYTD(null);
            } else {
                total.addActivationWTD(e.getActivationWTD())
                        .addActivationMTD(e.getActivationMTD())
                        .addActivationYTD(e.getActivationYTD());
                e.setPercentageActivationToSalesYTD(getProportion(e.getActivationYTD(), e.getSalesYTD()));
            }
            e.setPercentageRedemptionToSalesYTD(getProportion(e.getRedemptionYTD(), e.getSalesYTD()));
        });

        total.setPercentageRedemptionToSalesYTD(getProportion(total.getRedemptionYTD(), total.getSalesYTD()));

        beanList.add(total);

        return beanList;
    }

    private List<EgvTrackingBean> storeBean(Collection<EgvTrackingBean> list) {

        Map<String, List<EgvTrackingBean>> collect = list.stream().collect(Collectors.groupingBy(EgvTrackingBean::getTypeOfeGV));

        AtomicInteger noIndex = new AtomicInteger();

        List<EgvTrackingBean> beanList = new ArrayList<>();
        if (collect.containsKey(DIGITAL_VOUCHER_URL)) {
            noIndex.incrementAndGet();
            beanList.addAll(collect.get(DIGITAL_VOUCHER_URL).stream().map(m -> m.setNo(noIndex.get())).collect(Collectors.toList()));
        }
        if (collect.containsKey(DIGITAL_VOUCHER_NO_URL)) {
            noIndex.incrementAndGet();
            beanList.addAll(collect.get(DIGITAL_VOUCHER_NO_URL).stream().map(m -> m.setNo(noIndex.get())).collect(Collectors.toList()));
        }
        return beanList;
    }

    /**
     * 获取卡券激活时间MAP
     * @param voucher 卡券信息
     * @return 激活时间MAP
     */
    private Map<String, Date> getActivateTransactionDate(Map<String, Voucher> voucher) {

        //交易类型
        List<TransactionTypeEnum> transactionTypeEnums = Stream.of(TransactionTypeEnum.GIFT_CARD_ACTIVATE, TransactionTypeEnum.GIFT_CARD_ACTIVATE_ONLY).collect(Collectors.toList());

        //筛选符合体条件的卡券
        List<String> voucherCodeList = voucher
                .values()
                .stream()
                //无论是使用还是取消使用 只要存在过使用就可以统计在激活中 因为使用是在激活之后 即使取消使用 状态也会回到激活
                //而取消激活本身是取消销售所以取消激活的数据不会进入这个逻辑 因此排除取消激活
                .filter(e -> super.getVoucherStatus(e) == ReportVoucherStatusEnum.VOUCHER_ACTIVATED  || super.getVoucherStatus(e) == ReportVoucherStatusEnum.VOUCHER_REDEEMED).map(Voucher::getVoucherCode)
                .distinct()
                .collect(Collectors.toList());

        // 查询相关交易
        List<EgvTrackingActivateTransactionBo> transactionData = super.reportBusinessMapper.selectEgvTrackingActivateTransactionDate(voucherCodeList
                , transactionTypeEnums.stream().map(TransactionTypeEnum::getCode).collect(Collectors.toList()));

        //转换map
        return transactionData.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(EgvTrackingActivateTransactionBo::getVoucherCode, EgvTrackingActivateTransactionBo::getTransactionDate, (a, b) -> b));
    }


    /**
     * 获取卡券使用时间Map
     * @param voucher 卡券信息
     * @return 卡券使用时间map
     */
    private Map<String, Date> getRedeemTransactionDate(Map<String, Voucher> voucher) {

        //交易类型
        List<TransactionTypeEnum> transactionTypeEnums = Stream.of(TransactionTypeEnum.GIFT_CARD_REDEEM, TransactionTypeEnum.GIFT_CARD_BULK_CANCEL_REDEEM).collect(Collectors.toList());

        //筛选符合条件的卡券
        List<String> voucherCodeList = voucher
                .values()
                .stream()
                .filter(e -> super.getVoucherStatus(e) == ReportVoucherStatusEnum.VOUCHER_REDEEMED)
                .map(Voucher::getVoucherCode)
                .distinct()
                .collect(Collectors.toList());

        //查询相关交易
        List<EgvVoucherTrackingRedeemTransactionBo> transactionData = super.reportBusinessMapper.selectEgvTrackingRedeemTransactionDate(voucherCodeList
                , transactionTypeEnums.stream().map(TransactionTypeEnum::getCode).collect(Collectors.toList()));


        //筛选最新的卡券交易
        return PollPageHelper.group((v1, v2) -> {

                    // 如果v1为空则返回v2
                    if (null == v1) return v2;

                    // 如果v2为空则返回v1
                    if (null == v2) return v1;

                    // 如果v1的交易号大于v2的交易号则返回v1
                    if (v1.getTransactionNumber().compareTo(v2.getTransactionNumber()) > 0) return v1;

                    // 否则返回v2
                    return v2;

                }, EgvVoucherTrackingRedeemTransactionBo::getVoucherCode, transactionData).stream()
                //过滤掉非使用交易
                .filter(e -> TransactionTypeEnum.GIFT_CARD_REDEEM.equalsCode(e.getTransactionType()))
                //转换map
                .collect(Collectors.toMap(EgvVoucherTrackingRedeemTransactionBo::getVoucherCode, EgvVoucherTrackingRedeemTransactionBo::getTransactionDate, (a, b) -> b));
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.EGV_TRACKING_REPORT;
    }

    @Override
    public Object getHeadObject(ReportContext context) {

        final EgvTrackingQueryData queryData = (EgvTrackingQueryData) context.getQueryParam();
        final Date transactionDateStart = queryData.getTransactionDateStart();

        return new Head()
                .setFindTime(String.format("%s(W%s)", GvDateUtil.formatUs(transactionDateStart, GvDateUtil.FORMAT_US_DATETIME_DD_MMM_YYYY), DateUtils.toCalendar(transactionDateStart).get(Calendar.WEEK_OF_MONTH)))
                .setCpg(super.getMapByCode(queryData.getCpgCodeList(), Cpg.class).values().stream().map(Cpg::getCpgName).collect(Collectors.joining(",")))
                .setOutlet(super.getMapByCode(queryData.getOutletCodeList(), Outlet.class).values().stream().map(Outlet::getOutletName).collect(Collectors.joining(",")));
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Head {

        private String findTime;

        private String cpg;

        private String outlet;

    }
}
