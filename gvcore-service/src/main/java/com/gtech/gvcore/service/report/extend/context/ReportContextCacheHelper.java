package com.gtech.gvcore.service.report.extend.context;

import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * @ClassName ReportContextCacheHelper
 * @Description 报表缓存帮助类
 * <AUTHOR>
 * @Date 2023/4/7 17:58
 * @Version V1.0
 **/
@Slf4j
public class ReportContextCacheHelper {

    /**
     * 本地缓存
     */
    private final Map<String, Object> localCache = new HashMap<>();

    /**
     * 缓存一个value
     * @param key
     * @param value
     */
    public void putCache(String key, Object value) {

        log.info("ReportContextCacheHelper.putCache => put cache key: {}", key);

        localCache.put(key, value);
    }

    /**
     * 获得一个value 如果不存在则使用supplier创建一个对象并缓存
     * @param key 缓存key
     * @param type 缓存类型
     * @param supplier 缓存对象创建方法
     * @return 缓存对象
     * @param <T> 缓存类型
     */
    public <T> T getCacheIfAbsent(String key, Class<T> type, Supplier<T> supplier) {

        if (localCache.containsKey(key)) return getCache(key, type);

        putCache(key, supplier.get());

        return getCache(key, type);
    }

    /**
     * 获得一个value
     * @param key
     * @param type
     * @return
     * @param <T>
     */
    public <T> T getCache(String key, Class<T> type) {

        if (!localCache.containsKey(key)) return null;

        Object value = localCache.get(key);

        if (null == value) return null;

        return type.cast(value);
    }

    /**
     * 获得一个JoinDataMap
     * @param key 缓存key
     * @return 缓存对象
     * @param <V> JoinDataMap 泛型
     */
    public <V> JoinDataMap<V> getCacheJoinMap(String key) {

        if (!localCache.containsKey(key)) return new JoinDataMap<>();

        Object value = localCache.get(key);

        if (null == value) return new JoinDataMap<>();

        return (JoinDataMap<V>) value;
    }

    /**
     * 获得一个List
     * @param key 缓存key
     * @return List
     * @param <V> List泛型
     */
    public <V> List<V> getCacheList(String key) {

        if (!localCache.containsKey(key)) return new ArrayList<>();

        Object value = localCache.get(key);

        if (null == value) return new ArrayList<>();

        return (List<V>) value;
    }


}
