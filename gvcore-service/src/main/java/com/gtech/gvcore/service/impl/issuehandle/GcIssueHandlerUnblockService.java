package com.gtech.gvcore.service.impl.issuehandle;

import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.giftcard.domain.model.GcMgtCardStatus;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class GcIssueHandlerUnblockService extends GcIssueHandlerValidateService implements IssueHandlerBaseService {

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.GC_BULK_REACTIVATE;
    }

    @Override
    public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {

        return check(details, issuerCode);
    }

    @Override
    public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {

        List<IssueHandlingDetails> check = check(details, issuerCode);

        List<String> successVoucherCodes = check.stream()
                .filter(detail -> IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()))
                .map(IssueHandlingDetails::getVoucherCode)
                .collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(successVoucherCodes)) {
            performAction(successVoucherCodes);
        }

        return check;
    }


    private List<IssueHandlingDetails> check(List<IssueHandlingDetails> details, String issuerCode) {

        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }

        checkIfExist(details, getIssueHandlingType(), issuerCode);
        return details;
    }

    private int performAction(List<String> voucherCodes) {
        Weekend<GiftCardEntity> cardEntityWeekend = Weekend.of(GiftCardEntity.class);
        WeekendCriteria<GiftCardEntity, Object> criteria = cardEntityWeekend.weekendCriteria();
        criteria.andIn(GiftCardEntity::getCardNumber, voucherCodes);

        GiftCardEntity voucher = new GiftCardEntity();
        voucher.setManagementStatus(GcMgtCardStatus.ENABLE.name());
        return giftCardMapper.updateByConditionSelective(voucher, cardEntityWeekend);
    }

    @Override
    public void afterExecute(IssueHandling issueHandling) {

    }

}


