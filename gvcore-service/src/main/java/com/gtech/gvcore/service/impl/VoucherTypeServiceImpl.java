package com.gtech.gvcore.service.impl;

import com.gtech.basic.masterdata.core.controller.QueryResult;
import com.gtech.basic.masterdata.web.entity.MasterDataDDEntity;
import com.gtech.basic.masterdata.web.service.MasterDataDdService;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.request.vouchertype.*;
import com.gtech.gvcore.common.response.vouchertype.VoucherTypeResponse;
import com.gtech.gvcore.service.VoucherTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/2/11 15:30
 */
@Service
public class VoucherTypeServiceImpl implements VoucherTypeService {


    private static final String VOUCHER_TYPE = "Voucher_Type";
    

    @Autowired
    private MasterDataDdService masterDataDdService;


    @Override
    public Result<Void> createVoucherType(CreateVoucherTypeRequest param) {

        MasterDataDDEntity entity = BeanCopyUtils.jsonCopyBean(param,MasterDataDDEntity.class);
		entity.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        entity.setDdCode(VOUCHER_TYPE);
        masterDataDdService.save(entity);
        return Result.ok();

    }

    @Override
    public Result<Void> updateVoucherType(UpdateVoucherTypeRequest param) {
        MasterDataDDEntity entity = BeanCopyUtils.jsonCopyBean(param,MasterDataDDEntity.class);
		entity.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        entity.setDdCode(VOUCHER_TYPE);
        masterDataDdService.updateByCode(entity);
        return Result.ok();
    }

    @Override
    public Result<Void> deleteVoucherType(DeleteVoucherTypeRequest param) {
        MasterDataDDEntity entity = BeanCopyUtils.jsonCopyBean(param,MasterDataDDEntity.class);
		entity.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        entity.setDdCode(VOUCHER_TYPE);
        masterDataDdService.deleteByCode(entity);
        return Result.ok();
    }

    @Override
    public PageResult<VoucherTypeResponse> queryVoucherTypeList(QueryVoucherTypeRequest param) {

        Map<String, Object> map = new HashMap<>();
        map.put("ddCode",VOUCHER_TYPE);
		map.put("tenantCode", GvcoreConstants.SYSTEM_DEFAULT);
        QueryResult<MasterDataDDEntity> result = masterDataDdService.queryByPages(map);
        return new PageResult<>(BeanCopyUtils.jsonCopyList(result.getList(),VoucherTypeResponse.class),result.getTotal());

    }

    @Override
    public VoucherTypeResponse getVoucherType(GetVoucherTypeRequest param) {
        MasterDataDDEntity entity = BeanCopyUtils.jsonCopyBean(param,MasterDataDDEntity.class);
		entity.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        entity.setDdCode(VOUCHER_TYPE);
        return BeanCopyUtils.jsonCopyBean(masterDataDdService.getByCode(entity),VoucherTypeResponse.class);
    }

    @Override
    public Result<Void> updateVoucherTypeStatus(UpdateVoucherTypeStatusRequest param) {
        MasterDataDDEntity entity = BeanCopyUtils.jsonCopyBean(param,MasterDataDDEntity.class);
        entity.setTenantCode(GvcoreConstants.SYSTEM_DEFAULT);
        entity.setDdCode(VOUCHER_TYPE);
        masterDataDdService.updateByCode(entity);
        return Result.ok();
    }


}
