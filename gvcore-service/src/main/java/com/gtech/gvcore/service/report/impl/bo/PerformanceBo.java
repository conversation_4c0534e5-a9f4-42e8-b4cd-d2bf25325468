package com.gtech.gvcore.service.report.impl.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName PerformanceBo
 * @Description
 * <AUTHOR>
 * @Date 2022/12/29 14:56
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class PerformanceBo {

    private String issuerCode;

    private String merchantCode;

    private String outletCode;

    private String cpgCode;

    private String voucherCode;

    private String transactionType;

    private String transactionCode;

    private BigDecimal transactionNumber;

    private BigDecimal denomination;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }


}
