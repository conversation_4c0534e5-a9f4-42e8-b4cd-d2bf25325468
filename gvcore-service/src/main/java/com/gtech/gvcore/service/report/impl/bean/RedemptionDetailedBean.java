package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import com.gtech.gvcore.service.report.extend.ReportBeanCustomerAutoFull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 13:28
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class RedemptionDetailedBean implements ReportBeanCustomerAutoFull {

    @ExcelProperty(value = "Voucher Number")
    private String cardNumber;

    @ExcelProperty(value = "Booklet Number")
    private String bookletNumber;

    @ExcelProperty(value = "Merchant")
    private String merchant;

    @ExcelProperty(value = "Outlet")
    private String outlet;

    @ExcelProperty(value = "Descriptive Outlet Name")
    private String descriptiveOutletName;

    @ExcelProperty(value = "OutletCode")
    private String outletCode;

    @ExcelProperty(value = "Client Name")
    private String customerName;

    @ExcelProperty(value = "Company name")
    private String companyName;

    @ExcelProperty(value = "Department/Division/Branch")
    private String departmentDivisionBranch;

    @ExcelProperty(value = "Full Name (Customer Full Name)")
    private String customerFullName;

    @ExcelProperty(value = "First Name")
    private String firstName;

    @ExcelProperty(value = "Last Name")
    private String lastName;

    /*@ExcelProperty(value = "Email")
    private String email;
*/
    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    @ExcelProperty(value = "POS Name")
    private String posName;

    @ExcelProperty(value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(value = "Transaction Type")
    private String transactionType;

    @ReportAmountValue
    @ExcelProperty(value = "Amount", converter = ExportExcelNumberConverter.class)
    private String amount;

    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    @ExcelProperty(value = "Response Message")
    private String responseMessage;

    @ExcelProperty(value = "Date At Client")
    private String dateAtClient;

    @ReportAmountValue
    @ExcelProperty(value = "Pre Transaction Voucher Denomination", converter = ExportExcelNumberConverter.class)
    private String preTransactionVoucherDenomination;

    @ExcelProperty(value = "Transaction Mode")
    private String transactionMode = "ONLINE";

    @ExcelProperty(value = "Transaction PostDate")
    private String transactionPostDate;

    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    @ExcelProperty(value = "Issuance Year")
    private String issuanceYear;

    @ExcelProperty(value = "Reference Number")
    private String referenceNumber;

    @ExcelProperty(value = "Voucher Entry Mode")
    private String cardEntryMode = "GV POS";

    @ExcelProperty(value = "Batch Number")
    private String batchNumber;

    @ReportAmountValue
    @ExcelProperty(value = "Request Amount", converter = ExportExcelNumberConverter.class)
    private String requestAmount;

    @ExcelProperty(value = "Notes")
    private String notes;

    @ExcelProperty(value = "Approval Code")
    private String approvalCode;
}
