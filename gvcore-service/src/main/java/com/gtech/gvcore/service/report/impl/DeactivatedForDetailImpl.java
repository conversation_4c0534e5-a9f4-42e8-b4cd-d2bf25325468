package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.DeactivatedForDetailBean;
import com.gtech.gvcore.service.report.impl.bo.DeactivatedBo;
import com.gtech.gvcore.service.report.impl.param.DeactivatedQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description:
 */
@Service
public class DeactivatedForDetailImpl extends ReportSupport
        implements BusinessReport<DeactivatedQueryData, DeactivatedForDetailBean>, SingleReport {

    @Autowired private IssueHandlingDetailsService ihdService;

    @Override
    public DeactivatedQueryData builderQueryParam(CreateReportRequest reportParam) {

        DeactivatedQueryData queryData = new DeactivatedQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());
        queryData.setTransactionType(TransactionTypeEnum.GIFT_CARD_DEACTIVATE.getCode());

        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setVoucherCodeNumStart(reportParam.getVoucherCodeNumStart());
        queryData.setVoucherCodeNumEnd(reportParam.getVoucherCodeNumEnd());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        queryData.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());
        return queryData;
    }


    @Override
    public List<DeactivatedForDetailBean> getExportData(DeactivatedQueryData queryData) {

        //find
        final Collection<DeactivatedBo> list =
                Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(reportBusinessMapper::selectDeactivated, queryData))
                        .orElse(Collections.emptyList());
        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, DeactivatedBo::getCustomerCode, Customer.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, DeactivatedBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, DeactivatedBo::getOutletCode, Outlet.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, DeactivatedBo::getCpgCode, Cpg.class);
        final Map<String, String> voucherMap = this.ihdService.queryRemarkByVoucherCodeAndIssueType(list.stream().map(DeactivatedBo::getVoucherCode)
                .collect(Collectors.toList()), IssueHandlingTypeEnum.BULK_DEACTIVATE);

        //convert result
        return list.stream()
                .map(e -> new DeactivatedForDetailBean()
                        .setVoucherAmount(super.toAmount(e.getDenomination()))
                        .setCreatedOn(DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS))
                        .setVoucherNumber(e.getVoucherCode())
                        .setExpiryDate(DateUtil.format(e.getVoucherEffectiveDate(), DateUtil.FORMAT_DEFAULT))
                        .setInvoiceNumber(e.getInvoiceNumber())
                        .setDeactivateReason(voucherMap.get(e.getVoucherCode()))
                        .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setCompanyName(customerMap.findValue(e.getCustomerCode()).getCompanyName()))
                .collect(Collectors.toList());
    }


    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.BLOCKED_AND_DEACTIVATED_DETAILED;
    }
}
