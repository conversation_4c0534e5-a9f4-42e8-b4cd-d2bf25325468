package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName GcTransactionDetailedBo
 * @Description Gift Card Transaction Detailed Business Object
 * <AUTHOR>
 * @Date 2025-06-18
 * @Version V1.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class GcTransactionDetailedBo {
    private String transactionType;
    private String merchantCode;
    private String outletCode;
    private String transactionDate;
    private String cardNumber;
    private String cpgCode;
    private String createUser;
    private String posCode;
    private String terminal;
    private String organizationName;
    private String batchNumber;
    private String loginSource;
    private BigDecimal denomination;
    private BigDecimal amount;
    private String actualOutlet;
    private String forwardingActualId;
    private String responseMessage;
    private String transactionMode;
    private String buyerName;
    private String customerFirstName;
    private String customerLastName;
    private String mobile;
    private String invoiceNumber;
    private String otherInputParameter;

}
