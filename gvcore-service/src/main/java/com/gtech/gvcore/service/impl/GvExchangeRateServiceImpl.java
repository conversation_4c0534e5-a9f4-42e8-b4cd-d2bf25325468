package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.enums.GvExchangeRateStatusEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.exchangerate.CreateExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.DeleteExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.GetExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.QueryExchangeRateByPageRequest;
import com.gtech.gvcore.common.request.exchangerate.UpdateExchangeRateRequest;
import com.gtech.gvcore.common.request.exchangerate.UpdateExchangeRateStatusRequest;
import com.gtech.gvcore.common.response.exchangerate.GetExchangeRateResponse;
import com.gtech.gvcore.common.response.exchangerate.GvExchangeRateByPageResponse;
import com.gtech.gvcore.dao.mapper.GvExchangeRateMapper;
import com.gtech.gvcore.dao.model.GvExchangeRateEntity;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.GvExchangeRateService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * exchange rate(GvExchangeRate)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-25 15:13:58
 */
@Service
public class GvExchangeRateServiceImpl implements GvExchangeRateService {

    @Autowired
    private GvExchangeRateMapper gvExchangeRateMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Override
    public Result<Object> createExchangeRate(CreateExchangeRateRequest createExchangeRateRequest) {

        GvExchangeRateEntity gvExchangeRateEntity = BeanCopyUtils.jsonCopyBean(createExchangeRateRequest, GvExchangeRateEntity.class);

        String generateExchangeRateCode = gvCodeHelper.generateExchangeRateCode();

        gvExchangeRateEntity.setExchangeRateCode(generateExchangeRateCode);
        gvExchangeRateEntity.setCreateTime(new Date());
        gvExchangeRateEntity.setStatus(GvExchangeRateStatusEnum.STATUS_ENABLE.getCode());

        int insertSelectiveCount = gvExchangeRateMapper.insertSelective(gvExchangeRateEntity);

        if (insertSelectiveCount > 0) {
            return Result.ok(generateExchangeRateCode);
        }

        return Result.failed(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code(), ResultErrorCodeEnum.SYSTEM_EXCEPTION.desc());
    }

    @Override
    public Result<Object> getExchangeRate(GetExchangeRateRequest getExchangeRateRequest) {

        GvExchangeRateEntity exchangeRate = new GvExchangeRateEntity();
        exchangeRate.setExchangeRateCode(getExchangeRateRequest.getExchangeRateCode());

        Result<Object> checkResult = geCheckResult(exchangeRate);

        if (checkResult != null) {
            return checkResult;
        }

        GvExchangeRateEntity gvExchangeRate = gvExchangeRateMapper.selectOne(exchangeRate);

        GetExchangeRateResponse getExchangeRateResponse = new GetExchangeRateResponse();

        BeanUtils.copyProperties(gvExchangeRate, getExchangeRateResponse);


        return Result.ok(getExchangeRateResponse);
    }

    private Result<Object> geCheckResult(GvExchangeRateEntity exchangeRate) {

        int selectCount = gvExchangeRateMapper.selectCount(exchangeRate);
        if (selectCount <= 0) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }
        return null;
    }

    @Override
    public Result<Object> deleteExchangeRate(DeleteExchangeRateRequest deleteExchangeRateRequest) {

        GvExchangeRateEntity exchangeRate = new GvExchangeRateEntity();
        exchangeRate.setExchangeRateCode(deleteExchangeRateRequest.getExchangeRateCode());

        Result<Object> checkResult = geCheckResult(exchangeRate);

        if (checkResult != null) {
            return checkResult;
        }
        int deleteCount = gvExchangeRateMapper.delete(exchangeRate);
        if (deleteCount > 0) {
            return Result.ok();
        }

        return Result.failed(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code(), ResultErrorCodeEnum.SYSTEM_EXCEPTION.desc());
    }

    @Override
    public Result<Object> updateExchangeRate(UpdateExchangeRateRequest updateExchangeRateRequest) {

        GvExchangeRateEntity exchangeRate = new GvExchangeRateEntity();
        exchangeRate.setExchangeRateCode(updateExchangeRateRequest.getExchangeRateCode());

        Result<Object> checkResult = geCheckResult(exchangeRate);

        if (checkResult != null) {
            return checkResult;
        }

        GvExchangeRateEntity gvExchangeRate = BeanCopyUtils.jsonCopyBean(updateExchangeRateRequest, GvExchangeRateEntity.class);

        gvExchangeRate.setUpdateTime(new Date());

        Example example = new Example(GvExchangeRateEntity.class);

        example.createCriteria().andEqualTo(GvExchangeRateEntity.C_EXCHANGE_RATE_CODE, gvExchangeRate.getExchangeRateCode());

        int updateCount = gvExchangeRateMapper.updateByConditionSelective(gvExchangeRate, example);
        if (updateCount > 0) {
            return Result.ok();
        }

        return Result.failed(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code(), ResultErrorCodeEnum.SYSTEM_EXCEPTION.desc());
    }

    @Override
    public Result<Object> updateExchangeRateStatus(UpdateExchangeRateStatusRequest request) {


        GvExchangeRateEntity exchangeRate = new GvExchangeRateEntity();
        exchangeRate.setExchangeRateCode(request.getExchangeRateCode());

        Result<Object> checkResult = geCheckResult(exchangeRate);

        if (checkResult != null) {
            return checkResult;
        }

        GvExchangeRateEntity gvExchangeRate = BeanCopyUtils.jsonCopyBean(request, GvExchangeRateEntity.class);

        gvExchangeRate.setUpdateTime(new Date());

        Example example = new Example(GvExchangeRateEntity.class);

        example.createCriteria().andEqualTo(GvExchangeRateEntity.C_EXCHANGE_RATE_CODE, gvExchangeRate.getExchangeRateCode());

        int updateCount = gvExchangeRateMapper.updateByConditionSelective(gvExchangeRate, example);
        if (updateCount > 0) {
            return Result.ok();
        }

        return Result.failed(ResultErrorCodeEnum.SYSTEM_EXCEPTION.code(), ResultErrorCodeEnum.SYSTEM_EXCEPTION.desc());
    }

    @Override
    public PageResult<GvExchangeRateByPageResponse> queryExchangeRateByPage(QueryExchangeRateByPageRequest request) {

        Example example=new Example(GvExchangeRateEntity.class,true,false);

        example.createCriteria().andEqualTo(GvExchangeRateEntity.C_CURRENCY_CODE,request.getCurrencyCode());
        example.orderBy(GvExchangeRateEntity.C_UPDATE_TIME).desc();

        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<GvExchangeRateEntity> entityList = gvExchangeRateMapper.selectByCondition(example);
        PageInfo<GvExchangeRateEntity> pageInfo = new PageInfo<>(entityList);

        List<GvExchangeRateByPageResponse> responses = new ArrayList<>(request.getPageSize());

        entityList.forEach(item -> {
            GvExchangeRateByPageResponse response = new GvExchangeRateByPageResponse();
            BeanUtils.copyProperties(item, response);
            responses.add(response);
        });

        return PageResult.ok(responses, pageInfo.getTotal());
    }
}
