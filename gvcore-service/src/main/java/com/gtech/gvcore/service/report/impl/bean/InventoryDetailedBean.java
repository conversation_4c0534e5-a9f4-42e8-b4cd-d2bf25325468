package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/20 14:25
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class InventoryDetailedBean {

    @ExcelProperty(value = "Issuer")
    private String issuer;

    @ExcelProperty(value = "Merchant")
    private String merchant;

    @ExcelProperty(value = "Outlet Code")
    private String outletCode;

    @ExcelProperty(value = "Outlet")
    private String outlet;

    @ExcelProperty(value = "Voucher Program Group")
    private String voucherProgramGroup;

    @ExcelProperty(value = "Booklet Number")
    private String bookletNumber;

    @ExcelProperty(value = "Voucher Number")
    private String cardNumber;

    @ExcelProperty(value = "Voucher Status")
    private String cardStatus;

    @ExcelProperty(value = "Expiry Date")
    private String expiryDate;

    @ExcelProperty(value = "Issuance Year")
    private String issuanceYear;

    @ExcelProperty(value = "Creation Date")
    private String creationDate;

    @ExcelProperty(value = "denomination")
    private String denomination;

}
