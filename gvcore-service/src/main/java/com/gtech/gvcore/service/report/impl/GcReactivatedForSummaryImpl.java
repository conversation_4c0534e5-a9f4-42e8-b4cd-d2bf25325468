package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.CustomerTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcReactivatedForSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GcReactivatedBo;
import com.gtech.gvcore.service.report.impl.param.GcReactivatedQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description: Gift Card Reactivated (Block) Report Summary Implementation
 */
@Service
public class GcReactivatedForSummaryImpl extends ReportSupport
        implements BusinessReport<GcReactivatedQueryData, GcReactivatedForSummaryBean>, SingleReport {

    @Override
    public GcReactivatedQueryData builderQueryParam(CreateReportRequest reportParam) {
        GcReactivatedQueryData queryData = new GcReactivatedQueryData();
        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setCustomerCodes(reportParam.getCustomerCodes());
        if (StringUtils.isNotBlank(reportParam.getVoucherCode())) {
            queryData.setVoucherCode(Arrays.asList(reportParam.getVoucherCode().split(",")));
        }
        return queryData;
    }

    @Override
    public List<GcReactivatedForSummaryBean> getExportData(GcReactivatedQueryData queryData) {
        List<GcReactivatedBo> boList = new ArrayList<>();
        PollPageHelper.pollSelect(gcReportBusinessMapper::selectGcReactivated, queryData, boList::addAll);
        Collection<GcReactivatedSummaryStatisticalBo> list = Optional.of(boList)
                .map(e -> e.stream()
                        .collect(Collectors.toMap(
                                GcReactivatedSummaryStatisticalBo::groupKey,
                                GcReactivatedSummaryStatisticalBo::convert,
                                GcReactivatedSummaryStatisticalBo::merge))
                ).map(Map::values)
                .orElseGet(Collections::emptyList);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(list, GcReactivatedSummaryStatisticalBo::getCpgCode, GcCpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, GcReactivatedSummaryStatisticalBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcReactivatedSummaryStatisticalBo::getOutletCode, Outlet.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, GcReactivatedSummaryStatisticalBo::getOwnerCustomer, Customer.class);

        //convert result
        List<GcReactivatedForSummaryBean> collect = list.stream()
                .map(e -> {
                    Customer value = customerMap.findValue(e.getCustomerCode());
                    String customerName;
                    if (Objects.equals(value.getCustomerType(), CustomerTypeEnum.CORPORATE.code())) {
                        customerName = value.getCompanyName();
                    } else {
                        customerName = value.getCustomerName();
                    }
                    return new GcReactivatedForSummaryBean()
                            .setVoucherAmount(super.toAmount(e.getTotalAmount()))
                            .setNumberOfVouchers(String.valueOf(e.getNumberOfVouchers()))
                            .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                            .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                            .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                            .setCustomerName(customerName)
                            .setReactivatedReason(e.getReactivatedReason());
                })
                .collect(Collectors.toList());

        GcReactivatedForSummaryBean gcReactivatedForSummaryBean = new GcReactivatedForSummaryBean();
        gcReactivatedForSummaryBean.setMerchant("Total");
        gcReactivatedForSummaryBean.setNumberOfVouchers(list.stream().map(GcReactivatedSummaryStatisticalBo::getNumberOfVouchers).reduce(0, Integer::sum).toString());
        gcReactivatedForSummaryBean.setVoucherAmount(super.toAmount(list.stream().map(GcReactivatedSummaryStatisticalBo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
        collect.add(gcReactivatedForSummaryBean);
        return collect;
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_REACTIVATED_SUMMARY;
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class GcReactivatedSummaryStatisticalBo {

        /**
         * Merchant code.
         */
        private String merchantCode;

        /**
         * Outlet code.
         */
        private String outletCode;

        /**
         * Cpg code.
         */
        private String cpgCode;

        /**
         * Number of vouchers.
         */
        private int numberOfVouchers = 1;

        /**
         * Total amount.
         */
        private BigDecimal totalAmount;

        private String customerCode;

        private String reactivatedReason;

        private String ownerCustomer;

        public static GcReactivatedSummaryStatisticalBo convert(GcReactivatedBo reactivatedBo) {

            return new GcReactivatedSummaryStatisticalBo()
                    .setMerchantCode(reactivatedBo.getMerchantCode())
                    .setOutletCode(reactivatedBo.getOutletCode())
                    .setCpgCode(reactivatedBo.getCpgCode())
                    .setTotalAmount(reactivatedBo.getDenomination())
                    .setCustomerCode(reactivatedBo.getCustomerCode())
                    .setReactivatedReason(reactivatedBo.getUnblockReason())
                    .setOwnerCustomer(reactivatedBo.getOwnerCustomer());
        }

        public GcReactivatedSummaryStatisticalBo merge(GcReactivatedSummaryStatisticalBo bo) {

            this.numberOfVouchers += bo.getNumberOfVouchers();
            this.totalAmount = this.totalAmount.add(bo.getTotalAmount());

            return this;
        }

        public static String groupKey(GcReactivatedBo reactivatedBo) {

            return StringUtils.join(",", reactivatedBo.getMerchantCode(), reactivatedBo.getOutletCode(),
                    reactivatedBo.getCpgCode(), reactivatedBo.getOwnerCustomer(), reactivatedBo.getUnblockReason());
        }

    }
}
