package com.gtech.gvcore.service.distribution;

import com.gtech.commons.result.PageResult;
import com.gtech.gvcore.common.request.distribution.ConfirmDistributionRequest;
import com.gtech.gvcore.common.request.distribution.QueryCustomerDistributionRequest;
import com.gtech.gvcore.common.request.distribution.QueryDistributionDetailRequest;
import com.gtech.gvcore.common.request.distribution.SaveDistributionRequest;
import com.gtech.gvcore.common.response.distribution.GetCustomerDistributionResult;
import com.gtech.gvcore.common.response.distribution.QueryCustomerDistributionResult;
import com.gtech.gvcore.common.response.distribution.QueryDistributionDetailResult;
import com.gtech.gvcore.dto.SubmittedDistributionDetail;

/**
 * @ClassName DistributionService
 * @Description DistributionService
 * <AUTHOR>
 * @Date 2022/8/9 10:57
 * @Version V1.0
 **/
public interface DistributionService {
    String saveDistribution(SaveDistributionRequest request);

    void confirmDistribution(ConfirmDistributionRequest request);

    void progressDistribution(ConfirmDistributionRequest request, DistributionService distributionService);

    void beforeProgressDistribution(SubmittedDistributionDetail submittedDistributionDetail, String updateUserCode);

    void refreshDistributionCompletion();

    void resendVoucherEmail(String distributionItemCode, String voucherCode);

    void changeResendVoucherEmail(String distributionItemCode, String voucherCode, String email);

    PageResult<QueryCustomerDistributionResult> queryDistributionList(QueryCustomerDistributionRequest param);

    GetCustomerDistributionResult getCustomerDistribution(String distributionCode);

    void deleteCustomerDistribution(ConfirmDistributionRequest request);

    PageResult<QueryDistributionDetailResult> queryDistributionDetailList(QueryDistributionDetailRequest param);

    String downloadDistributionDetail(QueryDistributionDetailRequest param);
}
