package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName EgvTrackingBo
 * @Description
 * <AUTHOR>
 * @Date 2022/10/18 16:15
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class EgvTrackingBo implements GroupNewTransactionByVoucherCodeSupport {

    private String cpgCode;

    private String outletCode;

    private String voucherCode;

    private String transactionDate;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    private String transactionType;

    private String transactionCode;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        this.transactionCode = transactionCode;
    }
}
