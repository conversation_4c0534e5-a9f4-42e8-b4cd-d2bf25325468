package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/22 10:28
 */
@Getter
@Setter
@Accessors(chain = true)
public class RedemptionQueryData extends TransactionDataPageParam implements ReportQueryParam {

    //transaction date start 交易时间 开始
    private Date transactionDateStart;

    //transaction date end 交易时间 结束
    private Date transactionDateEnd;

    //merchant code list
    private List<String> merchantCodeList;

    //outlet code list
    private List<String> outletCodeList;

    //cpg code list
    private List<String> cpgCodeList;

    //invoice number 发票编号
    private String invoiceNumber;

    //Voucher code num start
    private Long voucherCodeNumStart;

    //Voucher code number end
    private Long voucherCodeNumEnd;

    private String transactionType;

    //二次加工条件
    private List<String> transactionCodeList;

}
