package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 14:19
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class TransactionsDetailedBean {

    @ExcelProperty(value = "Transaction Type")
    private String transactionType;

    @ExcelProperty(value = "Merchant")
    private String merchant;

    @ExcelProperty(value = "Outlet")
    private String outlet;

    @ExcelProperty(value = "Transaction Date")
    private String transactionDate;

    @ExcelProperty(value = "Voucher Number")
    private String voucherNumber;

    @ExcelProperty(value = "Initiated By")
    private String initiatedBy;

    @ExcelProperty(value = "POS Name")
    private String posName;

    @ExcelProperty(value = "Terminal")
    private String terminal;

    @ExcelProperty(value = "Organization Name")
    private String organizationName;

    @ExcelProperty(value = "Batch Number")
    private String batchNumber;

    @ExcelProperty(value = "Login Source")
    private String loginSource;

    @ReportAmountValue
    @ExcelProperty(value = "Denomination", converter = ExportExcelNumberConverter.class)
    private String denomination;

    @ExcelProperty(value = "Actual Outlet")
    private String actualOutlet;

    @ExcelProperty(value = "Forwarding Entity Id")
    private String forwardingEntityId;

    @ExcelProperty(value = "Response Message")
    private String responseMessage;

    @ExcelProperty(value = "Transaction Mode")
    private String transactionMode;

    @ExcelProperty(value = "Customer Salutation")
    private String customerSalutation;

    @ExcelProperty(value = "Customer First Name")
    private String customerFirstName;

    @ExcelProperty(value = "Customer Last Name")
    private String customerLastName;

    @ExcelProperty(value = "Mobile")
    private String mobile;

    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;

    @ExcelProperty(value = "Other Input Parameter")
    private String otherInputParameter;
}
