package com.gtech.gvcore.service.report.export.file;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.NumberUtils;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @ClassName ExportExcelNumberConverter
 * @Description NumberConvert
 * <AUTHOR>
 * @Date 2023/2/17 18:02
 * @Version V1.0
 **/
@Slf4j
public class ExportExcelNumberConverter implements Converter<String> {

    @Override
    public Class<?> supportJavaTypeKey() {

        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {

        return CellDataTypeEnum.NUMBER;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {

        if (StringUtils.isBlank(value)) return new WriteCellData<>();

        try {

            return NumberUtils.formatToCellData(GvConvertUtils.toBigDecimal(value, BigDecimal.ZERO), contentProperty);

        } catch (NumberFormatException e) {

            log.info("ExportExcelNumberConverter convertToExcelData error:{}", e.getMessage());

            return new WriteCellData<>(value);
        }


    }
}