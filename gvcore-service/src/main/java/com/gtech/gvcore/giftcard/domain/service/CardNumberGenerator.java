package com.gtech.gvcore.giftcard.domain.service;

import com.gtech.gvcore.dto.CardNumberConfigDto;
import com.gtech.gvcore.service.CardNumberConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 礼品卡卡号生成器
 * 生成16位礼品卡卡号，包含以下规则：
 * - 第1位：钱包主机标识
 * - 第2-3位：概念标识符
 * - 第4-5位：产品品牌
 * - 第6位：校验位
 * - 第7位：面额代码
 * - 第8-9位：发行年份
 * - 第10-16位：随机数
 */
@Component
public class CardNumberGenerator {
    
    // 钱包主机标识
    private static final int MAP_DIGITAL = 9;
    private static final int PARTNER_DIGITAL = 8;
    private static final int MAP_PHYSICAL = 7;
    
    // 概念标识符
    private static final String CONCEPT_MAP = "00";
    private static final String CONCEPT_MAP_TECH = "01";
    private static final String CONCEPT_DEPT_STORES = "02";
    private static final String CONCEPT_FASHION = "03";
    private static final String CONCEPT_ACTIVE = "04";
    private static final String CONCEPT_FNB = "05";
    private static final String CONCEPT_LEISURE = "06";
    private static final String CONCEPT_BHP = "07";
    private static final String CONCEPT_KIDS = "08";
    private static final String CONCEPT_BOOKS = "09";
    private static final String CONCEPT_TRAVEL = "10";
    private static final String CONCEPT_OTHERS = "11";
    
    // 品牌代码
    private static final String BRAND_GROUP = "00";
    
    // 面额代码
    private static final int DENOM_0K = 0;
    private static final int DENOM_1500K = 1;
    private static final int DENOM_2500K = 2;
    private static final int DENOM_5000K = 3;
    private static final int DENOM_500K = 4;
    private static final int DENOM_1000K = 5;
    private static final int DENOM_1250K = 6;
    private static final int DENOM_3000K = 7;
    private static final int DENOM_3500K = 8;
    
    private final Random random = new Random();
    private final AtomicInteger brandCounter = new AtomicInteger(1);


    @Autowired
    private CardNumberConfigService cardNumberConfigService;

    /**
     * 生成16位卡号
     * @param walletHost 钱包主机类型
     * @param conceptId 概念标识符
     * @param brand 品牌
     * @param denomination 面额
     * @return 16位卡号
     */
    public String generate(String walletHost, String conceptId, String brand, BigDecimal denomination) {
        StringBuilder cardNumber = new StringBuilder();
        
        // 1. 添加钱包主机标识 (1位)
        cardNumber.append(walletHost);
        
        // 2. 添加概念标识符 (2位)
        cardNumber.append(conceptId);
        
        // 3. 添加品牌代码 (2位)
        cardNumber.append(brand);
        
        // 4. 添加临时占位符作为校验位 (1位)
        cardNumber.append('0');
        
        // 5. 添加面额代码 (1位)
        cardNumber.append(getDenominationCode(denomination));
        
        // 6. 添加发行年份 (2位)
        cardNumber.append(getIssuanceYear());
        
        // 7. 添加随机数字 (7位)
        String randomNumber = String.format("%07d", random.nextInt(10000000));
        cardNumber.append(randomNumber);
        
        // 8. 计算并替换校验位
        int checkDigit = calculateCheckDigit(
            cardNumber.charAt(0),
            cardNumber.charAt(cardNumber.length() - 1)
        );
        cardNumber.setCharAt(5, Character.forDigit(checkDigit, 10));
        
        return cardNumber.toString();
    }
    
    private int getWalletHostDigit(String walletHost) {
        switch (walletHost.toLowerCase()) {
            case "map_digital":
                return MAP_DIGITAL;
            case "partner_digital":
                return PARTNER_DIGITAL;
            case "map_physical":
                return MAP_PHYSICAL;
            default:
                throw new IllegalArgumentException("Invalid wallet host: " + walletHost);
        }
    }
    
    private String getBrandCode(String brand) {
        if ("GROUP".equals(brand)) {
            return BRAND_GROUP;
        }
        // 为其他品牌生成递增编号
        int code = brandCounter.getAndIncrement();
        return String.format("%02d", code % 100);
    }


    
    private int getDenominationCode(BigDecimal denomination) {
        BigDecimal amount = denomination.setScale(0, BigDecimal.ROUND_HALF_UP);

        List<CardNumberConfigDto> cardNumberConfigDtos = cardNumberConfigService.queryAllDenominationConfigList();
        //如果配置中含有则使用配置中的 code
        for (CardNumberConfigDto cardNumberConfigDto : cardNumberConfigDtos) {
            if (amount.compareTo(new BigDecimal(cardNumberConfigDto.getDenomination())) == 0) {
                return Integer.parseInt(cardNumberConfigDto.getCode());
            }
        }

        switch (amount.toString()) {
            case "0":
                return DENOM_1500K;
            case "1500000":
                return DENOM_1500K;
            case "2500000":
                return DENOM_2500K;
            case "5000000":
                return DENOM_5000K;
            case "500000":
                return DENOM_500K;
            case "1000000":
                return DENOM_1000K;
            case "1250000":
                return DENOM_1250K;
            case "3000000":
                return DENOM_3000K;
            case "3500000":
                return DENOM_3500K;
            default:
                throw new IllegalArgumentException("Invalid denomination: " + denomination);
        }
    }
    
    private String getIssuanceYear() {
        int year = LocalDateTime.now().getYear();
        return String.format("%02d", year % 100);
    }
    
    /**
     * 计算校验位
     * 规则：(第1位值 + 第16位值) * 3，如果结果是两位数，取第二位
     */
    private int calculateCheckDigit(char firstDigit, char lastDigit) {
        // 获取数字值
        int first = Character.getNumericValue(firstDigit);
        int last = Character.getNumericValue(lastDigit);
        
        // 计算校验位
        int sum = (first + last) * 3;
        
        // 如果结果是两位数，取第二位
        return sum >= 10 ? sum % 10 : sum;
    }
    
    /**
     * 验证卡号是否有效
     */
    public boolean isValid(String cardNumber) {
        if (cardNumber == null || cardNumber.length() != 16 || !cardNumber.matches("\\d{16}")) {
            return false;
        }
        
        // 获取第1位和第16位的值
        int firstDigit = Character.getNumericValue(cardNumber.charAt(0));
        int lastDigit = Character.getNumericValue(cardNumber.charAt(15));
        
        // 计算校验和
        int sum = (firstDigit + lastDigit) * 3;
        
        // 获取期望的校验位
        int expectedCheckDigit = Character.getNumericValue(cardNumber.charAt(5));
        
        // 如果和是两位数，取第二位进行比较
        int actualCheckDigit = sum >= 10 ? sum % 10 : sum;
        
        return expectedCheckDigit == actualCheckDigit;
    }
    
    /**
     * 生成唯一的礼品卡卡号
     * 封装默认参数，方便批量生成时使用
     * @return 16位唯一卡号
     */
    public String generateUniqueCardNumber() {
        // 默认参数: 数字钱包，核心概念，集团品牌，默认1000面额
        return generate("1", "01", "01", new BigDecimal("1000000"));
    }
} 