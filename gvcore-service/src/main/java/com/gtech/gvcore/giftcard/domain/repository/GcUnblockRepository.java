package com.gtech.gvcore.giftcard.domain.repository;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.giftcard.domain.model.GcUnblockRecord;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcUnBlockEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcUnblockMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;
import java.util.Optional;

/**
 * 礼品卡启用记录仓储实现
 */
@Repository
public class GcUnblockRepository {

    @Autowired
    private GcUnblockMapper gcUnblockMapper;
    
    /**
     * 保存启用记录
     */
    @Transactional
    public GcUnblockRecord save(GcUnblockRecord record) {
        GcUnBlockEntity entity = BeanCopyUtils.jsonCopyBean(record, GcUnBlockEntity.class);
        
        if (entity.getId() == null) {
            // 新增记录时设置创建时间和更新时间
            entity.setCreateTime(new java.util.Date());
            entity.setUpdateTime(new java.util.Date());
            gcUnblockMapper.insertSelective(entity);
        } else {
            // 更新记录时只设置更新时间
            entity.setUpdateTime(new java.util.Date());
            gcUnblockMapper.updateByPrimaryKey(entity);
        }
        
        return record;
    }

    /**
     * 根据卡号查询启用记录
     */
    public List<GcUnblockRecord> findByCardNumber(String cardNumber) {
        Weekend<GcUnBlockEntity> weekend = Weekend.of(GcUnBlockEntity.class);
        weekend.weekendCriteria().andEqualTo(GcUnBlockEntity::getCardNumber, cardNumber);
        List<GcUnBlockEntity> entities = gcUnblockMapper.selectByCondition(weekend);
        return BeanCopyUtils.jsonCopyList(entities, GcUnblockRecord.class);
    }
}
