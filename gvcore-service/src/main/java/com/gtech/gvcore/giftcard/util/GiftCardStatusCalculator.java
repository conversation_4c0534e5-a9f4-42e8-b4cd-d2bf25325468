package com.gtech.gvcore.giftcard.util;

import com.gtech.gvcore.common.enums.GcCardStatusReportEnum;
import com.gtech.gvcore.giftcard.domain.model.GcCardStatus;
import com.gtech.gvcore.giftcard.domain.model.GcMgtCardStatus;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
@Builder
public class GiftCardStatusCalculator {

    private String cardStatus;

    private Integer activationExtensionCount;

    private Date expiryTime;

    private String managementStatus;

    private Date activationDeadline;


    public String determineCardStatus() {

        //0. Destroy优先级最高
        if (isDestroyed()) {
            return GcCardStatusReportEnum.DESTROY.getStatus();
        }
        // 2. 检查是否已过期
        if (isExpired()) {
            return GcCardStatusReportEnum.EXPIRED.getStatus();
        }
        // 3. 检查是否被停用
        if (isDeactivated()) {
            return GcCardStatusReportEnum.DEACTIVATED.getStatus();
        }
        // 4. 检查零余额状态
        if (isZeroBalanceStatus()) {
            return GcCardStatusReportEnum.ZERO_BALANCE.getStatus();
        }
        // 5. 检查激活状态
        if (isActivated()) {
            return GcCardStatusReportEnum.ACTIVATED.getStatus();
        }
        // 5. 检查激活期限状态
        return determineActivationStatus();
    }

    private boolean isDestroyed() {
        return GcMgtCardStatus.DESTROY.name().equals(this.getManagementStatus());
    }


    /**
     * 检查是否为零余额状态
     */
    private boolean isZeroBalanceStatus() {
        return GcCardStatus.ZERO_BALANCE.name().equals(this.getCardStatus());
    }

    /**
     * 检查是否已过期
     */
    private boolean isExpired() {
        return this.getExpiryTime() != null && this.getExpiryTime().before(new Date())
                && !Objects.equals(this.getCardStatus(), GcCardStatus.ZERO_BALANCE.name());
    }

    /**
     * 检查是否被停用
     */
    private boolean isDeactivated() {
        return GcMgtCardStatus.DISABLE.name().equals(this.getManagementStatus());
    }

    private boolean isActivated() {
        return GcCardStatus.ACTIVATED.name().equals(this.getCardStatus());
    }


    /**
     * 确定激活状态
     */
    private String determineActivationStatus() {
        Date activationDeadline = this.getActivationDeadline();

        if (hasExtensions()) {
            return GcCardStatusReportEnum.ACTIVATION_PERIOD_EXTENDED.getStatus();
        }
        if (activationDeadline.before(new Date())) {
            return GcCardStatusReportEnum.ACTIVATION_PERIOD_ENDED.getStatus();
        }
        return mapCardStatus(this.getCardStatus());
    }

    /**
     * 检查是否有延期记录
     */
    private boolean hasExtensions() {
        Integer extensionCount = this.getActivationExtensionCount();
        return extensionCount != null && extensionCount > 0;
    }

    private String mapCardStatus(String cardStatus) {
        if (cardStatus.equals(GcCardStatus.PURCHASED.name())) {
            return GcCardStatusReportEnum.PURCHASED.getStatus();
        }
        if (cardStatus.equals(GcCardStatus.ACTIVATED.name())) {
            return GcCardStatusReportEnum.ACTIVATED.getStatus();
        }
        return cardStatus;
    }
}
