package com.gtech.gvcore.giftcard.infrastructure.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcActivationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 礼品卡激活记录数据访问接口
 */
@Mapper
public interface GcActivationMapper extends GTechBaseMapper<GcActivationEntity> {
    
    /**
     * 根据卡号查询激活记录
     */
    @Select("SELECT * FROM gc_activation WHERE card_number = #{cardNumber} LIMIT 1")
    GcActivationEntity selectByCardNumber(@Param("cardNumber") String cardNumber);
    
    /**
     * 根据激活码查询激活记录
     */
    @Select("SELECT * FROM gc_activation WHERE activation_code = #{activationCode} LIMIT 1")
    GcActivationEntity selectByActivationCode(@Param("activationCode") String activationCode);
    
    /**
     * 根据激活人查询激活记录
     */
    @Select("SELECT * FROM gc_activation WHERE activated_by = #{activatedBy}")
    List<GcActivationEntity> selectByActivatedBy(@Param("activatedBy") String activatedBy);
} 