package com.gtech.gvcore.giftcard.masterdata.gcpg.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "Gift card cpg response")
public class GcCpgResponse {

    @ApiModelProperty(value = "issuerCode")
    private String issuerCode;
    @ApiModelProperty(value = "cpgCode")
    private String cpgCode;
    @ApiModelProperty(value = "cpgName")
    private String cpgName;
    @ApiModelProperty(value = "automaticActivate")
    private Boolean automaticActivate;

    @ApiModelProperty(value = "activationPeriod")
    private String activationPeriod;
    @ApiModelProperty(value = "activationGracePeriod")
    private String activationGracePeriod;
    @ApiModelProperty(value = "effectivePeriod")
    private String effectivePeriod;

    // Activation Period
    private Integer activationPeriodYears;
    private Integer activationPeriodMonths;
    private Integer activationPeriodDays;
    private Integer activationPeriodHours;

    // Activation Grace Period
    private Integer activationGracePeriodYears;
    private Integer activationGracePeriodMonths;
    private Integer activationGracePeriodDays;
    private Integer activationGracePeriodHours;

    // Effective Period
    private Integer effectivePeriodYears;
    private Integer effectivePeriodMonths;
    private Integer effectivePeriodDays;
    private Integer effectivePeriodHours;

    private String currency;
    private BigDecimal denomination;
    private String articleMopCode;
    private String walletHost;
    private String conceptIdentifier;
    private String productBrand;
    private String coverFrontUrl;
    private String coverBackUrl;
    private String status;
    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;
}
