package com.gtech.gvcore.giftcard.domain.model;

import lombok.Data;
import lombok.Getter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 礼品卡余额调整记录
 */
@Data
public class GcBalanceAdjustmentRecord {
    private final String id;
    private final String cardNumber;
    private final BigDecimal adjustmentAmount;
    private final BigDecimal balanceBefore;
    private final BigDecimal balanceAfter;
    private final String adjustmentType;
    private final String reason;
    private final String adjustedBy;
    private final LocalDateTime adjustmentTime;
    
    /**
     * 创建余额调整记录
     */
    public GcBalanceAdjustmentRecord(
        String cardNumber,
        BigDecimal adjustmentAmount,
        BigDecimal balanceBefore,
        String adjustmentType,
        String reason,
        String adjustedBy
    ) {
        this.id = UUID.randomUUID().toString();
        this.cardNumber = cardNumber;
        this.adjustmentAmount = adjustmentAmount;
        this.balanceBefore = balanceBefore;
        this.balanceAfter = balanceBefore.add(adjustmentAmount);
        this.adjustmentType = adjustmentType;
        this.reason = reason;
        this.adjustedBy = adjustedBy;
        this.adjustmentTime = LocalDateTime.now();
        
        // 验证余额是否合法
        if (this.balanceAfter.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("调整后余额不能为负");
        }
    }
} 