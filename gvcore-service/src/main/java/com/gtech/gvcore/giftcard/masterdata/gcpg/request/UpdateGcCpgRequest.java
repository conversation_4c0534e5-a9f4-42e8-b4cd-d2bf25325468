package com.gtech.gvcore.giftcard.masterdata.gcpg.request;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "UpdateGcCpgRequest")
public class UpdateGcCpgRequest extends CreateGcCpgRequest{

    @ApiModelProperty(value = "status")
    private String status;

    @ApiModelProperty(value = "cpgCode", required = true)
    private String cpgCode;

    @ApiModelProperty(value = "updateUser", required = true)
    private String updateUser;

}
