package com.gtech.gvcore.giftcard.infrastructure.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcSalesEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 礼品卡销售记录数据访问接口
 */
@Mapper
public interface GcSalesMapper extends GTechBaseMapper<GcSalesEntity> {

    /**
     * 根据销售编码查询销售记录
     */
    @Select("SELECT * FROM gc_sales WHERE sales_code = #{salesCode} LIMIT 1")
    GcSalesEntity selectBySalesCode(@Param("salesCode") String salesCode);

    /**
     * 根据卡号查询销售记录
     */
    @Select("SELECT * FROM gc_sales WHERE card_number = #{cardNumber}")
    List<GcSalesEntity> selectByCardNumber(@Param("cardNumber") String cardNumber);

    /**
     * 根据卡号查询销售记录
     */
    @Select("<script>" +
                    "SELECT * FROM gc_sales WHERE card_number in " +
                    "<foreach collection=\"cardNumbers\" item=\"cardNumber\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
                    "       #{cardNumber} " +
                    "      </foreach> " +
                    " </script>")
    List<GcSalesEntity> selectByCardNumbers(@Param("cardNumbers") List<String> cardNumbers);

    /**
     * 根据客户ID查询销售记录
     */
    @Select("SELECT * FROM gc_sales WHERE customer_id = #{customerId}")
    List<GcSalesEntity> selectByCustomerId(@Param("customerId") String customerId);

    /**
     * 根据发票号查询销售记录
     */
    @Select("SELECT * FROM gc_sales WHERE invoice_number = #{invoiceNumber}")
    List<GcSalesEntity> selectByInvoiceNumber(@Param("invoiceNumber") String invoiceNumber);

    /**
     * 根据商户代码查询销售记录
     */
    @Select("SELECT * FROM gc_sales WHERE merchant_code = #{merchantCode}")
    List<GcSalesEntity> selectByMerchantCode(@Param("merchantCode") String merchantCode);
} 