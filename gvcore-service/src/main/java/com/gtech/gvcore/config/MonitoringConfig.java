package com.gtech.gvcore.config;

import com.gtech.gvcore.monitor.UnifiedMonitorManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 监控配置类
 * 负责监控系统的配置和定时任务
 */
@Slf4j
@Component
public class MonitoringConfig {

    @Autowired
    private UnifiedMonitorManager monitorManager;

    @Value("${gv.monitor.stats.log.enabled:true}")
    private boolean statsLogEnabled;

    @Value("${gv.monitor.stats.reset.enabled:false}")
    private boolean autoResetEnabled;

    /**
     * 应用启动完成后记录内存使用情况
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("=== 应用启动完成 - 内存监控信息 ===");
        com.alibaba.fastjson.JSONObject memoryInfo = monitorManager.getCurrentMemoryInfo();
        log.info("启动后内存使用: {}", memoryInfo.toJSONString());
        log.info("统一监控系统已就绪");
    }

    /**
     * 定时记录全局监控统计（每小时）
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void logGlobalStats() {
        if (!statsLogEnabled) {
            return;
        }

        try {
            com.alibaba.fastjson.JSONObject stats = monitorManager.getGlobalStats();
            log.info("=== 监控统计报告 ===");
            log.info("统计信息: {}", stats.toJSONString());
            log.info("==================");
        } catch (Exception e) {
            log.error("记录监控统计失败", e);
        }
    }

    /**
     * 定时重置监控统计（每天凌晨）
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void resetDailyStats() {
        if (!autoResetEnabled) {
            return;
        }

        try {
            // 先记录最后的统计信息
            com.alibaba.fastjson.JSONObject finalStats = monitorManager.getGlobalStats();
            log.info("=== 每日统计重置 - 最终统计 ===");
            log.info("重置前统计: {}", finalStats.toJSONString());
            
            // 重置统计
            monitorManager.resetGlobalStats();
            log.info("每日监控统计已重置");
        } catch (Exception e) {
            log.error("重置每日监控统计失败", e);
        }
    }

    /**
     * 定时记录内存使用情况（每30分钟）
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void logMemoryUsage() {
        if (!statsLogEnabled) {
            return;
        }

        try {
            com.alibaba.fastjson.JSONObject memoryInfo = monitorManager.getCurrentMemoryInfo();
            
            // 解析内存信息
            com.alibaba.fastjson.JSONObject heap = memoryInfo.getJSONObject("heap");
            if (heap != null) {
                long heapUsed = heap.getLongValue("used");
                long heapMax = heap.getLongValue("max");
                String heapUsagePercent = heap.getString("usagePercent");
                
                log.info("内存使用情况 - 堆内存: {}MB/{}MB ({}%)", heapUsed, heapMax, heapUsagePercent);
                
                // 如果内存使用超过80%，记录警告
                if (Double.parseDouble(heapUsagePercent) > 80.0) {
                    log.warn("⚠️ 内存使用率过高: {}%，建议检查内存泄漏", heapUsagePercent);
                }
            }
        } catch (Exception e) {
            log.error("记录内存使用情况失败", e);
        }
    }

    /**
     * 手动触发监控报告
     */
    public void generateMonitoringReport() {
        log.info("=== 手动监控报告 ===");
        
        try {
            // 全局统计
            com.alibaba.fastjson.JSONObject globalStats = monitorManager.getGlobalStats();
            log.info("全局统计: {}", globalStats.toJSONString());
            
            // 内存信息
            com.alibaba.fastjson.JSONObject memoryInfo = monitorManager.getCurrentMemoryInfo();
            log.info("内存信息: {}", memoryInfo.toJSONString());
            
            // 当前监控状态
            boolean isMonitoring = monitorManager.isMonitoring();
            String currentRequestId = monitorManager.getCurrentRequestId();
            log.info("监控状态: 正在监控={}, 当前请求ID={}", isMonitoring, currentRequestId);
            
        } catch (Exception e) {
            log.error("生成监控报告失败", e);
        }
        
        log.info("==================");
    }
}
