package com.gtech.gvcore.config;

import com.alibaba.fastjson.JSONObject;
import com.gtech.gvcore.common.annotation.TrackObjects;
import com.gtech.gvcore.monitor.EnhancedObjectTracker;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 对象追踪AOP切面
 * 处理@TrackObjects注解
 */
@Slf4j
@Aspect
@Component
@Order(100) // 确保在其他切面之后执行
public class ObjectTrackingAspectconfig {

    /**
     * 处理方法级别的@TrackObjects注解
     */
    @Around("@annotation(com.gtech.gvcore.common.annotation.TrackObjects)")
    public Object trackMethodObjects(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        TrackObjects annotation = method.getAnnotation(TrackObjects.class);
        
        if (!annotation.enabled()) {
            return joinPoint.proceed();
        }
        
        return executeWithTracking(joinPoint, annotation, method.getName());
    }

    /**
     * 处理类级别的@TrackObjects注解
     */
    @Around("@within(com.gtech.gvcore.common.annotation.TrackObjects) && !@annotation(com.gtech.gvcore.common.annotation.TrackObjects)")
    public Object trackClassObjects(ProceedingJoinPoint joinPoint) throws Throwable {
        Class<?> targetClass = joinPoint.getTarget().getClass();
        TrackObjects annotation = targetClass.getAnnotation(TrackObjects.class);
        
        if (annotation == null || !annotation.enabled()) {
            return joinPoint.proceed();
        }
        
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        return executeWithTracking(joinPoint, annotation, targetClass.getSimpleName() + "." + signature.getMethod().getName());
    }

    /**
     * 执行带追踪的方法
     */
    private Object executeWithTracking(ProceedingJoinPoint joinPoint, TrackObjects annotation, String methodName) throws Throwable {
        if (annotation == null) {
            log.warn("TrackObjects注解为null，跳过追踪");
            return joinPoint.proceed();
        }

        // 构建描述信息
        String description = (annotation.description() != null && !annotation.description().isEmpty()) ?
            annotation.description() : methodName;

        // 开始追踪
        String sessionId = null;
        try {
            sessionId = EnhancedObjectTracker.startTracking(
                description,
                annotation.objectTypes() != null ? annotation.objectTypes() : new String[0],
                annotation.includeChildThreads()
            );
        } catch (Exception e) {
            log.error("开始对象追踪失败: {}", e.getMessage(), e);
            return joinPoint.proceed();
        }
        
        try {
            // 执行原方法
            Object result = joinPoint.proceed();
            
            // 如果启用自动日志，输出统计信息
            if (annotation.autoLog()) {
                JSONObject stats = EnhancedObjectTracker.getCurrentStats();
                if (stats != null && !stats.isEmpty()) {
                    Integer totalObjects = stats.getInteger("totalObjects");
                    if (totalObjects != null && totalObjects > 0) {
                        log.info("=== 对象追踪统计 === 方法: {}, 会话: {}, 总对象: {}, 详情: {}",
                            description, sessionId, totalObjects, stats.getJSONObject("objectCounts"));
                    }
                }
            }
            
            return result;
        } catch (Throwable throwable) {
            // 即使出现异常也要记录统计
            if (annotation.autoLog()) {
                try {
                    JSONObject stats = EnhancedObjectTracker.getCurrentStats();
                    if (stats != null && !stats.isEmpty()) {
                        Integer totalObjects = stats.getInteger("totalObjects");
                        log.warn("=== 对象追踪统计（异常） === 方法: {}, 会话: {}, 总对象: {}, 异常: {}",
                            description, sessionId, totalObjects != null ? totalObjects : 0, throwable.getMessage());
                    }
                } catch (Exception e) {
                    log.debug("获取异常时的追踪统计失败: {}", e.getMessage());
                }
            }
            throw throwable;
        } finally {
            // 结束追踪
            try {
                if (sessionId != null) {
                    EnhancedObjectTracker.endTracking();
                }
            } catch (Exception e) {
                log.error("结束对象追踪失败: {}", e.getMessage(), e);
            }
        }
    }
}
