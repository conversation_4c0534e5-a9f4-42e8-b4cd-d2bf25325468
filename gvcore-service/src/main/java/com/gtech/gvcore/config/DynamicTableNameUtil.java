package com.gtech.gvcore.config;

import com.gtech.gvcore.dao.mapper.ReportRequestMapper;
import com.gtech.gvcore.dao.model.OrderReportData;
import com.gtech.gvcore.dao.model.ReportRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.BoundSql;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class DynamicTableNameUtil {


    private final ReportRequestMapper reportRequestMapper;

    @Autowired
    public DynamicTableNameUtil(@Lazy ReportRequestMapper reportRequestMapper) {
        this.reportRequestMapper = reportRequestMapper;
    }

    public static String replaceTableName(String originalSql, String originalTableName, String newTableName) {
        return originalSql.replace(originalTableName, newTableName);
    }

    public  String replaceTableNameWithMonth(String originalSql, String originalTableName, BoundSql boundSql) {


        try {
            if (originalSql.contains("order_report_code") && null != getValue(boundSql)){
                //获取查询sql originalSql中order_report_code的值
                ReportRequest report = getValue(boundSql);

                Date createTime = report.getCreateTime();
                //获取某个Date类型数据的月份
                Calendar cal1 = Calendar.getInstance();
                cal1.setTime(createTime);
                int month1 = cal1.get(Calendar.MONTH) + 1;
                String newTableName1 = originalTableName + "_" + month1 % 3;
                return replaceTableName(originalSql, originalTableName, newTableName1);
            }
        }catch (Exception e){
            return monthGetIndex(originalSql, originalTableName);
        }

        return monthGetIndex(originalSql, originalTableName);
    }

    private static String monthGetIndex(String originalSql, String originalTableName) {
        Calendar cal = Calendar.getInstance();
        int month = cal.get(Calendar.MONTH) + 1;
        String newTableName = originalTableName + "_" + month % 3;
        return replaceTableName(originalSql, originalTableName, newTableName);
    }

    private ReportRequest getValue(BoundSql boundSql) {
        OrderReportData parameterObject = (OrderReportData) boundSql.getParameterObject();
        String orderReportCode = parameterObject.getOrderReportCode();

        ReportRequest req = new ReportRequest();
        req.setReportCode(orderReportCode);
        ReportRequest report = reportRequestMapper.selectOne(req);
        return report;
    }

}
