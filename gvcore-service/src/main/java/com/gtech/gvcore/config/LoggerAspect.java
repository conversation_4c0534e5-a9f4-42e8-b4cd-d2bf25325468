package com.gtech.gvcore.config;

import java.io.BufferedReader;
import java.io.InputStreamReader;

import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Queue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.web.util.OnCommittedResponseWrapper;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtech.gvcore.common.annotation.NotSysLogger;
import com.gtech.gvcore.monitor.annotation.TrackObjects;
import com.gtech.gvcore.dao.model.SysLogger;
import com.gtech.gvcore.monitor.UnifiedMonitorManager;
import com.gtech.gvcore.service.SystemLoggerService;

import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName LoggerAspect
 * @Description 调用日志切面
 * <AUTHOR>
 * @Date 2022/10/9 10:36
 * @Version V1.0
 **/
@Slf4j
@Aspect
@Component
@ConditionalOnProperty(value = "gv.system.logger.enable", matchIfMissing = true, havingValue = "true")
public class LoggerAspect {

    @Autowired
    private SystemLoggerService service;

    @Autowired
    private UnifiedMonitorManager monitorManager;

    // 日志队列
    private static final Queue<SysLogger> LOGGER_QUEUE = new ConcurrentLinkedQueue<>();

    // 本地线程锁
    private final Lock lock = new ReentrantLock();

    // 执行间隔
    private static final long EXECUTE_SLEEP_TIME = 5000L;

    // 执行状态
    private boolean enableRun = true;


    //管理是否实时监控内存
    @Value("${gv.system.logger.monitor.enable:true}")
    private static boolean enableMonitor = true;

    @Pointcut("@annotation(org.springframework.web.bind.annotation.PostMapping)")
    public void postMapping() {
        // NO SONAR
    }

    @Pointcut("@annotation(org.springframework.web.bind.annotation.GetMapping)")
    public void getMapping() {
        // NO SONAR
    }

    @Pointcut("@annotation(org.springframework.web.bind.annotation.PutMapping)")
    public void putMapping() {
        // NO SONAR
    }

    @Pointcut("@annotation(org.springframework.web.bind.annotation.DeleteMapping)")
    public void deleteMapping() {
        // NO SONAR
    }
    @Pointcut("@annotation(org.springframework.web.bind.annotation.RequestMapping)")
    public void requestMapping() {
        // NO SONAR
    }

    @Around(value = "postMapping() || getMapping() || putMapping() || deleteMapping() || requestMapping()")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {

        HttpServletRequest request = getRequest();
        if (null == request) {
            log.trace("LoggerAspect 解析错误 无法获取 HttpServletRequest");
            return proceedingJoinPoint.proceed();
        }

		final String servletPath = request.getRequestURI();
		if (excludeRequestPath(servletPath)) {
			log.trace("LoggerAspect 不记录的请求");
			return proceedingJoinPoint.proceed();
		}
        //init request info
        final NotSysLogger notSysLogger = getNotSysLogger(proceedingJoinPoint);
        final String userCode = request.getHeader("userCode");
        final String requestBody = getRequestBody(request, proceedingJoinPoint.getArgs());

        // Record start time and start monitoring
        final long startTime = System.currentTimeMillis();
        final String requestId = servletPath + "@" + startTime;

        // 检查是否有@TrackObjects注解，决定是否启用对象追踪
        boolean enableObjectTracking = hasTrackObjectsAnnotation(proceedingJoinPoint);

        // 只有在需要对象追踪时才开始监控
        if (enableObjectTracking) {
            monitorManager.startRequestMonitoring(requestId);
            log.debug("API {} 启用了对象追踪", servletPath);
        }

        try {

            log.debug("LoggerAspect request => {}" , request);

            //execute
            final Object proceed = proceedingJoinPoint.proceed();

            //response
            final String response = getResponseData(proceed);
            log.debug("LoggerAspect response => {}" , response);

            // Calculate execution time and end monitoring
            final long executionTime = System.currentTimeMillis() - startTime;

            // 结束监控并获取统计信息（如果启用了对象追踪）
            String objectStats = "{}";
            if (enableObjectTracking) {
                JSONObject monitoringResult = monitorManager.endRequestMonitoring();
                objectStats = monitoringResult.toJSONString();
            }

            this.saveLogger(servletPath, userCode, requestBody, response, executionTime, objectStats, notSysLogger);

            return proceed;

        } catch (Throwable throwable){
            log.error("切面异常：{}",throwable.getMessage());

            // Calculate execution time even for exceptions
            final long executionTime = System.currentTimeMillis() - startTime;

            // 结束监控（即使出现异常，如果启用了对象追踪）
            String objectStats = "{}";
            if (enableObjectTracking) {
                JSONObject monitoringResult = monitorManager.endRequestMonitoring();
                objectStats = monitoringResult.toJSONString();
            }

            this.saveLogger(servletPath, userCode, requestBody, JSON.toJSONString(throwable.getMessage()), executionTime, objectStats, notSysLogger);

            throw throwable;
        }

    }

    private String getRequestBody(final HttpServletRequest request, final Object[] args) {

        try {
            final StringBuilder requestBody = new StringBuilder();
            try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = bufferedReader.readLine()) != null) requestBody.append(line);
            }

            if (StringUtils.isBlank(requestBody.toString())){
                log.error("尝试解析请求原始参数失败.{}", JSON.toJSONString(requestBody));
                return getRequestParam(args);
            }
            return requestBody.toString();

        } catch (Exception e) {

            log.error("尝试解析请求原始参数失败.", e);

            return getRequestParam(args);
        }
    }

    private String getRequestParam(Object[] params) {
        try {
            if (ArrayUtils.isEmpty(params)) return null;

            return JSON.toJSONString(Arrays.stream(params)
                    .filter(e -> !(e instanceof HttpServletRequest))
                    .filter(e -> !(e instanceof BindingResult))
                    .filter(e -> !(e instanceof OnCommittedResponseWrapper))
                    .collect(Collectors.toList()));
        } catch (Exception e) {
            return JSON.toJSONString(e.getMessage());
        }
    }

    public static void main(String[] args) {
        try {
            String voucher ="M19";
            Long.valueOf(voucher);
        }catch (Exception e){
            System.out.println(JSON.toJSONString(e.getMessage()));
        }

    }

    private static NotSysLogger getNotSysLogger(ProceedingJoinPoint proceedingJoinPoint) {
        NotSysLogger notSysLogger;
        Signature signature = proceedingJoinPoint.getSignature();
        if (!(signature instanceof MethodSignature)) notSysLogger = null;
        else {
            MethodSignature methodSignature = (MethodSignature) signature;
            Method method = methodSignature.getMethod();
            notSysLogger = method.getDeclaredAnnotation(NotSysLogger.class);
        }
        return notSysLogger;
    }

    private void saveLogger(String servletPath, String userCode, String requestData, String response, long executionTime, String objectStats, NotSysLogger notSysLogger) {

        final SysLogger sysLogger = new SysLogger().setRequestData(requestData)
                .setRequestPath(servletPath)
                .setUserCode(userCode)
                .setResponseData(response)
                .setExecutionTime(executionTime)
                .setObjectStats(objectStats);

        if (null == notSysLogger) {
			// 全部不记录
			// LOGGER_QUEUE.add(sysLogger);
            return;
        }

        // 不记录日志
        if (notSysLogger.disableLogger()) return;

        //条件记录
        if (notSysLogger.notRequestLogger()) sysLogger.setRequestData(null);
        if (notSysLogger.notResponseLogger()) sysLogger.setResponseData(null);
        if (notSysLogger.notRequestPath()) sysLogger.setRequestPath(null);
        if (notSysLogger.notUser()) sysLogger.setUserCode(null);

        LOGGER_QUEUE.add(sysLogger);
    }

    private static HttpServletRequest getRequest() {

        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (null == requestAttributes) return null;

        ServletRequestAttributes attributes;
        try {
            attributes = (ServletRequestAttributes) requestAttributes;
        } catch (ClassCastException e) {
            log.error("LoggerAspect 解析错误 HttpServletRequest 转换失败", e);
            return null;
        }

        return attributes.getRequest();
    }

    private String getResponseData(Object param) {

        try {
            return null == param ? null : JSON.toJSONString(param);
        } catch (Exception e) {
            return JSON.toJSONString(e.getMessage());
        }
    }





    @PostConstruct
    public void init() {

        CompletableFuture.runAsync(() -> {
            log.info("LoggerAspects 开始监听日志队列");

            while (enableRun) {

                execute();
                try {
                    Thread.sleep(EXECUTE_SLEEP_TIME);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        });
    }

    private void execute() {

        try {
            if (lock.tryLock(7, TimeUnit.SECONDS)) {
                try {
                    while (!LOGGER_QUEUE.isEmpty()) service.addSysLogger(LOGGER_QUEUE.poll());
                } finally {
                    lock.unlock();
                }
            } else {
                log.info("saveLogger 获取锁失败");
            }
        } catch (InterruptedException ie) {
            log.info("saveLogger 获取锁异常");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("saveLogger exception: {}", e.getMessage());
        }
    }

    @PreDestroy
    public void destroy() {

        log.info("LoggerAspects 执行销毁...");

        enableRun = false;
        execute();

        log.info("LoggerAspects 销毁完成");
    }

    public boolean excludeRequestPath(String path) {

//        return "/error".equals(path);
		return path.indexOf("/gv/external") < 0;
    }

    /**
     * 检查方法或类是否有@TrackObjects注解
     */
    private boolean hasTrackObjectsAnnotation(ProceedingJoinPoint joinPoint) {
        try {
            // 检查方法级别的注解
            if (joinPoint.getSignature() instanceof org.aspectj.lang.reflect.MethodSignature) {
                org.aspectj.lang.reflect.MethodSignature signature = (org.aspectj.lang.reflect.MethodSignature) joinPoint.getSignature();
                java.lang.reflect.Method method = signature.getMethod();
                if (method.isAnnotationPresent(TrackObjects.class)) {
                    TrackObjects annotation = method.getAnnotation(TrackObjects.class);
                    return annotation.enabled();
                }
            }

            // 检查类级别的注解
            Class<?> targetClass = joinPoint.getTarget().getClass();
            if (targetClass.isAnnotationPresent(TrackObjects.class)) {
                TrackObjects annotation = targetClass.getAnnotation(TrackObjects.class);
                return annotation.enabled();
            }
        } catch (Exception e) {
            log.debug("检查@TrackObjects注解时发生异常: {}", e.getMessage());
        }

        return false;
    }


}
