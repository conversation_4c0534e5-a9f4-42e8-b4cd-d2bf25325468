package com.gtech.gvcore.components.atomic.impl;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.request.transactiondata.CreateTransactionDataRequest;
import com.gtech.gvcore.components.atomic.VoucherStatusStrategy;
import com.gtech.gvcore.components.atomic.request.AtomicRequest;
import com.gtech.gvcore.components.atomic.request.AtomicTransactionDataRequest;
import com.gtech.gvcore.service.TransactionDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TransactionDataImpl implements VoucherStatusStrategy {


    @Autowired
    private TransactionDataService transactionDataService;

    private AtomicRequest request;


    public TransactionDataImpl() {
    }

    public TransactionDataImpl(AtomicRequest request) {
        this.request = request;
    }


    @Override
    public int execute(AtomicRequest request) {

        if (null == request){
            request = this.request;
        }

        AtomicTransactionDataRequest transactionDataRequest = request.getTransactionDataRequest();
        CreateTransactionDataRequest dataRequest = BeanCopyUtils.jsonCopyBean(transactionDataRequest, CreateTransactionDataRequest.class);
        transactionDataService.createTransactionData(dataRequest);
        return 0;
    }
}
