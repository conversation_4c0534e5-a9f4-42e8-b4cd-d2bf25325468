package com.gtech.gvcore.components;

import com.gtech.gvcore.dao.model.SysLogger;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.service.TransactionDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class TransactionDataTaskComponent {

    @Autowired
    private TransactionDataService transactionDataService;

    private static final Queue<Map<String,List<TransactionData>>> TRANSACTION_DATA_QUEUE = new ConcurrentLinkedQueue<>();

    // 本地线程锁
    private final Lock lock = new ReentrantLock();

    // 执行间隔
    private static final long EXECUTE_SLEEP_TIME = 5000L;

    // 执行状态
    private boolean enableRun = true;

    @PostConstruct
    public void init() {

        CompletableFuture.runAsync(() -> {
            log.info("LoggerAspects 开始监听日志队列");

            while (enableRun) {

                execute();
                try {
                    Thread.sleep(EXECUTE_SLEEP_TIME);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        });
    }


    private void execute() {

        try {
            if (lock.tryLock(7, TimeUnit.SECONDS)) {
                try {
                    while (!TRANSACTION_DATA_QUEUE.isEmpty()) this.insertTransactionData(TRANSACTION_DATA_QUEUE.poll());
                } finally {
                    lock.unlock();
                }
            } else {
                log.info("saveLogger 获取锁失败");
            }
        } catch (InterruptedException ie) {
            log.info("saveLogger 获取锁异常");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("saveLogger exception: {}", e.getMessage());
        }
    }


    private void insertTransactionData(Map<String, List<TransactionData>> transactionDataMap){
        log.info("开始插入交易记录任务{}",transactionDataMap.keySet());
        transactionDataMap.forEach((k,v)->{
            //判断数量，如果超出1000，按照1000分割
            if (v.size()>1000){

            }













        });
    }







}
