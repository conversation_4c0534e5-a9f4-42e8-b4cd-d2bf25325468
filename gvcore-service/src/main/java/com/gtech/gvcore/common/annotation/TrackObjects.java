package com.gtech.gvcore.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 对象追踪注解
 * 用于标记需要追踪对象创建的方法或类
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface TrackObjects {
    
    /**
     * 是否启用对象追踪
     * @return 默认启用
     */
    boolean enabled() default true;
    
    /**
     * 追踪的对象类型（可选，为空则追踪所有）
     * @return 要追踪的对象类型数组
     */
    String[] objectTypes() default {};
    
    /**
     * 是否追踪子线程中的对象创建
     * @return 默认追踪子线程
     */
    boolean includeChildThreads() default true;
    
    /**
     * 追踪描述（用于日志标识）
     * @return 追踪描述
     */
    String description() default "";
    
    /**
     * 是否在方法结束时自动输出统计
     * @return 默认自动输出
     */
    boolean autoLog() default true;
}
