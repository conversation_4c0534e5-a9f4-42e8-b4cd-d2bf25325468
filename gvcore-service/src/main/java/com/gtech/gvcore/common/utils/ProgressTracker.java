package com.gtech.gvcore.common.utils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 进度跟踪工具类
 * 基于Redis实现的任务进度跟踪，支持主任务和子任务的进度跟踪
 */
@Slf4j
@Component
public class ProgressTracker {
    
    private static final String PROGRESS_KEY_PREFIX = "progress:";
    private static final String MAIN_TASK_KEY_PREFIX = PROGRESS_KEY_PREFIX + "main:";
    private static final String SUB_TASK_KEY_PREFIX = PROGRESS_KEY_PREFIX + "sub:";
    private static final int DEFAULT_EXPIRY_DAYS = 7;
    
    @Autowired
    @Qualifier("progressTrackerRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    /**
     * 为进度跟踪器配置专用的RedisTemplate
     */
    @Bean("progressTrackerRedisTemplate")
    public RedisTemplate<String, Object> progressTrackerRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 使用 GenericJackson2JsonRedisSerializer 进行值序列化
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);
        
        // 使用 StringRedisSerializer 进行键序列化
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    /**
     * 初始化主任务进度
     *
     * @param taskId     任务唯一标识
     * @param totalItems 总项目数
     * @param taskName   任务名称
     * @return 任务状态对象
     */
    public TaskProgress initMainTask(String taskId, int totalItems, String taskName) {
        return initTask(MAIN_TASK_KEY_PREFIX, taskId, totalItems, taskName);
    }
    
    /**
     * 初始化子任务进度
     *
     * @param mainTaskId 主任务ID
     * @param subTaskId  子任务唯一标识
     * @param totalItems 总项目数
     * @param taskName   任务名称
     * @return 任务状态对象
     */
    public TaskProgress initSubTask(String mainTaskId, String subTaskId, int totalItems, String taskName) {
        String compositeKey = mainTaskId + ":" + subTaskId;
        return initTask(SUB_TASK_KEY_PREFIX, compositeKey, totalItems, taskName);
    }
    
    /**
     * 初始化任务进度
     */
    private TaskProgress initTask(String keyPrefix, String taskId, int totalItems, String taskName) {
        if (taskId == null || taskId.isEmpty()) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        String redisKey = keyPrefix + taskId;
        
        TaskProgress progress = TaskProgress.builder()
                .taskId(taskId)
                .taskName(taskName)
                .status(TaskStatus.PENDING)
                .processedItems(0)
                .totalItems(totalItems)
                .percentage(0)
                .startTime(System.currentTimeMillis())
                .lastUpdateTime(System.currentTimeMillis())
                .build();
        
        try {
            redisTemplate.opsForValue().set(redisKey, progress, DEFAULT_EXPIRY_DAYS, TimeUnit.DAYS);
            log.debug("初始化任务进度成功: {}", progress);
            return progress;
        } catch (Exception e) {
            log.error("初始化任务进度失败: {}", e.getMessage(), e);
            return progress;
        }
    }
    
    /**
     * 更新主任务进度
     *
     * @param taskId        任务唯一标识
     * @param processedItems 已处理项目数
     * @param status        任务状态
     * @param message       可选状态消息
     * @return 更新后的任务状态对象
     */
    public TaskProgress updateMainTaskProgress(String taskId, int processedItems, TaskStatus status, String message) {
        return updateTaskProgress(MAIN_TASK_KEY_PREFIX, taskId, processedItems, status, message);
    }
    
    /**
     * 更新子任务进度
     *
     * @param mainTaskId    主任务ID
     * @param subTaskId     子任务唯一标识
     * @param processedItems 已处理项目数
     * @param status        任务状态
     * @param message       可选状态消息
     * @return 更新后的任务状态对象
     */
    public TaskProgress updateSubTaskProgress(String mainTaskId, String subTaskId, int processedItems, TaskStatus status, String message) {
        String compositeKey = mainTaskId + ":" + subTaskId;
        return updateTaskProgress(SUB_TASK_KEY_PREFIX, compositeKey, processedItems, status, message);
    }
    
    /**
     * 更新任务进度
     */
    private TaskProgress updateTaskProgress(String keyPrefix, String taskId, int processedItems, TaskStatus status, String message) {
        if (taskId == null || taskId.isEmpty()) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        String redisKey = keyPrefix + taskId;
        
        try {
            TaskProgress currentProgress = (TaskProgress) redisTemplate.opsForValue().get(redisKey);
            
            if (currentProgress == null) {
                log.warn("任务进度不存在，无法更新: {}", taskId);
                return TaskProgress.builder()
                        .taskId(taskId)
                        .status(TaskStatus.UNKNOWN)
                        .processedItems(0)
                        .totalItems(0)
                        .percentage(0)
                        .startTime(System.currentTimeMillis())
                        .lastUpdateTime(System.currentTimeMillis())
                        .message("任务不存在")
                        .build();
            }
            
            // 计算新的百分比进度
            int percentage = (currentProgress.getTotalItems() <= 0) ? 0 :
                    (int) ((processedItems * 100.0) / currentProgress.getTotalItems());
            
            // 更新进度信息
            TaskProgress updatedProgress = TaskProgress.builder()
                    .taskId(currentProgress.getTaskId())
                    .taskName(currentProgress.getTaskName())
                    .status(status)
                    .processedItems(processedItems)
                    .totalItems(currentProgress.getTotalItems())
                    .percentage(percentage)
                    .startTime(currentProgress.getStartTime())
                    .lastUpdateTime(System.currentTimeMillis())
                    .message(message)
                    .build();
            
            // 保存到Redis
            redisTemplate.opsForValue().set(redisKey, updatedProgress, DEFAULT_EXPIRY_DAYS, TimeUnit.DAYS);
            log.debug("更新任务进度成功: {}", updatedProgress);
            
            return updatedProgress;
        } catch (Exception e) {
            log.error("更新任务进度失败: {}", e.getMessage(), e);
            return TaskProgress.builder()
                    .taskId(taskId)
                    .status(TaskStatus.ERROR)
                    .processedItems(processedItems)
                    .totalItems(100) // 默认总量
                    .percentage(0)
                    .startTime(System.currentTimeMillis())
                    .lastUpdateTime(System.currentTimeMillis())
                    .message("更新进度时发生错误: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 获取主任务进度
     *
     * @param taskId 任务唯一标识
     * @return 任务状态对象
     */
    public TaskProgress getMainTaskProgress(String taskId) {
        if (taskId == null || taskId.isEmpty()) {
            log.warn("获取主任务进度时，任务ID为空");
            return createDefaultTaskProgress(taskId, "任务ID不能为空");
        }
        return getTaskProgress(MAIN_TASK_KEY_PREFIX, taskId);
    }
    
    /**
     * 获取子任务进度
     *
     * @param mainTaskId 主任务ID
     * @param subTaskId  子任务唯一标识
     * @return 任务状态对象
     */
    public TaskProgress getSubTaskProgress(String mainTaskId, String subTaskId) {
        if (mainTaskId == null || mainTaskId.isEmpty()) {
            log.warn("获取子任务进度时，主任务ID为空");
            return createDefaultTaskProgress(subTaskId, "主任务ID不能为空");
        }
        if (subTaskId == null || subTaskId.isEmpty()) {
            log.warn("获取子任务进度时，子任务ID为空");
            return createDefaultTaskProgress(mainTaskId + ":unknown", "子任务ID不能为空");
        }
        String compositeKey = mainTaskId + ":" + subTaskId;
        return getTaskProgress(SUB_TASK_KEY_PREFIX, compositeKey);
    }
    
    /**
     * 获取主任务的所有子任务
     *
     * @param mainTaskId 主任务ID
     * @return 子任务ID到子任务进度的映射
     */
    public Map<String, TaskProgress> getSubTasks(String mainTaskId) {
        Map<String, TaskProgress> result = new HashMap<>();
        
        try {
            String keyPattern = SUB_TASK_KEY_PREFIX + mainTaskId + ":*";
            
            // 获取所有匹配的键
            for (Object key : Objects.requireNonNull(redisTemplate.keys(keyPattern))) {
                String redisKey = (String) key;
                // 从键中提取子任务ID
                String subTaskId = redisKey.substring((SUB_TASK_KEY_PREFIX + mainTaskId + ":").length());
                
                // 获取进度
                TaskProgress progress = (TaskProgress) redisTemplate.opsForValue().get(redisKey);
                if (progress != null) {
                    result.put(subTaskId, progress);
                }
            }
        } catch (Exception e) {
            log.error("获取主任务的子任务列表失败: {}", e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * 获取任务进度
     */
    private TaskProgress getTaskProgress(String keyPrefix, String taskId) {
        if (taskId == null || taskId.isEmpty()) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        String redisKey = keyPrefix + taskId;
        
        try {
            TaskProgress progress = (TaskProgress) redisTemplate.opsForValue().get(redisKey);
            
            if (progress == null) {
                log.debug("任务进度不存在: {}", taskId);
                return TaskProgress.builder()
                        .taskId(taskId)
                        .status(TaskStatus.UNKNOWN)
                        .processedItems(0)
                        .totalItems(0)
                        .percentage(0)
                        .startTime(System.currentTimeMillis())
                        .lastUpdateTime(System.currentTimeMillis())
                        .build();
            }
            
            return progress;
        } catch (Exception e) {
            log.error("获取任务进度失败: {}", e.getMessage(), e);
            return TaskProgress.builder()
                    .taskId(taskId)
                    .status(TaskStatus.ERROR)
                    .message("获取进度时发生错误: " + e.getMessage())
                    .processedItems(0)
                    .totalItems(0)
                    .percentage(0)
                    .startTime(System.currentTimeMillis())
                    .lastUpdateTime(System.currentTimeMillis())
                    .build();
        }
    }
    
    /**
     * 删除主任务进度及其所有子任务进度
     *
     * @param taskId 任务唯一标识
     */
    public void clearTask(String taskId) {
        try {
            // 删除主任务
            String mainTaskKey = MAIN_TASK_KEY_PREFIX + taskId;
            redisTemplate.delete(mainTaskKey);
            
            // 删除所有子任务
            String keyPattern = SUB_TASK_KEY_PREFIX + taskId + ":*";
            redisTemplate.delete(redisTemplate.keys(keyPattern));
            
            log.debug("已清理任务进度: {}", taskId);
        } catch (Exception e) {
            log.error("清理任务进度失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 计算总体进度百分比
     *
     * @param mainTaskId 主任务ID
     * @return 总体进度百分比 (0-100)
     */
    public int calculateOverallProgress(String mainTaskId) {
        try {
            TaskProgress mainProgress = getMainTaskProgress(mainTaskId);
            
            if (mainProgress == null || mainProgress.getStatus() == TaskStatus.UNKNOWN) {
                return 0;
            }
            
            return mainProgress.getPercentage();
        } catch (Exception e) {
            log.error("计算总体进度失败: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 创建默认的任务进度对象
     */
    private TaskProgress createDefaultTaskProgress(String taskId, String message) {
        return TaskProgress.builder()
                .taskId(taskId != null ? taskId : "unknown")
                .status(TaskStatus.UNKNOWN)
                .processedItems(0)
                .totalItems(0)
                .percentage(0)
                .startTime(System.currentTimeMillis())
                .lastUpdateTime(System.currentTimeMillis())
                .message(message)
                .build();
    }
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        COMPLETED("已完成"),
        FAILED("失败"),
        CANCELED("已取消"),
        ERROR("处理错误"),
        UNKNOWN("未知状态");
        
        private final String description;
        
        TaskStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 任务进度DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskProgress implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String taskId;             // 任务ID
        private String taskName;           // 任务名称
        private TaskStatus status;         // 任务状态
        private int processedItems;        // 已处理项目数
        private int totalItems;            // 总项目数
        private int percentage;            // 完成百分比(0-100)
        private long startTime;            // 开始时间（毫秒时间戳）
        private long lastUpdateTime;       // 最后更新时间（毫秒时间戳）
        private String message;            // 可选状态消息
    }
} 