package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_company")
@ApiModel(value = "Company)", description = "company")
public class Company implements Serializable {
    private static final long serialVersionUID = 372302573972071244L;

    public static final String C_COMPANY_CODE = "companyCode";
    public static final String C_COMPANY_NAME = "companyName";
    /*public static final String C_ISSUER_CODE = "issuerCode";*/
    public static final String C_SBU = "sbu";
    public static final String C_STATUS = "status";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_UPDATE_TIME = "updateTime";


    /**
     * id
     */
    @Id
    private Long id;

    @Column(name = "company_code")
    private String companyCode;

    @Column(name = "company_name")
    private String companyName;

    /*@Column(name = "issuer_code")
    private String issuerCode;*/

    @Column(name = "sbu")
    private String sbu;

    @Column(name = "status")
    private Integer status;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;



}
