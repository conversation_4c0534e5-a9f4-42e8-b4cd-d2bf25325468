package com.gtech.gvcore.dao.model;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @ClassName ReportLiabilityGenerateHistory
 * @Description ReportLiabilityGenerateHistory
 * <AUTHOR>
 * @Date 2023/4/15 10:29
 * @Version V1.0
 * <p>
 *      DROP TABLE `gv_report_liability_generate_history`;
 *      CREATE TABLE `gv_report_liability_generate_history`
 *      (
 *          `id`                   INT PRIMARY KEY AUTO_INCREMENT      NOT NULL COMMENT 'id auto increment',
 *          `liability_table_code` VARCHAR(32)                         NOT NULL COMMENT 'Liability Table code (yy+month)',
 *          `generate_time`        TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'generate time',
 *          `last_success_time`    TIMESTAMP                           NULL COMMENT 'last success time',
 *          `last_refresh_time`    TIMESTAMP                           NULL COMMENT 'last refresh time',
 *          `error_log`            TEXT COMMENT 'error log'
 *      ) COMMENT 'liability generate history';
 * </p>
 *
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "gc_report_liability_generate_history")
public class GcReportLiabilityGenerateHistory {

  public static final String C_LIABILITY_TABLE_CODE = "liabilityTableCode";

  @Id
  private Long id;

  @Column(name = "liability_table_code")
  private String liabilityTableCode;

  @Column(name = "generate_time")
  private Date generateTime;

  @Column(name = "last_success_time")
  private Date lastSuccessTime;

  @Column(name = "last_refresh_time")
  private Date lastRefreshTime;

  @Column(name = "error_log")
  private String errorLog;
}
