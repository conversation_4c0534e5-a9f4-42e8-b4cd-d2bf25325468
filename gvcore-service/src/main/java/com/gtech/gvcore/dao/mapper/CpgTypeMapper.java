package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.CpgType;

@Mapper
public interface CpgTypeMapper extends GTechBaseMapper<CpgType> {

    /**
     * 
     * <AUTHOR>
     * @param cpgTypeCodeList
     * @return
     * @date 2022年3月17日
     */
    List<CpgType> queryByCpgTypeCodeList(List<String> cpgTypeCodeList);

}