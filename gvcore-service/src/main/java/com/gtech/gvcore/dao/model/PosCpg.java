package com.gtech.gvcore.dao.model;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_pos_cpg")
@ApiModel(value = "PosCpg", description = "")
public class PosCpg implements Serializable {
    private static final long serialVersionUID = -78965869520562677L;


    @Id
    private Long id;

    @Column(name = "pos_cpg_code")
    private String posCpgCode;
    
    @Column(name = "pos_code")
    private String posCode;
    
    @Column(name = "cpg_code")
    private String cpgCode;
    
    @Column(name = "`status`")
    private Integer status;
    
    @Column(name = "create_user")
    private String createUser;
    
    @Column(name = "create_time")
    private Date createTime;
    
    @Column(name = "update_user")
    private String updateUser;
    
    @Column(name = "update_time")
    private Date updateTime;


    public static final String CONST_POS_CPG_CODE = "posCpgCode";
    public static final String CONST_POS_CODE = "posCode";
    public static final String CONST_CPG_CODE = "cpgCode";
    public static final String CONST_STATUS = "status";
    public static final String CONST_CREATE_USER = "createUser";
    public static final String CONST_CREATE_TIME = "createTime";
    public static final String CONST_UPDATE_USER = "updateUser";
    public static final String CONST_UPDATE_TIME = "updateTime";
}
