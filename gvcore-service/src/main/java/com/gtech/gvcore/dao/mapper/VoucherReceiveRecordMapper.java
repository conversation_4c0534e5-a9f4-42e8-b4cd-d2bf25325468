package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.request.receive.QueryReceiveRecordRequest;
import com.gtech.gvcore.dao.dto.VoucherReceiveDto;
import com.gtech.gvcore.dao.model.VoucherReceiveRecord;

@Mapper
public interface VoucherReceiveRecordMapper extends GTechBaseMapper<VoucherReceiveRecord> {

	List<VoucherReceiveRecord> query(VoucherReceiveRecord model);

	List<VoucherReceiveDto> queryReceiveRecord(QueryReceiveRecordRequest request);
}