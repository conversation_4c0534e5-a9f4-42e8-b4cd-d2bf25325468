package com.gtech.gvcore.dao.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.gtech.gvcore.dao.model.SysLogger;

/**
 * @ClassName SysLoggerMapper
 * @Description 系统日志
 * <AUTHOR>
 * @Date 2022/10/9 16:22
 * @Version V1.0
 **/
@Mapper
public interface SysLoggerMapper {

	void insert(@Param("month") Integer month, @Param("data") SysLogger data);

	void truncate(@Param("month") Integer month);
	void truncateReportTable(@Param("month") Integer month);

	SysLogger selectOne(@Param("month") Integer month, @Param("id") Integer id);

	List<SysLogger> query(Map<String, Object> map);
}
