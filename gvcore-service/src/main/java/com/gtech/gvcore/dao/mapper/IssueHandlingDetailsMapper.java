package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.dto.IssueHandlingDetailsDto;
import com.gtech.gvcore.dao.dto.IssueHandlingTypeRemarkDto;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IssueHandlingDetailsMapper extends GTechBaseMapper<IssueHandlingDetails> {
    
	/**
	 * 
	 * @param dto
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	List<IssueHandlingDetails> queryByIssueHandlingCode(IssueHandlingDetailsDto dto);
	
	List<IssueHandlingDetails> queryDigitalByIssueHandlingCode(IssueHandlingDetailsDto dto);
	
	/**
	 * 
	 * @param dto
	 * @return
	 * <AUTHOR>
	 * @date 2022年4月6日
	 */
	int updateByProcess(IssueHandlingDetailsDto dto);

	@Select("<script> " +
			"SELECT sd.voucher_code, IFNULL(s.remarks, '') AS remark \n" +
			"FROM gv_issue_handling_details sd\n" +
			"         JOIN gv_issue_handling s ON sd.issue_handling_code = s.issue_handling_code\n" +
			"WHERE s.issue_type = #{issueType}\n" +
			"  AND sd.voucher_code IN " +
			"       <foreach collection=\"voucherCodes\" item=\"voucher\" open=\"(\" close=\")\" separator=\",\">"+
			"           #{voucher} " +
			"       </foreach> " +
			"</script>")
	public List<IssueHandlingTypeRemarkDto> selectRemarkByVoucherCodeAndIssueType(@Param(value = "voucherCodes") List<String> voucherCodes,
	                                                                              @Param(value = "issueType") String issueType);
}