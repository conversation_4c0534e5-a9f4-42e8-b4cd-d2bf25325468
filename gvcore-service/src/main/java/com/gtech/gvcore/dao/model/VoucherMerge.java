package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Table(name = "gv_voucher_merge")
@Data
public class VoucherMerge {

	public static final String C_STATUS = "status";
	public static final String C_VOUCHER_MAIN_CODE = "voucherMainCode";

    @Id
    private Long id;

    @Column(name = "voucher_main_code")
    private String voucherMainCode;

    @Column(name = "voucher_merge_code")
    private String voucherMergeCode;

	private Integer status;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_user")
    private String updateUser;

}