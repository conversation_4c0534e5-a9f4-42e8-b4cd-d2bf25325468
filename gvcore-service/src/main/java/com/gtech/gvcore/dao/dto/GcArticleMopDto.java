package com.gtech.gvcore.dao.dto;

import java.util.Date;

import lombok.Data;

/**
 * GC商品支付方式关联DTO类
 */
@Data
public class GcArticleMopDto {
    /**
     * id
     */
    private Long id;

    /**
     * 商品支付方式关联编码
     */
    private String articleMopCode;

    /**
     * 商品编码
     */
    private String articleCode;

    /**
     * 商品名称
     */
    private String articleCodeName;
    
    /**
     * SAP商品编码(GTIN)
     */
    private String sapArticleCode;

    /**
     * 支付方式编码
     */
    private String mopCode;

    /**
     * 支付方式名称
     */
    private String mopName;

    /**
     * 状态,0:禁用,1:启用
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remarks;
} 