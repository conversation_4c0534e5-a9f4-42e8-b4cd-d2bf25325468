package com.gtech.gvcore.dao.model;

import com.gtech.commons.dao.entity.GTechBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;


/**
 * GC商品分类实体类
 */
@Data
@Table( name = "gc_product_category")
public class GcProductCategory  {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * product category code,data UNIQUE KEY
     */
    @Column(name = "product_category_code")
    private String productCategoryCode;

    /**
     * category name
     */
    @Column(name = "category_name")
    private String categoryName;

    /**
     * issuer code
     */
    @Column(name = "issuer_code")
    private String issuerCode;

    /**
     * remarks
     */
    private String remarks;

    /**
     * status,0:disable,1:enable
     */
    private Integer status;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

} 