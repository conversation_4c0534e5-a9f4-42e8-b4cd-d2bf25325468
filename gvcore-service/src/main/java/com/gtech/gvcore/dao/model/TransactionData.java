package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * (GvTransactionData)实体类
 *
 * <AUTHOR>
 * @since 2022-04-12 17:28:44
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_transaction_data")
@ApiModel(value = "TransactionData)", description = "")
public class TransactionData implements Serializable {
    private static final long serialVersionUID = 807303264549948581L;

    @Id
    private Long id;

    /**
     * Transaction flow number
     */
    @Column(name = "transaction_code")
    private String transactionCode;

    /**
     * Transaction Id
     */
    @Column(name = "transaction_id")
    private String transactionId;

    /**
     * Transaction type
     */
    @Column(name = "transaction_type")
    private String transactionType;

    @Column(name = "merchant_code")
    private String merchantCode;

    /**
     * Issuer code
     */
    @Column(name = "issuer_code")
    private String issuerCode;

    @Column(name = "batch_id")
    private String batchId;

    /**
     * Bill Number
     */
    @Column(name = "bill_number")
    private String billNumber;

    @Column(name = "outlet_code")
    private String outletCode;

    /**
     * Cpg code
     */
    @Column(name = "cpg_code")
    private String cpgCode;

    /**
     * Transaction date 
     */
    @Column(name = "transaction_date")
    private Date transactionDate;

    @Column(name = "voucher_code")
    private String voucherCode;

    /**
     * Voucher code number.
     */
    @Column(name = "voucher_code_num")
    private Long voucherCodeNum;

    /**
     * Initiators
     */
    @Column(name = "initiated_by")
    private String initiatedBy;

    @Column(name = "pos_code")
    private String posCode;

    @Column(name = "batch_code")
    private String batchCode;

    /**
     * Login Source (WEBPOS)
     */
    @Column(name = "login_source")
    private String loginSource;

    private BigDecimal denomination;

    /**
     * Paid amount
     */
    @Column(name = "paid_amount")
    private BigDecimal paidAmount;

    /**
     * Payment Method
     */
    @Column(name = "payment_method")
    private String paymentMethod;

    /**
     * Discount amount
     */
    @Column(name = "discount_amount")
    private BigDecimal discountAmount;

    /**
     * Merchant 
     */
    @Column(name = "actual_outlet")
    private String actualOutlet;

    /**
     * card_entry_mode
     */
    @Column(name = "card_entry_mode")
    private String cardEntryMode;

    /**
     * Forwarding Entity Id
     */
    @Column(name = "forwarding_entity_id")
    private String forwardingEntityId;

    @Column(name = "response_message")
    private String responseMessage;

    /**
     * Transaction Mode (Online   )
     */
    @Column(name = "transaction_mode")
    private String transactionMode;

    /**
     * corporateName
     */
    @Column(name = "corporate_name")
    private String corporateName;

    /**
     * departmentDivisionBranch
     */
    @Column(name = "department_division_branch")
    private String departmentDivisionBranch;

    /**
     * Customer Salutation
     */
    @Column(name = "customer_salutation")
    private String customerSalutation;

    /**
     * Customer First Name
     */
    @Column(name = "customer_first_name")
    private String customerFirstName;

    /**
     * Customer Last Name
     */
    @Column(name = "customer_last_name")
    private String customerLastName;

    /**
     * Mobile
     */
    private String mobile;

    /**
     * Email
     */
    private String email;

    /**
     * Invoice Number
     */
    @Column(name = "invoice_number")
    private String invoiceNumber;

    /**
     * Other Input Parameter
     */
    @Column(name = "other_input_parameter")
    private String otherInputParameter;

    /**
     * CustomerType
     */
    @Column(name = "customer_type")
    private String customerType;

    /**
     * A zero value indicates successful response and non zero means failure
     */
    @Column(name = "success_or_failure")
    private String successOrFailure;

    /**
     * purchase order no
     */
    @Column(name = "purchase_order_no")
    private String purchaseOrderNo;

    /**
     * Voucher effective date.
     */
    @Column(name = "voucher_effective_date")
    private Date voucherEffectiveDate;

    @Column(name = "mop_code")
    private String mopCode;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * Customer code
     */
    @Column(name = "customer_code")
    private String customerCode;


    @Column(name = "reference_number")
    private String referenceNumber;

    @Column(name = "transaction_channel")
    private String transactionChannel;

    @Column(name = "approve_code")
    private String approveCode;

    @Column(name = "notes")
    private String notes;

    /** the constant of field {@link TransactionData#id} */
    public static final String C_ID = "id";
    /** the constant of field {@link TransactionData#transactionCode} */
    public static final String C_TRANSACTION_CODE = "transactionCode";
    public static final String C_TRANSACTION_ID = "transactionId";
    /** the constant of field {@link TransactionData#transactionType} */
    public static final String C_TRANSACTION_TYPE = "transactionType";
    /** the constant of field {@link TransactionData#merchantCode} */
    public static final String C_MERCHANT_CODE = "merchantCode";
    /** the constant of field {@link TransactionData#outletCode} */
    public static final String C_OUTLET_CODE = "outletCode";
    /** the constant of field {@link TransactionData#transactionDate} */
    public static final String C_TRANSACTION_DATE = "transactionDate";
    /** the constant of field {@link TransactionData#voucherCode} */
    public static final String C_VOUCHER_CODE = "voucherCode";
    /** the constant of field {@link TransactionData#voucherCodeNum} */
    public static final String C_VOUCHER_CODE_NUM = "voucherCodeNum";
    /** the constant of field {@link TransactionData#initiatedBy} */
    public static final String C_INITIATED_BY = "initiatedBy";
    /** the constant of field {@link TransactionData#posCode} */
    public static final String C_POS_CODE = "posCode";
    /** the constant of field {@link TransactionData#batchCode} */
    public static final String C_BATCH_CODE = "batchCode";
    /** the constant of field {@link TransactionData#loginSource} */
    public static final String C_LOGIN_SOURCE = "loginSource";
    /** the constant of field {@link TransactionData#denomination} */
    public static final String C_DENOMINATION = "denomination";
    /** the constant of field {@link TransactionData#actualOutlet} */
    public static final String C_ACTUAL_OUTLET = "actualOutlet";
    /** the constant of field {@link TransactionData#forwardingEntityId} */
    public static final String C_FORWARDING_ENTITY_ID = "forwardingEntityId";
    /** the constant of field {@link TransactionData#responseMessage} */
    public static final String C_RESPONSE_MESSAGE = "responseMessage";
    /** the constant of field {@link TransactionData#transactionMode} */
    public static final String C_TRANSACTION_MODE = "transactionMode";
    /** the constant of field {@link TransactionData#customerSalutation} */
    public static final String C_CUSTOMER_SALUTATION = "customerSalutation";
    /** the constant of field {@link TransactionData#customerFirstName} */
    public static final String C_CUSTOMER_FIRST_NAME = "customerFirstName";
    /** the constant of field {@link TransactionData#customerLastName} */
    public static final String C_CUSTOMER_LAST_NAME = "customerLastName";
    /** the constant of field {@link TransactionData#mobile} */
    public static final String C_MOBILE = "mobile";
    /** the constant of field {@link TransactionData#invoiceNumber} */
    public static final String C_INVOICE_NUMBER = "invoiceNumber";
    /** the constant of field {@link TransactionData#otherInputParameter} */
    public static final String C_OTHER_INPUT_PARAMETER = "otherInputParameter";
    /** the constant of field {@link TransactionData#successOrFailure} */
    public static final String C_SUCCESS_OR_FAILURE = "successOrFailure";
    /** the constant of field {@link TransactionData#purchaseOrderNo} */
    public static final String C_PURCHASE_ORDER_NO = "purchaseOrderNo";
    /** the constant of field {@link TransactionData#voucherEffectiveDate} */
    public static final String C_VOUCHER_EFFECTIVE_DATE = "voucherEffectiveDate";
    /** the constant of field {@link TransactionData#createUser} */
    public static final String C_CREATE_USER = "createUser";
    /** the constant of field {@link TransactionData#createTime} */
    public static final String C_CREATE_TIME = "createTime";
    /** the constant of field {@link TransactionData#updateUser} */
    public static final String C_UPDATE_USER = "updateUser";
    /** the constant of field {@link TransactionData#updateTime} */
    public static final String C_UPDATE_TIME = "updateTime";
}

