package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.dto.GcMeansOfPaymentDto;
import com.gtech.gvcore.dao.model.GcMeansOfPayment;

@Mapper
public interface GcMeansOfPaymentMapper extends GTechBaseMapper<GcMeansOfPayment> {

    /**
     * 根据条件查询支付方式列表
     * @param dto 查询条件
     * @return 支付方式列表
     */
    List<GcMeansOfPayment> selectSelective(GcMeansOfPaymentDto dto);

    /**
     * 根据编码列表查询支付方式
     * @param meansOfPaymentCodeList 支付方式编码列表
     * @return 支付方式列表
     */
    List<GcMeansOfPayment> queryByCodeList(List<String> meansOfPaymentCodeList);
} 