package com.gtech.gvcore.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 11:08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "gv_voucher_request")
@Builder
public class VoucherRequest implements Serializable {

    private static final long serialVersionUID = -6542166067488473295L;

    public static final String C_VOUCHER_REQUEST_CODE = "voucherRequestCode";
    @Id
    private Long id;
    @Column(name = "voucher_request_code")
    private String voucherRequestCode;
    @Column(name = "issuer_code")
    private String issuerCode;
    @Column(name = "business_type")
    private String businessType;
    @Column(name = "voucher_owner_code")
    private String voucherOwnerCode;
    @Column(name = "voucher_owner_name")
    private String voucherOwnerName;
    @Column(name = "receiver_code")
    private String receiverCode;
    @Column(name = "receiver_name")
    private String receiverName;
    @Column(name = "state_code")
    private String stateCode;
    @Column(name = "city_code")
    private String cityCode;
    @Column(name = "district_code")
    private String districtCode;
    @Column(name = "address1")
    private String address1;
    @Column(name = "email")
    private String email;
    @Column(name = "phone")
    private String phone;
    @Column(name = "mobile")
    private String mobile;
    @Column(name = "request_remarks")
    private String requestRemarks;
    @Column(name = "status")
    private Integer status;
    @Column(name = "voucher_num")
    private Integer voucherNum;
    @Column(name = "voucher_amount")
    private BigDecimal voucherAmount;
    @Column(name = "currency_code")
    private String currencyCode;
    @Column(name = "permission_code")
    private String permissionCode;
    @Column(name = "create_user")
    private String createUser;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "update_user")
    private String updateUser;
    @Column(name = "update_time")
    private Date updateTime;


    public VoucherRequest(String voucherRequestCode) {
        this.voucherRequestCode = voucherRequestCode;
    }
}
