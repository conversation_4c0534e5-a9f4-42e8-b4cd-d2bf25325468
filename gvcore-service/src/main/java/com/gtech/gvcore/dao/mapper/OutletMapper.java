package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.request.outlet.GetOutletRequest;
import com.gtech.gvcore.common.request.outlet.QueryOutletRequest;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.dto.OutletDto;
import com.gtech.gvcore.dao.dto.OutletIssuerNameInfo;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dto.OutletMerchantNameInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/17 15:45
 */
@Mapper
public interface OutletMapper extends GTechBaseMapper<Outlet> {


    @Select("<script>" +
            "SELECT " +
            " o.outlet_code, " +
            " o.outlet_name, " +
            " o.merchant_code, " +
            " o.issuer_code, " +
            " o.business_outlet_code, " +
            " o.outlet_type, " +
            " o.state_code, " +
            " o.sbu, " +
            " o.city_code, " +
            " o.district_code, " +
            " md.DISTRICT_NAME districtName, " +
            " o.address1, " +
            " o.address2, " +
            " o.pin_code, " +
            " o.first_name, " +
            " o.last_name, " +
            " o.email, " +
            " o.phone, " +
            " o.mobile, " +
            " o.alertnate_email, " +
            " o.alertnate_phone, " +
            " o.descriptive, " +
            " gm.merchant_name, " +
            " o.`status`, " +
            " o.create_user, " +
            " o.create_time, " +
            " o.update_user, " +
            " o.update_time," +
            " o.parent_outlet," +
            " po.outlet_name parentOutletName  " +
            "FROM " +
            " gv_outlet o " +
            " LEFT JOIN gv_merchant gm ON o.merchant_code = gm.merchant_code " +
            " LEFT JOIN t_masterdata_district md ON o.city_code = md.DISTRICT_PCODE AND o.district_code = md.DISTRICT_CODE " +
            " LEFT JOIN gv_outlet po ON o.parent_outlet = po.outlet_code " +
            " <where>" +
                " <if test=\" request.outletCode != null and request.outletCode != '' \"> " +
                    " AND o.outlet_code = #{request.outletCode} " +
                " </if> " +
                " <if test=\" request.merchantCodeList != null  and request.merchantCodeList.size() != 0  \"> " +
                    " AND gm.merchant_code in " +
                "<foreach item=\"code\" collection=\"request.merchantCodeList\" separator=\",\" open=\"(\" close=\")\" index=\"\">" +
                    "#{code}" +
                "</foreach>" +
                " </if> " +
                " <if test=\" request.merchantCode != null and request.merchantCode != '' \"> " +
                    " AND gm.merchant_code = #{request.merchantCode}" +
                " </if> " +

                " <if test=\" request.outletCodeList != null\"> " +
                    " AND o.outlet_code in " +
                "<foreach item=\"code\" collection=\"request.outletCodeList\" separator=\",\" open=\"(\" close=\")\" index=\"\">" +
	    			"#{code}" +
	    		"</foreach>" +
                " </if> " +
                " <if test=\" request.issuerCode != null and request.issuerCode != '' \"> " +
                    " AND o.issuer_code = #{request.issuerCode} " +
                " </if> " +
                " <if test=\" request.status != null \"> " +
                    " AND o.status = #{request.status} " +
                " </if> " +
                " <if test=\" request.outletName != null and request.outletName != '' \"> " +
                    " AND o.outlet_name like concat('%',#{request.outletName},'%')  " +
                " </if> " +
                " <if test=\" request.outletType != null and request.outletType != '' \"> " +
                    " AND o.outlet_type in (${request.outletType})" +
                " </if> " +
            " </where> " +
            "ORDER BY " +
            " o.outlet_name  " +
            "</script>")
    List<OutletResponse> queryOutletList(@Param("request") QueryOutletRequest request);




    @Select("<script>" +
            "SELECT " +
            " o.outlet_code, " +
            " o.outlet_name, " +
            " o.merchant_code, " +
            " o.issuer_code, " +
            " o.sbu, " +
            " o.business_outlet_code, " +
            " o.outlet_type, " +
            " o.state_code, " +
            " o.city_code, " +
            " o.district_code, " +
            " md.DISTRICT_NAME districtName, " +
            " o.address1, " +
            " o.address2, " +
            " o.pin_code, " +
            " o.first_name, " +
            " o.last_name, " +
            " o.email, " +
            " o.phone, " +
            " o.mobile, " +
            " o.alertnate_email, " +
            " o.alertnate_phone, " +
            " o.descriptive, " +
            " gm.merchant_name, " +
            " o.`status`, " +
            " o.create_user, " +
            " o.create_time, " +
            " o.update_user, " +
            " o.update_time," +
            " o.parent_outlet," +
            " po.outlet_name parentOutletName   " +
            "FROM " +
            " gv_outlet o " +
            " LEFT JOIN gv_merchant gm ON o.merchant_code = gm.merchant_code" +
            " LEFT JOIN t_masterdata_district md ON o.city_code = md.DISTRICT_PCODE AND o.district_code = md.DISTRICT_CODE " +
            " LEFT JOIN gv_outlet po ON o.parent_outlet = po.outlet_code " +
            " where o.outlet_code = #{request.outletCode} " +
            "</script>")
    OutletResponse getOutlet(@Param("request") GetOutletRequest request);

    Boolean ifExist(String issuerCode, String outletCode);

    /**
     * 
     * @param outletCodeList
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    List<Outlet> queryByOutletCodeList(List<String> outletCodeList);

    /**
     * 
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2022年4月22日
     */
    List<OutletIssuerNameInfo> queryOutletIssuerNameInfo(OutletDto dto);

    /**
     * 
     * @param outletCodeList
     * @return
     * <AUTHOR>
     * @date 2022年4月26日
     */
    List<OutletMerchantNameInfo> queryOutletMerchantName(List<String> outletCodeList);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年6月16日
     */
    List<String> queryAllOutletCodeByCompanyOrMerchant(OutletDto dto);

    List<String> selectAllTables();

    Integer updateOutletCode(@Param("tableName") String tableName, @Param("statement") String statement, @Param("oldOutlet") Outlet oldOutlet, @Param("newOutlet") Outlet newOutlet);

    Outlet getOutletInfo(@Param("newOutletCode") String newOutletCode);

    int updateParentCode(@Param("oldOutlet") Outlet oldOutlet, @Param("newOutlet") Outlet newOutlet);

    int updateIssuerCode(@Param("tableName") String tableName, @Param("issuerStatement") String issuerStatement, @Param("oldOutlet") Outlet oldOutlet, @Param("newOutlet") Outlet newOutlet);

    int updateMerchantCode(@Param("tableName") String tableName, @Param("merchantStatement") String merchantStatement, @Param("oldOutlet") Outlet oldOutlet, @Param("newOutlet") Outlet newOutlet);

    int updateOutletParentCode(@Param("oldOutlet") Outlet oldOutlet, @Param("newOutlet") Outlet newOutlet);

    List<String> selectAllColumns(@Param("allTableName") String allTableName);

    int selectCountWhereColumnWithOutlet(@Param("tableName") String tableName, @Param("column") String column);

    int updateVoucherAllocationVoucherOwnerName(@Param("newOutlet") Outlet newOutlet, @Param("oldOutlet") Outlet oldOutlet);

    int updateVoucherAllocationReceiverName(@Param("newOutlet") Outlet newOutlet, @Param("oldOutlet") Outlet oldOutlet);

    int updateVoucherReceiveOutBound(@Param("newOutlet") Outlet newOutlet, @Param("oldOutlet") Outlet oldOutlet);

    int updateVoucherReceiveInBound(@Param("newOutlet") Outlet newOutlet, @Param("oldOutlet") Outlet oldOutlet);

    int updateVoucherRequestVoucherOwnerName(@Param("newOutlet") Outlet newOutlet, @Param("oldOutlet") Outlet oldOutlet);

    int updateVoucherRequestReceiverName(@Param("newOutlet") Outlet newOutlet, @Param("oldOutlet") Outlet oldOutlet);
}
