package com.gtech.gvcore.dao.mapper;

import com.gtech.gvcore.dao.model.GcReportTempLiabilityDStructure;
import com.gtech.gvcore.dao.model.GcReportTempLiabilitySStructure;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.service.report.impl.bean.GcRegenerateActivationCodeBean;
import com.gtech.gvcore.service.report.impl.bo.*;
import com.gtech.gvcore.service.report.impl.param.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;
import java.util.Map;

/**
 * @ClassName ReportMapper
 * @Description report mapper
 * <AUTHOR>
 * @Date 2023/1/5 10:20
 * @Version V1.0
 **/
@Mapper
public interface GcReportBusinessMapper {
    List<GcSalesBo> gcSalesReport(GcSalesQueryData salesDetailed, @Param("rowBounds") RowBounds rowBounds);

    List<GcRedemptionBo> selectGcRedemption(RedemptionQueryData request, @Param("rowBounds") RowBounds rowBounds);

    List<GcCancelSalesBo> selectCancelSales(GcCancelSalesQueryData cancelSalesDetail, @Param("rowBounds") RowBounds rowBounds);

    List<DeactivatedBo> selectGcDeactivated(GcDeactivatedQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcReactivatedBo> selectGcReactivated(GcReactivatedQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcExpiryBo> selectGcExpiry(GcExpiryQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcExtendExpiryBo> selectGcExtendExpiry(GcExtendExpiryQueryData param, @Param("rowBounds") RowBounds rowBounds);

    // Get card to merchant/outlet mapping from sales records
    List<Map<String, Object>> getCardToMerchantMapFromGcSales(@Param("cardNumbers") List<String> cardNumbers);

    List<Map<String, Object>> getCardToOutletMapFromGcSales(@Param("cardNumbers") List<String> cardNumbers);

    // ---- Gift card transaction detail report range start ----
    List<GcTransactionDetailedBo> findActivationTransactions(GcTransactionDetailQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcTransactionDetailedBo> findSalesTransactions(GcTransactionDetailQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcTransactionDetailedBo> findCancelSalesTransactions(GcTransactionDetailQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcTransactionDetailedBo> findRedemptionTransactions(GcTransactionDetailQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcTransactionDetailedBo> findCancelRedemptionTransactions(GcTransactionDetailQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcTransactionDetailedBo> findActivationExtensionTransactions(GcTransactionDetailQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcTransactionDetailedBo> findDeactivateTransactions(GcTransactionDetailQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcTransactionDetailedBo> findReactivateTransactions(GcTransactionDetailQueryData param, @Param("rowBounds") RowBounds rowBounds);

    // 根据传入的参数和分页信息，查询礼品卡激活码生成汇总信息
    // ---- Gift card transaction detail report range end ----
    List<GcRegenerateActivationCodeBean> selectGcRegenerateActivationCodeSummary(GcRegenerateActivationCodeQueryData param);

    List<GcRegenerateActivationCodeDetailBo> selectGcRegenerateActivationCodeDetail(GcRegenerateActivationCodeQueryData param);

    List<GcReportTempLiabilitySStructure> liabilitySummaryReport(LiabilitySummaryQueryData liabilityDetailQueryData);

    List<GcReportTempLiabilityDStructure> liabilityDetailReport(LiabilityDetailQueryData param, RowBounds rowBounds);

    List<Map<String, String>> getBatchLatestGcActivationTime(@Param("cardNumbers") List<String> cardNumbers);

    List<Map<String, String>> getBatchLatestGcSalesTime(@Param("cardNumbers") List<String> cardNumbers);

    List<Map<String, String>> getBatchLatestGcRedemptionTime(@Param("cardNumbers") List<String> cardNumbers);
    List<Map<String, String>> getBatchLatestGcCancelRedemptionTime(@Param("cardNumbers") List<String> cardNumbers);

    List<Map<String, String>> getBatchLatestGcBlockTime(@Param("cardNumbers") List<String> cardNumbers);

    List<Map<String, String>> getBatchLatestGcUnblockTime(@Param("cardNumbers") List<String> cardNumbers);

    List<Map<String, String>> getBatchLatestGcCancelSalesTime(@Param("cardNumbers") List<String> cardNumbers);

    List<Map<String, String>> getBatchLatestGcExtendActivationTime(@Param("cardNumbers") List<String> cardNumbers);

    List<GcBalanceCorrectionBo> selectBalanceCorrection(GcBalanceCorrectionQueryData gcBalanceCorrectionQueryData, RowBounds rowBounds);

    List<GiftCardEntity> selectLatestGcStatus(GcLatestStatusQueryData queryData, RowBounds rowBounds);
}
