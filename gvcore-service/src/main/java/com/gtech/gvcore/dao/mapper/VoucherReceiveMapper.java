package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.VoucherReceive;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface VoucherReceiveMapper extends GTechBaseMapper<VoucherReceive> {

	List<VoucherReceive> query(Map<String, Object> map);

	void updateVoucherReceivedNum(@Param("receiveCode") String receiveCode, @Param("receivedNum") Integer receivedNum);

}