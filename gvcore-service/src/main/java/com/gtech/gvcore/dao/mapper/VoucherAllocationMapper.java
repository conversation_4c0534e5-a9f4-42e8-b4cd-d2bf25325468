package com.gtech.gvcore.dao.mapper;

import java.util.List;

import com.gtech.gvcore.dto.VoucherAllocationPermissionDto;
import org.apache.ibatis.annotations.Mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.dto.VoucherAllocationDto;
import com.gtech.gvcore.dao.model.VoucherAllocation;

@Mapper
public interface VoucherAllocationMapper extends GTechBaseMapper<VoucherAllocation> {

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年3月10日
     */
    int updateStatus(VoucherAllocationDto dto);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年3月11日
     */
    List<VoucherAllocation> queryVoucherAllocationByPage(VoucherAllocationPermissionDto dto);

    /**
     * 
     * <AUTHOR>
     * @param voucherAllocationCodeList
     * @return
     * @date 2022年3月11日
     */
    List<VoucherAllocation> queryByVoucherAllocationCodeList(List<String> voucherAllocationCodeList);

}