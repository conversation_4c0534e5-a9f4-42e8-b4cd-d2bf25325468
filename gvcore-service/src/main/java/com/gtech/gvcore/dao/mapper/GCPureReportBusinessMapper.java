package com.gtech.gvcore.dao.mapper;

import com.gtech.gvcore.service.report.impl.bo.WpubonBo;
import com.gtech.gvcore.service.report.impl.bo.WpubonCBo;
import com.gtech.gvcore.service.report.impl.bo.WpuumsBo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface GCPureReportBusinessMapper {

    List<WpubonCBo> selectWpubonCData(@Param("queryDate") Date queryDate, @Param("outletCodes") List<String> outletCodes);

    List<WpubonBo> selectWpubonData(@Param("queryDate") Date queryDate, @Param("outletCodes") List<String> outletCodes);

    List<WpuumsBo> selectWpuumsData(@Param("queryDate") Date queryDate, @Param("outletCodes") List<String> outletCodes);

} 