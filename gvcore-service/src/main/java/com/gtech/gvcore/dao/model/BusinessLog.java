
package com.gtech.gvcore.dao.model;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_business_log")
@ApiModel(value = "BusinessLog)", description = "")
public class BusinessLog implements Serializable {
    private static final long serialVersionUID = -85095642122816829L;
    
    
    
    @Id
    private Long id;

    @Column(name = "business_code")
    private String businessCode;
    
    @Column(name = "`status`")
    private Integer status;
    
    @Column(name = "success")
    private Integer success;
    
    @Column(name = "failed")
    private Integer failed;
    
    @Column(name = "content_code")
    private String contentCode;
    
    @Column(name = "content")
    private String content;
    
    @Column(name = "create_user")
    private String createUser;
    
    @Column(name = "create_time")
    private Date createTime;
    
    @Column(name = "update_user")
    private String updateUser;
    
    @Column(name = "update_time")
    private Date updateTime;


    /** the constant of field {@link BusinessLog#businessCode} */
    public static final String C_BUSINESS_CODE = "businessCode";
    /** the constant of field {@link BusinessLog#status} */
    public static final String C_STATUS = "status";
    /** the constant of field {@link BusinessLog#success} */
    public static final String C_SUCCESS = "success";
    /** the constant of field {@link BusinessLog#failed} */
    public static final String C_FAILED = "failed";
    /** the constant of field {@link BusinessLog#contentCode} */
    public static final String C_CONTENT_CODE = "contentCode";
    /** the constant of field {@link BusinessLog#content} */
    public static final String C_CONTENT = "content";
    /** the constant of field {@link BusinessLog#createUser} */
    public static final String C_CREATE_USER = "createUser";
    /** the constant of field {@link BusinessLog#createTime} */
    public static final String C_CREATE_TIME = "createTime";
    /** the constant of field {@link BusinessLog#updateUser} */
    public static final String C_UPDATE_USER = "updateUser";
    /** the constant of field {@link BusinessLog#updateTime} */
    public static final String C_UPDATE_TIME = "updateTime";
}
