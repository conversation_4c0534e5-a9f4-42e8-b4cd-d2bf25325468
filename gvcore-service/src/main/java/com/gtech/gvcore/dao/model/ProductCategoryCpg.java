package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "gv_product_category_cpg")
public class ProductCategoryCpg {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * product category cpg code,data UNIQUE KEY
     */
    @Column(name = "product_category_cpg_code")
    private String productCategoryCpgCode;

    /**
     * product category code
     */
    @Column(name = "product_category_code")
    private String productCategoryCode;

    /**
     * cpg code
     */
    @Column(name = "cpg_code")
    private String cpgCode;

    /**
     * status,0:disable,1:enable
     */
    private Integer status;

    /**
     * delete status
     */
    @Column(name = "delete_status")
    private Integer deleteStatus;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}