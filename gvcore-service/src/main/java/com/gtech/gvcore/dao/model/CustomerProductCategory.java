package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_customer_product_category")
@ApiModel(value = "CustomerProductCategory)", description = "customer product category")
public class CustomerProductCategory implements Serializable {
    private static final long serialVersionUID = 860071258965989239L;


    public static final String C_CUSTOMER_PRODUCT_CATEGORY_CODE = "customerProductCategoryCode";
    public static final String C_CUSTOMER_CODE = "customerCode";
    public static final String C_PRODUCT_CATEGORY_CODE = "productCategoryCode";
    public static final String C_STATUS = "status";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_UPDATE_TIME = "updateTime";


    @Id
    private Long id;

    @Column(name = "customer_product_category_code")
    private String customerProductCategoryCode;

    @Column(name = "customer_code")
    private String customerCode;

    @Column(name = "product_category_code")
    private String productCategoryCode;

    @Column(name = "`status`")
    private Integer status;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;



}
