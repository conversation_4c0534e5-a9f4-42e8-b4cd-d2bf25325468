package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.CpgMonthInventorySnapshot;
import com.gtech.gvcore.dto.CpgHistoricalInventory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName CpgMonthInventorySnapshotMapper
 * @Description CPG月库存快照
 * <AUTHOR>
 * @Date 2022/7/6 15:25
 * @Version V1.0
 **/
@Mapper
public interface CpgMonthInventorySnapshotMapper extends GTechBaseMapper<CpgMonthInventorySnapshot> {

    @Select(" <script>" +
            " SELECT month , IFNULL(SUM(available),0) AS available , IFNULL(SUM(distributed),0) AS distributed" +
            " FROM gv_cpg_month_inventory_snapshot" +
            " WHERE customer_code = #{customerCode}" +
            "   AND month &gt;= #{greaterThanOrEqualMonth}" +

            " <if test=\"null != cpgCode and cpgCode != ''\"> " +
            "   AND cpg_code = #{cpgCode} " +
            " </if> " +

            " GROUP BY month" +
            " </script>")
    List<CpgHistoricalInventory> queryHistoricalInventoryChart(@Param("customerCode") String customerCode,@Param("greaterThanOrEqualMonth") Integer greaterThanOrEqualMonth,@Param("cpgCode") String cpgCode);
}
