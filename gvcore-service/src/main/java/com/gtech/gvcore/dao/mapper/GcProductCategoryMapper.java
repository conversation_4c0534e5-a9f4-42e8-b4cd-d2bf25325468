package com.gtech.gvcore.dao.mapper;

import org.apache.ibatis.annotations.Mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.model.GcProductCategory;
import com.gtech.gvcore.dao.dto.GcProductCategoryDto;

import java.util.List;

/**
 * GC商品类别Mapper接口
 */
@Mapper
public interface GcProductCategoryMapper extends GTechBaseMapper<GcProductCategory> {
    /**
     * 根据条件查询商品类别
     *
     * @param gcProductCategoryDto 查询条件
     * @return 商品类别列表
     */
    List<GcProductCategory> selectSelective(GcProductCategory gcProductCategory);
} 