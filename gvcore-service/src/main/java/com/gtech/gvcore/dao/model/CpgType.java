package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "gv_cpg_type")
public class CpgType {

    public static final String C_CPG_TYPE_NAME = "cpgTypeName";
    public static final String C_UPDATE_TIME = "updateTime";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_ID = "id";
    public static final String CPG_TYPE_CODE = "cpgTypeCode";
    public static final String C_PREFIX = "prefix";
    public static final String C_STATUS = "status";

    /**
     * id
     */
    @Id
    private Long id;

    /**
     * data UNIQUE KEY
     */
    @Column(name = "cpg_type_code")
    private String cpgTypeCode;

    /**
     * CPG type name
     */
    @Column(name = "cpg_type_name")
    private String cpgTypeName;

    /**
     * prefix
     */
    private String prefix;

    /**
     * Automatic activate on sale: YES/NO
     */
    @Column(name = "automatic_activate")
    private String automaticActivate;

    /**
     * status,0:disable,1:enable
     */
    private Integer status;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}