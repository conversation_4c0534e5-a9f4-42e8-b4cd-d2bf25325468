package com.gtech.gvcore.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_issuer")
public class Issuer implements Serializable {
    private static final long serialVersionUID = -37390496202269589L;


    public static final String C_ISSUER_CODE = "issuerCode";
    public static final String C_ISSUER_NAME = "issuerName";
    public static final String C_STATUS = "status";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_UPDATE_TIME = "updateTime";

    /**
     * id
     */
    @Id
    private Long id;

    /**
     * issuer code
     */
    @Column(name = "issuer_code")
    private String issuerCode;

    /**
     * issuer name
     */
    @Column(name = "issuer_name")
    private String issuerName;

    /**
     * status,0:disable,1:enable
     */
    @Column(name = "`status`")
    private Integer status;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;



}
