package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.dao.dto.QueryCpgDto;
import com.gtech.gvcore.dao.model.VoucherRequestDetails;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 15:04
 */


@Mapper
public interface VoucherRequestDetailsMapper extends GTechBaseMapper<VoucherRequestDetails> {

    int batchInsert(@Param("details") List<VoucherRequestDetails> gvVoucherRequestDetailsEntities);

    /**
     * 
     * <AUTHOR>
     * @param voucherRequestCodeList
     * @return
     * @date 2022年3月11日
     */
    List<VoucherRequestDetails> queryDenominationByVoucherRequestCodeList(List<String> voucherRequestCodeList);



    @Select("<script> " +
            "SELECT gd.voucher_request_code, c.cpg_name  " +
            " FROM gv_voucher_request_details gd " +
            " LEFT JOIN gv_cpg c ON c.cpg_code = gd.cpg_code " +
            " WHERE voucher_request_code IN  " +
            " <foreach collection=\"list\" item=\"item\" open=\"(\" separator=\",\" close=\")\"> " +
            "  #{item} " +
            " </foreach>" +
            " </script>")
    List<QueryCpgDto> queryCpgByVoucherRequestCodeList(List<String> list);
    
    
    
    
    
}
