package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/19 17:26
 */

@Mapper
public interface CustomerOrderDetailsMapper extends GTechBaseMapper<CustomerOrderDetails> {
    @Select(value =
            "select " +
                    "gcod.customer_order_details_code as customerOrderDetailsCode, " +
                    "gcod.cpg_code as cpgCode, " +
                    " CASE " +
                    "   WHEN gv.cpg_name IS NULL THEN gcc.cpg_name " +
                    "   ELSE gv.cpg_name " +
                    " END AS cpgName, " +
                    "gcod.voucher_num as voucherNum, " +
					"gcod.denomination as denomination, " +
					"gcod.order_denomination as orderDenomination, "
                    // 如果detail 的 cpg 在gc_cpg中，则使用gc_cpg的article_mop_code，否则使用gv_article_mop的article_mop_code
                    + " CASE " +
                    "   WHEN gv.article_mop_code IS NULL THEN gcam.article_code_name " +
                    "   ELSE gam.article_code_name " +
                    " END AS articleCodeName, "

                    // 如果detail 的 cpg 在gc_cpg中，则使用gc_cpg的article_code_name，否则使用gv_article_mop的article_code_name
                    + " CASE " +
                    "   WHEN gv.article_mop_code IS NULL THEN gcam.article_code " +
                    "   ELSE gam.article_code " +
                    " END AS articleCode ," +

                    "gcod.customer_order_code as customerOrderCode " +

                    "from gv_customer_order_details as gcod " +
                    "left join gv_cpg as gv on gcod.cpg_code=gv.cpg_code " +
                    "left join gc_cpg as gcc on gcod.cpg_code=gcc.cpg_code " +
                    "left join gv_article_mop as gam on gv.article_mop_code=gam.article_mop_code " +
                    "left join gc_article_mop as gcam on gcc.article_mop_code=gcam.article_mop_code " +
                    "where gcod.customer_order_code=#{customerOrderCode} and gcod.delete_status=#{deleteStatus}")
    List<GetCustomerOrderDetailsResponse> selectCustomerDetails(@Param("customerOrderCode") String customerOrderCode, @Param("deleteStatus") Integer deleteStatus);

    @Select("<script>" +
            " SELECT customer_order_code" +
            " FROM gv_customer_order_details" +
            " WHERE cpg_code IN " +

            " <foreach item=\"cpgCode\" collection=\"cpgCodeList\" separator=\",\" open=\"(\" close=\")\" index=\"\">" +
            "   #{cpgCode}" +
            " </foreach>" +

            " GROUP BY customer_order_code" +
            "</script>")
    List<String> queryOrderCodesByCpgCodeList(@Param("cpgCodeList") List<String> cpgCodeList);

    @Select("<script>" +
            " SELECT IFNULL(SUM(voucher_num),0) FROM gv_customer_order_details" +
            " WHERE " +
            " customer_order_code IN " +

            " <foreach item=\"customerOrderCode\" collection=\"customerOrderCodeList\" separator=\",\" open=\"(\" close=\")\" index=\"\">" +
            "   #{customerOrderCode}" +
            " </foreach>" +

            " <if test=\" cpgCode != null and cpgCode != '' \"> AND cpg_code = #{cpgCode} </if>" +
            "</script>")
    int sumVoucherNumByOrderCodeAndCpgCode(@Param("customerOrderCodeList")List<String> customerOrderCodeList,@Param("cpgCode") String cpgCode);
}
