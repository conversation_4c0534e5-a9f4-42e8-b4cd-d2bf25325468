package com.gtech.gvcore.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.response.customerorder.GetCustomerOrderDetailsResponse;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/19 17:26
 */

@Mapper
public interface CustomerOrderDetailsMapper extends GTechBaseMapper<CustomerOrderDetails> {
    @Select(value =
            "select " +
                    "gcod.customer_order_details_code as customerOrderDetailsCode, " +
                    "gcod.cpg_code as cpgCode, " +
                    "gc.cpg_name as cpgName, " +
                    "gcod.voucher_num as voucherNum, " +
					"gcod.denomination as denomination, " + "gam.article_code as articleCode, "
					+
                    "gam.article_code_name as articleCodeName " +
                    "from gv_customer_order_details as gcod " +
                    "left join gv_cpg as gc on gcod.cpg_code=gc.cpg_code " +
                    "left join gv_article_mop as gam on gc.article_mop_code=gam.article_mop_code " +
                    "where gcod.customer_order_code=#{customerOrderCode} and gcod.delete_status=#{deleteStatus}")
    List<GetCustomerOrderDetailsResponse> selectCustomerDetails(@Param("customerOrderCode") String customerOrderCode, @Param("deleteStatus") Integer deleteStatus);

    @Select("<script>" +
            " SELECT customer_order_code" +
            " FROM gv_customer_order_details" +
            " WHERE cpg_code IN " +

            " <foreach item=\"cpgCode\" collection=\"cpgCodeList\" separator=\",\" open=\"(\" close=\")\" index=\"\">" +
            "   #{cpgCode}" +
            " </foreach>" +

            " GROUP BY customer_order_code" +
            "</script>")
    List<String> queryOrderCodesByCpgCodeList(@Param("cpgCodeList") List<String> cpgCodeList);

    @Select("<script>" +
            " SELECT IFNULL(SUM(voucher_num),0) FROM gv_customer_order_details" +
            " WHERE " +
            " customer_order_code IN " +

            " <foreach item=\"customerOrderCode\" collection=\"customerOrderCodeList\" separator=\",\" open=\"(\" close=\")\" index=\"\">" +
            "   #{customerOrderCode}" +
            " </foreach>" +

            " <if test=\" cpgCode != null and cpgCode != '' \"> AND cpg_code = #{cpgCode} </if>" +
            "</script>")
    int sumVoucherNumByOrderCodeAndCpgCode(@Param("customerOrderCodeList")List<String> customerOrderCodeList,@Param("cpgCode") String cpgCode);
}
