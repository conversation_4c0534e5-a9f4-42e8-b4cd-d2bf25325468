package com.gtech.gvcore.dao.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Data
@Table(name = "gv_product_category_disscount")
public class ProductCategoryDisscount {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * product category disscount code
     */
    @Column(name = "product_category_disscount_code")
    private String productCategoryDisscountCode;

    /**
     * product category code
     */
    @Column(name = "product_category_code")
    private String productCategoryCode;

    /**
     * Valid From
     */
    @Column(name = "valid_from")
    private Date validFrom;

    /**
     * Valid Upto
     */
    @Column(name = "valid_upto")
    private Date validUpto;

    /**
     * status,0:disable,1:enable
     */
    private Integer status;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}