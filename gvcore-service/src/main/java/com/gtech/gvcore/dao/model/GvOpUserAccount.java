package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/6/13 13:54
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "idm_op_user_account")
@ApiModel(value = "GvOpUserAccount)", description = "(P)用户账号")
public class GvOpUserAccount implements Serializable {
    private static final long serialVersionUID = 738591112396435356L;

    @Id
    private Long id;

    @Column(name = "customer_code")
    private String customerCode;

    @Column(name = "user_code")
    private String userCode;

    @Column(name = "user_type")
    private Integer userType;

    @Column(name = "account")
    private String account;

    @Column(name = "PASSWORD")
    private String password;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "email")
    private String email;

    @Column(name = "email_verified")
    private Integer emailVerified;

    @Column(name = "mobile")
    private String mobile;

    @Column(name = "mobile_verified")
    private Integer mobileVerified;

    @Column(name = "STATUS")
    private Integer status;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "logic_delete")
    private Integer logicDelete;

    @Column(name = "effective_time")
    private Date effectiveTime;

    @Column(name = "lock_time")
    private Date lockTime;

    @Column(name = "user_create_type")
    private Integer userCreateType;

    @Column(name = "op_version")
    private Integer opVersion;

    @Column(name = "tenant_creator")
    private Integer tenantCreator;

    @Column(name = "delete_time")
    private String deleteTime;


}

