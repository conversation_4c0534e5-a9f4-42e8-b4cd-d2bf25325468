package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.response.customerorder.QueryCustomerOrderResponse;
import com.gtech.gvcore.dao.dto.CustomerOrderDto;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dto.QueryCustomerOrderPermissionRequest;
import com.gtech.gvcore.dto.salespostingxml.SumCustomerOrderGroupByArticle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/17 16:47
 */

@Mapper
public interface CustomerOrderMapper extends GTechBaseMapper<CustomerOrder> {


    CustomerOrder selectOrderByStatus(String businessCode, String status);

    List<QueryCustomerOrderResponse> queryOrder(@Param("orderRequest") QueryCustomerOrderPermissionRequest queryCustomerOrderRequest);

    Boolean exists(String s);
    
    /**
     *
     * @param dto
     * @return
     *
     * <AUTHOR>
     * @date 2022年3月23日
     */
    int updateStatus(CustomerOrderDto dto);

    int updateStatusByAllocationCode(String voucherAllocationCode);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年6月10日
     */
    int updateByRelease(CustomerOrderDto dto);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年6月10日
     */
    List<SumCustomerOrderGroupByArticle> sumCustomerOrderGroupByArticle(CustomerOrderDto dto);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年6月10日
     */
    List<SumCustomerOrderGroupByArticle> sumCancelCustomerOrderGroupByArticle(@Param("dto") CustomerOrderDto dto);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年6月14日
     */
    List<CustomerOrder> sumGroupByMeansOfPaymentCode(CustomerOrderDto dto);

    /**
     * 
     * <AUTHOR>
     * @param dto
     * @return
     * @date 2022年6月14日
     */
    List<CustomerOrder> sumCancelGroupByMeansOfPaymentCode(CustomerOrderDto dto);

}
