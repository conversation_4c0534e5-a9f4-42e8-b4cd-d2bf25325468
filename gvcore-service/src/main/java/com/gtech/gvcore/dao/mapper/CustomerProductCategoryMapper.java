package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.response.customerproductcategory.CustomerProductCategoryResponse;
import com.gtech.gvcore.dao.model.CustomerProductCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/21 14:53
 */
@Mapper
public interface CustomerProductCategoryMapper extends GTechBaseMapper<CustomerProductCategory> {


    @Select("SELECT " +
            " cpc.id, " +
            " cpc.customer_product_category_code, " +
            " cpc.customer_code, " +
            " cpc.product_category_code, " +
            " pc.category_name as productCategoryName, " +
            " pc.`status`, " +
            " cpc.create_user, " +
            " cpc.create_time, " +
            " cpc.update_user, " +
            " cpc.update_time  " +
            "FROM " +
            " gv_customer_product_category cpc " +
            " LEFT JOIN gv_product_category pc ON cpc.product_category_code = pc.product_category_code" +
            " WHERE cpc.customer_code = #{customer} ")
    List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomer(String customer);



    @Select("" +
            " <script> " +
            "SELECT " +
            " cpc.id, " +
            " cpc.customer_product_category_code, " +
            " cpc.customer_code, " +
            " cpc.product_category_code, " +
            " pc.category_name as productCategoryName, " +
            " pc.`status`, " +
            " cpc.create_user, " +
            " cpc.create_time, " +
            " cpc.update_user, " +
            " cpc.update_time  " +
            "FROM " +
            " gv_customer_product_category cpc " +
            " LEFT JOIN gv_product_category pc ON cpc.product_category_code = pc.product_category_code" +
            " WHERE cpc.customer_code in " +
            "<foreach collection=\"customerList\" item=\"customer\" index=\"index\" open=\"(\" close=\")\" separator=\",\">"+
            "       #{customer} " +
            "      </foreach> " +
            " </script>")
    List<CustomerProductCategoryResponse> queryCustomerProductCategoryListByCustomerList(@Param("customerList") List<String> customerList);

}
