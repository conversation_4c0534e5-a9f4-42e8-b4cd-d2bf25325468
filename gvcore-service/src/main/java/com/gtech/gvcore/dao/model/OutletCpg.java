package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_outlet_cpg")
@ApiModel(value = "OutletCpg)", description = "outlet product category")
public class OutletCpg implements Serializable {
    private static final long serialVersionUID = 386056359466803940L;

    public static final String C_OUTLET_CPG_CODE = "outletCpgCode";
    public static final String C_OUTLET_CODE = "outletCode";
    public static final String C_CPG_CODE = "cpgCode";
    public static final String C_STATUS = "status";
    public static final String C_CREATE_USER = "createUser";
    public static final String C_CREATE_TIME = "createTime";
    public static final String C_UPDATE_USER = "updateUser";
    public static final String C_UPDATE_TIME = "updateTime";

    @Id
    private Long id;

    @Column(name = "outlet_cpg_code")
    private String outletCpgCode;

    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "cpg_code")
    private String cpgCode;

    @Column(name = "`status`")
    private Integer status;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    private Date updateTime;



}
