package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/2/17 16:35
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "gv_delivery")
@ApiModel(value = "GvDelivery)", description = "")
public class GvDelivery implements Serializable {


    private static final long serialVersionUID = 2288627786636815565L;


    @Id
    private Integer id;
    @Column(name = "delivery_code")
    private String deliveryCode;
    @Column(name = "customer_order_code")
    private String customerOrderCode;
    @Column(name = "delivery_mode")
    private String deliveryMode;
    @Column(name = "logistics_name")
    private String logisticsName;
    @Column(name = "awb")
    private String awb;
    @Column(name = "scan_of_receipt")
    private String scanOfReceipt;
    @Column(name = "delivery_date")
    private String deliveryDate;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "create_user")
    private String createUser;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "update_user")
    private String updateUser;











}
