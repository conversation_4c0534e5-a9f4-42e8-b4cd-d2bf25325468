package com.gtech.gvcore.dao.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 *
 * 系统日志
 * <AUTHOR>
 *
 * sql:
 *
 *  初始化 @Date 2022/10/09 17:18:00
 *
 *    create table gv_sys_logger
 *    (
 *    id             bigint auto_increment comment 'Primary key.' primary key,
 *    user_code      varchar(32)                           null comment 'User code. ',
 *    request_path   varchar(256)                          null comment 'Servlet path.',
 *    request_data   json                                  null comment 'JSON data. 交互数据JSON',
 *    response_data  json                                  null comment 'JSON data. 交互数据JSON',
 *    execution_time bigint                                null comment 'Request execution time in milliseconds.',
 *    object_stats   json                                 null comment 'JVM object and memory statistics including request-level tracking in JSON format.',
 *    create_time    timestamp   default CURRENT_TIMESTAMP not null comment 'Create time.'
 *    ) comment 'Operation logger.';
 *
 *    create index idx_logger_createTime
 *    on gv_sys_logger (create_time);
 *
 *    create index idx_logger_path
 *    on gv_sys_logger (request_path);
 *
 *    create index idx_logger_user
 *    on gv_sys_logger (user_code);
 *
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@ApiModel("sys logger")
@Table(name = "gv_sys_logger")
public class SysLogger {

    public static final String C_ID = "id";
    public static final String C_USER_CODE = "userCode";
    public static final String C_REQUEST_PATH = "requestPath";
    public static final String C_REQUEST_DATA = "requestData";
    public static final String C_RESPONSE_DATA = "responseData";
    public static final String C_EXECUTION_TIME = "executionTime";
    public static final String C_OBJECT_STATS = "objectStats";
    public static final String C_CREATE_TIME = "createTime";

    // Primary key.
    @Id
    private Long id;

    // User code.
    @ApiModelProperty("user code")
    @Column(name = "user_code")
    private String userCode;

    // Servlet path.
    @ApiModelProperty("request path")
    @Column(name = "request_path")
    private String requestPath;

    // JSON data. 交互数据JSON
    @ApiModelProperty("request data")
    @Column(name = "request_data")
    private String requestData;

    // JSON data. 交互数据JSON
    @ApiModelProperty("response data")
    @Column(name = "response_data")
    private String responseData;

    // Request execution time in milliseconds.
    @ApiModelProperty("execution time")
    @Column(name = "execution_time")
    private Long executionTime;

    // JVM object and memory statistics including request-level tracking in JSON format.
    @ApiModelProperty("object stats")
    @Column(name = "object_stats")
    private String objectStats;

    // Create time.
    @ApiModelProperty("create time")
    @Column(name = "create_time")
    private Date createTime;

}
