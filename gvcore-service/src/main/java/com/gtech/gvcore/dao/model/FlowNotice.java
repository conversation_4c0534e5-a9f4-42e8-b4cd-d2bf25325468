package com.gtech.gvcore.dao.model;

import java.util.Date;
import javax.persistence.*;

import lombok.Data;

@Table(name = "gv_flow_notice")
@Data
public class FlowNotice {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * flow code
     */
    @Column(name = "flow_code")
    private String flowCode;

    /**
     * flow node code
     */
    @Column(name = "flow_node_code")
    private String flowNodeCode;

    /**
     * flow notic type, 0-current, 1-cc, default 0
     */
    @Column(name = "flow_notice_type")
    private Integer flowNoticeType;

    /**
     * flow notice code
     */
    @Column(name = "flow_notice_code")
    private String flowNoticeCode;

    /**
     * flow notice code type, 0-role,1-user, default 0
     */
    @Column(name = "flow_notice_code_type")
    private Integer flowNoticeCodeType;

    /**
     * status
     */
    private Integer status;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;
}