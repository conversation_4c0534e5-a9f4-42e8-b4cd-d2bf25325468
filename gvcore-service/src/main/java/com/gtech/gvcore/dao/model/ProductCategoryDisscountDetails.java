package com.gtech.gvcore.dao.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;


@Data
@Table(name = "gv_product_category_disscount_details")
public class ProductCategoryDisscountDetails {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * product category disscount details code,data UNIQUE KEY
     */
    @Column(name = "product_category_disscount_details_code")
    private String productCategoryDisscountDetailsCode;

    /**
     * product category disscount code
     */
    @Column(name = "product_category_disscount_code")
    private String productCategoryDisscountCode;

    /**
     * from purchase value
     */
    @Column(name = "from_purchase_value")
    private BigDecimal fromPurchaseValue;

    /**
     * upto purchase value
     */
    @Column(name = "upto_purchase_value")
    private BigDecimal uptoPurchaseValue;

    /**
     * discount type
     */
    @Column(name = "discount_type")
    private String discountType;

    /**
     * discount
     */
    private BigDecimal discount;

    /**
     * maximum discount value
     */
    @Column(name = "maximum_discount_value")
    private BigDecimal maximumDiscountValue;

    /**
     * status,0:disable,1:enable
     */
    private Integer status;

    /**
     * delete status
     */
    @Column(name = "delete_status")
    private Integer deleteStatus;

    /**
     * create user
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * create time
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * update user
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * update time
     */
    @Column(name = "update_time")
    private Date updateTime;

}