package com.gtech.gvcore.helper;

import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.utils.AmountUtils;
import com.gtech.gvcore.common.utils.GvConvertUtils;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dto.GcCpgDTO;
import com.gtech.gvcore.giftcard.masterdata.gcpg.service.GcCpgService;
import com.gtech.gvcore.helper.bean.ProformaInvoice;
import com.gtech.gvcore.helper.bean.Quotation;
import com.gtech.gvcore.helper.bean.SalesNote;
import com.gtech.gvcore.helper.bean.SalesOrder;
import com.gtech.gvcore.service.ArticleMopService;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.CustomerService;
import com.gtech.gvcore.service.GcArticleMopService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @ClassName CustomerOrderPdfHelper
 * @Description
 * <AUTHOR>
 * @Date 2023/1/28 19:07
 * @Version V1.0
 **/
@Component
public class CustomerOrderPdfHelper {


    @Autowired
    private GTechRedisTemplate redisTemplate;

    @Lazy
    @Autowired
    private CustomerService customerService;
    @Lazy
    @Autowired
    private CpgService cpgService;
    @Lazy
    @Autowired
    private ArticleMopService articleMopService;
    @Lazy
    @Autowired
    private OssHelper ossHelper;

    @Lazy
    @Autowired
    private GcCpgService gcCpgService;

    @Lazy
    @Autowired
    private GcArticleMopService gcArticleMopService;


    private static final String PDF_PATCH = "/";

    public String getIncreaseCode(String key) {

        Long increase = redisTemplate.opsValueIncrement(GvcoreConstants.APP_CODE, key, 1);

        return String.format("%04d", increase);

    }

    public ArticleMop getArticleMop(String cpgCode) {

        Cpg cpg = cpgService.getCpgByCode(cpgCode);
        if (null == cpg) {

            GcCpgDTO gcCpg = gcCpgService.getCpg(cpgCode);
            if (null != gcCpg){
                GcArticleMop gcArticleMop = gcArticleMopService.queryByArticleMopCode(gcCpg.getArticleMopCode());
                if (null == gcArticleMop) return new ArticleMop();
                return BeanCopyUtils.jsonCopyBean(gcArticleMop, ArticleMop.class);
            }
        }

        if (null == cpg || StringUtils.isBlank(cpg.getArticleMopCode())) return new ArticleMop();

        return articleMopService.queryByArticleMopCode(cpg.getArticleMopCode());

    }

    public static String getBaseFileName() {

        return PDF_PATCH + System.nanoTime() + "__";
    }

    // --------------------------------------------------------SUPPORT END-------------------------------------------------------------------

    public String generateSalesNoteHtml(CustomerOrder customerOrder, List<CustomerOrderDetails> customerOrderDetails, String operateUser) {

        SalesNote salesNote = new SalesNote()
                .setCustomerName(customerOrder.getCustomerName())
                .setAmount(customerOrder.getAmount(), customerOrder.getVoucherAmount())
                .setDiscount(customerOrder.getAmount())
                .setSumQty(customerOrderDetails)
                .setVoucherAmount(customerOrderDetails)
                .setInvoiceNo(customerOrder.getInvoiceNo())
                .setOperateUser(operateUser)
                .setDetailList(customerOrderDetails.stream().map(item -> new SalesNote.SalesNoteItemHtmlDataDto()
                        .setDenomination(AmountUtils.idrFormat(ConvertUtils.toBigDecimal(item.getDenomination().compareTo(BigDecimal.ZERO) == 0 ? item.getOrderDenomination(): item.getDenomination() , BigDecimal.ZERO)))
                        .setVoucherAmount(AmountUtils.idrFormat(ConvertUtils.toBigDecimal(item.getVoucherAmount(), BigDecimal.ZERO)))
                        .setVoucherNum(AmountUtils.idrFormat(ConvertUtils.toBigDecimal(item.getVoucherNum(), BigDecimal.ZERO)))
                        .setArticleMopCode(GvConvertUtils.toObject(getArticleMop(item.getCpgCode()), new ArticleMop()).getArticleCodeName())).collect(Collectors.toList()));

        Optional.of(customerOrder)
                .map(CustomerOrder::getCustomerCode)
                .map(customerService::getCustomerSummary)
                .ifPresent(e -> salesNote
                        .setBeneficiaryName(e.getBeneficiaryName())
                        .setBranchName(e.getBranchName())
                        .setBankName(e.getBankName())
                        .setAccountNumber(e.getAccountNumber()));

        return this.ossHelper.upload(GvHtmlHelper.fillDataWithTemplate(SalesNote.SOURCE_TEMPLATE_PATH, salesNote), salesNote.getFileName());
    }
    
    public String quotation(final CustomerOrder customerOrder, final List<CustomerOrderDetails> customerOrderDetailsList, final CustomerOrderReceiver customerOrderReceiver) {

        AtomicInteger index = new AtomicInteger(1);

        final Quotation quotation = new Quotation()
                .setCustomerName(customerOrder.getCustomerName())
                .setShippingAddress(customerOrderReceiver.getShippingAddress())
                .setDiscount(customerOrder.getDiscount())
                .setDiscountAmount(customerOrder.getAmount())
                .setDiscountType(customerOrder.getDiscountType())
                .setDetailList(customerOrderDetailsList.stream().map(e -> {

                    final ArticleMop articleMop = getArticleMop(e.getCpgCode());

                    return new Quotation.Detail()
                            .setLine(index.getAndIncrement())
                            .setEgv(GvcoreConstants.MOP_CODE_VCE.equals(articleMop.getMopCode()))
                            .setDenomination(e.getDenomination())
                            .setVoucherNum(new BigDecimal(e.getVoucherNum()))
                            .setArticleCodeName(articleMop.getArticleCodeName());

                }).collect(Collectors.toList()));

        Optional.of(customerOrder)
                .map(CustomerOrder::getCustomerCode)
                .map(customerService::getCustomerSummary)
                .ifPresent(e -> quotation
                        .setBeneficiaryName(e.getBeneficiaryName())
                        .setBranchName(e.getBranchName())
                        .setBankName(e.getBankName())
                        .setAccountNumber(e.getAccountNumber()));

        return this.ossHelper.upload(GvHtmlHelper.fillDataWithTemplate(Quotation.SOURCE_TEMPLATE_PATH, quotation), quotation.getFileName());

    }

    public String salesOrder(final CustomerOrder customerOrder, final List<CustomerOrderDetails> customerOrderDetailsList) {

        AtomicInteger index = new AtomicInteger(1);

        final SalesOrder salesOrder = new SalesOrder()
                .setIncreaseNumber(getIncreaseCode(SalesOrder.INCREASE_KEY))
                .setCustomerName(customerOrder.getCustomerName())
                .setDiscount(customerOrder.getDiscount())
                .setDiscountAmount(customerOrder.getAmount())
                .setDiscountType(customerOrder.getDiscountType())
                .setDetailList(customerOrderDetailsList.stream().map(e -> {

                    final ArticleMop articleMop = getArticleMop(e.getCpgCode());

                    return new SalesOrder.Detail()
                            .setLine(index.getAndIncrement())
                            .setEgv(GvcoreConstants.MOP_CODE_VCE.equals(articleMop.getMopCode()))
                            .setDenomination(e.getDenomination())
                            .setVoucherNum(new BigDecimal(e.getVoucherNum()))
                            .setArticleCodeName(articleMop.getArticleCodeName());

                }).collect(Collectors.toList()));

        Optional.of(customerOrder)
                .map(CustomerOrder::getCustomerCode)
                .map(customerService::getCustomerSummary)
                .ifPresent(e -> salesOrder
                        .setBeneficiaryName(e.getBeneficiaryName())
                        .setBranchName(e.getBranchName())
                        .setDivision(e.getContactDivision())
                        .setBankName(e.getBankName())
                        .setAccountNumber(e.getAccountNumber()));

        return this.ossHelper.upload(GvHtmlHelper.fillDataWithTemplate(SalesOrder.SOURCE_TEMPLATE_PATH, salesOrder), salesOrder.getFileName());

    }


    public String proformaInvoice(final CustomerOrder customerOrder, final List<CustomerOrderDetails> customerOrderDetailsList, final CustomerOrderReceiver customerOrderReceiver) {

        AtomicInteger index = new AtomicInteger(1);

        final ProformaInvoice proformaInvoice = new ProformaInvoice()
                .setIncreaseNumber(getIncreaseCode(ProformaInvoice.INCREASE_KEY))
                .setCustomerName(customerOrder.getCustomerName())
                .setShippingAddress(customerOrderReceiver.getShippingAddress())
                .setDiscount(customerOrder.getDiscount())
                .setDiscountAmount(customerOrder.getAmount())
                .setDiscountType(customerOrder.getDiscountType())
                .setDetailList(customerOrderDetailsList.stream().map(e -> {

                    final ArticleMop articleMop = getArticleMop(e.getCpgCode());

                    return new ProformaInvoice.Detail()
                            .setLine(index.getAndIncrement())
                            .setEgv(GvcoreConstants.MOP_CODE_VCE.equals(articleMop.getMopCode()))
                            .setDenomination(e.getDenomination())
                            .setVoucherNum(new BigDecimal(e.getVoucherNum()))
                            .setArticleCodeName(articleMop.getArticleCodeName());

                }).collect(Collectors.toList()));

        Optional.of(customerOrder)
                .map(CustomerOrder::getCustomerCode)
                .map(customerService::getCustomerSummary)
                .ifPresent(e -> proformaInvoice.setPph(Objects.equals(e.getPph(), 1))
                        .setBeneficiaryName(e.getBeneficiaryName())
                        .setBranchName(e.getBranchName())
                        .setBankName(e.getBankName())
                        .setAccountNumber(e.getAccountNumber()));

        return this.ossHelper.upload(GvHtmlHelper.fillDataWithTemplate(ProformaInvoice.SOURCE_TEMPLATE_PATH, proformaInvoice), proformaInvoice.getFileName());
    }

}
