package com.gtech.gvcore.helper;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.gtech.basic.filecloud.api.model.OssPreAuthDTO;
import com.gtech.basic.filecloud.api.model.OssPreAuthResultDTO;
import com.gtech.basic.filecloud.commons.ErrorCodes;
import com.gtech.basic.filecloud.commons.FileCloudException;
import com.gtech.basic.filecloud.oss.OssManager;
import com.gtech.basic.filecloud.oss.model.OssBucketType;
import com.gtech.basic.filecloud.oss.model.OssNamespace;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;

/**
 * <AUTHOR> update
 */
@Slf4j
@Component
public class OssHelper {

    @Autowired
    private OssManager ossManager;

    @Autowired
    private FileHelper fileHelper;

    private static final String OSS_ACCESS_KEY = "oss.without-access-key";

    private static final String OSS_APPEND_DIR = "/export/";

    private static final String PDF_SUFFIX = ".pdf";
    private static final String HTML_SUFFIX = ".html";

    public static final String SYSTEM_DEFAULT = "SYSTEM_DEFAULT";
    public String grantAccessUrl(String key){
        // 授权oss文件访问链接
        URL url = ossManager.grantAccessUrl(OssNamespace.of(SYSTEM_DEFAULT, SYSTEM_DEFAULT, OssBucketType.PRIVATE), key);
        log.debug("OSS file access link:{}", url);
        return url.toString();
    }

    public String excelFileUploadToOss(String fullPath, ReportExportTypeEnum reportExportTypeEnum) {
        return fileUploadToOss(fullPath
                , fileHelper.generateOssFileName(reportExportTypeEnum.getExportName(), ExcelTypeEnum.XLSX.getValue()));
    }

    public String pdfFileUploadToOss(String fullPath, ReportExportTypeEnum reportExportTypeEnum) {
        return fileUploadToOss(fullPath
                , fileHelper.generateOssFileName(reportExportTypeEnum.getExportName(), OssHelper.PDF_SUFFIX));
    }

    public String excelFileUploadToOss(final InputStream inputStream, final ReportExportTypeEnum reportExportType) {
        return this.upload(inputStream, this.fileHelper.generateOssFileName(reportExportType.getExportName(), ExcelTypeEnum.XLSX.getValue()));
    }

    public String pdfFileUploadToOss(final InputStream inputStream, final ReportExportTypeEnum reportExportType) {
        return this.upload(inputStream, fileHelper.generateOssFileName(reportExportType.getExportName(), OssHelper.HTML_SUFFIX));
    }

    /**
     * 上传oss
     *
     * @param fullPath
     * @param ossTargetFileName oss上的文件名
     * @return
     */
    public String fileUploadToOss(String fullPath, String ossTargetFileName) {

        //生成目标文件名
        OssPreAuthResultDTO sign = ossManager.grantToken(buildOssTokenRequest(ossTargetFileName));

        OSS ossClient = new OSSClientBuilder().build(sign.getEndpoint(), sign.getAccessId(), OSS_ACCESS_KEY);

        // 本地要上传到 oss 的文件
        File localFile = new File(fullPath);

        try (FileInputStream inputStream = new FileInputStream(localFile)) {
            ossClient.putObject(new URL(sign.getGrantUrl()), inputStream, localFile.length(), sign.resolveHeads());
        } catch (Exception e) {
            log.error("[File] upload to OSS cause an error.", e);
            throw FileCloudException.code(ErrorCodes.OSS_UPLOAD_ERROR);
        } finally {
            ossClient.shutdown();
            fileHelper.fileCleanup(fullPath);
        }
        return sign.getObjectKey();
    }



    private OssPreAuthDTO buildOssTokenRequest(String fileName) {

        OssPreAuthDTO tokenRequest = OssPreAuthDTO.privateBucket();
        tokenRequest.setTenantCode(GvcoreConstants.APP_CODE);
        tokenRequest.setDomainCode(GvcoreConstants.TITAN_DOMAIN_CODE);
        tokenRequest.setPathSuffix(OSS_APPEND_DIR + RandomStringUtils.randomAlphanumeric(32) + "/" + fileName);

        return tokenRequest;
    }


    /**
     * 上传oss
     *
     * @param parameter         压缩信息
     * @param ossTargetFileName oss上的文件名,不需要后缀
     * @return
     */
    public String compressionUploadToOss(final FileCompressionHelper.EncryptCompressionParameter parameter, final String ossTargetFileName) {

        final String ossFileName = this.fileHelper.generateOssZipFileName(ossTargetFileName, ".zip");

        //生成目标文件名
        final OssPreAuthResultDTO sign = this.ossManager.grantToken(this.buildOssTokenRequest(ossFileName));

        final OSS ossClient = new OSSClientBuilder().build(sign.getEndpoint(), sign.getAccessId(), OssHelper.OSS_ACCESS_KEY);

        try (final InputStream inputStream = FileCompressionHelper.zipEncrypt(parameter)) {
            ossClient.putObject(new URL(sign.getGrantUrl()), inputStream, inputStream.available(), sign.resolveHeads());
        } catch (Exception e) {
            log.error("[File] upload to OSS cause an error.", e);
            throw FileCloudException.code(ErrorCodes.OSS_UPLOAD_ERROR);
        } finally {
            ossClient.shutdown();
        }

        // 上传至OSS上的文件链接
        log.debug("OSS file link:{}", sign.remoteLocation());



        return sign.getObjectKey();
    }


    /**
     * 上传文件到oss
     *
     * @param inputStream       要上传的文件的流信息
     * @param ossTargetFileName oss上的文件名,不需要后缀
     * @return
     */
    public String upload(final InputStream inputStream, final String ossTargetFileName) {

        //生成目标文件名
        final OssPreAuthResultDTO sign = this.ossManager.grantToken(this.buildOssTokenRequest(ossTargetFileName));

        final OSS ossClient = new OSSClientBuilder().build(sign.getEndpoint(), sign.getAccessId(), OSS_ACCESS_KEY);

        try (final InputStream stream = inputStream) {
            ossClient.putObject(new URL(sign.getGrantUrl()), stream, stream.available(), sign.resolveHeads());
        } catch (Exception e) {
            log.error("[File] upload to OSS cause an error.", e);
            throw FileCloudException.code(ErrorCodes.OSS_UPLOAD_ERROR);
        } finally {
            ossClient.shutdown();
        }

        // 上传至OSS上的文件链接
        log.debug("OSS file link:{}", sign.remoteLocation());


        return sign.getObjectKey();
    }

}

