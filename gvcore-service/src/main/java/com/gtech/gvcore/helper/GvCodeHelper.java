/**
 * Copyright (c) 2019 GTech All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of GTech. You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms of the license agreement
 * you entered into with GTech.
 * <p>
 * <PERSON><PERSON><PERSON> MAKES NO REPRESENTATIONS OR WA<PERSON><PERSON>NTIES ABOUT THE SUITABILITY OF THE SOFTWARE, EITHER EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
 * PARTICULAR PURPOSE, OR NON-INFRINGEMENT. GTECH SHALL NOT BE LIABLE FOR ANY DAMAGES SUFFERED BY
 * LICENSEE AS A RESULT OF USING, MODIFYING OR DISTRIBUTING THIS SOFTWARE OR ITS DERIVATIVES.
 */
package com.gtech.gvcore.helper;

import com.gtech.commons.redis.GTechCodeGenerator;
import com.gtech.commons.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Date;


@Component
public class GvCodeHelper {

    private static final String APPKEY = "GV";

    private static final String EXPR = "10[D:yyMMddHHmmss][SA:%06d]";


    private static final String ORDER_NUMBER = "0001";

    // Template name for generate user code.
    public static final String ISSUER_CODE_TEMPLATE_NAME = "ISSUER";
    // Incremental step size of auto-incrementing part of issuer code.
    public static final Long ISSUER_CODE_INCREASE_STEP = 1L;
    // Template expression for generate issuer code.
    private String issuerCodeTemplateExpr = "IS" + EXPR;


    // Template name for generate company code.
    public static final String COMPANY_CODE_TEMPLATE_NAME = "COMPANY";
    // Incremental step size of auto-incrementing part of company code.
    public static final Long COMPANY_CODE_INCREASE_STEP = 1L;
    // Template expression for generate company code.
    private String companyCodeTemplateExpr = "CO" + EXPR;


    // Template name for generate merchant code.
    public static final String MERCHANT_CODE_TEMPLATE_NAME = "MERCHANT";
    // Incremental step size of auto-incrementing part of merchant code.
    public static final Long MERCHANT_CODE_INCREASE_STEP = 1L;
    // Template expression for generate merchant code.
    private String merchantCodeTemplateExpr = "ME" + EXPR;

    // Template name for generate outlet code.
    public static final String OUTLET_CODE_TEMPLATE_NAME = "OUTLET";
    // Incremental step size of auto-incrementing part of outlet code.
    public static final Long OUTLET_CODE_INCREASE_STEP = 1L;
    // Template expression for generate outlet code.
    private String outletCodeTemplateExpr = "OU" + EXPR;

    // Template name for generate voucherRequest code.
    public static final String VOUCHER_REQUEST_CODE_TEMPLATE_NAME = "VOUCHER_REQUEST";
    // Incremental step size of auto-incrementing part of voucherRequest code.
    public static final Long VOUCHER_REQUEST_CODE_INCREASE_STEP = 1L;
    // Template expression for generate voucherRequest code.
    private String voucherRequestCodeTemplateExpr = "REQ[D:yy][SA:%05d]";

    // Template name for generate voucherRequestDetails code.
    public static final String VOUCHER_REQUEST_DETAILS_CODE_TEMPLATE_NAME = "VOUCHER_REQUEST_DETAILS";
    // Incremental step size of auto-incrementing part of voucherRequestDetails code.
    public static final Long VOUCHER_REQUEST_DETAILS_CODE_INCREASE_STEP = 1L;
    // Template expression for generate voucherRequestDetails code.
    private String voucherRequestDetailsCodeTemplateExpr = "VRD" + EXPR;

    // Template name for generate reportManagement code.
    public static final String REPORT_MANAGEMENT_CODE_TEMPLATE_NAME = "REPORT_MANAGEMENT";
    // Incremental step size of auto-incrementing part of voucherRequestDetails code.
    public static final Long REPORT_MANAGEMENT_CODE_INCREASE_STEP = 1L;
    // Template expression for generate voucherRequestDetails code.
    private String reportManagementCodeTemplateExpr = "RM" + EXPR;

    // Template name for generate releaseApproveAmount code.
    public static final String RELEASE_APPROVE_AMOUNT_CODE_TEMPLATE_NAME = "RELEASE_APPROVE_AMOUNT";
    // Incremental step size of auto-incrementing part of releaseApproveAmount code.
    public static final Long RELEASE_APPROVE_AMOUNT_CODE_INCREASE_STEP = 1L;
    // Template expression for generate releaseApproveAmount code.
    private String releaseApproveAmountCodeTemplateExpr = "RAA" + EXPR;

    // Template name for generate customerOrder code.
    public static final String CUSTOMER_ORDER_CODE_TEMPLATE_NAME = "CUSTOMER_ORDER";
    // Incremental step size of auto-incrementing part of customerOrder code.
    public static final Long CUSTOMER_ORDER_CODE_INCREASE_STEP = 1L;
    // Template expression for generate customerOrder code.
    private String customerOrderCodeTemplateExpr = "CO" + EXPR;

    // Template name for generate customerOrder code.
    public static final String TRANSACTION_CODE_TEMPLATE_NAME = "TRANSACTION_CODE";
    // Incremental step size of auto-incrementing part of customerOrder code.
    public static final Long TRANSACTION_CODE_INCREASE_STEP = 1L;
    // Template expression for generate customerOrder code.
    private String transactionCodeTemplateExpr = "TRA" + EXPR;

    // Template name for generate customerOrder code.
    public static final String CUSTOMER_ORDER_EMAIL_CODE_TEMPLATE_NAME = "CUSTOMER_ORDER_EMAIL";
    // Incremental step size of auto-incrementing part of customerOrder code.
    public static final Long CUSTOMER_ORDER_EMAIL_CODE_INCREASE_STEP = 1L;
    // Template expression for generate customerOrder code.
    private String customerOrderEmailCodeTemplateExpr = "COE" + EXPR;

    // Template name for generate customerOrder code.
    public static final String CUSTOMER_ORDER_DETAIL_CODE_TEMPLATE_NAME = "CUSTOMER_ORDER_DETAIL";
    // Incremental step size of auto-incrementing part of customerOrder code.
    public static final Long CUSTOMER_ORDER_DETAIL_CODE_INCREASE_STEP = 1L;
    // Template expression for generate customerOrder code.
    private String customerOrderDetailCodeTemplateExpr = "COD" + EXPR;

    // Template name for generate approveNodeRecord code.
    public static final String APPROVE_NODE_RECORD_TEMPLATE_NAME = "APPROVE_NODE_RECORD";
    // Incremental step size of auto-incrementing part of approveNodeRecord code.
    public static final Long APPROVE_NODE_RECORD_INCREASE_STEP = 1L;
    // Template expression for generate approveNodeRecord code.
    private String approveNodeRecordTemplateExpr = "ANR" + EXPR;

    // Template name for generate schedulerReport code.
    public static final String SCHEDULER_REPORT_TEMPLATE_NAME = "SCHEDULER_REPORT";
    // Incremental step size of auto-incrementing part of schedulerReport code.
    public static final Long SCHEDULER_REPORT_INCREASE_STEP = 1L;
    // Template expression for generate schedulerReport code.
    private String schedulerReportTemplateExpr = "SR" + EXPR;

    // Template name for generate schedulerReport code.
    public static final String ORDER_REPORT_TEMPLATE_NAME = "ORDER_REPORT";
    // Incremental step size of auto-incrementing part of schedulerReport code.
    public static final Long ORDER_REPORT_INCREASE_STEP = 1L;
    // Template expression for generate schedulerReport code.
    private String orderReportTemplateExpr = "OR" + EXPR;

    // Template name for generate purchaseOrderNumber
    public static final String PURCHASE_ORDER_NUMBER_TEMPLATE_NAME = "PURCHASE_ORDER_NUMBER";
    // Incremental step size of auto-incrementing part of purchaseOrderNumber.
    public static final Long PURCHASE_ORDER_NUMBER_INCREASE_STEP = 1L;
    // Template expression for generate purchaseOrderNumber
    private String purchaseOrderNumberTemplateExpr = "[D:MMyy]-[SA:%04d]";

    // Template name for generate releaseApproveNode code.
    public static final String RELEASE_APPROVE_NODE_CODE_TEMPLATE_NAME = "RELEASE_APPROVE_NODE";
    // Incremental step size of auto-incrementing part of voucherRequestDetails code.
    public static final Long RELEASE_APPROVE_NODE_CODE_INCREASE_STEP = 1L;
    // Template expression for generate voucherRequestDetails code.
    private String releaseApproveNodeCodeTemplateExpr = "RAN" + EXPR;

    // Template name for generate customer code.
    public static final String CUSTOMER_CODE_TEMPLATE_NAME = "CUSTOMER";
    // Incremental step size of auto-incrementing part of customer code.
    public static final Long CUSTOMER_CODE_INCREASE_STEP = 1L;
    // Template expression for generate customer code.
    private String customerCodeTemplateExpr = "CU" + EXPR;


    // Template name for generate customer payment method code.
    public static final String CUSTOMER_PAYMENT_METHOD_CODE_TEMPLATE_NAME = "CUSTOMER_PAYMENT_METHOD";
    // Incremental step size of auto-incrementing part of customer payment method code.
    public static final Long CUSTOMER_PAYMENT_METHOD_CODE_INCREASE_STEP = 1L;
    // Template expression for generate customer payment method code.
    private String customerPaymentMethodCodeTemplateExpr = "CPM" + EXPR;


    // Template name for generate outlet product category code.
    public static final String OUTLET_CPG_CODE_TEMPLATE_NAME = "OUTLET_CPG";
    // Incremental step size of auto-incrementing part of outlet product category code.
    public static final Long OUTLET_CPG_CODE_INCREASE_STEP = 1L;
    // Template expression for generate outlet product category code.
    private String outletCPGCodeTemplateExpr = "OC" + EXPR;


    // Template name for generate customer product category code.
    public static final String CUSTOMER_PRODUCT_CATEGORY_CODE_TEMPLATE_NAME = "CUSTOMER_PRODUCT_CATEGORY";
    // Incremental step size of auto-incrementing part of customer product category code.
    public static final Long CUSTOMER_PRODUCT_CATEGORY_CODE_INCREASE_STEP = 1L;
    // Template expression for generate customer product category code.
    private String customerProductCategoryCodeTemplateExpr = "CPC" + EXPR;


    public static final String BUSINESS_LOG_CODE_TEMPLATE_NAME = "BUSINESS_LOG";
    public static final Long BUSINESS_LOG_CODE_INCREASE_STEP = 1L;
    private String businessLogCodeTemplateExpr = "BL" + EXPR;

    public static final String BUSINESS_LOG_DETAIL_CODE_TEMPLATE_NAME = "BUSINESS_LOG_DETAIL";
    public static final Long BUSINESS_LOG_DETAIL_CODE_INCREASE_STEP = 1L;
    private String businessLogDetailCodeTemplateExpr = "BLD" + EXPR;


    // Template name for generate printer code.
    public static final String PRINTER_CODE_TEMPLATE_NAME = "PRINTER";
    // Incremental step size of auto-incrementing part of printer code.
    public static final Long PRINTER_CODE_INCREASE_STEP = 1L;
    // Template expression for generate printer code.
    private String printerCodeTemplateExpr = "CPC" + EXPR;


    public static final String POS_CODE_TEMPLATE_NAME = "POS";
    public static final Long POS_CODE_INCREASE_STEP = 1L;
    private String posCodeTemplateExpr = "POS" + EXPR;


    public static final String POS_ACCOUNT_CODE_TEMPLATE_NAME = "POSACCOUNT";
    public static final Long POS_ACCOUNT_CODE_INCREASE_STEP = 1L;
    private String posAccountCodeTemplateExpr = "PA" + EXPR;


    public static final String TRANSACTION_DATA_CODE_TEMPLATE_NAME = "TRA";
    public static final Long TRANSACTION_DATA_CODE_INCREASE_STEP = 1L;
    private String transactionDataCodeTemplateExpr = "TRA" + EXPR;

    public static final String POS_CPG_CODE_TEMPLATE_NAME = "POS_CPG";
    public static final Long POS_CPG_CODE_INCREASE_STEP = 1L;
    private String posCpgCodeTemplateExpr = "POG" + EXPR;


    // Template name for generate cpgType code.
    public static final String CPG_TYPE_CODE_TEMPLATE_NAME = "CPG_TYPE";
    // Incremental step size of auto-incrementing part of cpgType code.
    public static final Long CPG_TYPE_CODE_INCREASE_STEP = 1L;
    // Template expression for generate cpgType code.
    private String cpgTypeCodeTemplateExpr = "CPC" + EXPR;



    public static final Long USER_CODE_INCREASE_STEP = 1L;
    private String userCodeTemplateExpr = "OPU" + EXPR;


    // Template name for generate appendWriteExcel code.
    public static final String APPEND_WRITE_EXCEL_CODE_TEMPLATE_NAME = "APPEND_WRITE_EXCEL";
    // Incremental step size of auto-incrementing part of appendWriteExcel code.
    public static final Long APPEND_WRITE_EXCEL_INCREASE_STEP = 1L;
    // Template expression for generate appendWriteExcel code.
    private String appendWriteExcelCodeTemplateExpr = "EP" + EXPR;

    // Distribution email
    public static final String DISTRIBUTION_EMAIL_TEMPLATE_CODE_TEMPLATE_NAME = "ET";
    public static final Long DISTRIBUTION_EMAIL_TEMPLATE_CODE_INCREASE_STEP = 1L;
    private String distributionEmailTemplateCodeTemplateExpr = "ET" + EXPR;

    // Distribution
    public static final String DISTRIBUTION_CODE_TEMPLATE_NAME = "";
    public static final Long DISTRIBUTION_CODE_INCREASE_STEP = 1L;
    private String distributionCodeTemplateExpr = "[D:yyMMdd][SA:%08d]";

    // Distribution email
    public static final String DISTRIBUTION_ITEM_CODE_TEMPLATE_NAME = "DI";
    public static final Long DISTRIBUTION_ITEM_CODE_INCREASE_STEP = 1L;
    private String distributionItemCodeTemplateExpr = "DI[D:yyMMddHHmm][SD:%06d]";



    public static final String INVOICE_NUMBER = "INVOICE_NUMBER";
    public static final Long INVOICE_NUMBER_STEP = 1L;
    private String invoiceNumberTemplateExpr = "MAPGV" + "[D:yy][SA:%06d]";

    public static final String APPROVE_CODE = "APPROVE_CODE";
    public static final Long APPROVE_CODE_STEP = 1L;
    private String approveCodeTemplateExpr = "[D:yy][SA:%07d]";

    public static final String DELIVERY_CODE = "DELIVERY_CODE";
    public static final Long DELIVERY_CODE_STEP = 1L;
    private String deliveryCodeTemplateExpr = "DE" + "[D:yyMMddHHmm][SA:%06d]";


    /**
     * Template name for generate exchangeRate code.
     */
    public static final String EXCHANGE_RATE_CODE_TEMPLATE_NAME = "EXCHANGE_RATE";

    /**
     * Incremental step size of auto-incrementing part of exchangeRate code.
     */
    public static final Long EXCHANGE_RATE_CODE_INCREASE_STEP = 1L;
    /**
     * Template expression for generate exchangeRate code.
     */
    private String exchangeRateCodeTemplateExpr = "CPC" + EXPR;

    private static final String ALLOCATION_TEMPLATENAME = "AllocationCode";
    
    private static final String ISSUEHANDLING_TEMPLATENAME = "IssueHandlingCode";

    private static final String EXPR_YYMMDDHHMM_4 = "[D:yyMMddHHmm][SA:%04d]";

    private static final long INCREASE_STEP_1 = 1L;

    @Autowired
    private GTechCodeGenerator codeGenerator;


    @Autowired
    private RedisTemplate redisTemplate;

    // 添加ThreadLocal StringBuilder以减少字符串创建
    private static final ThreadLocal<StringBuilder> stringBuilderCache = 
        ThreadLocal.withInitial(() -> new StringBuilder(128));

    private String getCodeWithCachedBuilder(String prefix, String templateName, String templateExpr, Long step) {
        String code = this.codeGenerator.generateCode(prefix, templateName, templateExpr, step);
        
        // 使用缓存的StringBuilder来避免创建新的String对象
        StringBuilder sb = stringBuilderCache.get();
        sb.setLength(0); // 清空
        sb.append(code);
        return sb.toString();
    }

    /**
     * 单号每天从1开始
     * @return
     */
    public synchronized String generateOrderNumber(String storeCode){
        String year = DateUtil.format(new Date(), "ddMMyy");
        //获取当前年月日
        //String year = "20210208";
        //获取redis中的年月日
        String redisYear = (String) redisTemplate.opsForValue().get(storeCode+"taskNumberYear");
        //定义计数
        Integer count = null;
        //判断redis中日期是否为空
        if (StringUtils.isEmpty(redisYear)){
            redisTemplate.opsForValue().set(storeCode+"taskNumberYear",year);
            redisTemplate.opsForValue().set(storeCode+"taskNumber","1");
        }else {
            //不为空  当前日期和redis中日期对比
            if (redisYear.equals(year)){
                //获取redis中计数
                String number = (String) redisTemplate.opsForValue().get(storeCode+"taskNumber");
                // 如果计数为空 添加新的计数 1
                if (StringUtils.isEmpty(number)){
                    redisTemplate.opsForValue().set(storeCode+"taskNumber","1");
                }else {
                    //如果不为空 +1 从新添加到redis中 -> 修改（直接返回）
                    /*Integer count2 =  Integer.parseInt(number)+1;
                    redisTemplate.opsForValue().set(storeCode+"taskNumber",count2.toString() );*/
                }
            }else {
                //如果redis中日期和 当前日期不相等 从新赋值
                redisTemplate.opsForValue().set(storeCode+"taskNumber","1");
                redisTemplate.opsForValue().set(storeCode+"taskNumberYear",year);
            }
        }
        //从新获取日期 防止没有获取到更新后得日期
        year = (String) redisTemplate.opsForValue().get(storeCode+"taskNumberYear");
        Integer num = Integer.parseInt((String)redisTemplate.opsForValue().get(storeCode+"taskNumber"));
        //四位 数值 将 1 变成 0001
        String s = String.format("%04d", num);
        return storeCode+"-"+year+"-"+s;
    }

    public synchronized Boolean updateRedisOrderNumber(String storeCode, String poNumber){
        String date = DateUtil.format(new Date(), "ddMMyy");
        boolean ddMMyy = poNumber.startsWith(storeCode +"-"+ date);
        if (!ddMMyy){
            return false;
        }
        String substring = poNumber.substring(poNumber.length() -4, poNumber.length());


        redisTemplate.opsForValue().set(storeCode+"taskNumber",substring);

        return true;
    }






    public String generateOpUserCode() {
        return this.codeGenerator.generateCode("IDM", "USRCD", this.userCodeTemplateExpr, USER_CODE_INCREASE_STEP);
    }


    public String generateInvoiceNumber() {
        return getCodeWithCachedBuilder("GV", INVOICE_NUMBER, this.invoiceNumberTemplateExpr, INVOICE_NUMBER_STEP);
    }

    public String generateApproveCode() {
        return getCodeWithCachedBuilder("GV", APPROVE_CODE, this.approveCodeTemplateExpr, APPROVE_CODE_STEP);
    }


    public String generateDeliveryCode() {
        return this.codeGenerator.generateCode("GV", DELIVERY_CODE, this.deliveryCodeTemplateExpr, DELIVERY_CODE_STEP);
    }

    public String generateBatchId() {
        return this.codeGenerator.generateCode("GV", APPROVE_CODE, this.EXPR, APPROVE_CODE_STEP);
    }

    public String generateReferenceNumber() {
        return getCodeWithCachedBuilder("GV", APPROVE_CODE, this.approveCodeTemplateExpr, APPROVE_CODE_STEP);
    }

    public String generateFlowCode() {

        return codeGenerator.generateCode("GV", "FLOW", "FL" + EXPR, 1l);
    }

    /**
     * Generate voucher receive batch code.
     */
    public String generateVoucherReceiveBatchCode() {

        return codeGenerator.generateCode("GV", "VOUCHER_RECEIVE_BATCH", "REB" + EXPR, 1l);
    }


    public String generatePosCode() {

        return codeGenerator.generateCode("GV", POS_CODE_TEMPLATE_NAME, posCodeTemplateExpr, POS_CODE_INCREASE_STEP);
    }

    public String generatePosAccountCode() {

        return codeGenerator.generateCode("GV", POS_ACCOUNT_CODE_TEMPLATE_NAME, posAccountCodeTemplateExpr, POS_ACCOUNT_CODE_INCREASE_STEP);
    }

    public String generateTransactionDataCode() {
        return getCodeWithCachedBuilder("GV", TRANSACTION_DATA_CODE_TEMPLATE_NAME, 
            transactionDataCodeTemplateExpr, TRANSACTION_DATA_CODE_INCREASE_STEP);
    }


    public String generatePosCpgCode() {

        return codeGenerator.generateCode("GV", POS_CPG_CODE_TEMPLATE_NAME, posCpgCodeTemplateExpr, POS_CPG_CODE_INCREASE_STEP);
    }

    /**
     * Generate voucher receive code.
     */
    public String generateVoucherReceiveCode() {

        return codeGenerator.generateCode("GV", "VOUCHER_RECEIVE", "REC[D:yy][SA:%05d]", 1l);
    }

    public String generateCancelVoucherReceiveCode() {

        return codeGenerator.generateCode("GV", "VOUCHER_CANCEL_RECEIVE", "CREC[D:yy][SA:%05d]", 1l);
    }


    public String generateVoucherBatchCode() {

        return codeGenerator.generateCode("GV", "VOUCHER_BATCH", "EGV[D:yyMMddHHmmss][SA:%04d]", 1L);
    }

    public String generateFileNameCode() {

        return codeGenerator.generateCode("GV", "FILE_NAME", "FILE[D:yyMMddHHmm]-[SA:%03d]", 1L);
    }

    /**
     * Generate customer payment method code.
     */
    public String generateCustomerProductCategoryCode() {

        return codeGenerator.generateCode("GV", CUSTOMER_PRODUCT_CATEGORY_CODE_TEMPLATE_NAME, customerProductCategoryCodeTemplateExpr, CUSTOMER_PRODUCT_CATEGORY_CODE_INCREASE_STEP);
    }


    public String generateBusinessLogCode() {

        return codeGenerator.generateCode("GV", BUSINESS_LOG_CODE_TEMPLATE_NAME, businessLogCodeTemplateExpr, BUSINESS_LOG_CODE_INCREASE_STEP);
    }


    public String generateBusinessLogDetailCode() {

        return codeGenerator.generateCode("GV", BUSINESS_LOG_DETAIL_CODE_TEMPLATE_NAME, businessLogDetailCodeTemplateExpr, BUSINESS_LOG_DETAIL_CODE_INCREASE_STEP);
    }


    /**
     * Generate customer payment method code.
     */
    public String generateOutletCpgCode() {

        return codeGenerator.generateCode("GV", OUTLET_CPG_CODE_TEMPLATE_NAME, outletCPGCodeTemplateExpr, OUTLET_CPG_CODE_INCREASE_STEP);
    }


    /**
     * Generate customer payment method code.
     */
    public String generateCustomerPaymentMethodCode() {

        return codeGenerator.generateCode("GV", CUSTOMER_PAYMENT_METHOD_CODE_TEMPLATE_NAME, customerPaymentMethodCodeTemplateExpr, CUSTOMER_PAYMENT_METHOD_CODE_INCREASE_STEP);
    }

    /**
     * Generate issuer code.
     */
    public String generateCustomerCode() {

        return codeGenerator.generateCode("GV", CUSTOMER_CODE_TEMPLATE_NAME, customerCodeTemplateExpr, CUSTOMER_CODE_INCREASE_STEP);
    }

    /**
     * Generate issuer code.
     */
    public String generateIssuerCode() {

        return codeGenerator.generateCode("GV", ISSUER_CODE_TEMPLATE_NAME, issuerCodeTemplateExpr, ISSUER_CODE_INCREASE_STEP);
    }

    /**
     * Generate company code.
     */
    public String generateCompanyCode() {

        return codeGenerator.generateCode("GV", COMPANY_CODE_TEMPLATE_NAME, companyCodeTemplateExpr, COMPANY_CODE_INCREASE_STEP);
    }

    /**
     * Generate merchant code.
     */
    public String generateMerchantCode() {

        return codeGenerator.generateCode("GV", MERCHANT_CODE_TEMPLATE_NAME, merchantCodeTemplateExpr, MERCHANT_CODE_INCREASE_STEP);
    }

    /**
     * Generate outlet code.
     */
    public String generateOutletCode() {

        return codeGenerator.generateCode("GV", OUTLET_CODE_TEMPLATE_NAME, outletCodeTemplateExpr, OUTLET_CODE_INCREASE_STEP);
    }

    /**
     * author Dragon
     * Generate voucherRequest code.
     */
    public String generateVoucherRequestCode() {

        return codeGenerator.generateCode("GV", VOUCHER_REQUEST_CODE_TEMPLATE_NAME, voucherRequestCodeTemplateExpr, VOUCHER_REQUEST_CODE_INCREASE_STEP);
    }

    /**
     * author Dragon
     * Generate voucherRequestDetails code.
     */
    public String generateVoucherRequestDetailsCode() {

        return codeGenerator.generateCode("GV", VOUCHER_REQUEST_DETAILS_CODE_TEMPLATE_NAME, voucherRequestDetailsCodeTemplateExpr, VOUCHER_REQUEST_DETAILS_CODE_INCREASE_STEP);
    }

    /**
     * author Dragon
     * Generate reportManagement code.
     */
    public String generateReportManagementCode() {
        return codeGenerator.generateCode("GV", REPORT_MANAGEMENT_CODE_TEMPLATE_NAME, reportManagementCodeTemplateExpr, REPORT_MANAGEMENT_CODE_INCREASE_STEP);
    }

    /**
     * author Dragon
     *
     * @return releaseApproveAmountCode
     */
    public String generateReleaseApproveAmountCode() {
        return codeGenerator.generateCode("GV", RELEASE_APPROVE_NODE_CODE_TEMPLATE_NAME, releaseApproveNodeCodeTemplateExpr, RELEASE_APPROVE_NODE_CODE_INCREASE_STEP);

    }

    /**
     * author Dragon
     *
     * @return releaseApproveNodeCode
     */
    public String generateReleaseApproveNodeCode() {
        return codeGenerator.generateCode("GV", RELEASE_APPROVE_AMOUNT_CODE_TEMPLATE_NAME, releaseApproveAmountCodeTemplateExpr, RELEASE_APPROVE_AMOUNT_CODE_INCREASE_STEP);

    }

    /**
     * author Dragon
     *
     * @return customerOrderEmailCode
     */
    public String generateCustomerOrderEmailCode() {
        return codeGenerator.generateCode("GV", CUSTOMER_ORDER_EMAIL_CODE_TEMPLATE_NAME, customerOrderEmailCodeTemplateExpr, CUSTOMER_ORDER_EMAIL_CODE_INCREASE_STEP);

    }

    /**
     * author Dragon
     *
     * @return customerOrderCode
     */
    public String generateCustomerOrderCode() {
        return codeGenerator.generateCode("GV", CUSTOMER_ORDER_CODE_TEMPLATE_NAME, customerOrderCodeTemplateExpr, CUSTOMER_ORDER_CODE_INCREASE_STEP);

    }

    /**
     * author Dragon
     *
     * @return customerOrderDetailCode
     */
    public String generateCustomerOrderDetailCode() {
        return codeGenerator.generateCode("GV", CUSTOMER_ORDER_DETAIL_CODE_TEMPLATE_NAME, customerOrderDetailCodeTemplateExpr, CUSTOMER_ORDER_DETAIL_CODE_INCREASE_STEP);

    }

    /**
     * author Dragon
     *
     * @return purchaseOrderNumber
     */
    public String generatePurchaseOrderNumber() {
        return codeGenerator.generateCode("GV", PURCHASE_ORDER_NUMBER_TEMPLATE_NAME, purchaseOrderNumberTemplateExpr, PURCHASE_ORDER_NUMBER_INCREASE_STEP);

    }

    /**
     * author Dragon
     *
     * @return Approve node record code
     */
    public String generateApproveNodeRecordCode() {
        return codeGenerator.generateCode("GV", APPROVE_NODE_RECORD_TEMPLATE_NAME, approveNodeRecordTemplateExpr, APPROVE_NODE_RECORD_INCREASE_STEP);
    }

    /**
     * author Dragon
     *
     * @return Approve node record code
     */
    public String generateSchedulerReportCode() {
        return codeGenerator.generateCode("GV", SCHEDULER_REPORT_TEMPLATE_NAME, schedulerReportTemplateExpr, SCHEDULER_REPORT_INCREASE_STEP);
    }

    /**
     * author Dragon
     *
     * @return Approve node record code
     */
    public String generateOrderReport() {
        return codeGenerator.generateCode("GV", ORDER_REPORT_TEMPLATE_NAME, orderReportTemplateExpr, ORDER_REPORT_INCREASE_STEP);
    }

    /**
     * 生成用于文件追加写入code
     * @return
     */
    public String generateWriteExcelCode() {
        return codeGenerator.generateCode("GV", APPEND_WRITE_EXCEL_CODE_TEMPLATE_NAME, appendWriteExcelCodeTemplateExpr, APPEND_WRITE_EXCEL_INCREASE_STEP);
    }

    /**
     * Generate printer Code
     */
    public String generatePrinterCode() {

        return codeGenerator.generateCode("GV", PRINTER_CODE_TEMPLATE_NAME, printerCodeTemplateExpr, PRINTER_CODE_INCREASE_STEP);
    }

    /**
     * Generate CpgType Code
     */
    public String generateCpgTypeCode() {

        return codeGenerator.generateCode("GV", CPG_TYPE_CODE_TEMPLATE_NAME, cpgTypeCodeTemplateExpr, CPG_TYPE_CODE_INCREASE_STEP);
    }

    /**
     * generateCExchangeRateCode
     *
     * @return
     */
    public String generateExchangeRateCode() {
        return codeGenerator.generateCode("GV", EXCHANGE_RATE_CODE_TEMPLATE_NAME, exchangeRateCodeTemplateExpr, EXCHANGE_RATE_CODE_INCREASE_STEP);
    }

    /**
     * @return
     * <AUTHOR>
     * @date 2022年3月9日
     */
    public String voucherAllocationCode() {
        return codeGenerator.generateCode(APPKEY, ALLOCATION_TEMPLATENAME, EXPR_YYMMDDHHMM_4, INCREASE_STEP_1);
    }
    
    public String issueHandlingCode() {
        return codeGenerator.generateCode(APPKEY, ISSUEHANDLING_TEMPLATENAME, EXPR_YYMMDDHHMM_4, INCREASE_STEP_1);
    }

    public String generateDistributionEmailTemplateCode() {

        return codeGenerator.generateCode(APPKEY, DISTRIBUTION_EMAIL_TEMPLATE_CODE_TEMPLATE_NAME, distributionEmailTemplateCodeTemplateExpr, DISTRIBUTION_EMAIL_TEMPLATE_CODE_INCREASE_STEP);
    }

    public String generateDistributionCode() {

        return this.codeGenerator.generateCode(APPKEY, DISTRIBUTION_CODE_TEMPLATE_NAME, this.distributionCodeTemplateExpr, DISTRIBUTION_CODE_INCREASE_STEP);
    }

    public String generateDistributionItemCode() {

        return this.codeGenerator.generateCode(APPKEY, DISTRIBUTION_ITEM_CODE_TEMPLATE_NAME, this.distributionItemCodeTemplateExpr, DISTRIBUTION_ITEM_CODE_INCREASE_STEP);
    }

    public String generateDashboardCode() {

        return this.codeGenerator.generateCode(APPKEY, DISTRIBUTION_CODE_TEMPLATE_NAME, this.distributionCodeTemplateExpr, DISTRIBUTION_CODE_INCREASE_STEP);
    }

    public String generateDashboardDetailCode() {

        return this.codeGenerator.generateCode(APPKEY, DISTRIBUTION_CODE_TEMPLATE_NAME, this.distributionCodeTemplateExpr, DISTRIBUTION_CODE_INCREASE_STEP);
    }


    public String generateTransactionCode() {
        return codeGenerator.generateCode("GV", TRANSACTION_CODE_TEMPLATE_NAME, transactionCodeTemplateExpr, TRANSACTION_CODE_INCREASE_STEP);

    }

}
