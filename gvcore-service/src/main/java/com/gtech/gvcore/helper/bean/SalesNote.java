package com.gtech.gvcore.helper.bean;


import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.utils.AmountUtils;
import com.gtech.gvcore.common.utils.CollectorsUtils;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import com.gtech.gvcore.helper.CustomerOrderPdfHelper;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

@Getter
@Setter
@Accessors(chain = true)
public class SalesNote {

    public static final String SOURCE_TEMPLATE_PATH = "templates/html/SalesNote.html";
    public static final String INCREASE_KEY = "CUSTOMER_ORDER:PDF:SALES_ORDER";
    private static final String FILE_NAME_TEMPLATE = "%sSalesNote.html";

    public String getFileName() {

        return String.format(FILE_NAME_TEMPLATE, CustomerOrderPdfHelper.getBaseFileName());
    }

    private String invoiceNo;

    private String nowDate = DateUtil.format(new Date(), "dd/MM/yyyy");

    private String nowTime = DateUtil.format(new Date(), "HH:mm:ss");

    private String customerName;

    private String operateUser;

    private List<SalesNoteItemHtmlDataDto> detailList;

    private String voucherAmount;

    private String discount;

    private String amount;

    private String sumQty;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class SalesNoteItemHtmlDataDto {

        private String articleMopCode;

        private String voucherNum;

        private String denomination;

        private String voucherAmount;

    }

    public SalesNote setSumQty(List<CustomerOrderDetails> customerOrderDetails) {

        this.sumQty = AmountUtils.idrFormat(customerOrderDetails.stream().mapToInt(CustomerOrderDetails::getVoucherNum).sum());
        return this;
    }

    public SalesNote setVoucherAmount(List<CustomerOrderDetails> customerOrderDetails) {

        this.voucherAmount = AmountUtils.idrFormat(generateSalesNoteSumAmount(customerOrderDetails, CustomerOrderDetails::getVoucherAmount));
        return this;
    }

    public SalesNote setDiscount(BigDecimal discount) {

        this.discount = "-" + AmountUtils.idrFormat(discount);
        return this;
    }

    public SalesNote setAmount(BigDecimal discount, BigDecimal voucherAmount) {

        this.amount = AmountUtils.idrFormat(voucherAmount.subtract(discount));
        return this;
    }

    public BigDecimal generateSalesNoteSumAmount(List<CustomerOrderDetails> customerOrderDetails, Function<CustomerOrderDetails, BigDecimal> field) {

        BigDecimal value = customerOrderDetails.stream()
                .map(field)
                .filter(Objects::nonNull)
                .collect(CollectorsUtils.summingBigDecimal(e -> e));

        return ConvertUtils.toBigDecimal(value, BigDecimal.ZERO);
    }

    public BigDecimal generateSalesNotConvertAmount(List<CustomerOrderDetails> customerOrderDetails, Function<CustomerOrderDetails, BigDecimal> field) {

        BigDecimal value = generateSalesNoteSumAmount(customerOrderDetails, field);

        return BigDecimal.ZERO.compareTo(value) >= 0 ? BigDecimal.ZERO : value;
    }

    private String beneficiaryName;

    private String branchName;

    private String bankName;

    private String accountNumber;
}