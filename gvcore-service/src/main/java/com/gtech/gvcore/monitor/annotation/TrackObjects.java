package com.gtech.gvcore.monitor.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 对象创建追踪注解
 * 在方法或类上使用此注解，自动监控对象创建
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface TrackObjects {
    
    /**
     * 是否启用监控
     */
    boolean enabled() default true;
    
    /**
     * 监控描述
     */
    String description() default "";
    
    /**
     * 是否包含子线程
     */
    boolean includeSubThreads() default false;
    
    /**
     * 要监控的对象类型（为空则监控所有支持的类型）
     */
    String[] objectTypes() default {};
    
    /**
     * 监控阈值，只有创建对象数量超过此值才记录日志
     */
    int threshold() default 0;
}
