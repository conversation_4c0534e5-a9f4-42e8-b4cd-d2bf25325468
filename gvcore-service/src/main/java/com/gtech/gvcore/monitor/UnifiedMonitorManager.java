package com.gtech.gvcore.monitor;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 统一监控管理器
 * 整合所有监控功能，提供统一的监控接口
 */
@Slf4j
@Component
public class UnifiedMonitorManager {

    // 请求级别的对象计数器
    private static final ThreadLocal<Map<String, AtomicInteger>> REQUEST_OBJECT_COUNTERS = 
            ThreadLocal.withInitial(ConcurrentHashMap::new);
    
    // 请求开始时间
    private static final ThreadLocal<Long> REQUEST_START_TIME = new ThreadLocal<>();
    
    // 请求ID
    private static final ThreadLocal<String> REQUEST_ID = new ThreadLocal<>();
    
    // 全局统计
    private static final AtomicLong TOTAL_REQUESTS = new AtomicLong(0);
    private static final AtomicLong TOTAL_OBJECTS_CREATED = new AtomicLong(0);

    /**
     * 开始监控请求
     */
    public void startRequestMonitoring(String requestId) {
        REQUEST_ID.set(requestId);
        REQUEST_START_TIME.set(System.currentTimeMillis());
        REQUEST_OBJECT_COUNTERS.get().clear();
        TOTAL_REQUESTS.incrementAndGet();
        
        log.debug("开始监控请求: {}", requestId);
    }

    /**
     * 记录对象创建
     */
    public void recordObjectCreation(String objectType, int count) {
        String requestId = REQUEST_ID.get();
        if (requestId == null) {
            return; // 没有开始监控，忽略
        }

        Map<String, AtomicInteger> counters = REQUEST_OBJECT_COUNTERS.get();
        counters.computeIfAbsent(objectType, k -> new AtomicInteger(0)).addAndGet(count);
        TOTAL_OBJECTS_CREATED.addAndGet(count);
        
        log.debug("请求 {} 创建对象: {} +{}", requestId, objectType, count);
    }

    /**
     * 记录对象创建（单个对象）
     */
    public void recordObjectCreation(Object obj) {
        if (obj != null) {
            recordObjectCreation(obj.getClass().getSimpleName(), 1);
        }
    }

    /**
     * 结束请求监控并返回统计信息
     */
    public JSONObject endRequestMonitoring() {
        String requestId = REQUEST_ID.get();
        Long startTime = REQUEST_START_TIME.get();
        
        if (requestId == null || startTime == null) {
            return new JSONObject();
        }

        try {
            long duration = System.currentTimeMillis() - startTime;
            Map<String, AtomicInteger> counters = REQUEST_OBJECT_COUNTERS.get();
            
            JSONObject result = new JSONObject();
            result.put("requestId", requestId);
            result.put("duration", duration);
            result.put("timestamp", System.currentTimeMillis());
            
            // 对象创建统计
            JSONObject objectCounts = new JSONObject();
            int totalObjects = 0;
            
            for (Map.Entry<String, AtomicInteger> entry : counters.entrySet()) {
                int count = entry.getValue().get();
                objectCounts.put(entry.getKey(), count);
                totalObjects += count;
            }
            
            result.put("objectCounts", objectCounts);
            result.put("totalObjects", totalObjects);
            
            // 内存信息
            result.put("memoryInfo", getCurrentMemoryInfo());
            
            if (totalObjects > 0) {
                log.info("请求监控结果 - ID: {}, 耗时: {}ms, 创建对象: {}个, 详情: {}", 
                        requestId, duration, totalObjects, objectCounts.toJSONString());
            }
            
            return result;
        } finally {
            // 清理ThreadLocal
            REQUEST_ID.remove();
            REQUEST_START_TIME.remove();
            REQUEST_OBJECT_COUNTERS.remove();
        }
    }

    /**
     * 获取当前内存信息
     */
    public JSONObject getCurrentMemoryInfo() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();

        JSONObject memoryInfo = new JSONObject();
        
        // 堆内存
        JSONObject heapInfo = new JSONObject();
        heapInfo.put("used", heapUsage.getUsed() / 1024 / 1024); // MB
        heapInfo.put("max", heapUsage.getMax() / 1024 / 1024); // MB
        heapInfo.put("usagePercent", String.format("%.2f", (double) heapUsage.getUsed() / heapUsage.getMax() * 100));
        memoryInfo.put("heap", heapInfo);
        
        // 非堆内存
        JSONObject nonHeapInfo = new JSONObject();
        nonHeapInfo.put("used", nonHeapUsage.getUsed() / 1024 / 1024); // MB
        nonHeapInfo.put("max", nonHeapUsage.getMax() == -1 ? -1 : nonHeapUsage.getMax() / 1024 / 1024); // MB
        memoryInfo.put("nonHeap", nonHeapInfo);
        
        return memoryInfo;
    }

    /**
     * 获取全局统计信息
     */
    public JSONObject getGlobalStats() {
        JSONObject stats = new JSONObject();
        stats.put("totalRequests", TOTAL_REQUESTS.get());
        stats.put("totalObjectsCreated", TOTAL_OBJECTS_CREATED.get());
        stats.put("currentMemory", getCurrentMemoryInfo());
        stats.put("timestamp", System.currentTimeMillis());
        return stats;
    }

    /**
     * 检查是否正在监控
     */
    public boolean isMonitoring() {
        return REQUEST_ID.get() != null;
    }

    /**
     * 获取当前请求ID
     */
    public String getCurrentRequestId() {
        return REQUEST_ID.get();
    }

    /**
     * 重置全局统计
     */
    public void resetGlobalStats() {
        TOTAL_REQUESTS.set(0);
        TOTAL_OBJECTS_CREATED.set(0);
        log.info("全局监控统计已重置");
    }
}
