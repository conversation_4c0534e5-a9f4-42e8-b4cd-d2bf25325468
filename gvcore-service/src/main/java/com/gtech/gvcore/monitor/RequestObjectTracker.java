package com.gtech.gvcore.monitor;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 请求级别的对象追踪器
 * 用于追踪单个API请求创建的对象数量
 */
@Slf4j
public class RequestObjectTracker {

    // 当前请求的对象计数器
    private static final ThreadLocal<Map<String, AtomicInteger>> REQUEST_OBJECT_COUNTERS = 
            ThreadLocal.withInitial(ConcurrentHashMap::new);
    
    // 当前请求的标识
    private static final ThreadLocal<String> REQUEST_ID = new ThreadLocal<>();

    /**
     * 开始追踪请求
     * @param requestId 请求标识（如API路径 + 时间戳）
     */
    public static void startTracking(String requestId) {
        REQUEST_ID.set(requestId);
        REQUEST_OBJECT_COUNTERS.get().clear();
        log.debug("开始追踪请求: {}", requestId);
    }

    /**
     * 记录对象创建
     * @param objectType 对象类型
     * @param count 创建数量（默认为1）
     */
    public static void trackObjectCreation(String objectType, int count) {
        String requestId = REQUEST_ID.get();
        if (requestId == null) {
            return; // 没有开始追踪，忽略
        }

        Map<String, AtomicInteger> counters = REQUEST_OBJECT_COUNTERS.get();
        counters.computeIfAbsent(objectType, k -> new AtomicInteger(0)).addAndGet(count);
        
        log.debug("请求 {} 创建对象: {} +{}", requestId, objectType, count);
    }

    /**
     * 记录对象创建（默认数量为1）
     * @param objectType 对象类型
     */
    public static void trackObjectCreation(String objectType) {
        trackObjectCreation(objectType, 1);
    }

    /**
     * 记录对象创建（通过对象实例）
     * @param obj 创建的对象实例
     */
    public static void trackObjectCreation(Object obj) {
        if (obj != null) {
            trackObjectCreation(obj.getClass().getSimpleName());
        }
    }

    /**
     * 结束追踪并返回统计结果
     * @return JSON格式的对象创建统计
     */
    public static String endTracking() {
        String requestId = REQUEST_ID.get();
        if (requestId == null) {
            return "{}";
        }

        try {
            Map<String, AtomicInteger> counters = REQUEST_OBJECT_COUNTERS.get();
            JSONObject result = new JSONObject();
            result.put("requestId", requestId);
            result.put("timestamp", System.currentTimeMillis());
            
            JSONObject objectCounts = new JSONObject();
            int totalObjects = 0;
            
            for (Map.Entry<String, AtomicInteger> entry : counters.entrySet()) {
                int count = entry.getValue().get();
                objectCounts.put(entry.getKey(), count);
                totalObjects += count;
            }
            
            result.put("objectCounts", objectCounts);
            result.put("totalObjects", totalObjects);
            
            log.info("请求 {} 对象创建统计: 总计{}个对象, 详情: {}", requestId, totalObjects, objectCounts.toJSONString());
            
            return result.toJSONString();
        } finally {
            // 清理ThreadLocal
            REQUEST_ID.remove();
            REQUEST_OBJECT_COUNTERS.remove();
        }
    }

    /**
     * 获取当前请求的对象统计（不结束追踪）
     * @return JSON格式的当前对象统计
     */
    public static String getCurrentStats() {
        String requestId = REQUEST_ID.get();
        if (requestId == null) {
            return "{}";
        }

        Map<String, AtomicInteger> counters = REQUEST_OBJECT_COUNTERS.get();
        JSONObject result = new JSONObject();
        result.put("requestId", requestId);
        result.put("timestamp", System.currentTimeMillis());
        
        JSONObject objectCounts = new JSONObject();
        int totalObjects = 0;
        
        for (Map.Entry<String, AtomicInteger> entry : counters.entrySet()) {
            int count = entry.getValue().get();
            objectCounts.put(entry.getKey(), count);
            totalObjects += count;
        }
        
        result.put("objectCounts", objectCounts);
        result.put("totalObjects", totalObjects);
        
        return result.toJSONString();
    }

    /**
     * 检查是否正在追踪
     * @return 是否正在追踪
     */
    public static boolean isTracking() {
        return REQUEST_ID.get() != null;
    }

    /**
     * 获取当前请求ID
     * @return 当前请求ID
     */
    public static String getCurrentRequestId() {
        return REQUEST_ID.get();
    }

    /**
     * 批量记录对象创建
     * @param objectCounts 对象类型和数量的映射
     */
    public static void trackObjectCreations(Map<String, Integer> objectCounts) {
        if (objectCounts == null || objectCounts.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Integer> entry : objectCounts.entrySet()) {
            trackObjectCreation(entry.getKey(), entry.getValue());
        }
    }
}
