package com.gtech.gvcore.monitor;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 增强的对象追踪器
 * 支持子线程追踪和自动对象检测
 */
@Slf4j
public class EnhancedObjectTracker {

    // 追踪会话存储
    private static final Map<String, TrackingSession> TRACKING_SESSIONS = new ConcurrentHashMap<>();
    
    // 当前线程的追踪会话ID
    private static final ThreadLocal<String> CURRENT_SESSION_ID = new ThreadLocal<>();
    
    // 全局会话ID生成器
    private static final AtomicLong SESSION_ID_GENERATOR = new AtomicLong(0);

    /**
     * 追踪会话
     */
    public static class TrackingSession {
        private final String sessionId;
        private final String description;
        private final long startTime;
        private final Set<String> objectTypes;
        private final boolean includeChildThreads;
        private final Map<String, AtomicInteger> objectCounts = new ConcurrentHashMap<>();
        private final Set<Long> trackedThreads = ConcurrentHashMap.newKeySet();
        
        public TrackingSession(String sessionId, String description, String[] objectTypes, boolean includeChildThreads) {
            this.sessionId = sessionId;
            this.description = description;
            this.startTime = System.currentTimeMillis();
            this.includeChildThreads = includeChildThreads;
            this.objectTypes = (objectTypes != null && objectTypes.length > 0) ?
                new HashSet<>(Arrays.asList(objectTypes)) : null;
            this.trackedThreads.add(Thread.currentThread().getId());
        }
        
        public void trackObject(String objectType, int count) {
            // 检查是否需要过滤对象类型
            if (objectTypes != null && !objectTypes.contains(objectType)) {
                return;
            }
            
            objectCounts.computeIfAbsent(objectType, k -> new AtomicInteger(0)).addAndGet(count);
        }
        
        public boolean shouldTrackCurrentThread() {
            long currentThreadId = Thread.currentThread().getId();
            if (trackedThreads.contains(currentThreadId)) {
                return true;
            }
            
            if (includeChildThreads) {
                // 将当前线程添加到追踪列表
                trackedThreads.add(currentThreadId);
                return true;
            }
            
            return false;
        }
        
        public JSONObject getStatistics() {
            JSONObject result = new JSONObject();
            result.put("sessionId", sessionId);
            result.put("description", description);
            result.put("startTime", startTime);
            result.put("duration", System.currentTimeMillis() - startTime);
            result.put("trackedThreads", trackedThreads.size());
            
            JSONObject objectCounts = new JSONObject();
            int totalObjects = 0;
            
            for (Map.Entry<String, AtomicInteger> entry : this.objectCounts.entrySet()) {
                int count = entry.getValue().get();
                objectCounts.put(entry.getKey(), count);
                totalObjects += count;
            }
            
            result.put("objectCounts", objectCounts);
            result.put("totalObjects", totalObjects);
            
            return result;
        }
    }

    /**
     * 开始追踪会话
     */
    public static String startTracking(String description, String[] objectTypes, boolean includeChildThreads) {
        String sessionId = "session_" + SESSION_ID_GENERATOR.incrementAndGet() + "_" + System.currentTimeMillis();
        
        TrackingSession session = new TrackingSession(sessionId, description, objectTypes, includeChildThreads);
        TRACKING_SESSIONS.put(sessionId, session);
        CURRENT_SESSION_ID.set(sessionId);
        
        log.debug("开始对象追踪会话: {} - {}", sessionId, description);
        return sessionId;
    }

    /**
     * 结束追踪会话
     */
    public static JSONObject endTracking() {
        String sessionId = CURRENT_SESSION_ID.get();
        if (sessionId == null) {
            return new JSONObject();
        }

        try {
            TrackingSession session = TRACKING_SESSIONS.remove(sessionId);
            if (session != null) {
                JSONObject stats = session.getStatistics();
                log.info("对象追踪会话结束: {} - 总计{}个对象", sessionId, stats.getInteger("totalObjects"));
                return stats;
            }
        } finally {
            CURRENT_SESSION_ID.remove();
        }
        
        return new JSONObject();
    }

    /**
     * 追踪对象创建（自动检测调用）
     */
    public static void trackObjectCreation(String objectType, int count) {
        String sessionId = CURRENT_SESSION_ID.get();
        if (sessionId == null) {
            return;
        }

        TrackingSession session = TRACKING_SESSIONS.get(sessionId);
        if (session != null && session.shouldTrackCurrentThread()) {
            session.trackObject(objectType, count);
        }
    }

    /**
     * 追踪对象创建（单个对象）
     */
    public static void trackObjectCreation(Object obj) {
        if (obj != null) {
            trackObjectCreation(obj.getClass().getSimpleName(), 1);
        }
    }

    /**
     * 获取当前会话统计
     */
    public static JSONObject getCurrentStats() {
        String sessionId = CURRENT_SESSION_ID.get();
        if (sessionId == null) {
            return new JSONObject();
        }

        TrackingSession session = TRACKING_SESSIONS.get(sessionId);
        return session != null ? session.getStatistics() : new JSONObject();
    }

    /**
     * 检查是否正在追踪
     */
    public static boolean isTracking() {
        return CURRENT_SESSION_ID.get() != null;
    }

    /**
     * 传播追踪上下文到子线程
     */
    public static void propagateToChildThread() {
        String sessionId = CURRENT_SESSION_ID.get();
        if (sessionId != null) {
            CURRENT_SESSION_ID.set(sessionId);
        }
    }

    /**
     * 清理过期的追踪会话
     */
    public static void cleanupExpiredSessions() {
        long currentTime = System.currentTimeMillis();
        TRACKING_SESSIONS.entrySet().removeIf(entry -> {
            TrackingSession session = entry.getValue();
            // 清理超过1小时的会话
            return (currentTime - session.startTime) > 3600000;
        });
    }
}
