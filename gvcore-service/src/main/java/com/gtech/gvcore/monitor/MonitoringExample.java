package com.gtech.gvcore.monitor;

import com.gtech.gvcore.monitor.annotation.TrackObjects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基于注解的监控系统使用示例
 * 展示如何使用@TrackObjects注解自动监控对象创建，对代码无侵入
 */
@Slf4j
@Component
public class MonitoringExample {

    @Autowired
    private UnifiedMonitorManager monitorManager;

    /**
     * 示例1：使用方法级别的@TrackObjects注解
     * 自动监控此方法中创建的对象，完全无侵入
     */
    @TrackObjects(description = "业务方法示例", threshold = 5)
    public void businessMethodExample() {
        // 正常编写业务代码，无需任何监控相关的代码
        List<String> list1 = new ArrayList<>();
        Map<String, Object> map1 = new HashMap<>();

        // 创建更多对象
        for (int i = 0; i < 10; i++) {
            List<String> tempList = new ArrayList<>();
            tempList.add("item" + i);
            map1.put("key" + i, tempList);
        }

        StringBuilder sb = new StringBuilder();
        sb.append("处理完成");

        log.info("业务方法执行完成，对象创建会被自动追踪");
    }

    /**
     * 示例2：批量处理方法，只监控特定类型的对象
     */
    @TrackObjects(
        description = "批量处理示例",
        objectTypes = {"ArrayList", "HashMap"},
        threshold = 100
    )
    public void batchProcessExample() {
        // 正常编写批量处理代码
        List<Map<String, Object>> results = new ArrayList<>();

        for (int i = 0; i < 1000; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", i);
            item.put("name", "item" + i);
            results.add(item);

            // 每100个创建一个临时列表
            if (i % 100 == 0) {
                List<String> tempList = new ArrayList<>();
                tempList.add("batch" + (i / 100));
            }
        }

        log.info("批量处理完成，处理了{}个项目", results.size());
    }

    /**
     * 示例3：支持子线程的监控
     */
    @TrackObjects(
        description = "多线程处理示例",
        includeSubThreads = true,
        threshold = 50
    )
    public void multiThreadExample() {
        List<String> mainThreadList = new ArrayList<>();

        // 主线程创建对象
        for (int i = 0; i < 20; i++) {
            mainThreadList.add("main-" + i);
        }

        // 子线程也会被监控
        Thread subThread = new Thread(() -> {
            List<String> subThreadList = new ArrayList<>();
            Map<String, Object> subThreadMap = new HashMap<>();

            for (int i = 0; i < 30; i++) {
                subThreadList.add("sub-" + i);
                subThreadMap.put("key" + i, "value" + i);
            }

            log.info("子线程处理完成");
        });

        subThread.start();
        try {
            subThread.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("多线程处理完成");
    }

    /**
     * 示例4：禁用监控的方法
     */
    @TrackObjects(enabled = false)
    public void disabledTrackingExample() {
        // 即使有注解，但enabled=false，所以不会监控
        List<String> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        for (int i = 0; i < 100; i++) {
            list.add("item" + i);
            map.put("key" + i, "value" + i);
        }

        log.info("此方法不会被监控");
    }

    /**
     * 示例5：普通方法（无注解）
     */
    public void normalMethodExample() {
        // 没有@TrackObjects注解，不会被监控
        List<String> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        log.info("普通方法，不会被监控");
    }

    /**
     * 获取监控统计信息
     */
    public void getMonitoringStatsExample() {
        // 获取全局统计信息
        com.alibaba.fastjson.JSONObject globalStats = monitorManager.getGlobalStats();
        log.info("全局监控统计: {}", globalStats.toJSONString());

        // 获取当前内存信息
        com.alibaba.fastjson.JSONObject memoryInfo = monitorManager.getCurrentMemoryInfo();
        log.info("当前内存信息: {}", memoryInfo.toJSONString());
    }
}

