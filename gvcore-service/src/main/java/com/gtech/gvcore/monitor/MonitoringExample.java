package com.gtech.gvcore.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 监控系统使用示例
 * 展示如何在代码中使用新的统一监控系统
 */
@Slf4j
@Component
public class MonitoringExample {

    @Autowired
    private UnifiedMonitorManager monitorManager;

    /**
     * 示例：在业务方法中手动追踪对象创建
     */
    public void businessMethodExample() {
        // 在需要追踪的业务方法中，手动记录对象创建
        
        // 方式1：使用工厂方法创建并自动追踪
        List<String> list1 = ObjectCreationDetector.Factory.createArrayList();
        Map<String, Object> map1 = ObjectCreationDetector.Factory.createHashMap();
        
        // 方式2：使用Manual工具类手动追踪
        List<String> list2 = ObjectCreationDetector.Manual.trackArrayList(100);
        Map<String, Object> map2 = ObjectCreationDetector.Manual.trackHashMap(50);
        
        // 方式3：手动追踪已创建的对象
        StringBuilder sb = new StringBuilder();
        ObjectCreationDetector.Manual.trackObject(sb);
        
        // 方式4：批量追踪多个对象
        String str1 = new String("test1");
        String str2 = new String("test2");
        ObjectCreationDetector.Manual.trackObjects(str1, str2);
        
        // 方式5：按类型和数量追踪
        ObjectCreationDetector.Manual.trackObject("CustomObject", 5);
        
        log.info("业务方法执行完成，创建了多个对象");
    }

    /**
     * 示例：在批量处理中追踪对象创建
     */
    public void batchProcessExample() {
        // 批量处理时，可以追踪大量对象的创建
        
        for (int i = 0; i < 1000; i++) {
            // 创建对象并追踪
            List<String> batchList = ObjectCreationDetector.Manual.trackArrayList();
            batchList.add("item" + i);
            
            // 每100个记录一次批量统计
            if (i % 100 == 0) {
                ObjectCreationDetector.Manual.trackObject("BatchItem", 100);
            }
        }
        
        log.info("批量处理完成");
    }

    /**
     * 示例：检查是否正在监控
     */
    public void conditionalTrackingExample() {
        // 只在监控开启时才进行追踪，避免性能影响
        if (ObjectCreationDetector.isMonitoring()) {
            List<String> list = ObjectCreationDetector.Manual.trackArrayList();
            log.debug("正在监控，已追踪ArrayList创建");
        } else {
            List<String> list = new java.util.ArrayList<>();
            log.debug("未开启监控，直接创建ArrayList");
        }
    }

    /**
     * 示例：获取监控统计信息
     */
    public void getMonitoringStatsExample() {
        // 获取全局统计信息
        com.alibaba.fastjson.JSONObject globalStats = monitorManager.getGlobalStats();
        log.info("全局监控统计: {}", globalStats.toJSONString());
        
        // 获取当前内存信息
        com.alibaba.fastjson.JSONObject memoryInfo = monitorManager.getCurrentMemoryInfo();
        log.info("当前内存信息: {}", memoryInfo.toJSONString());
        
        // 检查当前是否正在监控某个请求
        if (monitorManager.isMonitoring()) {
            String currentRequestId = monitorManager.getCurrentRequestId();
            log.info("当前正在监控请求: {}", currentRequestId);
        }
    }

    /**
     * 示例：重置监控统计
     */
    public void resetStatsExample() {
        // 在需要时重置全局统计（比如定期清理）
        monitorManager.resetGlobalStats();
        log.info("监控统计已重置");
    }
}
