package com.gtech.gvcore.monitor;

import com.alibaba.fastjson.JSONObject;
import com.gtech.gvcore.monitor.annotation.TrackObjects;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

/**
 * 对象追踪切面
 * 基于@TrackObjects注解自动监控对象创建，对代码无侵入
 */
@Slf4j
@Aspect
//@Component  // 临时禁用，因为LoggerAspect已经集成了监控功能
@Order(100) // 确保在LoggerAspect之后执行
public class ObjectTrackingAspect {

    @Autowired
    private UnifiedMonitorManager monitorManager;

    /**
     * 拦截带有@TrackObjects注解的方法
     */
    @Around("@annotation(com.gtech.gvcore.monitor.annotation.TrackObjects)")
    public Object trackMethodObjects(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        TrackObjects annotation = method.getAnnotation(TrackObjects.class);
        
        return executeWithTracking(joinPoint, annotation, getMethodDescription(method));
    }

    /**
     * 拦截类级别的@TrackObjects注解
     */
    @Around("@within(com.gtech.gvcore.monitor.annotation.TrackObjects) && !@annotation(com.gtech.gvcore.monitor.annotation.TrackObjects)")
    public Object trackClassObjects(ProceedingJoinPoint joinPoint) throws Throwable {
        Class<?> targetClass = joinPoint.getTarget().getClass();
        TrackObjects annotation = targetClass.getAnnotation(TrackObjects.class);
        
        return executeWithTracking(joinPoint, annotation, getClassMethodDescription(targetClass, joinPoint));
    }

    /**
     * 执行带追踪的方法
     */
    private Object executeWithTracking(ProceedingJoinPoint joinPoint, TrackObjects annotation, String description) throws Throwable {
        if (!annotation.enabled()) {
            return joinPoint.proceed();
        }

        // 检查是否已经在监控中（避免重复监控）
        boolean alreadyMonitoring = monitorManager.isMonitoring();
        String trackingId = null;

        if (!alreadyMonitoring) {
            // 开始新的监控会话
            trackingId = description + "@" + System.currentTimeMillis();
            monitorManager.startRequestMonitoring(trackingId);
            log.debug("开始对象追踪: {}", description);
        }

        // 记录方法执行前的对象创建基线
        long beforeObjectCount = getGlobalObjectCreationCount();

        try {
            Object result;

            if (annotation.includeSubThreads()) {
                // 支持子线程监控
                result = executeWithSubThreadTracking(joinPoint, annotation);
            } else {
                // 普通执行
                result = joinPoint.proceed();
            }

            // 方法执行后，估算创建的对象数量
            estimateObjectCreation(result, annotation);

            return result;

        } finally {
            if (!alreadyMonitoring && trackingId != null) {
                // 记录方法执行后的对象创建情况
                long afterObjectCount = getGlobalObjectCreationCount();
                long estimatedObjects = afterObjectCount - beforeObjectCount;

                if (estimatedObjects > 0) {
                    monitorManager.recordObjectCreation("EstimatedObjects", (int) estimatedObjects);
                }

                // 结束监控并记录结果
                JSONObject trackingResult = monitorManager.endRequestMonitoring();
                logTrackingResult(annotation, description, trackingResult);
            }
        }
    }

    /**
     * 支持子线程的执行
     */
    private Object executeWithSubThreadTracking(ProceedingJoinPoint joinPoint, TrackObjects annotation) throws Throwable {
        // 获取当前监控状态
        String currentRequestId = monitorManager.getCurrentRequestId();
        
        // 在子线程中传播监控上下文
        CompletableFuture<Void> propagationFuture = CompletableFuture.runAsync(() -> {
            if (currentRequestId != null) {
                // 在子线程中继续使用相同的监控会话
                // 这里可以扩展支持子线程的监控逻辑
                log.debug("子线程监控上下文传播: {}", currentRequestId);
            }
        });

        try {
            Object result = joinPoint.proceed();
            
            // 等待子线程完成（可选，根据需要调整）
            if (!propagationFuture.isDone()) {
                propagationFuture.get(100, java.util.concurrent.TimeUnit.MILLISECONDS);
            }
            
            return result;
        } catch (Exception e) {
            propagationFuture.cancel(true);
            throw e;
        }
    }

    /**
     * 记录追踪结果
     */
    private void logTrackingResult(TrackObjects annotation, String description, JSONObject result) {
        try {
            int totalObjects = result.getIntValue("totalObjects");
            
            // 检查阈值
            if (totalObjects < annotation.threshold()) {
                log.debug("对象创建数量({})未达到阈值({}), 跳过记录: {}", totalObjects, annotation.threshold(), description);
                return;
            }

            // 过滤指定的对象类型
            String[] targetTypes = annotation.objectTypes();
            if (targetTypes.length > 0) {
                JSONObject objectCounts = result.getJSONObject("objectCounts");
                if (objectCounts != null) {
                    boolean hasTargetType = Arrays.stream(targetTypes)
                            .anyMatch(type -> objectCounts.containsKey(type) && objectCounts.getIntValue(type) > 0);
                    
                    if (!hasTargetType) {
                        log.debug("未检测到指定类型的对象创建: {} in {}", Arrays.toString(targetTypes), description);
                        return;
                    }
                }
            }

            // 记录追踪结果
            long duration = result.getLongValue("duration");
            JSONObject objectCounts = result.getJSONObject("objectCounts");
            
            log.info("🔍 对象追踪结果 - {}: 耗时{}ms, 创建对象{}个, 详情: {}", 
                    description, duration, totalObjects, 
                    objectCounts != null ? objectCounts.toJSONString() : "{}");
                    
        } catch (Exception e) {
            log.warn("记录对象追踪结果失败: {}", e.getMessage());
        }
    }

    /**
     * 获取方法描述
     */
    private String getMethodDescription(Method method) {
        return method.getDeclaringClass().getSimpleName() + "." + method.getName();
    }

    /**
     * 获取类方法描述
     */
    private String getClassMethodDescription(Class<?> clazz, ProceedingJoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        return clazz.getSimpleName() + "." + methodName;
    }

    /**
     * 获取全局对象创建计数（简单估算）
     */
    private long getGlobalObjectCreationCount() {
        // 这里可以通过JVM的方式获取对象创建数量，但比较复杂
        // 暂时返回当前时间作为基线
        return System.nanoTime();
    }

    /**
     * 估算对象创建数量
     * 通过分析返回结果来估算创建的对象数量
     */
    private void estimateObjectCreation(Object result, TrackObjects annotation) {
        if (result == null) {
            return;
        }

        try {
            // 分析返回结果，估算对象创建数量
            int estimatedCount = 0;

            if (result instanceof java.util.Collection) {
                java.util.Collection<?> collection = (java.util.Collection<?>) result;
                estimatedCount += collection.size();
                monitorManager.recordObjectCreation("Collection", 1);
                monitorManager.recordObjectCreation("CollectionItems", collection.size());
            } else if (result instanceof java.util.Map) {
                java.util.Map<?, ?> map = (java.util.Map<?, ?>) result;
                estimatedCount += map.size();
                monitorManager.recordObjectCreation("Map", 1);
                monitorManager.recordObjectCreation("MapEntries", map.size());

                // 分析Map的内容
                analyzeMapContent(map);
            } else if (result instanceof String) {
                monitorManager.recordObjectCreation("String", 1);
            }

            // 基于方法名称进行启发式估算
            String methodName = getMethodName(result);
            if (methodName.contains("create") || methodName.contains("build") || methodName.contains("generate")) {
                estimatedCount += 5; // 创建类方法通常会创建多个对象
                monitorManager.recordObjectCreation("CreationMethod", 5);
            }

            log.debug("估算对象创建数量: {}", estimatedCount);

        } catch (Exception e) {
            log.debug("估算对象创建失败: {}", e.getMessage());
        }
    }

    /**
     * 分析Map内容，估算对象创建
     */
    private void analyzeMapContent(java.util.Map<?, ?> map) {
        int listCount = 0;
        int mapCount = 0;
        int stringCount = 0;

        for (Object value : map.values()) {
            if (value instanceof java.util.List) {
                listCount++;
                java.util.List<?> list = (java.util.List<?>) value;
                monitorManager.recordObjectCreation("NestedList", 1);
                monitorManager.recordObjectCreation("NestedListItems", list.size());
            } else if (value instanceof java.util.Map) {
                mapCount++;
                monitorManager.recordObjectCreation("NestedMap", 1);
            } else if (value instanceof String) {
                stringCount++;
            }
        }

        if (listCount > 0) {
            monitorManager.recordObjectCreation("ArrayList", listCount);
        }
        if (mapCount > 0) {
            monitorManager.recordObjectCreation("HashMap", mapCount);
        }
        if (stringCount > 0) {
            monitorManager.recordObjectCreation("String", stringCount);
        }
    }

    /**
     * 获取方法名称
     */
    private String getMethodName(Object result) {
        if (result != null) {
            return result.getClass().getSimpleName();
        }
        return "";
    }
}
