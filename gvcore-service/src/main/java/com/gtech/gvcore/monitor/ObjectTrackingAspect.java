package com.gtech.gvcore.monitor;

import com.alibaba.fastjson.JSONObject;
import com.gtech.gvcore.monitor.annotation.TrackObjects;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

/**
 * 对象追踪切面
 * 基于@TrackObjects注解自动监控对象创建，对代码无侵入
 */
@Slf4j
@Aspect
@Component
@Order(100) // 确保在LoggerAspect之后执行
public class ObjectTrackingAspect {

    @Autowired
    private UnifiedMonitorManager monitorManager;

    /**
     * 拦截带有@TrackObjects注解的方法
     */
    @Around("@annotation(com.gtech.gvcore.monitor.annotation.TrackObjects)")
    public Object trackMethodObjects(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        TrackObjects annotation = method.getAnnotation(TrackObjects.class);
        
        return executeWithTracking(joinPoint, annotation, getMethodDescription(method));
    }

    /**
     * 拦截类级别的@TrackObjects注解
     */
    @Around("@within(com.gtech.gvcore.monitor.annotation.TrackObjects) && !@annotation(com.gtech.gvcore.monitor.annotation.TrackObjects)")
    public Object trackClassObjects(ProceedingJoinPoint joinPoint) throws Throwable {
        Class<?> targetClass = joinPoint.getTarget().getClass();
        TrackObjects annotation = targetClass.getAnnotation(TrackObjects.class);
        
        return executeWithTracking(joinPoint, annotation, getClassMethodDescription(targetClass, joinPoint));
    }

    /**
     * 执行带追踪的方法
     */
    private Object executeWithTracking(ProceedingJoinPoint joinPoint, TrackObjects annotation, String description) throws Throwable {
        if (!annotation.enabled()) {
            return joinPoint.proceed();
        }

        // 检查是否已经在监控中（避免重复监控）
        boolean alreadyMonitoring = monitorManager.isMonitoring();
        String trackingId = null;
        
        if (!alreadyMonitoring) {
            // 开始新的监控会话
            trackingId = description + "@" + System.currentTimeMillis();
            monitorManager.startRequestMonitoring(trackingId);
            log.debug("开始对象追踪: {}", description);
        }

        try {
            Object result;
            
            if (annotation.includeSubThreads()) {
                // 支持子线程监控
                result = executeWithSubThreadTracking(joinPoint, annotation);
            } else {
                // 普通执行
                result = joinPoint.proceed();
            }

            return result;
            
        } finally {
            if (!alreadyMonitoring && trackingId != null) {
                // 结束监控并记录结果
                JSONObject trackingResult = monitorManager.endRequestMonitoring();
                logTrackingResult(annotation, description, trackingResult);
            }
        }
    }

    /**
     * 支持子线程的执行
     */
    private Object executeWithSubThreadTracking(ProceedingJoinPoint joinPoint, TrackObjects annotation) throws Throwable {
        // 获取当前监控状态
        String currentRequestId = monitorManager.getCurrentRequestId();
        
        // 在子线程中传播监控上下文
        CompletableFuture<Void> propagationFuture = CompletableFuture.runAsync(() -> {
            if (currentRequestId != null) {
                // 在子线程中继续使用相同的监控会话
                // 这里可以扩展支持子线程的监控逻辑
                log.debug("子线程监控上下文传播: {}", currentRequestId);
            }
        });

        try {
            Object result = joinPoint.proceed();
            
            // 等待子线程完成（可选，根据需要调整）
            if (!propagationFuture.isDone()) {
                propagationFuture.get(100, java.util.concurrent.TimeUnit.MILLISECONDS);
            }
            
            return result;
        } catch (Exception e) {
            propagationFuture.cancel(true);
            throw e;
        }
    }

    /**
     * 记录追踪结果
     */
    private void logTrackingResult(TrackObjects annotation, String description, JSONObject result) {
        try {
            int totalObjects = result.getIntValue("totalObjects");
            
            // 检查阈值
            if (totalObjects < annotation.threshold()) {
                log.debug("对象创建数量({})未达到阈值({}), 跳过记录: {}", totalObjects, annotation.threshold(), description);
                return;
            }

            // 过滤指定的对象类型
            String[] targetTypes = annotation.objectTypes();
            if (targetTypes.length > 0) {
                JSONObject objectCounts = result.getJSONObject("objectCounts");
                if (objectCounts != null) {
                    boolean hasTargetType = Arrays.stream(targetTypes)
                            .anyMatch(type -> objectCounts.containsKey(type) && objectCounts.getIntValue(type) > 0);
                    
                    if (!hasTargetType) {
                        log.debug("未检测到指定类型的对象创建: {} in {}", Arrays.toString(targetTypes), description);
                        return;
                    }
                }
            }

            // 记录追踪结果
            long duration = result.getLongValue("duration");
            JSONObject objectCounts = result.getJSONObject("objectCounts");
            
            log.info("🔍 对象追踪结果 - {}: 耗时{}ms, 创建对象{}个, 详情: {}", 
                    description, duration, totalObjects, 
                    objectCounts != null ? objectCounts.toJSONString() : "{}");
                    
        } catch (Exception e) {
            log.warn("记录对象追踪结果失败: {}", e.getMessage());
        }
    }

    /**
     * 获取方法描述
     */
    private String getMethodDescription(Method method) {
        return method.getDeclaringClass().getSimpleName() + "." + method.getName();
    }

    /**
     * 获取类方法描述
     */
    private String getClassMethodDescription(Class<?> clazz, ProceedingJoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        return clazz.getSimpleName() + "." + methodName;
    }
}
