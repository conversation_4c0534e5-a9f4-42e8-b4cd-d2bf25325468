package com.gtech.gvcore.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 对象创建检测器
 * 提供手动追踪对象创建的工具方法
 */
@Slf4j
@Component
public class ObjectCreationDetector {

    private static UnifiedMonitorManager monitorManager;

    @Autowired
    public void setMonitorManager(UnifiedMonitorManager monitorManager) {
        ObjectCreationDetector.monitorManager = monitorManager;
        log.info("对象创建检测器初始化完成");
    }

    /**
     * 手动追踪工具类
     * 提供常用集合类的追踪版本
     */
    public static class Manual {

        /**
         * 创建并追踪ArrayList
         */
        public static <T> List<T> trackArrayList() {
            List<T> list = new ArrayList<>();
            if (monitorManager != null) {
                monitorManager.recordObjectCreation("ArrayList", 1);
            }
            return list;
        }

        /**
         * 创建并追踪ArrayList（指定初始容量）
         */
        public static <T> List<T> trackArrayList(int initialCapacity) {
            List<T> list = new ArrayList<>(initialCapacity);
            if (monitorManager != null) {
                monitorManager.recordObjectCreation("ArrayList", 1);
            }
            return list;
        }

        /**
         * 创建并追踪HashMap
         */
        public static <K, V> Map<K, V> trackHashMap() {
            Map<K, V> map = new HashMap<>();
            if (monitorManager != null) {
                monitorManager.recordObjectCreation("HashMap", 1);
            }
            return map;
        }

        /**
         * 创建并追踪HashMap（指定初始容量）
         */
        public static <K, V> Map<K, V> trackHashMap(int initialCapacity) {
            Map<K, V> map = new HashMap<>(initialCapacity);
            if (monitorManager != null) {
                monitorManager.recordObjectCreation("HashMap", 1);
            }
            return map;
        }

        /**
         * 创建并追踪ConcurrentHashMap
         */
        public static <K, V> Map<K, V> trackConcurrentHashMap() {
            Map<K, V> map = new ConcurrentHashMap<>();
            if (monitorManager != null) {
                monitorManager.recordObjectCreation("ConcurrentHashMap", 1);
            }
            return map;
        }

        /**
         * 创建并追踪HashSet
         */
        public static <T> Set<T> trackHashSet() {
            Set<T> set = new HashSet<>();
            if (monitorManager != null) {
                monitorManager.recordObjectCreation("HashSet", 1);
            }
            return set;
        }

        /**
         * 创建并追踪StringBuilder
         */
        public static StringBuilder trackStringBuilder() {
            StringBuilder sb = new StringBuilder();
            if (monitorManager != null) {
                monitorManager.recordObjectCreation("StringBuilder", 1);
            }
            return sb;
        }

        /**
         * 创建并追踪StringBuilder（指定初始容量）
         */
        public static StringBuilder trackStringBuilder(int capacity) {
            StringBuilder sb = new StringBuilder(capacity);
            if (monitorManager != null) {
                monitorManager.recordObjectCreation("StringBuilder", 1);
            }
            return sb;
        }

        /**
         * 手动追踪对象创建
         */
        public static void trackObject(Object obj) {
            if (obj != null && monitorManager != null) {
                monitorManager.recordObjectCreation(obj);
            }
        }

        /**
         * 手动追踪对象创建（指定类型和数量）
         */
        public static void trackObject(String objectType, int count) {
            if (monitorManager != null) {
                monitorManager.recordObjectCreation(objectType, count);
            }
        }

        /**
         * 批量追踪对象创建
         */
        public static void trackObjects(Object... objects) {
            if (objects != null && monitorManager != null) {
                for (Object obj : objects) {
                    if (obj != null) {
                        monitorManager.recordObjectCreation(obj);
                    }
                }
            }
        }
    }

    /**
     * 工厂方法，用于创建常用对象并自动追踪
     */
    public static class Factory {

        /**
         * 创建ArrayList并追踪
         */
        public static <T> List<T> createArrayList() {
            return Manual.trackArrayList();
        }

        /**
         * 创建HashMap并追踪
         */
        public static <K, V> Map<K, V> createHashMap() {
            return Manual.trackHashMap();
        }

        /**
         * 创建StringBuilder并追踪
         */
        public static StringBuilder createStringBuilder() {
            return Manual.trackStringBuilder();
        }
    }

    /**
     * 检查是否正在监控
     */
    public static boolean isMonitoring() {
        return monitorManager != null && monitorManager.isMonitoring();
    }
}
