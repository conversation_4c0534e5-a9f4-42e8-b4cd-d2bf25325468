package com.gtech.gvcore.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.instrument.ClassFileTransformer;
import java.lang.instrument.Instrumentation;
import java.security.ProtectionDomain;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 对象创建检测器
 * 使用轻量级方式检测对象创建
 */
@Slf4j
@Component
public class ObjectCreationDetector {

    private static final ScheduledExecutorService CLEANUP_EXECUTOR = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "ObjectTracker-Cleanup");
        t.setDaemon(true);
        return t;
    });

    @PostConstruct
    public void init() {
        // 启动清理任务
        CLEANUP_EXECUTOR.scheduleAtFixedRate(
            EnhancedObjectTracker::cleanupExpiredSessions,
            1, 1, TimeUnit.HOURS
        );
        
        log.info("对象创建检测器初始化完成");
    }

    /**
     * 手动检测对象创建的工具方法
     * 在关键代码位置调用
     */
    public static class Manual {
        
        /**
         * 检测ArrayList创建
         */
        public static <T> java.util.ArrayList<T> trackArrayList() {
            java.util.ArrayList<T> list = new java.util.ArrayList<>();
            EnhancedObjectTracker.trackObjectCreation("ArrayList", 1);
            return list;
        }
        
        /**
         * 检测ArrayList创建（带初始容量）
         */
        public static <T> java.util.ArrayList<T> trackArrayList(int initialCapacity) {
            java.util.ArrayList<T> list = new java.util.ArrayList<>(initialCapacity);
            EnhancedObjectTracker.trackObjectCreation("ArrayList", 1);
            return list;
        }
        
        /**
         * 检测HashMap创建
         */
        public static <K, V> java.util.HashMap<K, V> trackHashMap() {
            java.util.HashMap<K, V> map = new java.util.HashMap<>();
            EnhancedObjectTracker.trackObjectCreation("HashMap", 1);
            return map;
        }
        
        /**
         * 检测StringBuilder创建
         */
        public static StringBuilder trackStringBuilder() {
            StringBuilder sb = new StringBuilder();
            EnhancedObjectTracker.trackObjectCreation("StringBuilder", 1);
            return sb;
        }
        
        /**
         * 检测StringBuilder创建（带初始容量）
         */
        public static StringBuilder trackStringBuilder(int capacity) {
            StringBuilder sb = new StringBuilder(capacity);
            EnhancedObjectTracker.trackObjectCreation("StringBuilder", 1);
            return sb;
        }
        
        /**
         * 检测自定义对象创建
         */
        public static void trackObject(Object obj) {
            if (obj != null) {
                EnhancedObjectTracker.trackObjectCreation(obj.getClass().getSimpleName(), 1);
            }
        }
        
        /**
         * 批量检测对象创建
         */
        public static void trackObjects(String objectType, int count) {
            EnhancedObjectTracker.trackObjectCreation(objectType, count);
        }
    }

    /**
     * 线程工厂，用于创建支持追踪的线程
     */
    public static class TrackingThreadFactory implements java.util.concurrent.ThreadFactory {
        private final java.util.concurrent.ThreadFactory delegate;
        
        public TrackingThreadFactory(java.util.concurrent.ThreadFactory delegate) {
            this.delegate = delegate;
        }
        
        @Override
        public Thread newThread(Runnable r) {
            return delegate.newThread(() -> {
                // 传播追踪上下文到子线程
                EnhancedObjectTracker.propagateToChildThread();
                try {
                    r.run();
                } finally {
                    // 清理子线程的追踪上下文
                    // 注意：这里不调用endTracking，因为会话由主线程管理
                }
            });
        }
    }

    /**
     * 包装Executor以支持追踪
     */
    public static java.util.concurrent.ExecutorService wrapExecutor(java.util.concurrent.ExecutorService executor) {
        return new TrackingExecutorWrapper(executor);
    }

    /**
     * 追踪支持的ExecutorService包装器
     */
    private static class TrackingExecutorWrapper implements java.util.concurrent.ExecutorService {
        private final java.util.concurrent.ExecutorService delegate;
        
        public TrackingExecutorWrapper(java.util.concurrent.ExecutorService delegate) {
            this.delegate = delegate;
        }
        
        @Override
        public void execute(Runnable command) {
            delegate.execute(wrapRunnable(command));
        }
        
        @Override
        public java.util.concurrent.Future<?> submit(Runnable task) {
            return delegate.submit(wrapRunnable(task));
        }
        
        @Override
        public <T> java.util.concurrent.Future<T> submit(Runnable task, T result) {
            return delegate.submit(wrapRunnable(task), result);
        }
        
        @Override
        public <T> java.util.concurrent.Future<T> submit(java.util.concurrent.Callable<T> task) {
            return delegate.submit(wrapCallable(task));
        }
        
        private Runnable wrapRunnable(Runnable runnable) {
            return () -> {
                EnhancedObjectTracker.propagateToChildThread();
                runnable.run();
            };
        }
        
        private <T> java.util.concurrent.Callable<T> wrapCallable(java.util.concurrent.Callable<T> callable) {
            return () -> {
                EnhancedObjectTracker.propagateToChildThread();
                return callable.call();
            };
        }
        
        // 委托其他方法
        @Override public void shutdown() { delegate.shutdown(); }
        @Override public java.util.List<Runnable> shutdownNow() { return delegate.shutdownNow(); }
        @Override public boolean isShutdown() { return delegate.isShutdown(); }
        @Override public boolean isTerminated() { return delegate.isTerminated(); }
        @Override public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException {
            return delegate.awaitTermination(timeout, unit);
        }
        @Override public <T> java.util.List<java.util.concurrent.Future<T>> invokeAll(java.util.Collection<? extends java.util.concurrent.Callable<T>> tasks) throws InterruptedException {
            return delegate.invokeAll(tasks.stream().map(this::wrapCallable).collect(java.util.stream.Collectors.toList()));
        }
        @Override public <T> java.util.List<java.util.concurrent.Future<T>> invokeAll(java.util.Collection<? extends java.util.concurrent.Callable<T>> tasks, long timeout, TimeUnit unit) throws InterruptedException {
            return delegate.invokeAll(tasks.stream().map(this::wrapCallable).collect(java.util.stream.Collectors.toList()), timeout, unit);
        }
        @Override public <T> T invokeAny(java.util.Collection<? extends java.util.concurrent.Callable<T>> tasks) throws InterruptedException, java.util.concurrent.ExecutionException {
            return delegate.invokeAny(tasks.stream().map(this::wrapCallable).collect(java.util.stream.Collectors.toList()));
        }
        @Override public <T> T invokeAny(java.util.Collection<? extends java.util.concurrent.Callable<T>> tasks, long timeout, TimeUnit unit) throws InterruptedException, java.util.concurrent.ExecutionException, java.util.concurrent.TimeoutException {
            return delegate.invokeAny(tasks.stream().map(this::wrapCallable).collect(java.util.stream.Collectors.toList()), timeout, unit);
        }
    }
}
