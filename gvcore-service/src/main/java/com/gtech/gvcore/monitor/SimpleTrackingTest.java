package com.gtech.gvcore.monitor;

import com.gtech.gvcore.monitor.annotation.TrackObjects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 简单的对象追踪测试类
 * 用于验证@TrackObjects注解是否正常工作
 */
@Slf4j
@Component
public class SimpleTrackingTest {

    /**
     * 最简单的追踪测试
     */
    @TrackObjects
    public void simpleTest() {
        log.info("开始简单追踪测试");
        
        // 创建一些对象
        List<String> list = new ArrayList<>();
        list.add("test1");
        list.add("test2");
        
        StringBuilder sb = new StringBuilder();
        sb.append("Hello World");
        
        log.info("简单追踪测试完成");
    }

    /**
     * 带描述的追踪测试
     */
    @TrackObjects(description = "带描述的测试")
    public void testWithDescription() {
        log.info("开始带描述的追踪测试");
        
        for (int i = 0; i < 10; i++) {
            String str = "Test " + i;
            // 这里会创建String对象
        }
        
        log.info("带描述的追踪测试完成");
    }

    /**
     * 指定对象类型的追踪测试
     */
    @TrackObjects(
        description = "指定类型追踪", 
        objectTypes = {"ArrayList", "StringBuilder"}
    )
    public void testSpecificTypes() {
        log.info("开始指定类型追踪测试");
        
        // 这些会被追踪
        List<String> list = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        
        // 这些不会被追踪（因为没有在objectTypes中）
        String str = "This won't be tracked";
        Integer num = 123;
        
        log.info("指定类型追踪测试完成");
    }

    /**
     * 禁用追踪的测试
     */
    @TrackObjects(enabled = false)
    public void testDisabled() {
        log.info("开始禁用追踪测试");
        
        // 这些都不会被追踪
        for (int i = 0; i < 100; i++) {
            List<String> list = new ArrayList<>();
            StringBuilder sb = new StringBuilder();
        }
        
        log.info("禁用追踪测试完成");
    }

    /**
     * 不自动输出日志的测试
     */
    @TrackObjects(description = "手动日志测试", autoLog = false)
    public String testManualLog() {
        log.info("开始手动日志测试");
        
        List<String> results = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            results.add("Result " + i);
        }
        
        // 手动获取统计信息
        try {
            com.alibaba.fastjson.JSONObject stats = EnhancedObjectTracker.getCurrentStats();
            log.info("手动获取的统计信息: {}", stats.toJSONString());
        } catch (Exception e) {
            log.warn("获取统计信息失败: {}", e.getMessage());
        }
        
        log.info("手动日志测试完成");
        return "测试完成";
    }
}
