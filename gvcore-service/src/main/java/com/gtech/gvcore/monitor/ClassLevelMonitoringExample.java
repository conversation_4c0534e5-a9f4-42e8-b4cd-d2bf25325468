package com.gtech.gvcore.monitor;

import com.gtech.gvcore.monitor.annotation.TrackObjects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类级别监控示例
 * 在类上使用@TrackObjects注解，该类的所有public方法都会被监控
 */
@Slf4j
@Component
@TrackObjects(
    description = "类级别监控示例", 
    threshold = 10,
    objectTypes = {"ArrayList", "HashMap", "StringBuilder"}
)
public class ClassLevelMonitoringExample {

    /**
     * 此方法会被自动监控（继承类级别的@TrackObjects）
     */
    public void method1() {
        List<String> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        
        for (int i = 0; i < 15; i++) {
            list.add("item" + i);
            map.put("key" + i, "value" + i);
        }
        
        log.info("方法1执行完成");
    }

    /**
     * 此方法也会被自动监控
     */
    public void method2() {
        StringBuilder sb = new StringBuilder();
        List<Integer> numbers = new ArrayList<>();
        
        for (int i = 0; i < 20; i++) {
            sb.append("number").append(i).append(",");
            numbers.add(i);
        }
        
        log.info("方法2执行完成");
    }

    /**
     * 方法级别的注解会覆盖类级别的设置
     */
    @TrackObjects(enabled = false)
    public void method3() {
        // 即使类有@TrackObjects，但方法级别设置enabled=false，所以不会被监控
        List<String> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        
        log.info("方法3不会被监控");
    }

    /**
     * 方法级别的注解覆盖类级别的阈值
     */
    @TrackObjects(threshold = 5, description = "低阈值方法")
    public void method4() {
        // 使用方法级别的阈值（5），而不是类级别的阈值（10）
        List<String> list = new ArrayList<>();
        for (int i = 0; i < 8; i++) {
            list.add("item" + i);
        }
        
        log.info("方法4使用低阈值");
    }

    /**
     * private方法不会被监控
     */
    private void privateMethod() {
        List<String> list = new ArrayList<>();
        log.info("私有方法不会被监控");
    }

    /**
     * 调用私有方法的public方法会被监控
     */
    public void callPrivateMethod() {
        List<String> list = new ArrayList<>();
        privateMethod(); // 私有方法内的对象创建不会被监控
        
        log.info("调用私有方法完成");
    }
}
