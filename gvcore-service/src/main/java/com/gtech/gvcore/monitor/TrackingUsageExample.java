package com.gtech.gvcore.monitor;

import com.alibaba.fastjson.JSONObject;
import com.gtech.gvcore.common.annotation.TrackObjects;
import com.gtech.gvcore.dao.model.TransactionData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 对象追踪使用示例
 * 展示如何使用@TrackObjects注解进行自动追踪
 */
@Slf4j
@Service
public class TrackingUsageExample {

    /**
     * 示例1：方法级别追踪 - 追踪所有对象
     */
    @TrackObjects(description = "批量创建交易数据")
    public List<TransactionData> createBatchTransactions(String batchId, int count) {
        // 使用追踪版本的ArrayList
        List<TransactionData> transactions = ObjectCreationDetector.Manual.trackArrayList();
        
        for (int i = 0; i < count; i++) {
            TransactionData data = new TransactionData();
            data.setVoucherCode("BATCH_" + batchId + "_" + i);
            data.setTransactionType("5");
            
            // 手动追踪自定义对象
            ObjectCreationDetector.Manual.trackObject(data);
            
            transactions.add(data);
        }
        
        return transactions;
    }

    /**
     * 示例2：只追踪特定类型的对象
     */
    @TrackObjects(
        description = "处理交易数据", 
        objectTypes = {"TransactionData", "StringBuilder"},
        autoLog = true
    )
    public void processTransactions(List<TransactionData> transactions) {
        StringBuilder report = ObjectCreationDetector.Manual.trackStringBuilder();
        
        for (TransactionData data : transactions) {
            // 创建处理结果对象
            TransactionData processedData = new TransactionData();
            processedData.setVoucherCode(data.getVoucherCode() + "_PROCESSED");
            
            ObjectCreationDetector.Manual.trackObject(processedData);
            
            report.append("Processed: ").append(data.getVoucherCode()).append("\n");
        }
        
        log.info("处理报告: {}", report.toString());
    }

    /**
     * 示例3：包含子线程追踪
     */
    @TrackObjects(
        description = "并行处理数据", 
        includeChildThreads = true,
        autoLog = true
    )
    public void parallelProcessing(String batchId) {
        // 使用支持追踪的线程池
        ExecutorService executor = ObjectCreationDetector.wrapExecutor(
            Executors.newFixedThreadPool(4)
        );
        
        try {
            // 创建多个并行任务
            CompletableFuture<Void> task1 = CompletableFuture.runAsync(() -> {
                processSubTask("task1", 100);
            }, executor);
            
            CompletableFuture<Void> task2 = CompletableFuture.runAsync(() -> {
                processSubTask("task2", 150);
            }, executor);
            
            CompletableFuture<Void> task3 = CompletableFuture.runAsync(() -> {
                processSubTask("task3", 200);
            }, executor);
            
            // 等待所有任务完成
            CompletableFuture.allOf(task1, task2, task3).join();
            
        } finally {
            executor.shutdown();
        }
    }

    /**
     * 子任务处理
     */
    private void processSubTask(String taskName, int objectCount) {
        log.info("开始处理子任务: {}", taskName);
        
        // 在子线程中创建对象
        List<String> results = ObjectCreationDetector.Manual.trackArrayList();
        
        for (int i = 0; i < objectCount; i++) {
            String result = "Result_" + taskName + "_" + i;
            results.add(result);
            
            // 追踪String对象创建
            ObjectCreationDetector.Manual.trackObjects("String", 1);
        }
        
        log.info("子任务 {} 完成，创建了 {} 个对象", taskName, objectCount);
    }

    /**
     * 示例4：类级别追踪（所有方法都会被追踪）
     */
    @TrackObjects(description = "数据处理服务", objectTypes = {"TransactionData"})
    public static class DataProcessingService {
        
        public void method1() {
            TransactionData data = new TransactionData();
            ObjectCreationDetector.Manual.trackObject(data);
        }
        
        public void method2() {
            for (int i = 0; i < 10; i++) {
                TransactionData data = new TransactionData();
                ObjectCreationDetector.Manual.trackObject(data);
            }
        }
    }

    /**
     * 示例5：禁用追踪的方法
     */
    @TrackObjects(enabled = false)
    public void methodWithoutTracking() {
        // 这个方法不会被追踪
        for (int i = 0; i < 1000; i++) {
            new TransactionData();
        }
    }

    /**
     * 示例6：自定义描述和不自动输出日志
     */
    @TrackObjects(
        description = "大批量数据处理", 
        autoLog = false
    )
    public String heavyDataProcessing(int dataSize) {
        List<TransactionData> data = ObjectCreationDetector.Manual.trackArrayList(dataSize);
        
        for (int i = 0; i < dataSize; i++) {
            TransactionData item = new TransactionData();
            item.setVoucherCode("HEAVY_" + i);
            ObjectCreationDetector.Manual.trackObject(item);
            data.add(item);
        }
        
        // 手动获取统计信息
        JSONObject stats = EnhancedObjectTracker.getCurrentStats();
        log.info("大批量处理完成，统计信息: {}", stats.toJSONString());
        
        return "处理完成，共创建 " + stats.getInteger("totalObjects") + " 个对象";
    }
}
