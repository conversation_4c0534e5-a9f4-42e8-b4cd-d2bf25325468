package com.gtech.gvcore.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryPoolMXBean;
import java.lang.management.MemoryUsage;
import java.lang.ref.WeakReference;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * JVM对象监控工具
 * 用于监控JVM中的对象数量和内存使用情况
 */
@Slf4j
public class JvmObjectMonitor {

    // 对象实例跟踪器（使用弱引用避免影响GC）
    private static final Map<String, Map<String, WeakReference<Object>>> OBJECT_TRACKER = new ConcurrentHashMap<>();
    private static final int MAX_TRACKED_OBJECTS_PER_TYPE = 100; // 每种类型最多跟踪100个对象

    /**
     * 获取详细的JVM对象统计信息
     * @return JSON格式的统计信息
     */
    public static String getDetailedObjectStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 基本内存信息
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
            
            stats.put("timestamp", System.currentTimeMillis());
            stats.put("heapUsedMB", heapUsage.getUsed() / (1024 * 1024));
            stats.put("heapMaxMB", heapUsage.getMax() > 0 ? heapUsage.getMax() / (1024 * 1024) : -1);
            stats.put("heapCommittedMB", heapUsage.getCommitted() / (1024 * 1024));
            stats.put("nonHeapUsedMB", nonHeapUsage.getUsed() / (1024 * 1024));
            
            // 内存池详细信息
            List<Map<String, Object>> poolStats = getMemoryPoolStatistics();
            stats.put("memoryPools", poolStats);
            
            // GC信息
            Map<String, Object> gcStats = getGarbageCollectionStatistics();
            stats.put("gc", gcStats);
            
            return JSON.toJSONString(stats);
        } catch (Exception e) {
            log.error("获取JVM对象统计信息失败", e);
            return "{\"error\":\"" + e.getMessage() + "\"}";
        }
    }

    /**
     * 获取简化的对象统计信息（用于日志记录）
     * 包含原memory_snapshot的信息和更详细的内存区域统计
     * @return JSON格式的统计信息字符串
     */
    public static String getSimpleObjectStatistics() {
        try {
            JSONObject stats = new JSONObject();

            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();

            // 基本堆内存信息
            JSONObject heapInfo = new JSONObject();
            heapInfo.put("usedMB", heapUsage.getUsed() / (1024 * 1024));
            heapInfo.put("maxMB", heapUsage.getMax() > 0 ? heapUsage.getMax() / (1024 * 1024) : -1);
            heapInfo.put("usagePercent", heapUsage.getMax() > 0 ?
                Math.round((double) heapUsage.getUsed() / heapUsage.getMax() * 100 * 100.0) / 100.0 : 0);
            stats.put("heap", heapInfo);

            // 获取Eden区使用情况（年轻代）
            Optional<MemoryPoolMXBean> edenPool = ManagementFactory.getMemoryPoolMXBeans()
                    .stream()
                    .filter(pool -> pool.getName().contains("Eden"))
                    .findFirst();

            if (edenPool.isPresent() && edenPool.get().getUsage() != null) {
                JSONObject edenInfo = new JSONObject();
                MemoryUsage edenUsage = edenPool.get().getUsage();
                edenInfo.put("usedMB", edenUsage.getUsed() / (1024 * 1024));
                edenInfo.put("maxMB", edenUsage.getMax() > 0 ? edenUsage.getMax() / (1024 * 1024) : -1);
                if (edenUsage.getMax() > 0) {
                    edenInfo.put("usagePercent", Math.round((double) edenUsage.getUsed() / edenUsage.getMax() * 100 * 100.0) / 100.0);
                }
                stats.put("eden", edenInfo);
            }

            // 获取Old Gen使用情况（老年代）
            Optional<MemoryPoolMXBean> oldGenPool = ManagementFactory.getMemoryPoolMXBeans()
                    .stream()
                    .filter(pool -> pool.getName().contains("Old") || pool.getName().contains("Tenured"))
                    .findFirst();

            if (oldGenPool.isPresent() && oldGenPool.get().getUsage() != null) {
                JSONObject oldGenInfo = new JSONObject();
                MemoryUsage oldGenUsage = oldGenPool.get().getUsage();
                oldGenInfo.put("usedMB", oldGenUsage.getUsed() / (1024 * 1024));
                oldGenInfo.put("maxMB", oldGenUsage.getMax() > 0 ? oldGenUsage.getMax() / (1024 * 1024) : -1);
                if (oldGenUsage.getMax() > 0) {
                    oldGenInfo.put("usagePercent", Math.round((double) oldGenUsage.getUsed() / oldGenUsage.getMax() * 100 * 100.0) / 100.0);
                }
                stats.put("oldGen", oldGenInfo);
            }

            // 添加Top5对象类型统计
            Map<String, Integer> topObjects = getTopObjectTypesMap(5);
            if (!topObjects.isEmpty()) {
                stats.put("topObjects", topObjects);
            }

            // 添加时间戳
            stats.put("timestamp", System.currentTimeMillis());

            return stats.toJSONString();
        } catch (Exception e) {
            log.warn("获取简化对象统计信息失败: {}", e.getMessage());
            return "{\"error\":\"" + e.getMessage() + "\"}";
        }
    }

    /**
     * 获取内存池统计信息
     */
    private static List<Map<String, Object>> getMemoryPoolStatistics() {
        return ManagementFactory.getMemoryPoolMXBeans()
                .stream()
                .filter(pool -> pool.getUsage() != null)
                .map(pool -> {
                    Map<String, Object> poolInfo = new HashMap<>();
                    MemoryUsage usage = pool.getUsage();
                    poolInfo.put("name", pool.getName());
                    poolInfo.put("type", pool.getType().toString());
                    poolInfo.put("usedMB", usage.getUsed() / (1024 * 1024));
                    poolInfo.put("maxMB", usage.getMax() > 0 ? usage.getMax() / (1024 * 1024) : -1);
                    poolInfo.put("committedMB", usage.getCommitted() / (1024 * 1024));
                    
                    // 计算使用率
                    if (usage.getMax() > 0) {
                        double usagePercent = (double) usage.getUsed() / usage.getMax() * 100;
                        poolInfo.put("usagePercent", Math.round(usagePercent * 100.0) / 100.0);
                    }
                    
                    return poolInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取垃圾回收统计信息
     */
    private static Map<String, Object> getGarbageCollectionStatistics() {
        Map<String, Object> gcStats = new HashMap<>();
        
        try {
            ManagementFactory.getGarbageCollectorMXBeans().forEach(gcBean -> {
                Map<String, Object> gcInfo = new HashMap<>();
                gcInfo.put("collectionCount", gcBean.getCollectionCount());
                gcInfo.put("collectionTime", gcBean.getCollectionTime());
                gcStats.put(gcBean.getName(), gcInfo);
            });
        } catch (Exception e) {
            log.warn("获取GC统计信息失败: {}", e.getMessage());
            gcStats.put("error", e.getMessage());
        }
        
        return gcStats;
    }

    /**
     * 获取Top N对象类型统计
     * @param topN 返回前N个对象类型
     * @return 格式化的对象统计字符串
     */
    private static String getTopObjectTypes(int topN) {
        try {
            // 获取具体对象实例统计
            Map<String, Integer> objectInstanceStats = getObjectInstanceStats();

            if (!objectInstanceStats.isEmpty()) {
                // 返回具体对象实例统计
                return objectInstanceStats.entrySet().stream()
                        .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                        .limit(topN)
                        .map(entry -> entry.getKey() + ":" + entry.getValue())
                        .collect(Collectors.joining(","));
            }

            // 备选方案：获取类加载器统计信息
            Map<String, Long> classStats = getLoadedClassStats();

            if (classStats.isEmpty()) {
                // 最后备选方案：获取基本统计
                return getBasicObjectStats();
            }

            // 返回Top N类
            return classStats.entrySet().stream()
                    .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                    .limit(topN)
                    .map(entry -> entry.getKey() + ":" + entry.getValue())
                    .collect(Collectors.joining(","));

        } catch (Exception e) {
            log.warn("获取对象类型统计失败: {}", e.getMessage());
            return getBasicObjectStats();
        }
    }

    /**
     * 获取Top N对象类型统计（返回Map格式）
     * @param topN 返回前N个对象类型
     * @return 对象类型统计Map
     */
    private static Map<String, Integer> getTopObjectTypesMap(int topN) {
        try {
            // 获取具体对象实例统计
            Map<String, Integer> objectInstanceStats = getObjectInstanceStats();

            if (!objectInstanceStats.isEmpty()) {
                // 返回具体对象实例统计
                return objectInstanceStats.entrySet().stream()
                        .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                        .limit(topN)
                        .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new
                        ));
            }

            // 备选方案：获取类加载器统计信息
            Map<String, Long> classStats = getLoadedClassStats();

            if (!classStats.isEmpty()) {
                return classStats.entrySet().stream()
                        .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                        .limit(topN)
                        .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().intValue(),
                            (e1, e2) -> e1,
                            LinkedHashMap::new
                        ));
            }

            return new HashMap<>();

        } catch (Exception e) {
            log.warn("获取对象类型统计Map失败: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 获取已加载类的统计信息
     */
    private static Map<String, Long> getLoadedClassStats() {
        Map<String, Long> stats = new HashMap<>();

        try {
            // 获取类加载器MXBean
            java.lang.management.ClassLoadingMXBean classLoadingBean = ManagementFactory.getClassLoadingMXBean();
            long loadedClassCount = classLoadingBean.getLoadedClassCount();
            long totalLoadedClassCount = classLoadingBean.getTotalLoadedClassCount();
            long unloadedClassCount = classLoadingBean.getUnloadedClassCount();

            // 添加基本统计
            stats.put("LoadedClasses", loadedClassCount);
            stats.put("TotalLoaded", totalLoadedClassCount);
            stats.put("Unloaded", unloadedClassCount);

            // 尝试获取更详细的内存池信息作为对象类型的代理
            ManagementFactory.getMemoryPoolMXBeans().forEach(pool -> {
                if (pool.getUsage() != null && pool.getUsage().getUsed() > 0) {
                    String poolName = getSimpleClassName(pool.getName());
                    long usedMB = pool.getUsage().getUsed() / (1024 * 1024);
                    if (usedMB > 0) {
                        stats.put(poolName + "Pool", usedMB);
                    }
                }
            });

        } catch (Exception e) {
            log.debug("获取类加载统计失败: {}", e.getMessage());
        }

        return stats;
    }



    /**
     * 获取基本对象统计（备选方案）
     */
    private static String getBasicObjectStats() {
        try {
            // 获取类加载器基本信息
            java.lang.management.ClassLoadingMXBean classLoadingBean = ManagementFactory.getClassLoadingMXBean();
            long loadedClasses = classLoadingBean.getLoadedClassCount();

            // 获取线程数
            java.lang.management.ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
            int threadCount = threadBean.getThreadCount();

            return "Classes:" + loadedClasses + ",Threads:" + threadCount;
        } catch (Exception e) {
            return "Stats:N/A";
        }
    }

    /**
     * 获取简化的类名
     */
    private static String getSimpleClassName(String fullClassName) {
        if (fullClassName == null || fullClassName.isEmpty()) {
            return fullClassName;
        }

        // 移除包名，只保留类名
        int lastDot = fullClassName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fullClassName.length() - 1) {
            return fullClassName.substring(lastDot + 1);
        }

        return fullClassName;
    }

    /**
     * 获取对象实例统计信息
     * 通过反射和堆分析获取具体的对象实例
     */
    private static Map<String, Integer> getObjectInstanceStats() {
        Map<String, Integer> stats = new HashMap<>();

        try {
            // 尝试通过JMX获取堆转储信息
            MBeanServer server = ManagementFactory.getPlatformMBeanServer();

            // 检查HotSpot诊断MBean
            try {
                ObjectName hotspotDiag = new ObjectName("com.sun.management:type=HotSpotDiagnostic");
                if (server.isRegistered(hotspotDiag)) {
                    // 获取一些关键对象的统计
                    stats.putAll(analyzeCommonObjects());
                }
            } catch (Exception e) {
                log.debug("无法通过HotSpot诊断获取对象统计: {}", e.getMessage());
            }

            // 如果没有获取到具体统计，尝试分析跟踪的对象
            if (stats.isEmpty()) {
                stats.putAll(analyzeTrackedObjects());
            }

        } catch (Exception e) {
            log.warn("获取对象实例统计失败: {}", e.getMessage());
        }

        return stats;
    }

    /**
     * 分析常见对象类型
     */
    private static Map<String, Integer> analyzeCommonObjects() {
        Map<String, Integer> stats = new HashMap<>();

        try {
            // 分析一些常见的可能导致内存泄漏的对象类型
            Runtime runtime = Runtime.getRuntime();

            // 估算String对象数量（基于字符串常量池）
            long stringPoolSize = estimateStringPoolSize();
            if (stringPoolSize > 0) {
                stats.put("String", (int) stringPoolSize);
            }

            // 获取线程相关对象
            java.lang.management.ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
            int threadCount = threadBean.getThreadCount();
            stats.put("Thread", threadCount);

            // 获取类加载器相关
            java.lang.management.ClassLoadingMXBean classBean = ManagementFactory.getClassLoadingMXBean();
            stats.put("Class", (int) classBean.getLoadedClassCount());

            // 分析内存池中的对象分布
            ManagementFactory.getMemoryPoolMXBeans().forEach(pool -> {
                if (pool.getUsage() != null && pool.getUsage().getUsed() > 0) {
                    String poolName = getSimpleClassName(pool.getName());
                    long usedMB = pool.getUsage().getUsed() / (1024 * 1024);
                    if (usedMB > 10) { // 只统计使用超过10MB的池
                        // 估算对象数量（假设平均每个对象1KB）
                        int estimatedObjects = (int) (usedMB * 1024);
                        stats.put(poolName + "Objects", estimatedObjects);
                    }
                }
            });

        } catch (Exception e) {
            log.debug("分析常见对象失败: {}", e.getMessage());
        }

        return stats;
    }

    /**
     * 估算字符串常量池大小
     */
    private static long estimateStringPoolSize() {
        try {
            // 通过内存使用情况估算
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long nonHeapUsed = memoryBean.getNonHeapMemoryUsage().getUsed();

            // 字符串常量池通常占非堆内存的一部分
            return (nonHeapUsed / (1024 * 64)); // 粗略估算
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 分析跟踪的对象
     */
    private static Map<String, Integer> analyzeTrackedObjects() {
        Map<String, Integer> stats = new HashMap<>();

        try {
            OBJECT_TRACKER.forEach((className, objectMap) -> {
                // 清理已被GC的弱引用
                objectMap.entrySet().removeIf(entry -> entry.getValue().get() == null);

                if (!objectMap.isEmpty()) {
                    stats.put(className, objectMap.size());
                }
            });
        } catch (Exception e) {
            log.debug("分析跟踪对象失败: {}", e.getMessage());
        }

        return stats;
    }

    /**
     * 注册对象实例进行跟踪
     * @param obj 要跟踪的对象
     * @param identifier 对象标识符（如字段名）
     */
    public static void trackObject(Object obj, String identifier) {
        if (obj == null) return;

        try {
            String className = obj.getClass().getSimpleName();
            OBJECT_TRACKER.computeIfAbsent(className, k -> new ConcurrentHashMap<>());

            Map<String, WeakReference<Object>> objectMap = OBJECT_TRACKER.get(className);

            // 限制每种类型跟踪的对象数量
            if (objectMap.size() < MAX_TRACKED_OBJECTS_PER_TYPE) {
                String key = identifier + "@" + System.identityHashCode(obj);
                objectMap.put(key, new WeakReference<>(obj));
            }
        } catch (Exception e) {
            log.debug("跟踪对象失败: {}", e.getMessage());
        }
    }

    /**
     * 获取详细的对象实例信息
     * @return 详细的对象统计信息
     */
    public static String getDetailedObjectInstances() {
        StringBuilder result = new StringBuilder();

        try {
            OBJECT_TRACKER.forEach((className, objectMap) -> {
                result.append(className).append(":[");

                objectMap.entrySet().stream()
                        .limit(5) // 只显示前5个实例
                        .forEach(entry -> {
                            Object obj = entry.getValue().get();
                            if (obj != null) {
                                String instanceInfo = getObjectInstanceInfo(obj, entry.getKey());
                                result.append(instanceInfo).append(",");
                            }
                        });

                if (result.charAt(result.length() - 1) == ',') {
                    result.setLength(result.length() - 1);
                }
                result.append("],");
            });

            if (result.length() > 0 && result.charAt(result.length() - 1) == ',') {
                result.setLength(result.length() - 1);
            }

        } catch (Exception e) {
            log.warn("获取详细对象实例失败: {}", e.getMessage());
            return "Error:" + e.getMessage();
        }

        return result.toString();
    }

    /**
     * 获取对象实例的详细信息
     */
    private static String getObjectInstanceInfo(Object obj, String key) {
        try {
            StringBuilder info = new StringBuilder();
            info.append("{id:").append(key);

            // 尝试获取一些关键字段的值
            Field[] fields = obj.getClass().getDeclaredFields();
            int fieldCount = 0;

            for (Field field : fields) {
                if (fieldCount >= 2) break; // 只显示前2个字段

                try {
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    if (value != null) {
                        info.append(",").append(field.getName()).append(":").append(value.toString());
                        fieldCount++;
                    }
                } catch (Exception ignored) {
                    // 忽略无法访问的字段
                }
            }

            info.append("}");
            return info.toString();
        } catch (Exception e) {
            return "{id:" + key + ",error:" + e.getMessage() + "}";
        }
    }

    /**
     * 检查是否存在内存泄漏风险
     * @return 风险评估结果
     */
    public static MemoryLeakRisk assessMemoryLeakRisk() {
        try {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            
            double heapUsagePercent = (double) heapUsage.getUsed() / heapUsage.getMax() * 100;
            
            // 检查老年代使用情况
            Optional<MemoryPoolMXBean> oldGenPool = ManagementFactory.getMemoryPoolMXBeans()
                    .stream()
                    .filter(pool -> pool.getName().contains("Old") || pool.getName().contains("Tenured"))
                    .findFirst();
            
            double oldGenUsagePercent = 0;
            if (oldGenPool.isPresent() && oldGenPool.get().getUsage() != null) {
                MemoryUsage oldGenUsage = oldGenPool.get().getUsage();
                if (oldGenUsage.getMax() > 0) {
                    oldGenUsagePercent = (double) oldGenUsage.getUsed() / oldGenUsage.getMax() * 100;
                }
            }
            
            // 风险评估
            if (heapUsagePercent > 90 || oldGenUsagePercent > 85) {
                return new MemoryLeakRisk(RiskLevel.HIGH, 
                    String.format("堆内存使用率: %.1f%%, 老年代使用率: %.1f%%", heapUsagePercent, oldGenUsagePercent));
            } else if (heapUsagePercent > 80 || oldGenUsagePercent > 70) {
                return new MemoryLeakRisk(RiskLevel.MEDIUM, 
                    String.format("堆内存使用率: %.1f%%, 老年代使用率: %.1f%%", heapUsagePercent, oldGenUsagePercent));
            } else {
                return new MemoryLeakRisk(RiskLevel.LOW, 
                    String.format("堆内存使用率: %.1f%%, 老年代使用率: %.1f%%", heapUsagePercent, oldGenUsagePercent));
            }
        } catch (Exception e) {
            return new MemoryLeakRisk(RiskLevel.UNKNOWN, "评估失败: " + e.getMessage());
        }
    }

    /**
     * 内存泄漏风险等级
     */
    public enum RiskLevel {
        LOW, MEDIUM, HIGH, UNKNOWN
    }

    /**
     * 内存泄漏风险评估结果
     */
    public static class MemoryLeakRisk {
        private final RiskLevel level;
        private final String description;

        public MemoryLeakRisk(RiskLevel level, String description) {
            this.level = level;
            this.description = description;
        }

        public RiskLevel getLevel() { return level; }
        public String getDescription() { return description; }
        
        @Override
        public String toString() {
            return level + ": " + description;
        }
    }
}
