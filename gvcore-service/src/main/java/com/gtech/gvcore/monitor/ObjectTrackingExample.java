package com.gtech.gvcore.monitor;

import com.gtech.gvcore.dao.model.TransactionData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 对象跟踪使用示例
 * 展示如何在业务代码中使用RequestObjectTracker跟踪对象创建
 */
@Slf4j
@Component
public class ObjectTrackingExample {

    /**
     * 在Service方法中跟踪对象创建
     */
    public List<TransactionData> createBatchTransactions(String batchId, int count) {
        List<TransactionData> transactions = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            // 创建TransactionData对象
            TransactionData data = new TransactionData();
            data.setVoucherCode("BATCH_" + batchId + "_" + i);
            data.setTransactionType("5");

            // 跟踪对象创建
            RequestObjectTracker.trackObjectCreation(data);

            transactions.add(data);

            // 模拟创建其他相关对象
            String logMessage = "Created transaction: " + data.getVoucherCode();
            RequestObjectTracker.trackObjectCreation("String"); // 跟踪String对象创建
        }

        // 跟踪List对象
        RequestObjectTracker.trackObjectCreation("ArrayList");

        return transactions;
    }

    /**
     * 在DAO层跟踪数据库对象创建
     */
    public void saveTransactionData(TransactionData data) {
        // 模拟保存前的对象创建
        RequestObjectTracker.trackObjectCreation("PreparedStatement");
        RequestObjectTracker.trackObjectCreation("ResultSet");

        // 实际保存逻辑...
        log.info("保存交易数据: {}", data.getVoucherCode());

        // 跟踪保存后可能创建的对象
        RequestObjectTracker.trackObjectCreation("DatabaseMetaData");
    }

    /**
     * 示例：在批量处理中跟踪对象
     */
    public void processBatchData(String batchId) {
        try {
            // 模拟批量处理
            for (int i = 0; i < 1000; i++) {
                TransactionData data = new TransactionData();
                data.setVoucherCode("BATCH_" + batchId + "_" + i);
                data.setTransactionType("5");

                // 跟踪对象创建
                RequestObjectTracker.trackObjectCreation(data);

                // 每100个对象记录一次统计
                if (i % 100 == 0) {
                    String stats = RequestObjectTracker.getCurrentStats();
                    log.info("批量处理进度 {}/1000, 当前请求对象统计: {}", i, stats);
                }
            }

        } catch (Exception e) {
            log.error("批量处理失败", e);
        }
    }
}
