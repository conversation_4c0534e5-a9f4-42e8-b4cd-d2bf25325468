package com.gtech.gvcore.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @ClassName CpgHistoricalInventory
 * @Description Cpg历史库存折线图数据
 * <AUTHOR>
 * @Date 2022/7/18 16:10
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class CpgHistoricalInventory {

    /**
     * 月份: yyyyMM
     */
    private Integer month;

    /**
     * 可用数量(总数量-已分发数量)
     */
    private Integer available;

    /**
     * 已分发数量
     */
    private Integer distributed;

}
