package com.gtech.gvcore.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
public class TaskProgress {
    private Long totalUnits;
    private Long currentUnits;

    // 默认构造函数
    public TaskProgress() {}

    // 带参数的构造函数
    public TaskProgress(long totalUnits, long currentUnits) {
        this.totalUnits = totalUnits;
        this.currentUnits = currentUnits;
    }
}
