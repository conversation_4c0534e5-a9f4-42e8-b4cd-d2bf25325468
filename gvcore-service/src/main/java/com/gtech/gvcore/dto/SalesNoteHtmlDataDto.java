package com.gtech.gvcore.dto;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.utils.AmountUtils;
import com.gtech.gvcore.common.utils.CollectorsUtils;
import com.gtech.gvcore.dao.model.CustomerOrderDetails;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * @ClassName SalesNoteHtmlDataDto
 * @Description sales note html生成实体
 * <AUTHOR>
 * @Date 2022/7/27 17:02
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class SalesNoteHtmlDataDto {

    private String invoiceNo;

    private String nowDate = DateUtil.format(new Date(), "dd/MM/yyyy");

    private String nowTime = DateUtil.format(new Date(), "HH:mm:ss");

    private String customerName;

    private String operateUser;

    private List<SalesNoteItemHtmlDataDto> detailList;

    private String voucherAmount;

    private String discount;

    private String amount;

    private String sumQty;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class SalesNoteItemHtmlDataDto {

        private String articleMopCode;

        private String voucherNum;

        private String denomination;

        private String voucherAmount;

    }

    public SalesNoteHtmlDataDto setSumQty(List<CustomerOrderDetails> customerOrderDetails) {

        this.sumQty = AmountUtils.idrFormat(customerOrderDetails.stream().mapToInt(CustomerOrderDetails::getVoucherNum).sum());
        return this;
    }

    public SalesNoteHtmlDataDto setVoucherAmount(List<CustomerOrderDetails> customerOrderDetails) {

        this.voucherAmount = AmountUtils.idrFormat(generateSalesNoteSumAmount(customerOrderDetails, CustomerOrderDetails::getVoucherAmount));
        return this;
    }

    public SalesNoteHtmlDataDto setDiscount(List<CustomerOrderDetails> customerOrderDetails) {

        this.discount =  "-" + AmountUtils.idrFormat(generateSalesNoteSumAmount(customerOrderDetails, CustomerOrderDetails::getDiscount));
        return this;
    }

    public SalesNoteHtmlDataDto setAmount(List<CustomerOrderDetails> customerOrderDetails) {

        this.amount = AmountUtils.idrFormat(generateSalesNotConvertAmount(customerOrderDetails, CustomerOrderDetails::getAmount));
        return this;
    }

    public BigDecimal generateSalesNoteSumAmount(List<CustomerOrderDetails> customerOrderDetails, Function<CustomerOrderDetails, BigDecimal> field){

        BigDecimal value = customerOrderDetails.stream()
                .map(field)
                .filter(Objects::nonNull)
                .collect(CollectorsUtils.summingBigDecimal(e -> e));

        return ConvertUtils.toBigDecimal(value, BigDecimal.ZERO);
    }

    public BigDecimal generateSalesNotConvertAmount(List<CustomerOrderDetails> customerOrderDetails,  Function<CustomerOrderDetails, BigDecimal> field){

        BigDecimal value = generateSalesNoteSumAmount(customerOrderDetails, field);

        return BigDecimal.ZERO.compareTo(value) >= 0 ? BigDecimal.ZERO : value;
    }
}
