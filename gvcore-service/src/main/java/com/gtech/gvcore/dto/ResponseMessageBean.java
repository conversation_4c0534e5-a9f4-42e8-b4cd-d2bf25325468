package com.gtech.gvcore.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ResponseMessageBean {

    private String code;

    private String message;

    private MessageData data;

    private Boolean success;

    @Getter
    @Setter
    @ToString
    @Accessors(chain = true)
    public static class MessageData {

        private String messageId;

    }

    public static ResponseMessageBean fail() {
        return new ResponseMessageBean().setSuccess(false);
    }

}