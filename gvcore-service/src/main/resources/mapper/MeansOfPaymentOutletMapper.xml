<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.MeansOfPaymentOutletMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.MeansOfPaymentOutlet">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="means_of_payment_outlet_code" jdbcType="VARCHAR" property="meansOfPaymentOutletCode" />
    <result column="means_of_payment_code" jdbcType="VARCHAR" property="meansOfPaymentCode" />
    <result column="outlet_code" jdbcType="VARCHAR" property="outletCode" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, means_of_payment_outlet_code, means_of_payment_code, outlet_code, delete_status, 
    create_user, create_time, update_user, update_time
  </sql>
  <sql id="gv_means_of_payment_outlet_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="meansOfPaymentOutletCode != null and meansOfPaymentOutletCode.trim().length() != 0">
        AND (means_of_payment_outlet_code = #{meansOfPaymentOutletCode})
      </if>
      <if test="meansOfPaymentCode != null and meansOfPaymentCode.trim().length() != 0">
        AND (means_of_payment_code = #{meansOfPaymentCode})
      </if>
      <if test="outletCode != null and outletCode.trim().length() != 0">
        AND (outlet_code = #{outletCode})
      </if>
      <if test="deleteStatus != null">
        AND (delete_status = #{deleteStatus})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  
  <select id="queryByMeansOfPaymentCodeList" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" /> 
    FROM gv_means_of_payment_outlet 
    WHERE means_of_payment_code IN
    <foreach collection="meansOfPaymentCodeList" item="item" open="(" separator="," close=")">
        #{item}
    </foreach>
    <if test="deleteStatus != null">
        AND delete_status = #{deleteStatus}
    </if>
  </select>
  
</mapper>