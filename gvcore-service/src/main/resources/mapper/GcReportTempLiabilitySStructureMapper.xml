<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gtech.gvcore.dao.mapper.GcReportTempLiabilitySStructureMapper">

    <insert id="insertBatch">
        INSERT INTO gc_report_temp_liability_s_${tableCode} (
        `issuer_code`
        , `merchant_code`
        , `outlet_code`
        , `cpg_code`
        , `activated_amount`
        , `purchased_amount`
        , `deactivated_amount`
        , `expired_amount`
        , `total_amount`
        , `expiry_date`
        ) VALUES
        <foreach collection="summaryList" item="detail" index="index" separator=",">
            (
            #{detail.issuerCode}
            , #{detail.merchantCode}
            , #{detail.outletCode}
            , #{detail.cpgCode}
            , #{detail.activatedAmount}
            , #{detail.purchasedAmount}
            , #{detail.deactivatedAmount}
            , #{detail.expiredAmount}
            , #{detail.totalAmount}
            , #{detail.expiryDate}
            )
        </foreach>

    </insert>
</mapper>