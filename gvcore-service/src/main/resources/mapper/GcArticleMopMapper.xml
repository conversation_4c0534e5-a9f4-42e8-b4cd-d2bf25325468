<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.GcArticleMopMapper">

    <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.GcArticleMop">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="article_mop_code" jdbcType="VARCHAR" property="articleMopCode" />
        <result column="article_code" jdbcType="VARCHAR" property="articleCode" />
        <result column="article_code_name" jdbcType="VARCHAR" property="articleCodeName" />
        <result column="sap_article_code" jdbcType="VARCHAR" property="sapArticleCode" />
        <result column="mop_code" jdbcType="VARCHAR" property="mopCode" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, article_mop_code, article_code, article_code_name, sap_article_code, mop_code,
        status, create_user, create_time, update_user, update_time
    </sql>

    <sql id="gc_article_mop_query_condition">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="articleMopCode != null and articleMopCode.trim().length() != 0">
                AND (article_mop_code = #{articleMopCode})
            </if>
            <if test="articleCode != null and articleCode.trim().length() != 0">
                AND (article_code = #{articleCode})
            </if>
            <if test="articleCodeName != null and articleCodeName.trim().length() != 0">
                AND (article_code_name = #{articleCodeName})
            </if>
            <if test="sapArticleCode != null and sapArticleCode.trim().length() != 0">
                AND (sap_article_code = #{sapArticleCode})
            </if>
            <if test="mopCode != null and mopCode.trim().length() != 0">
                AND (mop_code = #{mopCode})
            </if>
            <if test="status != null">
                AND (status = #{status})
            </if>
            <if test="createUser != null and createUser.trim().length() != 0">
                AND (create_user = #{createUser})
            </if>
            <if test="createTime != null">
                AND (create_time = #{createTime})
            </if>
            <if test="updateUser != null and updateUser.trim().length() != 0">
                AND (update_user = #{updateUser})
            </if>
            <if test="updateTime != null">
                AND (update_time = #{updateTime})
            </if>
        </trim>
    </sql>

    <select id="selectSelective" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM gc_article_mop
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="articleMopCode != null and articleMopCode.trim().length() != 0">
                AND (article_mop_code = #{articleMopCode})
            </if>
            <if test="articleCode != null and articleCode.trim().length() != 0">
                AND (article_code = #{articleCode})
            </if>
            <if test="articleCodeName != null and articleCodeName.trim().length() != 0">
                AND (article_code_name LIKE CONCAT('%',#{articleCodeName},'%'))
            </if>
            <if test="mopCode != null and mopCode.trim().length() != 0">
                AND (mop_code = #{mopCode})
            </if>
            <if test="status != null">
                AND (status = #{status})
            </if>
            <if test="createUser != null and createUser.trim().length() != 0">
                AND (create_user = #{createUser})
            </if>
            <if test="createTime != null">
                AND (create_time = #{createTime})
            </if>
            <if test="updateUser != null and updateUser.trim().length() != 0">
                AND (update_user = #{updateUser})
            </if>
            <if test="updateTime != null">
                AND (update_time = #{updateTime})
            </if>
        </trim>
        ORDER BY id DESC
    </select>

    <select id="queryByArticleMopCodeList" parameterType="list" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM gc_article_mop
        WHERE article_mop_code IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getArticleNameByCpgCode" resultType="com.gtech.gvcore.dao.model.GcArticleMop">
        select gam.id,
        gam.article_mop_code,
        gam.article_code,
        gam.article_code_name,
        gam.sap_article_code,
        gam.mop_code,
        gam.status,
        gam.create_user,
        gam.create_time,
        gam.update_user,
        gam.update_time
        from gc_article_mop as gam
        left join gv_cpg gc on gam.article_mop_code = gc.article_mop_code
        where gc.cpg_code = #{cpgCode}
    </select>
    
</mapper> 