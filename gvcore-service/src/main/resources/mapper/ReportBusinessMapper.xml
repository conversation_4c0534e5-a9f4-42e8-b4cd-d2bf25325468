<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.ReportBusinessMapper">

    <!-- *************************************** GoodsInTransit BEGIN *************************************** -->
    <select id="selectGoodsInTransitSummary" parameterType="com.gtech.gvcore.service.report.impl.param.GoodsInTransitQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.GoodsInTransitSummaryBo">

        SELECT r.voucher_request_code               AS requestId
        , r.voucher_owner_code                 AS outbound
        , r.receiver_code                      AS inbound
        , d.cpg_code                           AS cpgCode
        , SUM(d.voucher_num)                   AS voucherCount
        , SUM(d.voucher_num * d.denomination)  AS amount
        , rc.create_time                       AS allLocationTime
        , inbound_time                         AS inbound_time
        FROM `gv_voucher_request` r
        JOIN gv_voucher_allocation a ON a.business_type != 'customerorder' AND a.source_data_code = r.voucher_request_code
        JOIN gv_voucher_receive rc ON a.voucher_allocation_code = rc.source_data_code
        JOIN gv_voucher_receive_batch d ON rc.voucher_receive_code = d.voucher_receive_code
        LEFT JOIN (
        SELECT MAX(cdi.update_time)     AS inbound_time
        , cdi.voucher_receive_code AS voucher_receive_code
        , cdi.cpg_code             AS cpg_code
        FROM gv_voucher_receive_record cdi
        GROUP BY cdi.voucher_receive_code, cdi.cpg_code
        ) rcd ON rc.voucher_receive_code = rcd.voucher_receive_code AND d.cpg_code = rcd.cpg_code
        WHERE r.status != 2
        <if test="requestIdList != null and requestIdList.size() > 0">           AND r.voucher_request_code IN <foreach open="(" separator="," close=")" collection="requestIdList" item="requestId"> #{requestId} </foreach> </if>
        <if test="cpgCodeList != null and cpgCodeList.size() > 0">               AND d.cpg_code             IN <foreach open="(" separator="," close=")" collection="cpgCodeList" item="cpgCode"> #{cpgCode} </foreach> </if>
        <if test="outboundCodeList != null and outboundCodeList.size() > 0">     AND r.voucher_owner_code   IN <foreach open="(" separator="," close=")" collection="outboundCodeList" item="outboundCode"> #{outboundCode} </foreach> </if>
        <if test="inboundCodeList != null and inboundCodeList.size() > 0">       AND r.receiver_code        IN <foreach open="(" separator="," close=")" collection="inboundCodeList" item="inboundCode" > #{inboundCode} </foreach> </if>
        <if test="transactionDateStart != null and transactionDateEnd != null "> AND rc.create_time         BETWEEN #{transactionDateStart} AND #{transactionDateEnd} </if>
        GROUP BY r.voucher_request_code, r.voucher_owner_code, r.receiver_code, d.cpg_code, rc.create_time
        ORDER BY voucher_request_code DESC
    </select>

    <select id="selectGoodsInTransitDetail" parameterType="com.gtech.gvcore.service.report.impl.param.GoodsInTransitQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.GoodsInTransitDetailBo">

        SELECT r.voucher_request_code   AS requestId
         , rc.voucher_receive_code  AS receiveCode
         , r.voucher_owner_code     AS outbound
         , r.receiver_code          AS inbound
         , d.cpg_code               AS cpgCode
         , d.voucher_start_no       AS voucherStartNo
         , d.voucher_end_no         AS voucherEndNo
         , rc.create_time           AS allLocationTime
        FROM `gv_voucher_request` r
        JOIN gv_voucher_allocation a ON a.business_type != 'customerorder' AND a.source_data_code = r.voucher_request_code
        JOIN gv_voucher_receive rc ON a.voucher_allocation_code = rc.source_data_code
        JOIN gv_voucher_receive_batch d ON rc.voucher_receive_code = d.voucher_receive_code
        WHERE r.status != 2
        <if test="requestIdList != null and requestIdList.size() > 0">           AND r.voucher_request_code IN <foreach open="(" separator="," close=")" collection="requestIdList" item="requestId"> #{requestId} </foreach> </if>
        <if test="cpgCodeList != null and cpgCodeList.size() > 0">               AND d.cpg_code             IN <foreach open="(" separator="," close=")" collection="cpgCodeList" item="cpgCode"> #{cpgCode} </foreach> </if>
        <if test="outboundCodeList != null and outboundCodeList.size() > 0">     AND r.voucher_owner_code   IN <foreach open="(" separator="," close=")" collection="outboundCodeList" item="outboundCode"> #{outboundCode} </foreach> </if>
        <if test="inboundCodeList != null and inboundCodeList.size() > 0">       AND r.receiver_code        IN <foreach open="(" separator="," close=")" collection="inboundCodeList" item="inboundCode" > #{inboundCode} </foreach> </if>
        <if test="transactionDateStart != null and transactionDateEnd != null "> AND rc.create_time         BETWEEN #{transactionDateStart} AND #{transactionDateEnd} </if>
        <if test="voucherCodeNumStart != null">                                  AND d.voucher_end_no       <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
        <if test="voucherCodeNumEnd != null">                                    AND d.voucher_start_no     <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
        ORDER BY voucher_request_code DESC
    </select>

    <select id="selectReceiveTimeByVoucherCodeAndReceiveCode" resultType="com.gtech.gvcore.service.report.impl.bo.GoodsReceiveBo">
        SELECT voucher_start_no     AS voucherStartNo
             , voucher_end_no       AS voucherEndNo
             , update_time          AS receiveDate
        FROM gv_voucher_receive_record
        WHERE voucher_receive_code = #{receiveCode}
    </select>


    <select id="selectGoodsBookletDto" resultType="com.gtech.gvcore.service.report.impl.bo.GoodsBookletBo">
        SELECT booklet_code, voucher_start_no, voucher_end_no FROM gv_voucher_booklet WHERE voucher_start_no <![CDATA[ > ]]> #{voucherStart} AND voucher_end_no <![CDATA[ < ]]> #{voucherEnd}
        UNION ALL (SELECT booklet_code, voucher_start_no, voucher_end_no FROM gv_voucher_booklet WHERE voucher_start_no <![CDATA[ <= ]]> #{voucherStart} ORDER BY voucher_start_no DESC LIMIT 1)
        UNION ALL (SELECT booklet_code, voucher_start_no, voucher_end_no FROM gv_voucher_booklet WHERE voucher_end_no <![CDATA[ >= ]]> #{voucherEnd} ORDER BY voucher_end_no LIMIT 1);
    </select>


    <!-- *************************************** GoodsInTransit END *************************************** -->

    <!-- *************************************** VoucherLifeCycle START *************************************** -->
    <select id="selectVoucherLifeCycleVoucherMovement" resultType="com.gtech.gvcore.service.report.impl.support.life.bo.VoucherLifeCycleMovementBo">

        SELECT r.voucher_request_code  AS requestId
             , r.voucher_owner_code    AS outbound
             , r.receiver_code         AS inbound
             , r.create_time           AS requestTime
             , a.create_time           AS approvedTime
             , rc.create_time          AS allLocationTime
             , rc.voucher_receive_code AS receiverCode
        FROM `gv_voucher_request` r
                 JOIN gv_voucher_allocation a ON a.business_type != 'customerorder' AND a.source_data_code = r.voucher_request_code
            JOIN gv_voucher_receive rc ON a.voucher_allocation_code = rc.source_data_code
            JOIN gv_voucher_receive_batch d ON rc.voucher_receive_code = d.voucher_receive_code
        WHERE r.status != 2
          AND d.voucher_end_no       <![CDATA[ >= ]]> #{voucherCode}
          AND d.voucher_start_no     <![CDATA[ <= ]]> #{voucherCode}
        ORDER BY voucher_request_code DESC
    </select>

    <select id="voucherLifeCycleEexistVoucher" parameterType="com.gtech.gvcore.service.report.impl.param.VoucherLifeCycleQueryData" resultType="java.lang.Integer">
        SELECT 1
        FROM gv_voucher_${tableIndex}
        WHERE voucher_code_num = #{voucherCodeNum}
        LIMIT 1
    </select>

    <select id="selectVoucherLifeCycleReceiveTime" resultType="java.util.Date">
        SELECT MAX(update_time)
        FROM gv_voucher_receive_record
        WHERE voucher_receive_code = #{receiveCode}
          AND voucher_end_no       <![CDATA[ >= ]]> #{voucherCode}
          AND voucher_start_no     <![CDATA[ <= ]]> #{voucherCode}
    </select>


    <select id="selectVoucherLifeCycleTransactionDataByVoucherCodeNum" resultType="com.gtech.gvcore.dao.model.TransactionData">
        SELECT *
        FROM gv_transaction_data_${tableIndex}
        WHERE voucher_code_num = #{voucherCodeNum}
    </select>



    <!-- *************************************** VoucherLifeCycle END *************************************** -->

    <!-- *************************************** VoucherMovement START *************************************** -->
    <select id="selectVoucherMovement" parameterType="com.gtech.gvcore.service.report.impl.param.VoucherMovementSummaryQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.VoucherMovementBo">

        SELECT voucher_code
        , transaction_code
        , transaction_type
        FROM ${tableName}
        WHERE transaction_type IN (10, 20)
        <if test="customerCode != null and customerCode.trim().length() != 0"> AND customer_code = #{customerCode} </if>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="issuerCodeList != null and issuerCodeList.size() != 0"> AND issuer_code IN
            <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="voucherCodeNumStart != null"> AND voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
        <if test="voucherCodeNumEnd != null"> AND voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
    </select>
    <!-- *************************************** VoucherMovement END *************************************** -->

    <!-- *************************************** Deactivated START *************************************** -->
    <select id="selectDeactivated" resultType="com.gtech.gvcore.service.report.impl.bo.DeactivatedBo">

        SELECT t.transaction_code
             , t.voucher_code
             , t.merchant_code
             , t.outlet_code
             , t.cpg_code
             , DATE_FORMAT(t.voucher_effective_date, '%Y-%m-%d %H:%i:%S') AS voucher_effective_date
             , t.denomination
             , DATE_FORMAT(t.transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
             , t.invoice_number
             , t.customer_code
        FROM ${tableName} t
        WHERE t.transaction_type = #{transactionType}
             <if test="transactionDateStart != null and transactionDateEnd != null ">
                 AND (t.transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
             </if>
             <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                 AND t.issuer_code IN
                 <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                     #{item}
                 </foreach>
             </if>
             <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                 AND t.merchant_code IN
                 <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                     #{item}
                 </foreach>
             </if>
             <if test="outletCodeList != null and outletCodeList.size() != 0">
                 AND t.outlet_code IN
                 <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                     #{item}
                 </foreach>
             </if>
             <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                 AND t.cpg_code IN
                 <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                     #{item}
                 </foreach>
             </if>
             <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0"> AND t.invoice_number = #{invoiceNumber} </if>
             <if test="voucherCodeNumStart != null"> AND t.voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
             <if test="voucherCodeNumEnd != null"> AND t.voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
             <if test="voucherEffectiveDateStart != null and voucherEffectiveDateEnd != null">
                 AND t.voucher_effective_date <![CDATA[ >= ]]>  #{voucherEffectiveDateStart}
                 AND t.voucher_effective_date <![CDATA[ < ]]> #{voucherEffectiveDateEnd}
             </if>
    </select>

    <!-- *************************************** Deactivated END *************************************** -->

    <!-- *************************************** BookletInventory START *************************************** -->

    <select id="bookletInventoryDetailed" resultType="com.gtech.gvcore.service.report.impl.bo.BookletInventoryDetailedBo">

        SELECT b.issuer_code
        , v.voucher_owner_code                                         AS outletCode
        , v.cpg_code                                                   AS cpgCode
        , b.booklet_code                                               AS bookletNumber
        , b.voucher_start_no                                           AS startCardNumber
        , b.voucher_end_no                                             AS endCardNumber
        , DATE_FORMAT(v.voucher_effective_date, '%Y-%m-%d %H:%i:%S')   AS effectiveTime
        , b.`status`                                                   AS bookletStatus
        , COUNT(v.voucher_code)                                        AS cardCount
        FROM gv_voucher_booklet b
        LEFT JOIN  gv_voucher v ON v.booklet_code = b.booklet_code
        WHERE 1 = 1
        AND (v.voucher_owner_type = 'outlet' or v.voucher_owner_type = 'warehouse')
        <!-- MER-1892 同步 MER-1485 只要已创建状态的卡券 -->
        AND v.status = '0'
        AND v.mop_code = 'VCR'
        AND v.voucher_status = '1'
        AND v.voucher_effective_date &gt;= now()
        AND v.circulation_status = '3'
        <if test="request.transactionDateStart != null and request.transactionDateEnd != null ">
            AND v.voucher_code IN (
            SELECT DISTINCT td.voucher_code
            FROM gv_transaction_data td
            WHERE (td.transaction_date BETWEEN #{request.transactionDateStart} AND #{request.transactionDateEnd})
            )
        </if>
        <if test=" null != request.issuerCodes and request.issuerCodes.size != 0">
            AND v.issuer_code IN
            <foreach collection="request.issuerCodes" item="issuerCode" open="(" separator="," close=")">
                #{issuerCode}
            </foreach>
        </if>
        <if test=" null != request.outletCodes and request.outletCodes.size != 0">
            AND v.voucher_owner_code IN
            <foreach collection="request.outletCodes" item="outletCode" open="(" separator="," close=")">
                #{outletCode}
            </foreach>
        </if>
        <if test=" null != request.cpgCodes and request.cpgCodes.size != 0">
            AND v.cpg_code IN
            <foreach collection="request.cpgCodes" item="cpgCode" open="(" separator="," close=")">
                #{cpgCode}
            </foreach>
        </if>
        <if test=" request.voucherCodeNumStart != null and request.voucherCodeNumStart != '' ">
            AND v.voucher_code_num <![CDATA[ >= ]]> #{request.voucherCodeNumStart}
        </if>
        <if test=" request.voucherCodeNumEnd != null and request.voucherCodeNumEnd != '' ">
            AND v.voucher_code_num <![CDATA[ <= ]]> #{request.voucherCodeNumEnd}
        </if>
        <if test=" request.startBookletNo != null and request.startBookletNo != '' ">
            AND b.booklet_code <![CDATA[ >= ]]> #{request.startBookletNo}
        </if>
        <if test=" request.endBookletNo != null and request.endBookletNo != '' ">
            AND b.booklet_code <![CDATA[ <= ]]> #{request.endBookletNo}
        </if>
        <if test="request.voucherEffectiveDateStart != null and request.voucherEffectiveDateEnd != null ">
            AND (v.voucher_effective_date BETWEEN #{request.voucherEffectiveDateStart} AND #{request.voucherEffectiveDateEnd})
        </if>
        GROUP BY b.booklet_code
    </select>

    <select id="bookletInventorySummary" resultType="com.gtech.gvcore.service.report.impl.bo.BookletInventorySummaryBo">
        SELECT

        o.outlet_name outletName
        ,o.outlet_code outletCode
        ,i.issuer_name ISSUER
        ,m.merchant_name merchant
        ,c.cpg_name voucherProgramGroup
        ,DATE_FORMAT(v.voucher_effective_date, '%Y-%m-%d %H:%i:%S') expiryDate
        ,t.cpg_type_name cardProgramGroupType
        ,COUNT( distinct(b.booklet_code)) bookletCount
        ,b.`status` bookletStatus
        FROM gv_voucher_booklet b
        LEFT JOIN gv_voucher v ON v.booklet_code = b.booklet_code
        LEFT JOIN gv_cpg c ON v.cpg_code = c.cpg_code
        LEFT JOIN gv_voucher_batch gvb on v.voucher_batch_code = gvb.voucher_batch_code
        LEFT JOIN gv_cpg_type t ON c.cpg_type_code = t.cpg_type_code
        LEFT JOIN gv_outlet o ON v.voucher_owner_code = o.outlet_code
        LEFT JOIN gv_issuer i ON v.issuer_code = i.issuer_code
        LEFT JOIN gv_merchant m ON o.merchant_code = m.merchant_code
        WHERE (v.voucher_owner_type = 'outlet' or v.voucher_owner_type = 'warehouse')
        <!-- MER-1892 同步 MER-1485 只要已创建状态的卡券 -->
        AND v.status = '0'
        AND v.mop_code = 'VCR'
        AND v.voucher_status = '1'
        AND v.voucher_effective_date &gt;= now()
        AND v.circulation_status = '3'
        <if test=" null != request.issuerCodes and request.issuerCodes.size != 0">
            AND v.issuer_code IN
            <foreach collection="request.issuerCodes" item="issuerCode" open="(" separator="," close=")">
                #{issuerCode}
            </foreach>
        </if>
        <if test=" null != request.outletCodes and request.outletCodes.size != 0">
            AND v.voucher_owner_code IN
            <foreach collection="request.outletCodes" item="outletCode" open="(" separator="," close=")">
                #{outletCode}
            </foreach>
        </if>
        <if test="request.voucherEffectiveDateStart != null and request.voucherEffectiveDateEnd != null ">
            AND (v.voucher_effective_date BETWEEN #{request.voucherEffectiveDateStart} AND #{request.voucherEffectiveDateEnd})
        </if>
        <if test=" null != request.cpgCodes and request.cpgCodes.size != 0">
            AND c.cpg_code IN
            <foreach collection="request.cpgCodes" item="cpgCode" open="(" separator="," close=")">
                #{cpgCode}
            </foreach>
        </if>
        <if test=" null != request.bookletStatus and request.bookletStatus.size != 0">
            AND b.`status` IN
            <foreach collection="request.bookletStatus" item="bookletStatus" open="(" separator="," close=")">
                #{bookletStatus}
            </foreach>
        </if>
        GROUP BY b.booklet_code

    </select>

    <!-- *************************************** BookletInventory END *************************************** -->


    <!-- *************************************** CancelRedeem START *************************************** -->

    <select id="selectCancelRedeem" parameterType="com.gtech.gvcore.service.report.impl.param.CancelRedeemQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.CancelRedeemBo">
        SELECT transaction_code
        , transaction_type
        , voucher_code
        , voucher_code_num
        , merchant_code
        , outlet_code
        , cpg_code
        , denomination
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        , invoice_number
        FROM ${tableName}
        WHERE transaction_type IN (1, 11)
        AND success_or_failure = '0'
        <if test=" null != issuerCodeList and issuerCodeList.size != 0">
            <foreach collection="issuerCodeList" item="issuerCode" open=" AND issuer_code IN (" separator="," close=")">
                #{issuerCode}
            </foreach>
        </if>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test=" null != merchantCodeList and merchantCodeList.size != 0">
            <foreach collection="merchantCodeList" item="merchantCode" open=" AND merchant_code IN (" separator="," close=")">
                #{merchantCode}
            </foreach>
        </if>
        <if test=" null != outletCodeList and outletCodeList.size != 0">
            <foreach collection="outletCodeList" item="outletCode" open=" AND outlet_code IN(" separator="," close=")">
                #{outletCode}
            </foreach>
        </if>
        <if test=" null != cpgCodeList and cpgCodeList.size != 0">
            <foreach collection="cpgCodeList" item="cpgCode" open=" AND cpg_code IN (" separator="," close=")">
                #{cpgCode}
            </foreach>

        </if>
        <if test=" null != invoiceNo and '' != invoiceNo"> AND invoice_number = #{invoiceNo} </if>
        <if test=" null != voucherCodeNumStart "> AND voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
        <if test=" null != voucherCodeNumEnd "> AND voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
    </select>

    <!-- *************************************** CancelRedeem END *************************************** -->


    <!-- *************************************** CancelSales START *************************************** -->
    <select id="selectCancelSales" parameterType="com.gtech.gvcore.service.report.impl.param.CancelSalesQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.CancelSalesBo">
        SELECT voucher_code                                                                 AS `voucherCode`
        , transaction_type                                                             AS `transactionType`
        , transaction_code                                                             AS `transactionCode`
        , merchant_code                                                                AS `merchantCode`
        , outlet_code                                                                  AS `outletCode`
        , cpg_code                                                                     AS `cpgCode`
        , denomination                                                                 AS `denomination`
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S')                           AS `transactionDate`
        , invoice_number                                                               AS `invoiceNumber`
        , customer_code                                                                AS `customerCode`
        FROM ${tableName}
        WHERE transaction_type IN (10, 20)
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="issuerCode != null and issuerCode.trim().length() != 0"> AND issuer_code = #{issuerCode} </if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0"> AND invoice_number = #{invoiceNumber} </if>
        <if test="voucherCodeNumStart != null"> AND voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
        <if test="voucherCodeNumEnd != null"> AND voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
        <if test="voucherEffectiveDateStart != null and voucherEffectiveDateEnd != null ">
            AND (voucher_effective_date BETWEEN #{voucherEffectiveDateStart} AND #{voucherEffectiveDateEnd})
        </if>
        <if test="customerType != null and customerType.trim().length() != 0"> AND customer_type = #{customerType} </if>
        <if test="customerCodeList != null and customerCodeList.size() != 0">
            AND customer_code IN
            <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="transactionIdList != null and transactionIdList.size() != 0">
            AND transaction_id IN
            <foreach collection="transactionIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- *************************************** CancelSales END *************************************** -->

    <!-- *************************************** EgvTracking START *************************************** -->

    <select id="selectEgvTrackingVoucher" parameterType="com.gtech.gvcore.service.report.impl.param.EgvTrackingQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.EgvTrackingBo">

        SELECT cpg_code
        , outlet_code
        , voucher_code
        , transaction_code
        , transaction_type
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        FROM ${tableName}
        WHERE transaction_type IN (10, 20)
        AND mop_code = 'VCE'
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
    </select>

    <select id="selectEgvTrackingActivateTransactionDate" resultType="com.gtech.gvcore.service.report.impl.bo.EgvTrackingActivateTransactionBo">

        SELECT voucher_code
        , DATE_FORMAT(MAX(transaction_date), '%Y-%m-%d %H:%i:%S') AS transaction_date
        FROM gv_transaction_data
        WHERE transaction_type IN
        <foreach collection="transactionTypes" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        <if test="voucherCodeList != null and voucherCodeList.size() != 0"> AND voucher_code IN
            <foreach collection="voucherCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectEgvTrackingRedeemTransactionDate" resultType="com.gtech.gvcore.service.report.impl.bo.EgvVoucherTrackingRedeemTransactionBo">

        SELECT voucher_code
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        , transaction_code
        , transaction_type
        FROM gv_transaction_data
        WHERE transaction_type IN
        <foreach collection="transactionTypes" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        <if test="voucherCodeList != null and voucherCodeList.size() != 0"> AND voucher_code IN
            <foreach collection="voucherCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- *************************************** EgvTrackingQ END *************************************** -->

    <!-- *************************************** ExpiryGv START *************************************** -->

    <select id="selectExpiryGv" parameterType="com.gtech.gvcore.service.report.impl.param.ExpiryGvQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.ExpiryGvBo">
        SELECT voucher_code
        , transaction_code
        , transaction_type
        FROM ${tableName}
        WHERE 1 = 1
        <if test="issuerCodeList != null and issuerCodeList.size() != 0">
            AND `issuer_code` IN
            <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND `cpg_code` IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="voucherEffectiveDateStart != null and voucherEffectiveDateEnd != null">
            AND `voucher_effective_date` <![CDATA[ >= ]]>  #{voucherEffectiveDateStart}
            AND `voucher_effective_date` <![CDATA[ <= ]]> #{voucherEffectiveDateEnd}
        </if>

        <!-- customer code 这个字段在该报表只对销售数据筛选有效 非销售数据 则无效 -->
        <if test="customerCode == null or customerCode.trim().length() == 0">
            AND transaction_type IN (10, 20, 1, 11)
        </if>
        <if test="customerCode != null and customerCode.trim().length() != 0">

            AND ((transaction_type IN (10, 20) AND `customer_code` = #{customerCode}) OR transaction_type IN (1, 11) )
        </if>
    </select>
    <!-- *************************************** ExpiryGv END *************************************** -->




    <!-- *************************************** ExpiryGv START *************************************** -->

    <select id="selectExpiryGvOldData" parameterType="com.gtech.gvcore.service.report.impl.param.ExpiryGvQueryData" resultType="String">
        SELECT voucher_code
        FROM ${tableName}

        WHERE
         create_time <![CDATA[ <= ]]> '2024-08-05'
        AND `status` <![CDATA[ <> ]]>  '2'
        <if test="issuerCodeList != null and issuerCodeList.size() != 0">
            AND `issuer_code` IN
            <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND `cpg_code` IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="voucherEffectiveDateStart != null and voucherEffectiveDateEnd != null">
            AND `voucher_effective_date` <![CDATA[ >= ]]>  #{voucherEffectiveDateStart}
            AND `voucher_effective_date` <![CDATA[ <= ]]> #{voucherEffectiveDateEnd}
        </if>
        <if test="customerCode != null and customerCode.trim().length() != 0">

            AND voucher_owner_code = customerCode
        </if>
    </select>
    <!-- *************************************** ExpiryGv END *************************************** -->



    <!-- *************************************** FinanceRetailAdmin SALES END *************************************** -->


    <select id="financeRetailAdminSales" parameterType="com.gtech.gvcore.service.report.impl.param.FinanceRetailAdminSalesQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.FinanceRetailAdminSalesBo">

        SELECT transaction_code
        , merchant_code
        , outlet_code
        , customer_code
        , pos_code
        , cpg_code
        , voucher_code
        , corporate_name
        , customer_first_name
        , customer_last_name
        , email
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        , transaction_type
        , denomination
        , invoice_number
        , response_message
        , transaction_mode
        , reference_number
        , batch_code
        , approve_code
        , voucher_effective_date
        , notes
        FROM ${tableName}
        WHERE success_or_failure = '0'
        AND transaction_type IN (10, 20)
        <if test = "transactionDateStart != null and transactionDateEnd != null ">
            AND (transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test = "invoiceNumber != null and invoiceNumber.trim().length() != 0 ">AND invoice_number = #{invoiceNumber}</if>
        <if test = "merchantCodeList != null and merchantCodeList.size() != 0 ">
            AND merchant_code IN
            <foreach collection = "merchantCodeList" item = "item" open = "( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test = "outletCodeList != null and outletCodeList.size() != 0 ">
            AND outlet_code IN
            <foreach collection = "outletCodeList" item = "item" open = "( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test = "cpgCodeList != null and cpgCodeList.size() != 0 ">
            AND cpg_code IN
            <foreach collection = "cpgCodeList" item = "item" open = "( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test = "voucherCodeNumStart != null ">AND voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart}</if>
        <if test = "voucherCodeNumEnd != null ">AND voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd}</if>

    </select>

    <!-- *************************************** FinanceRetailAdmin SALES END *************************************** -->


    <!-- *************************************** FinanceRetailAdmin REDEEM START *************************************** -->

    <select id="selectFinanceRetailRedeemAdmin" resultType="com.gtech.gvcore.service.report.impl.bo.FinanceRetailAdminRedemptionBo">
        SELECT transaction_code
        , merchant_code
        , outlet_code
        , customer_code
        , pos_code
        , cpg_code
        , voucher_code
        , corporate_name
        , customer_first_name
        , customer_last_name
        , email
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        , transaction_type
        , denomination
        , invoice_number
        , response_message
        , transaction_mode
        , reference_number
        , batch_code
        , approve_code
        , voucher_effective_date
        , notes
        FROM ${tableName} t
        WHERE t.success_or_failure = '0'
        AND t.transaction_type IN (1, 11)
        <if test = "transactionDateStart != null and transactionDateEnd != null ">
            AND (t.transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test = "invoiceNumber != null and invoiceNumber.trim().length() != 0 ">AND t.invoice_number = #{invoiceNumber}</if>
        <if test = "merchantCodeList != null and merchantCodeList.size() != 0 ">
            AND t.merchant_code IN
            <foreach collection = "merchantCodeList" item = "item" open = "( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test = "outletCodeList != null and outletCodeList.size() != 0 ">
            AND t.outlet_code IN
            <foreach collection = "outletCodeList" item = "item" open = "( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test = "cpgCodeList != null and cpgCodeList.size() != 0 ">
            AND t.cpg_code IN
            <foreach collection = "cpgCodeList" item = "item" open = "( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test = "voucherCodeNumStart != null ">AND t.voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart}</if>
        <if test = "voucherCodeNumEnd != null ">AND t.voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd}</if>
    </select>

    <!-- *************************************** FinanceRetailAdmin REDEEM END *************************************** -->

    <!-- *************************************** Inventory START *************************************** -->

    <select id="inventoryDetailed" resultType="com.gtech.gvcore.service.report.impl.bo.InventoryDetailedBo" >
        SELECT v.voucher_owner_code                                         AS outletCode
        , v.issuer_code                                                AS issuerCode
        , v.cpg_code                                                   AS cpgCode
        , v.booklet_code                                               AS bookletNumber
        , v.voucher_code                                               AS cardNumber
        , DATE_FORMAT(v.voucher_effective_date, '%Y-%m-%d %H:%i:%S')   AS voucherEffectiveDate
        , v.`status`                                                   AS status
        , v.`voucher_status`                                           AS voucherStatus
        , v.create_time                                                AS createTime
        , b.`status`                                                   AS bookletStatus
        , v.denomination                                               AS denomination
        FROM gv_voucher v
        LEFT JOIN gv_voucher_booklet b ON v.booklet_code = b.booklet_code
        WHERE 1 = 1
        AND (v.voucher_owner_type = 'outlet' or v.voucher_owner_type = 'warehouse')
        <!-- MER-1485 只要已创建状态的卡券 -->
        AND v.status = '0'
        AND v.mop_code = 'VCR'
        AND v.voucher_status = '1'
        AND v.voucher_effective_date &gt;= now()
        AND v.circulation_status = '3'
        <if test="request.transactionDateStart != null and request.transactionDateEnd != null ">
            AND v.voucher_code IN (SELECT DISTINCT td.voucher_code FROM gv_transaction_data td WHERE (td.transaction_date BETWEEN #{request.transactionDateStart} AND #{request.transactionDateEnd}))
        </if>
        <if test=" null != request.issuerCodes and request.issuerCodes.size != 0">
            AND v.issuer_code IN
            <foreach collection="request.issuerCodes" item="issuerCode" open="(" separator="," close=")">
                #{issuerCode}
            </foreach>
        </if>
        <if test=" null != request.outletCodes and request.outletCodes.size != 0">
            AND v.voucher_owner_code IN
            <foreach collection="request.outletCodes" item="outletCode" open="(" separator="," close=")">
                #{outletCode}
            </foreach>
        </if>
        <if test=" null != request.cpgCodes and request.cpgCodes.size != 0">
            AND v.cpg_code IN
            <foreach collection="request.cpgCodes" item="cpgCode" open="(" separator="," close=")">
                #{cpgCode}
            </foreach>
        </if>
        <if test=" request.voucherCodeNumStart != null and request.voucherCodeNumStart != '' "> AND v.voucher_code_num &gt;= #{request.voucherCodeNumStart} </if>
        <if test=" request.voucherCodeNumEnd != null and request.voucherCodeNumEnd != '' "> AND v.voucher_code_num &lt;= #{request.voucherCodeNumEnd} </if>
        <if test=" request.startBookletNo != null and request.startBookletNo != '' "> AND b.booklet_code &gt;= #{request.startBookletNo} </if>
        <if test=" request.endBookletNo != null and request.endBookletNo != '' "> AND b.booklet_code &lt;= #{request.endBookletNo} </if>
        <if test="request.voucherEffectiveDateStart != null and request.voucherEffectiveDateEnd != null "> AND (v.voucher_effective_date BETWEEN #{request.voucherEffectiveDateStart} AND #{request.voucherEffectiveDateEnd}) </if>
        GROUP BY v.voucher_code,v.booklet_code

    </select>

    <select id="inventorySummary" resultType="com.gtech.gvcore.service.report.impl.bo.InventorySummaryBo" >
        SELECT o.business_outlet_code outletCode
        , o.outlet_name outletName
        , i.issuer_name ISSUER
        , m.merchant_name merchant
        , c.cpg_name voucherProgramGroup
        , DATE_FORMAT(v.voucher_effective_date, '%Y-%m-%d %H:%i:%S') as expiryDate
        , t.cpg_type_name cardProgramGroupType
        , v.`status` cardStatus
        , COUNT( v.voucher_code ) cardsCount
        , COUNT( DISTINCT(b.booklet_code)) bookletCount
        , b.`status` bookletStatus
        FROM gv_voucher v
        LEFT JOIN gv_outlet o ON v.voucher_owner_code = o.outlet_code
        LEFT JOIN gv_issuer i ON v.issuer_code = i.issuer_code
        LEFT JOIN gv_merchant m ON o.merchant_code = m.merchant_code
        LEFT JOIN gv_cpg c ON v.cpg_code = c.cpg_code
        LEFT JOIN gv_cpg_type t ON c.cpg_type_code = t.cpg_type_code
        LEFT JOIN gv_voucher_booklet b ON b.booklet_code = v.booklet_code
        WHERE (v.voucher_owner_type = 'outlet' or v.voucher_owner_type = 'warehouse')
        AND v.circulation_status = '3'
        <!-- MER-1485 只要已创建状态的卡券 -->
        AND v.`status` = '0'
        AND v.mop_code = 'VCR'
        AND v.voucher_status = '1'
        AND v.voucher_effective_date &gt;= now()

        <if test="request.transactionDateStart != null and request.transactionDateEnd != null ">
            AND v.voucher_code IN (SELECT DISTINCT td.voucher_code FROM gv_transaction_data td WHERE (td.transaction_date BETWEEN #{request.transactionDateStart} AND #{request.transactionDateEnd}))
        </if>
        <if test=" null != request.issuerCodes and request.issuerCodes.size != 0">
            AND v.issuer_code IN
            <foreach collection="request.issuerCodes" item="issuerCode" open="(" separator="," close=")">
                #{issuerCode}
            </foreach>
        </if>
        <if test=" null != request.outletCodes and request.outletCodes.size != 0">
            AND v.voucher_owner_code IN
            <foreach collection="request.outletCodes" item="outletCode" open="(" separator="," close=")">
                #{outletCode}
            </foreach>
        </if>
        <if test="request.voucherEffectiveDateStart != null and request.voucherEffectiveDateEnd != null "> AND (v.voucher_effective_date BETWEEN #{request.voucherEffectiveDateStart} AND #{request.voucherEffectiveDateEnd}) </if>
        <if test=" null != request.cpgCodes and request.cpgCodes.size != 0">
            AND c.cpg_code IN
            <foreach collection="request.cpgCodes" item="cpgCode" open="(" separator="," close=")">
                #{cpgCode}
            </foreach>
        </if>
        <if test=" null != request.bookletStatus and request.bookletStatus.size != 0">
            AND b.`status` IN
            <foreach collection="request.bookletStatus" item="bookletStatus" open="(" separator="," close=")">
                #{bookletStatus}
            </foreach>
        </if>
        GROUP BY o.outlet_code, c.cpg_code, cardStatus, expiryDate

    </select>

    <!-- *************************************** Inventory END *************************************** -->


    <!-- *************************************** LatestGvStatus START *************************************** -->
    <select id="latestGvStatusReport" parameterType="com.gtech.gvcore.service.report.impl.param.LatestGvStatusQueryData" resultType="com.gtech.gvcore.dao.model.Voucher">
        SELECT v.voucher_code
             , v.booklet_code
             , v.cpg_code
             , v.voucher_owner_code
             , v.denomination
             , v.voucher_status
             , v.mop_code
             , v.status
             , v.circulation_status
             , v.voucher_effective_date
             , v.create_time
        FROM ${tableName} v
        WHERE
            <!-- 排除还在打印仓的卡券 -->
            !(v.status = 0 and v.mop_code = 'VCR' AND (v.voucher_owner_code = '' OR v.voucher_owner_code IS NULL))
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND v.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            ${appendVoucherStatusSql}
            <if test="voucherCodeNumStart != null"> AND v.voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
            <if test="voucherCodeNumEnd != null"> AND v.voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
            <if test="voucherCodeList != null and voucherCodeList.size() != 0">
                AND v.voucher_code IN
                <foreach collection="voucherCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>
    <!-- *************************************** LatestGvStatus END *************************************** -->


    <!-- *************************************** LiabilityDetail START *************************************** -->

    <select id="liabilityDetailReport" parameterType="com.gtech.gvcore.service.report.impl.param.LiabilityDetailQueryData" resultType="com.gtech.gvcore.dao.model.ReportTempLiabilityDStructure">
        SELECT `id`
        , `issuer_code`
        , `merchant_code`
        , `outlet_code`
        , `cpg_code`
        , `voucher_status`
        , `denomination`
        , `effective_date`
        , `voucher_code_page_index`
        , `voucher_codes`
        FROM   gv_report_temp_liability_d_${tableCode}
        WHERE  1 = 1
        <if test="issuerCode != null and issuerCode.trim().length() != 0"> AND issuer_code = #{issuerCode} </if>
<!--        <if test="merchantCodeList != null and merchantCodeList.size() != 0">-->
<!--            AND merchant_code IN-->
<!--            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="outletCodeList != null and outletCodeList.size() != 0">-->
<!--            AND outlet_code IN-->
<!--            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reportVoucherStatusList != null and reportVoucherStatusList.size() != 0">
            AND voucher_status IN
            <foreach collection="reportVoucherStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="liabilitySummaryReport" parameterType="com.gtech.gvcore.service.report.impl.param.LiabilitySummaryQueryData" resultType="com.gtech.gvcore.dao.model.ReportTempLiabilitySStructure">
        SELECT `id`
        , `issuer_code`
        , `merchant_code`
        , `outlet_code`
        , `cpg_code`
        , `activated_amount`
        , `purchased_amount`
        , `deactivated_amount`
        , `expired_amount`
        , `recently_expired_amount`
        FROM gv_report_temp_liability_s_${tableCode}
        WHERE 1 = 1
        <if test="issuerCode != null and issuerCode.trim().length() != 0"> AND issuer_code = #{issuerCode} </if>
<!--        <if test="merchantCodeList != null and merchantCodeList.size() != 0">-->
<!--            AND merchant_code IN-->
<!--            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="outletCodeList != null and outletCodeList.size() != 0">-->
<!--            AND outlet_code IN-->
<!--            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- *************************************** LiabilityDetail END *************************************** -->


    <!-- *************************************** Partner START *************************************** -->

    <select id="partnerSalesDetail" parameterType="com.gtech.gvcore.service.report.impl.param.PartnerDetailQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.PartnerDetailBo">
        SELECT transaction_code
        , issuer_code
        , transaction_id
        , voucher_code
        , transaction_type
        , merchant_code
        , outlet_code
        , customer_code
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        , pos_code
        , cpg_code
        , invoice_number
        , reference_number
        , approve_code
        , corporate_name
        , customer_first_name
        , customer_last_name
        , email
        FROM ${tableName}
        WHERE success_or_failure = 0
        AND transaction_type IN (10, 20)
        AND customer_code IS NOT NULL
        AND customer_code != ''
        AND outlet_code = #{outletCode}
        <if test="transactionCorporateName != null and transactionCorporateName.trim().length() != 0"> AND corporate_name LIKE CONCAT('%', #{transactionCorporateName}, '%') </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="customerCodeList != null and customerCodeList.size() != 0">
            AND customer_code IN
            <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="voucherCodeNumStart != null"> AND voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
        <if test="voucherCodeNumEnd != null"> AND voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
        <if test="invoiceNumber != null and invoiceNumber.length() != 0"> AND invoice_number = #{invoiceNumber} </if>
        GROUP BY issuer_code, merchant_code, outlet_code, cpg_code, voucher_code
    </select>
    <!-- *************************************** Partner END *************************************** -->


    <!-- *************************************** Redemption START *************************************** -->

    <select id="selectRedemption" parameterType="com.gtech.gvcore.service.report.impl.param.RedemptionQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.RedemptionBo">
        SELECT transaction_code
        , merchant_code
        , outlet_code
        , customer_code
        , pos_code
        , cpg_code
        , voucher_code
        , corporate_name
        , customer_first_name
        , customer_last_name
        , email
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        , transaction_type
        , denomination
        , invoice_number
        , response_message
        , transaction_mode
        , reference_number
        , batch_code
        , approve_code
        , voucher_effective_date
        , notes
        FROM ${tableName} t
        WHERE t.success_or_failure = '0'
        AND t.transaction_type IN (1, 11)
        <if test = "transactionDateStart != null and transactionDateEnd != null ">
            AND (t.transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test = "invoiceNumber != null and invoiceNumber.trim().length() != 0 ">AND t.invoice_number = #{invoiceNumber}</if>
        <if test = "merchantCodeList != null and merchantCodeList.size() != 0 ">
            AND t.merchant_code IN
            <foreach collection = "merchantCodeList" item = "item" open = "( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test = "outletCodeList != null and outletCodeList.size() != 0 ">
            AND t.outlet_code IN
            <foreach collection = "outletCodeList" item = "item" open = "( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test = "cpgCodeList != null and cpgCodeList.size() != 0 ">
            AND t.cpg_code IN
            <foreach collection = "cpgCodeList" item = "item" open = "( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test = "voucherCodeNumStart != null ">AND t.voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart}</if>
        <if test = "voucherCodeNumEnd != null ">AND t.voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd}</if>
    </select>

    <!-- *************************************** Redemption END *************************************** -->


    <!-- *************************************** Reissued START *************************************** -->

    <select id="selectReissued" parameterType="com.gtech.gvcore.service.report.impl.param.ReissuedQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.ReissuedBo">
        SELECT voucher_code
        , merchant_code
        , outlet_code
        , cpg_code
        , voucher_effective_date as effectiveDate
        , denomination
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        , invoice_number
        FROM ${tableName} t
        WHERE t.transaction_type = 18
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (t.transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>

        <if test="merchantCodeList != null and merchantCodeList.size() > 0">
            AND (
            t.merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="nullMerchantCode == true">
                OR t.merchant_code IS NULL
            </if>
            )
        </if>

        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND (
            t.outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="nullOutletCode == true">
                OR t.outlet_code IS NULL
            </if>
            )
        </if>



        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND t.cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0"> AND t.invoice_number = #{invoiceNumber} </if>
        <if test="voucherCodeNumStart != null"> AND t.voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
        <if test="voucherCodeNumEnd != null"> AND t.voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
    </select>

    <!-- *************************************** Reissued END *************************************** -->


    <!-- *************************************** Sales START *************************************** -->

    <select id="salesReport" parameterType="com.gtech.gvcore.service.report.impl.param.SalesDetailedQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.SalesBo">
        SELECT transaction_code
        , transaction_type
        , transaction_id
        , merchant_code
        , bill_number
        , outlet_code
        , cpg_code
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        , voucher_code
        , pos_code
        , denomination
        , response_message
        , invoice_number
        , reference_number
        , approve_code
        FROM ${tableName}
        WHERE transaction_type IN (10, 20,18)
        AND success_or_failure = '0'
        <if test="transactionIdList != null and transactionIdList.size() != 0">
            AND transaction_id IN
            <foreach collection="transactionIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="issuerCode != null and issuerCode.length() != 0">AND issuer_code = #{issuerCode}</if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="invoiceNumber != null and invoiceNumber.length() != 0"> AND invoice_number = #{invoiceNumber} </if>
        <if test="voucherCodeNumStart != null"> AND voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
        <if test="voucherCodeNumEnd != null"> AND voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
        order by create_time, id
    </select>

    <select id="selectSalesIssuerHandlingRemarkByVoucherCode" resultType="com.gtech.gvcore.service.report.impl.bo.SalesVoucherToRemarkBo">
        SELECT voucher_code AS voucherCode, remarks AS remark
        FROM gv_issue_handling ih
                 JOIN (
            SELECT voucher_code, issue_handling_code
            FROM gv_issue_handling_details
            WHERE id IN (
                SELECT MAX(id)
                FROM gv_issue_handling_details
                WHERE voucher_code IN
                    <foreach collection="voucherCodeList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            )
        ) ihd ON ih.issue_handling_code = ihd.issue_handling_code
    </select>

    <select id="selectSalesAnyVoucherCodeByTransactionId" resultType="com.gtech.gvcore.service.report.impl.bo.SalesAnyVoucherCodeBo">
        SELECT transaction_id AS transactionId, any_value(voucher_code) AS voucherCode
        FROM gv_transaction_data
        WHERE transaction_id IN
            <foreach collection="transactionIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP by transaction_id;
    </select>

    <!-- *************************************** Sales END *************************************** -->


    <!-- *************************************** TransactionDetailed START *************************************** -->

    <select id="transactionDetailReport" resultType="com.gtech.gvcore.service.report.impl.bo.TransactionDetailedBo">

        SELECT transaction_type
        , outlet_code
        , voucher_code
        , create_user
        , pos_code
        , merchant_code
        , batch_code
        , login_source
        , denomination
        , actual_outlet
        , forwarding_entity_id
        , response_message
        , transaction_mode
        , customer_salutation
        , customer_first_name
        , customer_last_name
        , mobile
        , invoice_number
        , other_input_parameter
        , create_user
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS `transaction_date`
        FROM ${tableName} t
        WHERE 1 = 1
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test=" voucherCodeNumStart != null "> AND voucher_code_num &gt;= #{voucherCodeNumStart} </if>
        <if test=" voucherCodeNumEnd != null  "> AND voucher_code_num &lt;= #{voucherCodeNumEnd} </if>
        <if test=" expiryStatusStartDate != null and  expiryStatusStartDate != null "> AND transaction_date &gt;= #{expiryStatusStartDate} </if>
        <if test=" expiryStatusEndDate != null and  expiryStatusEndDate != null "> AND transaction_date &lt;= #{expiryStatusEndDate} </if>
        <if test=" voucherEffectiveDateStart != null and  voucherEffectiveDateStart != null "> AND voucher_effective_date &gt;= #{voucherEffectiveDateStart} </if>
        <if test=" voucherEffectiveDateEnd != null and  voucherEffectiveDateEnd != null "> AND voucher_effective_date &lt;= #{voucherEffectiveDateEnd} </if>
        <if test=" transactionStatus != null and transactionStatus != '' "> AND success_or_failure = #{transactionStatus} </if>
        <!-- !customer type 数据库没有存值 -->
        <if test=" customerType != null and customerType != '' "> AND customer_type = #{customerType} </if>
        <if test=" invoiceNumber != null and invoiceNumber != '' "> AND invoice_number = #{invoiceNumber} </if>

        <if test="purchaseOrderVoucherCodeList != null and purchaseOrderVoucherCodeList.size() != 0">
            AND voucher_code IN
            <foreach collection="purchaseOrderVoucherCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="issuerCodes != null and issuerCodes.size() != 0">
            AND issuer_code IN
            <foreach collection="issuerCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="merchantCodes != null and merchantCodes.size() != 0">
            AND merchant_code IN
            <foreach collection="merchantCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodes != null and outletCodes.size() != 0">
            AND outlet_code IN
            <foreach collection="outletCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodes != null and cpgCodes.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="transactionType != null and transactionType.size() != 0">
            AND transaction_type IN
            <foreach collection="transactionType" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customerCodes != null and customerCodes.size() != 0">
            AND customer_code IN
            <foreach collection="customerCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bulkOrderStatusTransactionIdList != null and bulkOrderStatusTransactionIdList.size() != 0">
            AND transaction_id IN
            <foreach collection="bulkOrderStatusTransactionIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- *************************************** TransactionDetailed END *************************************** -->


    <!-- *************************************** Performance START *************************************** -->

    <select id="selectPerformance" parameterType="com.gtech.gvcore.service.report.impl.param.PerformanceQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.PerformanceBo">

        SELECT transaction_code
        , transaction_type
        , issuer_code
        , merchant_code
        , outlet_code
        , cpg_code
        , voucher_code
        , denomination
        FROM ${tableName}
        WHERE transaction_type IN (10, 20, 1, 11)
        AND success_or_failure = '0'
        <if test="issuerCodeList != null and issuerCodeList.size() != 0"> AND issuer_code IN
            <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>

    </select>
    <!-- *************************************** Performance END *************************************** -->



    <!-- *************************************** Reactivated START *************************************** -->

    <select id="selectReactivated" resultType="com.gtech.gvcore.service.report.impl.bo.ReactivatedBo">

        SELECT t.transaction_code
        , t.voucher_code
        , t.merchant_code
        , t.outlet_code
        , t.cpg_code
        , DATE_FORMAT(t.voucher_effective_date, '%Y-%m-%d %H:%i:%S') AS voucher_effective_date
        , t.denomination
        , DATE_FORMAT(t.transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        , t.invoice_number
        , t.customer_code
        FROM ${tableName} t
        WHERE t.transaction_type = 14
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (t.transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="issuerCodeList != null and issuerCodeList.size() != 0">
            AND t.issuer_code IN
            <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND t.merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND t.outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (t.transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND t.cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0"> AND t.invoice_number = #{invoiceNumber} </if>
        <if test="voucherCodeNumStart != null"> AND t.voucher_code_num <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
        <if test="voucherCodeNumEnd != null"> AND t.voucher_code_num <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>
        <if test="voucherEffectiveDateStart != null and voucherEffectiveDateEnd != null">
            AND t.voucher_effective_date <![CDATA[ >= ]]>  #{voucherEffectiveDateStart}
            AND t.voucher_effective_date <![CDATA[ < ]]> #{voucherEffectiveDateEnd}
        </if>
    </select>

    <!-- *************************************** Reactivated END *************************************** -->

    <!-- *************************************** AGING START *************************************** -->
    <select id="selectAgingVoucher" parameterType="com.gtech.gvcore.service.report.impl.param.AgingReportQueryParamData" resultType="com.gtech.gvcore.service.report.impl.bo.AgingBo">

        SELECT customer_code
        , merchant_code
        , voucher_code
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') as transaction_date
        , transaction_code
        , transaction_type
        FROM ${tableName}
        WHERE transaction_type IN (10, 20)
        <if test="issuerCodeList != null and issuerCodeList.size() != 0">AND issuer_code IN
            <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customerCodeList != null and customerCodeList.size() != 0">
            AND customer_code IN
            <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (transaction_date BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
    </select>

    <select id="selectAgingVoucherTransactionData" resultType="com.gtech.gvcore.service.report.impl.bo.AgingVoucherTransactionBoVoucher">

        SELECT merchant_code
        , voucher_code
        , DATE_FORMAT(transaction_date, '%Y-%m-%d %H:%i:%S') AS transaction_date
        , transaction_code
        , transaction_type
        FROM ${tableName}
        WHERE transaction_type IN (1, 11)
        <if test="voucherCodeList != null and voucherCodeList.size() != 0">AND voucher_code IN
            <foreach collection="voucherCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY voucher_code
    </select>
    <!-- *************************************** AGING END *************************************** -->



    <!-- *************************************** VOUCHER_REQUEST_ALLOCATE_RECEIVE START *************************************** -->

    <select id="selectVoucherRequestAllocateReceive" resultType="com.gtech.gvcore.service.report.impl.bo.VoucherRequestAllocateReceiveBo">
        SELECT r.voucher_request_code               AS requestId
        , r.receiver_name                      AS requestSource
        , d.cpg_code                           AS cpgCode
        , d.voucher_num                        AS numberOfVoucher
        , SUM(d.voucher_num * d.denomination)  AS voucherAmount
        , r.create_time                        AS createTime
        , r.create_user                        AS createUser
        , d.booklet_start_no                   AS bookletStartNo
        , d.booklet_end_no                     AS bookletEndNo
        , d.voucher_start_no                   AS voucherStartNo
        , d.voucher_end_no                     AS voucherEndNo
        , r.status                             AS allocateStatus
        FROM `gv_voucher_request` r
        JOIN gv_voucher_request_details rd     ON r.voucher_request_code = rd.voucher_request_code
        LEFT JOIN gv_voucher_allocation a      ON a.source_data_code = r.voucher_request_code
        LEFT JOIN gv_voucher_receive rc        ON a.voucher_allocation_code = rc.source_data_code
        LEFT JOIN gv_voucher_receive_batch d   ON rc.voucher_receive_code = d.voucher_receive_code
        WHERE r.business_type = 'sales'
        <if test="issuerCodeList != null and issuerCodeList.size() != 0">
            <foreach collection="issuerCodeList" item="item" open=" AND r.issuer_code IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="createTimeStart != null and createTimeEnd != null "> AND (r.create_time BETWEEN #{createTimeStart} AND #{createTimeEnd}) </if>
        <if test="requestSourceList != null and requestSourceList.size() != 0">
            <foreach collection="requestSourceList" item="item" open=" AND r.receiver_code IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            <foreach collection="cpgCodeList" item="item" open=" AND rd.cpg_code IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="allocateStatusList != null and allocateStatusList.size() != 0">
            <foreach collection="allocateStatusList" item="item" open=" AND r.status IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="requestNo != null and requestNo.trim().length() != 0"> AND r.voucher_request_code = #{requestNo} </if>
        GROUP BY r.voucher_request_code, r.receiver_code, d.cpg_code, d.booklet_start_no, d.voucher_start_no

    </select>

    <!-- *************************************** VOUCHER_REQUEST_ALLOCATE_RECEIVE END *************************************** -->

    <!-- *************************************** VOUCHER_REQUEST_PRINTING START *************************************** -->

    <select id="selectVoucherPrinting" resultType="com.gtech.gvcore.service.report.impl.bo.VoucherPrintingBo">
        SELECT issuer_code
        , cpg_code
        , purchase_order_no
        , voucher_batch_code
        , create_time
        , create_user
        , printer_code
        , voucher_effective_date
        , booklet_start_no
        , booklet_end_no
        , booklet_num
        , voucher_start_no
        , voucher_end_no
        , voucher_num
        , status
        FROM gv_voucher_batch
        WHERE booklet_start_no IS NOT NULL
        <if test="createTimeBegin != null and createTimeEnd != null "> AND (create_time BETWEEN #{createTimeBegin} AND #{createTimeEnd}) </if>
        <if test="issuerCodeList != null and issuerCodeList.size() != 0">
            <foreach collection="issuerCodeList" item="item" open=" AND issuer_code IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            <foreach collection="cpgCodeList" item="item" open=" AND cpg_code IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="voucherPrintingStatusList != null and voucherPrintingStatusList.size() != 0">
            <foreach collection="voucherPrintingStatusList" item="item" open=" AND status IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test = "printingVendorList != null and printingVendorList.size() != 0">
            <foreach collection="printingVendorList" item="item" open=" AND printer_code IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="effectiveDateBegin != null and effectiveDateEnd != null "> AND (voucher_effective_date BETWEEN #{effectiveDateBegin} AND #{effectiveDateEnd}) </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo.trim().length() != 0"> AND purchase_order_no = #{purchaseOrderNo} </if>
    </select>

    <!-- *************************************** VOUCHER_REQUEST_PRINTING END *************************************** -->

    <!-- *************************************** VOUCHER_RETURN_TRANSFER START *************************************** -->

    <select id="selectReturnTransfer" resultType="com.gtech.gvcore.service.report.impl.bo.VoucherReturnAndTransferBo">

        SELECT r.voucher_request_code               AS requestId
        , r.voucher_owner_name                 AS fromStoreName
        , r.receiver_name                      AS toStoreName
        , d.cpg_code                           AS cpgCode
        , d.voucher_num                        AS numberOfVoucher
        , SUM(d.voucher_num * d.denomination)  AS voucherAmount
        , r.create_time                        AS createTime
        , r.create_user                        AS createUser
        , d.booklet_start_no                   AS bookletStartNo
        , d.booklet_end_no                     AS bookletEndNo
        , d.voucher_start_no                   AS voucherStartNo
        , d.voucher_end_no                     AS voucherEndNo
        , r.status                             AS `status`
        , r.business_type                      AS businessType
        FROM `gv_voucher_request` r
        JOIN gv_voucher_request_details rd on r.voucher_request_code = rd.voucher_request_code
        LEFT JOIN gv_voucher_allocation a ON a.source_data_code = r.voucher_request_code
        LEFT JOIN gv_voucher_receive rc ON a.voucher_allocation_code = rc.source_data_code
        LEFT JOIN gv_voucher_receive_batch d ON rc.voucher_receive_code = d.voucher_receive_code
        WHERE

        <if test="findPendingTransfer and !findPendingReturn">
            r.business_type = 'transfer'
        </if>
        <if test="findPendingReturn and !findPendingTransfer">
            r.business_type = 'return'
        </if>
        <if test="findPendingTransfer == findPendingReturn">
            r.business_type IN ('transfer', 'return')
        </if>

        <if test="issuerCodeList != null and issuerCodeList.size() != 0">
            <foreach collection="issuerCodeList" item="item" open=" AND r.issuer_code IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="createTimeStart != null and createTimeEnd != null "> AND (r.create_time BETWEEN #{createTimeStart} AND #{createTimeEnd}) </if>
        <if test="requestId != null and requestId.trim().length() != 0"> AND r.voucher_request_code = #{requestId} </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            <foreach collection="cpgCodeList" item="item" open=" AND rd.cpg_code IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="(voucherReturnAndTransferStatusList != null and voucherReturnAndTransferStatusList.size() != 0)">
            <foreach collection="voucherReturnAndTransferStatusList" item="item" open=" AND r.status IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="fromStoreCodeList != null and fromStoreCodeList.size() != 0">
            <foreach collection="fromStoreCodeList" item="item" open=" AND r.voucher_owner_code IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="toStoreCodeList != null and toStoreCodeList.size() != 0">
            <foreach collection="toStoreCodeList" item="item" open=" AND r.receiver_code IN (" separator="," close=")">
                #{item}
            </foreach>
        </if>

        GROUP BY r.voucher_request_code, r.receiver_code, d.cpg_code, d.booklet_start_no, d.voucher_start_no

    </select>

    <!-- *************************************** VOUCHER_RETURN_TRANSFER END *************************************** -->

    <!-- *************************************** ? START *************************************** -->
    <!-- *************************************** ? END *************************************** -->
</mapper>