<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.ApproveNodeRecordMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.ApproveNodeRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="approve_node_record_code" jdbcType="VARCHAR" property="approveNodeRecordCode" />
    <result column="release_approve_amount_type" jdbcType="VARCHAR" property="releaseApproveAmountType" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="release_approve_node_name" jdbcType="INTEGER" property="releaseApproveNodeName" />
    <result column="approve_user" jdbcType="VARCHAR" property="approveUser" />
    <result column="approve_role_code" jdbcType="VARCHAR" property="approveRoleCode" />
    <result column="next_role_code" jdbcType="VARCHAR" property="nextRoleCode" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, approve_node_record_code, release_approve_amount_type, business_code, note, status, 
    release_approve_node_name, approve_user, approve_role_code, next_role_code, delete_status, 
    create_time, create_user, update_time, update_user, issuer_code
  </sql>
  <sql id="gv_approve_node_record_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="approveNodeRecordCode != null and approveNodeRecordCode.trim().length() != 0">
        AND (approve_node_record_code = #{approveNodeRecordCode})
      </if>
      <if test="releaseApproveAmountType != null and releaseApproveAmountType.trim().length() != 0">
        AND (release_approve_amount_type = #{releaseApproveAmountType})
      </if>
      <if test="businessCode != null and businessCode.trim().length() != 0">
        AND (business_code = #{businessCode})
      </if>
      <if test="note != null and note.trim().length() != 0">
        AND (note = #{note})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="releaseApproveNodeName != null">
        AND (release_approve_node_name = #{releaseApproveNodeName})
      </if>
      <if test="approveUser != null and approveUser.trim().length() != 0">
        AND (approve_user = #{approveUser})
      </if>
      <if test="approveRoleCode != null and approveRoleCode.trim().length() != 0">
        AND (approve_role_code = #{approveRoleCode})
      </if>
      <if test="nextRoleCode != null and nextRoleCode.trim().length() != 0">
        AND (next_role_code = #{nextRoleCode})
      </if>
      <if test="deleteStatus != null">
        AND (delete_status = #{deleteStatus})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
    </trim>
  </sql>

    <select id="selectByBusinessAndType" resultType="com.gtech.gvcore.dao.model.ApproveNodeRecord">
        select
        <include refid="Base_Column_List"/>
        from gv_approve_node_record
        where release_approve_amount_type = #{releaseApproveType01}
        and business_code = #{businessCode}
        AND delete_status = #{deleteStatus} 
        ORDER BY id 
    </select>

    <select id="selectNewNote" resultType="com.gtech.gvcore.dao.model.ApproveNodeRecord">
        select
        <include refid="Base_Column_List"/>
        from gv_approve_node_record
        where business_code = #{voucherRequestCode}
        and release_approve_amount_type = #{approveType}
        AND delete_status = 0 
        order by create_time desc
        limit 1
    </select>

    <select id="selectNewNoteVCE" resultType="com.gtech.gvcore.dao.model.ApproveNodeRecord">
        select ganr.id,
               ganr.approve_node_record_code,
               ganr.release_approve_amount_type,
               ganr.business_code,
               ganr.note,
               ganr.status,
               ganr.release_approve_node_name,
               ganr.approve_user,
               ganr.approve_role_code,
               ganr.next_role_code,
               ganr.create_time,
               ganr.create_user,
               ganr.update_time,
               ganr.update_user,
               ganr.issuer_code
        from gv_voucher as gv
                left join gv_customer_order gco on gv.voucher_batch_code = gco.voucher_batch_code
                left join gv_approve_node_record as ganr on gco.customer_order_code = ganr.business_code
        where gv.voucher_code = #{voucherCode}
          and ganr.release_approve_amount_type = #{type}
          AND ganr.delete_status = 0

        ORDER BY
            ganr.create_time DESC
            LIMIT 1
    </select>
    <select id="existRelease" resultType="java.lang.Boolean">
        select count(1)
        from gv_approve_node_record
        where business_code=#{customerOrderCode} and release_approve_amount_type='Release Note' and next_role_code is not null and next_role_code !=''
          AND delete_status = 0 
    </select>
    <select id="existApprove" resultType="java.lang.Boolean">
        select count(1)
        from gv_approve_node_record
        where business_code=#{voucherRequestCode} and release_approve_amount_type='Approve Note' and next_role_code is not null and next_role_code !=''
          AND delete_status = 0 
    </select>
  
  <update id="updateByDeleted" parameterType="com.gtech.gvcore.dao.model.ApproveNodeRecord">
    UPDATE gv_approve_node_record 
       SET delete_status = #{deleteStatus},
           update_user = #{updateUser},
           update_time = #{updateTime} 
    WHERE business_code = #{businessCode} 
      AND release_approve_amount_type = #{releaseApproveAmountType} 
      AND delete_status &lt;&gt; #{deleteStatus} 
  </update>

</mapper>