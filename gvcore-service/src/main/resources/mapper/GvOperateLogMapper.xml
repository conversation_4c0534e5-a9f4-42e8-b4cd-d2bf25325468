<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.GvOperateLogMapper">

    <resultMap type="com.gtech.gvcore.dao.model.GvOperateLogEntity" id="GvOperateLogMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="businessCode" column="business_code" jdbcType="VARCHAR"/>
        <result property="input" column="input" jdbcType="VARCHAR"/>
        <result property="output" column="output" jdbcType="VARCHAR"/>
        <result property="method" column="method" jdbcType="VARCHAR"/>
        <result property="operateUser" column="operate_user" jdbcType="VARCHAR"/>
        <result property="firstName" column="first_name" jdbcType="VARCHAR"/>
        <result property="lastName" column="last_name" jdbcType="VARCHAR"/>
        <result property="success" column="success" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>
</mapper>

