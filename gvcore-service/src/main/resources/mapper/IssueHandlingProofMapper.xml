<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.IssueHandlingProofMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.IssueHandlingProof">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="issue_handling_proof_code" jdbcType="VARCHAR" property="issueHandlingProofCode" />
    <result column="issue_handling_code" jdbcType="VARCHAR" property="issueHandlingCode" />
    <result column="proof_file_name" jdbcType="VARCHAR" property="proofFileName" />
    <result column="proof_file_url" jdbcType="VARCHAR" property="proofFileUrl" />
    <result column="permission_code" jdbcType="VARCHAR" property="permissionCode" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, issue_handling_proof_code, issue_handling_code, proof_file_name, proof_file_url, 
    permission_code, create_user, create_time, update_user, update_time
  </sql>
  <sql id="gv_issue_handling_proof_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="issueHandlingProofCode != null and issueHandlingProofCode.trim().length() != 0">
        AND (issue_handling_proof_code = #{issueHandlingProofCode})
      </if>
      <if test="issueHandlingCode != null and issueHandlingCode.trim().length() != 0">
        AND (issue_handling_code = #{issueHandlingCode})
      </if>
      <if test="proofFileName != null and proofFileName.trim().length() != 0">
        AND (proof_file_name = #{proofFileName})
      </if>
      <if test="proofFileUrl != null and proofFileUrl.trim().length() != 0">
        AND (proof_file_url = #{proofFileUrl})
      </if>
      <if test="permissionCode != null and permissionCode.trim().length() != 0">
        AND (permission_code = #{permissionCode})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from gv_issue_handling_proof 
    <include refid="gv_issue_handling_proof_query_condition" />
    <if test="order and orderStr == null">
      order by id desc
    </if>
  </select>
  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true">
    insert into gv_issue_handling_proof ( 
    id,issue_handling_proof_code,issue_handling_code,proof_file_name,proof_file_url,permission_code,create_user,create_time,update_user,update_time ) values 
    <foreach collection="list" index="index" item="item" separator=",">
       ( #{item.id,jdbcType=BIGINT},#{item.issueHandlingProofCode,jdbcType=VARCHAR},#{item.issueHandlingCode,jdbcType=VARCHAR},#{item.proofFileName,jdbcType=VARCHAR},#{item.proofFileUrl,jdbcType=VARCHAR},#{item.permissionCode,jdbcType=VARCHAR},#{item.createUser,jdbcType=VARCHAR},#{item.createTime,jdbcType=TIMESTAMP},#{item.updateUser,jdbcType=VARCHAR},#{item.updateTime,jdbcType=TIMESTAMP} ) 
    </foreach>
  </insert>
  <select id="count" resultType="int">
    select count(*) from gv_issue_handling_proof 
    <include refid="gv_issue_handling_proof_query_condition" />
  </select>
  <select id="selectSysDate" resultType="java.util.Date">
     SELECT NOW() 
  </select>
  <update id="updateStatusByPrimaryKey" parameterType="Map">
    update gv_issue_handling_proof 
     set STATUS = #{status,jdbcType=INTEGER},UPDATE_TIME=SYSDATE() 
    where  id = #{id,jdbcType=BIGINT}
    <if test="oldStatus != null">
       and STATUS = #{oldStatus,jdbcType=INTEGER} 
    </if>
  </update>
</mapper>