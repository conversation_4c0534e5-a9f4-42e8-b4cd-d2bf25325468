<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.ReleaseApproveNodeMapper">
    <delete id="deleteAll">
        delete from gv_release_approve_node where 1=1
    </delete>

    <select id="selectByAmountCodeAndSort" resultType="com.gtech.gvcore.dao.model.ReleaseApproveNode">
        select node_name, role_code
        from gv_release_approve_node
        where release_approve_amount_code = #{releaseApproveAmountCode}
        order by node_name
    </select>

    <select id="selectNodesByAmountAndType" resultType="com.gtech.gvcore.dao.model.ReleaseApproveNode">
        select gran.id,
               gran.release_approve_node_code,
               gran.release_approve_amount_code,
               gran.node_name,
               gran.role_code,
               gran.create_time,
               gran.create_user,
               gran.update_time,
               gran.update_user,
               gran.issuer_code
        from gv_release_approve_amount graa
                 left join gv_release_approve_node gran
                            on graa.release_approve_amount_code = gran.release_approve_amount_code
        where graa.type = #{releaseApproveType}
          and graa.start_num &lt;= #{voucherAmount}
          and graa.end_num &gt;= #{voucherAmount}
          and graa.issuer_code = #{issuerCode}
        ORDER BY gran.node_name 
    </select>
</mapper>