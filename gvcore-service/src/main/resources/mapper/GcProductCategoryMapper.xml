<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.GcProductCategoryMapper">

    <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.GcProductCategory">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="product_category_code" jdbcType="VARCHAR" property="productCategoryCode" />
        <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
        <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
        -->
        id, product_category_code, category_name, issuer_code, remarks, status, create_user,
        create_time, update_user, update_time
    </sql>


    <select id="selectSelective" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM gc_product_category
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="productCategoryCode != null and productCategoryCode.trim().length() != 0">
                AND product_category_code = #{productCategoryCode}
            </if>
            <if test="categoryName != null and categoryName.trim().length() != 0">
                AND category_name LIKE CONCAT('%',#{categoryName},'%')
            </if>
            <if test="issuerCode != null and issuerCode.trim().length() != 0">
                AND issuer_code = #{issuerCode}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="createUser != null and createUser.trim().length() != 0">
                AND create_user = #{createUser}
            </if>
            <if test="updateUser != null and updateUser.trim().length() != 0">
                AND update_user = #{updateUser}
            </if>
        </trim>
        ORDER BY id DESC
    </select>
</mapper> 