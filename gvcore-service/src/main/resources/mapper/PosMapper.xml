<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.PosMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.Pos">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pos_code" jdbcType="VARCHAR" property="posCode" />
    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
    <result column="pos_name" jdbcType="VARCHAR" property="posName" />
    <result column="machine_id" jdbcType="VARCHAR" property="machineId" />
    <result column="pos_entry_mode_Id" jdbcType="VARCHAR" property="posEntryModeId" />
    <result column="outlet_code" jdbcType="VARCHAR" property="outletCode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, pos_code, issuer_code, pos_name, machine_id, pos_entry_mode_Id, outlet_code, 
    status, create_user, create_time, update_user, update_time
  </sql>
  <sql id="gv_pos_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="posCode != null and posCode.trim().length() != 0">
        AND (pos_code = #{posCode})
      </if>
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
      <if test="posName != null and posName.trim().length() != 0">
        AND (pos_name = #{posName})
      </if>
      <if test="machineId != null and machineId.trim().length() != 0">
        AND (machine_id = #{machineId})
      </if>
      <if test="posEntryModeId != null and posEntryModeId.trim().length() != 0">
        AND (pos_entry_mode_Id = #{posEntryModeId})
      </if>
      <if test="outletCode != null and outletCode.trim().length() != 0">
        AND (outlet_code = #{outletCode})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  
  <select id="queryByPosCodeList" parameterType="list" resultMap="BaseResultMap">
  	SELECT <include refid="Base_Column_List" /> 
  	FROM gv_pos 
  	WHERE pos_code IN 
  	<foreach collection="list" item="item" open="(" separator="," close=")">
  		#{item}
  	</foreach>
  </select>


    <select id="queryPosList" resultType="com.gtech.gvcore.common.response.pos.PosResponse">
        SELECT p.pos_code, p.issuer_code, p.pos_name, p.machine_id, p.pos_entry_mode_id, p.outlet_code, p.`status`,
        p.create_user, p.create_time, p.update_user, p.update_time, p.account, p.PASSWORD, m.merchant_name FROM gv_pos p
        LEFT JOIN gv_outlet o ON p.outlet_code = o.outlet_code LEFT JOIN gv_merchant m ON m.merchant_code =
        o.merchant_code
        <where>
            <if test=" request.posName != null and request.posName != '' ">
                AND p.pos_name LIKE CONCAT('%',#{request.posName},'%')
            </if>
            <if test=" request.status != null and request.status != '' ">
                AND p.`status` = #{request.status}
            </if>

            <if test="request.outletCodes != null and request.outletCodes.size() != 0">
                AND p.outlet_code IN
                <foreach collection="request.outletCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.merchantCodes != null and request.merchantCodes.size() != 0">
                AND m.merchant_code IN
                <foreach collection="request.merchantCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>


        </where>
        ORDER BY m.merchant_name
    </select>
</mapper>