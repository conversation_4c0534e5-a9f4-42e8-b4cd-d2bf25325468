<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.IssueHandlingMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.IssueHandling">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="issue_handling_code" jdbcType="VARCHAR" property="issueHandlingCode" />
    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
    <result column="issue_type" jdbcType="VARCHAR" property="issueType" />
    <result column="uploaded_file_type" jdbcType="VARCHAR" property="uploadedFileType" />
    <result column="uploaded_file_name" jdbcType="VARCHAR" property="uploadedFileName" />
    <result column="uploaded_file_url" jdbcType="VARCHAR" property="uploadedFileUrl" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="count_voucher" jdbcType="INTEGER" property="countVoucher" />
    <result column="process_status" jdbcType="INTEGER" property="processStatus" />
    <result column="count_failed" jdbcType="INTEGER" property="countFailed" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="create_user_email" jdbcType="VARCHAR" property="createUserEmail" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="permission_code" jdbcType="VARCHAR" property="permissionCode" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, issue_handling_code, issuer_code, issue_type, uploaded_file_type, uploaded_file_name, 
    uploaded_file_url, remarks, count_voucher, process_status, count_failed, result, 
    create_user_email, status, permission_code, create_user, create_time, update_user, 
    update_time
  </sql>
  <sql id="gv_issue_handling_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="issueHandlingCode != null and issueHandlingCode.trim().length() != 0">
        AND (issue_handling_code = #{issueHandlingCode})
      </if>
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
      <if test="issueType != null and issueType.trim().length() != 0">
        AND (issue_type = #{issueType})
      </if>
      <if test="uploadedFileType != null and uploadedFileType.trim().length() != 0">
        AND (uploaded_file_type = #{uploadedFileType})
      </if>
      <if test="uploadedFileName != null and uploadedFileName.trim().length() != 0">
        AND (uploaded_file_name = #{uploadedFileName})
      </if>
      <if test="uploadedFileUrl != null and uploadedFileUrl.trim().length() != 0">
        AND (uploaded_file_url = #{uploadedFileUrl})
      </if>
      <if test="remarks != null and remarks.trim().length() != 0">
        AND (remarks = #{remarks})
      </if>
      <if test="countVoucher != null">
        AND (count_voucher = #{countVoucher})
      </if>
      <if test="processStatus != null">
        AND (process_status = #{processStatus})
      </if>
      <if test="countFailed != null">
        AND (count_failed = #{countFailed})
      </if>
      <if test="result != null and result.trim().length() != 0">
        AND (result = #{result})
      </if>
      <if test="createUserEmail != null and createUserEmail.trim().length() != 0">
        AND (create_user_email = #{createUserEmail})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="permissionCode != null and permissionCode.trim().length() != 0">
        AND (permission_code = #{permissionCode})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  
  <update id="updateStatus" parameterType="com.gtech.gvcore.dao.dto.IssueHandlingDto">
  	UPDATE gv_issue_handling 
  	   SET status = #{status},
  	   	   <if test="processStatus != null">
  	   	   	process_status = #{processStatus},
  	   	   </if>
  	   	   <if test="countFailed != null">
			count_failed = #{countFailed},
  	   	   </if>
          <if test="approveNotes != null and approveNotes != '' ">
              approve_notes = #{approveNotes},
          </if>
  	   	   update_user = #{updateUser},
  	   	   update_time = #{updateTime}


  	WHERE issue_handling_code = #{issueHandlingCode}
  	  AND status = #{oldStatus} 
  	  <if test="processStatus != null">
		AND process_status = #{oldProcessStatus}
  	  </if>
  </update>
  
  <select id="selectSelective" parameterType="com.gtech.gvcore.dao.dto.IssueHandlingDto" resultMap="BaseResultMap">
  	SELECT <include refid="Base_Column_List"/> 
  	FROM gv_issue_handling 
  	<trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="issueHandlingCode != null and issueHandlingCode.trim().length() != 0">
        AND (issue_handling_code = #{issueHandlingCode})
      </if>
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
      <if test="issueType != null and issueType.trim().length() != 0">
        AND (issue_type = #{issueType})
      </if>
      <if test="uploadedFileType != null and uploadedFileType.trim().length() != 0">
        AND (uploaded_file_type = #{uploadedFileType})
      </if>
      <if test="uploadedFileName != null and uploadedFileName.trim().length() != 0">
        AND (uploaded_file_name = #{uploadedFileName})
      </if>
      <if test="uploadedFileUrl != null and uploadedFileUrl.trim().length() != 0">
        AND (uploaded_file_url = #{uploadedFileUrl})
      </if>
      <if test="remarks != null and remarks.trim().length() != 0">
        AND (remarks = #{remarks})
      </if>
      <if test="countVoucher != null">
        AND (count_voucher = #{countVoucher})
      </if>
      <if test="processStatus != null">
        AND (process_status = #{processStatus})
      </if>
      <if test="countFailed != null">
        AND (count_failed = #{countFailed})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="createTimeStart != null and createTimeEnd != null">
        AND create_time BETWEEN #{createTimeStart} AND #{createTimeEnd} 
      </if>
    </trim>
    ORDER BY id DESC 
  </select>
  
  <update id="updateByEdit" parameterType="com.gtech.gvcore.dao.dto.IssueHandlingDto">
    UPDATE gv_issue_handling 
       SET uploaded_file_name = #{uploadedFileName},
           uploaded_file_url = #{uploadedFileUrl},
           remarks = #{remarks},
           count_voucher = #{countVoucher},
           status = #{status}, 
           update_user = #{updateUser},
           update_time = #{updateTime} 
    WHERE issue_handling_code = #{issueHandlingCode}
      AND status = #{oldStatus} 
  </update>
  
</mapper>