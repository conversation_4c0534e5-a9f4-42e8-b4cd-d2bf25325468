<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.CpgMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.Cpg">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cpg_code" jdbcType="VARCHAR" property="cpgCode" />
    <result column="cpg_name" jdbcType="VARCHAR" property="cpgName" />
    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
    <result column="cpg_type_code" jdbcType="VARCHAR" property="cpgTypeCode" />
    <result column="grace_periods" jdbcType="INTEGER" property="gracePeriods" />
    <result column="effective_years" jdbcType="INTEGER" property="effectiveYears" />
    <result column="effective_month" jdbcType="INTEGER" property="effectiveMonth" />
    <result column="effective_day" jdbcType="INTEGER" property="effectiveDay" />
    <result column="effective_hour" jdbcType="INTEGER" property="effectiveHour" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="denomination" jdbcType="DECIMAL" property="denomination" />
    <result column="article_mop_code" jdbcType="VARCHAR" property="articleMopCode" />
    <result column="booklet_voucher_num" jdbcType="INTEGER" property="bookletVoucherNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, cpg_code, cpg_name, issuer_code, cpg_type_code, grace_periods, 
    effective_years, effective_month, effective_day, effective_hour, currency_code, denomination, 
    article_mop_code, booklet_voucher_num, status, create_user, create_time, update_user, 
    update_time , disable_generation
  </sql>
  <sql id="gv_cpg_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="cpgCode != null and cpgCode.trim().length() != 0">
        AND (cpg_code = #{cpgCode})
      </if>
      <if test="cpgName != null and cpgName.trim().length() != 0">
        AND (cpg_name = #{cpgName})
      </if>
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
      <if test="cpgTypeCode != null and cpgTypeCode.trim().length() != 0">
        AND (cpg_type_code = #{cpgTypeCode})
      </if>
      <if test="gracePeriods != null">
        AND (grace_periods = #{gracePeriods})
      </if>
      <if test="effectiveYears != null">
        AND (effective_years = #{effectiveYears})
      </if>
      <if test="effectiveMonth != null">
        AND (effective_month = #{effectiveMonth})
      </if>
      <if test="effectiveDay != null">
        AND (effective_day = #{effectiveDay})
      </if>
      <if test="effectiveHour != null">
        AND (effective_hour = #{effectiveHour})
      </if>
      <if test="currencyCode != null and currencyCode.trim().length() != 0">
        AND (currency_code = #{currencyCode})
      </if>
      <if test="denomination != null">
        AND (denomination = #{denomination})
      </if>
      <if test="articleMopCode != null and articleMopCode.trim().length() != 0">
        AND (article_mop_code = #{articleMopCode})
      </if>
      <if test="bookletVoucherNum != null">
        AND (booklet_voucher_num = #{bookletVoucherNum})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>

    <select id="selectSelective" parameterType="com.gtech.gvcore.dao.dto.CpgDto" resultMap="BaseResultMap">
        SELECT cpg.id, cpg.cpg_code, cpg.cpg_name, cpg.issuer_code, cpg.cpg_type_code, cpg.grace_periods, 
            cpg.effective_years, cpg.effective_month, cpg.effective_day, cpg.effective_hour, cpg.currency_code, cpg.denomination, 
            cpg.article_mop_code, cpg.booklet_voucher_num, cpg.status, cpg.create_user, cpg.create_time, cpg.update_user, cpg.update_time, disable_generation
        FROM gv_cpg cpg 
        <if test="mopCode != null and mopCode.trim().length() != 0">
        LEFT JOIN gv_article_mop mop ON cpg.article_mop_code = mop.article_mop_code 
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="issuerCode != null and issuerCode.trim().length() != 0">
                AND issuer_code = #{issuerCode} 
            </if>
            <if test="cpgCode != null and cpgCode.trim().length() != 0">
                AND cpg_code = #{cpgCode}
            </if>
            <if test="cpgName != null and cpgName.trim().length() != 0">
                AND cpg_name LIKE CONCAT('%',#{cpgName},'%')
            </if>
            <if test="cpgTypeCode != null and cpgTypeCode.trim().length() != 0">
                AND cpg_type_code = #{cpgTypeCode}
            </if>
            <if test="gracePeriods != null">
                AND grace_periods = #{gracePeriods}
            </if>
            <if test="effectiveYears != null">
                AND effective_years = #{effectiveYears}
            </if>
            <if test="effectiveMonth != null">
                AND effective_month = #{effectiveMonth}
            </if>
            <if test="effectiveDay != null">
                AND effective_day = #{effectiveDay}
            </if>
            <if test="currencyCode != null and currencyCode.trim().length() != 0">
                AND currency_code = #{currencyCode}
            </if>
            <if test="denomination != null">
                AND denomination = #{denomination}
            </if>
            <if test="articleMopCode != null and articleMopCode.trim().length() != 0">
                AND article_mop_code = #{articleMopCode}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="createUser != null and createUser.trim().length() != 0">
                AND create_user = #{createUser}
            </if>
            <if test="createTime != null">
                AND create_time = #{createTime}
            </if>
            <if test="updateUser != null and updateUser.trim().length() != 0">
                AND update_user = #{updateUser}
            </if>
            <if test="updateTime != null">
                AND update_time = #{updateTime}
            </if>
            <if test="mopCode != null and mopCode.trim().length() != 0">
        		AND mop.mop_code = #{mopCode} 
      		</if>
            <if test="disableGeneration != null and disableGeneration.trim().length() != 0">
                AND disable_generation = #{disableGeneration}
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() > 0">
                AND issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
        ORDER BY id DESC
    </select>


    <select id="queryByCpgCodeList" parameterType="list" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM gv_cpg
        WHERE cpg_code IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectDenomination"
            resultType="com.gtech.gvcore.common.response.cpg.QueryCpgByOutletCodeResponse">
        select goc.cpg_code, cpg_name, denomination
        from gv_outlet_cpg as goc
              join gv_cpg as gc on goc.cpg_code = gc.cpg_code
              join gv_article_mop as gam on gc.article_mop_code = gam.article_mop_code and gam.mop_code = 'VCR'
        where goc.outlet_code = #{outletCode}
        ORDER by denomination
    </select>
    
    <select id="queryAutomaticActivateCpg" resultType="string">
        SELECT cpg.cpg_code 
        FROM gv_cpg cpg 
        LEFT JOIN gv_cpg_type cpgType ON cpgType.cpg_type_code = cpg.cpg_type_code
        WHERE cpg.cpg_code IN
          <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
          AND cpgType.automatic_activate = #{automaticActivate}
          AND cpg.issuer_code = #{issuerCode} 
    </select>

</mapper>