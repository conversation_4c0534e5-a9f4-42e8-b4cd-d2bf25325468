<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.FlowNoticeMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.FlowNotice">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="flow_code" jdbcType="VARCHAR" property="flowCode" />
    <result column="flow_node_code" jdbcType="VARCHAR" property="flowNodeCode" />
    <result column="flow_notice_type" jdbcType="INTEGER" property="flowNoticeType" />
    <result column="flow_notice_code" jdbcType="VARCHAR" property="flowNoticeCode" />
    <result column="flow_notice_code_type" jdbcType="INTEGER" property="flowNoticeCodeType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, flow_code, flow_node_code, flow_notice_type, flow_notice_code, 
    flow_notice_code_type, status, create_time, update_time, create_user, update_user
  </sql>
  <sql id="gv_flow_notice_query_fuzzy_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="flowCode != null and flowCode.trim().length() != 0">
         AND (flow_code like concat('%',#{flowCode},'%')) 
      </if>
      <if test="flowNodeCode != null and flowNodeCode.trim().length() != 0">
         AND (flow_node_code like concat('%',#{flowNodeCode},'%')) 
      </if>
      <if test="flowNoticeType != null">
        AND (flow_notice_type = #{flowNoticeType})
      </if>
      <if test="flowNoticeCode != null and flowNoticeCode.trim().length() != 0">
         AND (flow_notice_code like concat('%',#{flowNoticeCode},'%')) 
      </if>
      <if test="flowNoticeCodeType != null">
        AND (flow_notice_code_type = #{flowNoticeCodeType})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
         AND (create_user like concat('%',#{createUser},'%')) 
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
         AND (update_user like concat('%',#{updateUser},'%')) 
      </if>
    </trim>
  </sql>
  <sql id="gv_flow_notice_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="flowCode != null and flowCode.trim().length() != 0">
        AND (flow_code = #{flowCode})
      </if>
      <if test="flowNodeCode != null and flowNodeCode.trim().length() != 0">
        AND (flow_node_code = #{flowNodeCode})
      </if>
      <if test="flowNoticeType != null">
        AND (flow_notice_type = #{flowNoticeType})
      </if>
      <if test="flowNoticeCode != null and flowNoticeCode.trim().length() != 0">
        AND (flow_notice_code = #{flowNoticeCode})
      </if>
      <if test="flowNoticeCodeType != null">
        AND (flow_notice_code_type = #{flowNoticeCodeType})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from gv_flow_notice 
    <if test="fuzzy">
      <include refid="gv_flow_notice_query_fuzzy_condition" />
    </if>
    <if test="!fuzzy">
      <include refid="gv_flow_notice_query_condition" />
    </if>
     order by id desc 
  </select>
</mapper>