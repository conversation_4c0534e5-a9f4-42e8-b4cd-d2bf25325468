<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gtech.gvcore.dao.mapper.ReportTempLiabilityDStructureMapper">
    
    
    <insert id="insertDetail">

        INSERT INTO gv_report_temp_liability_d_${tableCode} (
            `issuer_code`
            , `merchant_code`
            , `outlet_code`
            , `cpg_code`
            , `voucher_status`
            , `denomination`
            , `effective_date`
            , `voucher_code_page_index`
            , `voucher_codes`
        ) VALUES (
            #{detail.issuerCode}
            , #{detail.merchantCode}
            , #{detail.outletCode}
            , #{detail.cpgCode}
            , #{detail.voucherStatus}
            , #{detail.denomination}
            , #{detail.effectiveDate}
            , #{detail.voucherCodePageIndex}
            , #{detail.voucherCodes}
        )
    </insert>

    <insert id="insertBatch">
        INSERT INTO gv_report_temp_liability_d_${tableCode} (
            `issuer_code`
            , `merchant_code`
            , `outlet_code`
            , `cpg_code`
            , `voucher_status`
            , `denomination`
            , `effective_date`
            , `voucher_code_page_index`
            , `voucher_codes`
        ) VALUES
        <foreach collection="detailList" item="detail" index="index" separator=",">
            (
                 #{detail.issuerCode}
                 , #{detail.merchantCode}
                 , #{detail.outletCode}
                 , #{detail.cpgCode}
                 , #{detail.voucherStatus}
                 , #{detail.denomination}
                 , #{detail.effectiveDate}
                 , #{detail.voucherCodePageIndex}
                 , #{detail.voucherCodes}
            )
        </foreach>
    </insert>
</mapper>