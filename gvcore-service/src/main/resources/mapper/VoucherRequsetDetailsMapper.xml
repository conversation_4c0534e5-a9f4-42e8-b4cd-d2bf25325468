<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.VoucherRequestDetailsMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.VoucherRequestDetails">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="voucher_request_details_code" jdbcType="VARCHAR" property="voucherRequestDetailsCode" />
    <result column="voucher_request_code" jdbcType="VARCHAR" property="voucherRequestCode" />
    <result column="denomination" jdbcType="DECIMAL" property="denomination" />
    <result column="voucher_num" jdbcType="INTEGER" property="voucherNum" />
    <result column="voucher_amount" jdbcType="DECIMAL" property="voucherAmount" />
    <result column="permission_code" jdbcType="VARCHAR" property="permissionCode" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, voucher_request_details_code, voucher_request_code, denomination, voucher_num, 
    voucher_amount, permission_code, create_user, create_time, update_user, update_time
  </sql>
	

    <insert id="batchInsert">
        insert into gv_voucher_request_details(voucher_request_details_code, voucher_request_code,
        denomination,cpg_code, voucher_num, voucher_amount, permission_code,
        create_user, create_time)
        values
        <foreach collection="details" item="detail" separator="," index="index">
            (
            #{detail.voucherRequestDetailsCode},
            #{detail.voucherRequestCode},
            #{detail.denomination},
            #{detail.cpgCode},
            #{detail.voucherNum},
            #{detail.voucherAmount},
            #{detail.permissionCode},
            #{detail.createUser},
            now()
            )
        </foreach>
    </insert>
    
  <select id="queryDenominationByVoucherRequestCodeList" parameterType="list" resultMap="BaseResultMap">
	SELECT voucher_request_code, denomination 
	FROM gv_voucher_request_details 
	WHERE voucher_request_code IN 
	<foreach collection="list" item="item" open="(" separator="," close=")">
		#{item}
	</foreach>
  </select>
    
</mapper>