<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gtech.gvcore.dao.mapper.GcReportLiabilityGenerateHistoryMapper">

    <!--
   因使用$符号进行表操作 因此 必须最大程度的限制该mapper的操作范围 以免造成sql注入
-->
    <insert id="createLiabilityTable">
        CREATE TABLE gc_report_temp_liability_${tableType}_${tableCode}
            LIKE gc_report_temp_liability_${tableType}_structure
    </insert>

    <!--
      因使用$符号进行表操作 因此 必须最大程度的限制该mapper的操作范围 以免造成sql注入
    -->
    <delete id="truncateLiabilityTable">
        TRUNCATE gc_report_temp_liability_${tableType}_${tableCode}
    </delete>

    <delete id="clearTable">
        DELETE FROM gc_report_temp_liability_${tableType}_${tableCode}
    </delete>

    <select id="selectVoucher" resultType="com.gtech.gvcore.service.report.impl.support.liability.model.GcLiabilityVoucherMode">
        SELECT id
             , card_number
             , denomination
             , balance
             , expiry_time       as expiry_time
             , status
             , management_status
             , activation_deadline
             , activation_extension_count
             , issuer_code
             , cpg_code
             , sales_outlet
             , expiry_time
        FROM gc_gift_card
        WHERE create_time  <![CDATA[<]]> #{createTime}
          AND sales_time IS NOT NULL
          AND management_status != 'destroy'
	AND `status` != 'ZERO_BALANCE'
        AND id  <![CDATA[>]]>  #{lastId}
        ORDER BY id
            LIMIT #{pageSize}
    </select>


    <select id="existTable" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM ${tableName}
    </select>

</mapper>