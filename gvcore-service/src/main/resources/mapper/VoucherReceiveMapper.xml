<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.VoucherReceiveMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.VoucherReceive">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="voucher_receive_code" jdbcType="VARCHAR" property="voucherReceiveCode" />
    <result column="receiver_code" jdbcType="VARCHAR" property="receiverCode" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="source_data_code" jdbcType="VARCHAR" property="sourceDataCode" />
    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
    <result column="outbound" jdbcType="VARCHAR" property="outbound" />
    <result column="inbound" jdbcType="VARCHAR" property="inbound" />
    <result column="voucher_num" jdbcType="INTEGER" property="voucherNum" />
    <result column="received_num" jdbcType="INTEGER" property="receivedNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="permission_code" jdbcType="VARCHAR" property="permissionCode" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, voucher_receive_code, receiver_code, source_type, source_data_code, issuer_code, outbound, inbound, 
    voucher_num, received_num, status, permission_code, create_user, create_time, update_user, 
    update_time
  </sql>
  <sql id="gv_voucher_receive_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      AND issuer_code = #{issuerCode}
      AND receiver_code IN
          <foreach collection="outletCodeRangeList" item="outletCode" separator="," open="(" close=")">
            #{outletCode}
          </foreach>
      <if test="voucherReceiveCode != null and voucherReceiveCode.trim().length() != 0">
        AND (voucher_receive_code = #{voucherReceiveCode})
      </if>
      <if test="receiverCode != null and receiverCode.trim().length() != 0">
        AND (receiver_code = #{receiverCode})
      </if>
        AND (source_type != 'customerorder')
      <if test="sourceDataCode != null and sourceDataCode.trim().length() != 0">
        AND (source_data_code = #{sourceDataCode})
      </if>
      <if test="outbound != null and outbound.trim().length() != 0">
        AND (outbound like concat('%',#{outbound},'%') )
      </if>
      <if test="inbound != null and inbound.trim().length() != 0">
        AND (inbound like concat('%',#{inbound},'%') )
      </if>
      <if test="voucherNum != null">
        AND (voucher_num = #{voucherNum})
      </if>
      <if test="receivedNum != null">
        AND (received_num = #{receivedNum})
      </if>
      <if test="status != null">
        AND (status = #{status})
      </if>
      <if test="permissionCode != null and permissionCode.trim().length() != 0">
        AND (permission_code = #{permissionCode})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
      <if test="receiveCodeList != null">
      	AND voucher_receive_code in 
      	<foreach collection="receiveCodeList" item="code" separator="," open="(" close=")">
      		#{code}
      	</foreach>
      </if>
    </trim>
  </sql>
  <select id="query" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
     from gv_voucher_receive 
      <include refid="gv_voucher_receive_query_condition" />
     order by id desc 
  </select>
  
  
  <update id="updateVoucherReceivedNum" >
  	update gv_voucher_receive 
  	set received_num = received_num + #{receivedNum}
  	where voucher_receive_code = #{receiveCode}
  </update>

</mapper>