<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.CustomerOrderMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.CustomerOrder">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="customer_order_code" jdbcType="VARCHAR" property="customerOrderCode" />
    <result column="issuer_code" jdbcType="VARCHAR" property="issuerCode" />
    <result column="outlet_code" jdbcType="VARCHAR" property="outletCode" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="mop_code" jdbcType="VARCHAR" property="mopCode" />
    <result column="voucher_num" jdbcType="INTEGER" property="voucherNum" />
    <result column="voucher_amount" jdbcType="DECIMAL" property="voucherAmount" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_type" jdbcType="VARCHAR" property="customerType" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="contact_first_name" jdbcType="VARCHAR" property="contactFirstName" />
    <result column="contact_last_name" jdbcType="VARCHAR" property="contactLastName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
    <result column="product_category_code" jdbcType="VARCHAR" property="productCategoryCode" />
    <result column="discount_type" jdbcType="VARCHAR" property="discountType" />
    <result column="means_of_payment_code" jdbcType="VARCHAR" property="meansOfPaymentCode" />
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="delive_type" jdbcType="INTEGER" property="deliveType" />
    <result column="logistics_name" jdbcType="VARCHAR" property="logisticsName" />
    <result column="track_no" jdbcType="VARCHAR" property="trackNo" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="voucher_batch_code" jdbcType="VARCHAR" property="voucherBatchCode" />
    <result column="release_time" jdbcType="TIMESTAMP" property="releaseTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, customer_order_code, issuer_code, outlet_code, purchase_order_no, mop_code, voucher_num, 
    voucher_amount, discount, amount, currency_code, customer_code, customer_name, customer_type, 
    company_name, contact_first_name, contact_last_name, contact_phone, contact_email, 
    product_category_code, discount_type, means_of_payment_code, invoice_no, delive_type, 
    logistics_name, track_no, status, voucher_batch_code, release_time, create_user, 
    create_time, update_user, update_time
  </sql>
  <sql id="gv_customer_order_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="customerOrderCode != null and customerOrderCode.trim().length() != 0">
        AND (customer_order_code = #{customerOrderCode})
      </if>
      <if test="issuerCode != null and issuerCode.trim().length() != 0">
        AND (issuer_code = #{issuerCode})
      </if>
      <if test="outletCode != null and outletCode.trim().length() != 0">
        AND (outlet_code = #{outletCode})
      </if>
      <if test="purchaseOrderNo != null and purchaseOrderNo.trim().length() != 0">
        AND (purchase_order_no = #{purchaseOrderNo})
      </if>
      <if test="mopCode != null and mopCode.trim().length() != 0">
        AND (mop_code = #{mopCode})
      </if>
      <if test="voucherNum != null">
        AND (voucher_num = #{voucherNum})
      </if>
      <if test="voucherAmount != null">
        AND (voucher_amount = #{voucherAmount})
      </if>
      <if test="discount != null">
        AND (discount = #{discount})
      </if>
      <if test="amount != null">
        AND (amount = #{amount})
      </if>
      <if test="currencyCode != null and currencyCode.trim().length() != 0">
        AND (currency_code = #{currencyCode})
      </if>
      <if test="customerCode != null and customerCode.trim().length() != 0">
        AND (customer_code = #{customerCode})
      </if>
      <if test="customerName != null and customerName.trim().length() != 0">
        AND (customer_name = #{customerName})
      </if>
      <if test="customerType != null and customerType.trim().length() != 0">
        AND (customer_type = #{customerType})
      </if>
      <if test="companyName != null and companyName.trim().length() != 0">
        AND (company_name = #{companyName})
      </if>
      <if test="contactFirstName != null and contactFirstName.trim().length() != 0">
        AND (contact_first_name = #{contactFirstName})
      </if>
      <if test="contactLastName != null and contactLastName.trim().length() != 0">
        AND (contact_last_name = #{contactLastName})
      </if>
      <if test="contactPhone != null and contactPhone.trim().length() != 0">
        AND (contact_phone = #{contactPhone})
      </if>
      <if test="contactEmail != null and contactEmail.trim().length() != 0">
        AND (contact_email = #{contactEmail})
      </if>
      <if test="productCategoryCode != null and productCategoryCode.trim().length() != 0">
        AND (product_category_code = #{productCategoryCode})
      </if>
      <if test="discountType != null and discountType.trim().length() != 0">
        AND (discount_type = #{discountType})
      </if>
      <if test="meansOfPaymentCode != null and meansOfPaymentCode.trim().length() != 0">
        AND (means_of_payment_code = #{meansOfPaymentCode})
      </if>
      <if test="invoiceNo != null and invoiceNo.trim().length() != 0">
        AND (invoice_no = #{invoiceNo})
      </if>
      <if test="deliveType != null">
        AND (delive_type = #{deliveType})
      </if>
      <if test="logisticsName != null and logisticsName.trim().length() != 0">
        AND (logistics_name = #{logisticsName})
      </if>
      <if test="trackNo != null and trackNo.trim().length() != 0">
        AND (track_no = #{trackNo})
      </if>
      <if test="status != null and status.trim().length() != 0">
        AND (status = #{status})
      </if>
      <if test="voucherBatchCode != null and voucherBatchCode.trim().length() != 0">
        AND (voucher_batch_code = #{voucherBatchCode})
      </if>
      <if test="releaseTime != null">
        AND (release_time = #{releaseTime})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>

    <update id="updateStatus" parameterType="com.gtech.gvcore.dao.dto.CustomerOrderDto">
        UPDATE gv_customer_order
        SET status      = #{status},
            update_user = #{updateUser},
            update_time = #{updateTime}
        WHERE customer_order_code = #{customerOrderCode}
          AND status = #{oldStatus}
    </update>
    <update id="updateStatusByAllocationCode">
        update gv_customer_order
        set `status`='Cancel'
        where customer_order_code = (select source_data_code
                                     from gv_voucher_allocation
                                     where voucher_allocation_code = #{voucherAllocationCode})
    </update>

    <select id="selectOrderByStatus" resultType="com.gtech.gvcore.dao.model.CustomerOrder">
        select id,
               customer_order_code,
               issuer_code,
               purchase_order_no,
               mop_code,
               voucher_num,
               voucher_amount,
               discount,
               amount,
               currency_code,
               customer_code,
               customer_name,
               customer_type,
               company_name,
               contact_first_name,
               contact_last_name,
               contact_phone,
               contact_email,
               product_category_code,
               discount_type,
               delive_type,
               logistics_name,
               track_no,
               status,
               create_user,
               create_time,
               update_user,
               update_time
        from gv_customer_order
        where customer_code = #{businessCode}
          and status = #{status}
    </select>


    <select id="queryOrder"
            resultType="com.gtech.gvcore.common.response.customerorder.QueryCustomerOrderResponse">
        select
        gco.customer_order_code as customerOrderCode,
        gco.voucher_num as voucherNum,
        gco.purchase_order_no as purchaseOrderNo,
        gco.invoice_no as invoiceNo,
        customer_name as customerName,
        customer_code as customerCode,
        gco.voucher_amount as voucherAmount,
        gco.company_name as companyName,
        contact_first_name as contactFirstName,
        contact_last_name as contactLastName,
        contact_phone as contactPhone,
        gco.mop_code as mopCode,
        gco.voucher_batch_code as voucherBatchCode,
        contact_email as contactEmail,
        status as status,
        gco.create_user as createUser,
        gco.create_time as createTime,
        gco.update_time as updateTime,
        gco.delive_type as deliveType,
        gco.delivery_date as deliveryDate,
        gco.order_address as orderAddress
        from gv_customer_order as gco
        left join gv_customer_order_details gcod on gco.customer_order_code = gcod.customer_order_code
        <where>
            gco.issuer_code=#{orderRequest.issuerCode}

            <if test="orderRequest.outletCodeRangeList !=null and orderRequest.outletCodeRangeList.size>0 ">
                AND gco.outlet_code IN
                <foreach collection="orderRequest.outletCodeRangeList" item="outletCode" open="(" close=")" separator=",">
                    #{outletCode}
                </foreach>
            </if>
            <if test="orderRequest.customerName !=null and orderRequest.customerName.trim().length() !=0">
                and gco.customer_name like concat('%',#{orderRequest.customerName},'%')
            </if>
            <if test="orderRequest.denomination !=null and orderRequest.denomination.trim().length() !=0">
                and gcod.denomination = #{orderRequest.denomination}
            </if>
            <if test="orderRequest.voucherType !=null and orderRequest.voucherType.trim().length() !=0">
                and gco.mop_code=#{orderRequest.voucherType}
            </if>
            <if test="orderRequest.customerCode !=null and orderRequest.customerCode.trim().length() !=0">
                and gco.customer_code=#{orderRequest.customerCode}
            </if>
            <if test="orderRequest.email !=null and orderRequest.email.trim().length() !=0">
                and gco.contact_email=#{orderRequest.email}
            </if>
            <if test="orderRequest.status !=null and orderRequest.status.trim().length() !=0">
                and gco.status=#{orderRequest.status}
            </if>
            <if test="orderRequest.createTimeStart !=null and orderRequest.createTimeStart.trim().length() !=0">
                and gco.create_time &gt;=#{orderRequest.createTimeStart}
            </if>
            <if test="orderRequest.createTimeEnd !=null and orderRequest.createTimeEnd.trim().length() !=0">
                and gco.create_time &lt;=#{orderRequest.createTimeEnd}
            </if>
            <if test="orderRequest.deliveType !=null and orderRequest.deliveType.trim().length() !=0">
                and gco.delive_type=#{orderRequest.deliveType}
            </if>

            <if test="orderRequest.createUserList !=null and orderRequest.createUserList.size>0 ">
            	and gco.create_user in
            	<foreach collection="orderRequest.createUserList" item="code" open="(" close=")" separator=",">
            		#{code}
            	</foreach>
            </if>
            
            
            AND gcod.delete_status = 0
        </where>
        group by gco.customer_order_code order by gco.create_time desc
    </select>

    <select id="exists" resultType="java.lang.Boolean">
        select count(1)
        from gv_customer_order
        where purchase_order_no = #{s}
    </select>

  <update id="updateByRelease" parameterType="com.gtech.gvcore.dao.dto.CustomerOrderDto">
	UPDATE gv_customer_order
	   SET status = #{status},
           sales_note_url = #{salesNoteUrl},
	   	   release_time = #{releaseTime},
	       update_user = #{updateUser},
	       update_time = #{updateTime} 
	WHERE customer_order_code = #{customerOrderCode} 
	  AND status = #{oldStatus} 
  </update>
  
  <select id="sumCustomerOrderGroupByArticle" parameterType="com.gtech.gvcore.dao.dto.CustomerOrderDto" resultType="com.gtech.gvcore.dto.salespostingxml.SumCustomerOrderGroupByArticle">
  	SELECT IFNULL(article.sap_article_code,'') articleCode, IFNULL(SUM(cod.voucher_amount), 0) voucherAmount, IFNULL(SUM(cod.voucher_num), 0) voucherNum,
  		IFNULL(SUM(cod.amount), 0) amount, IFNULL(SUM(cod.discount), 0) discount 
  	FROM gv_customer_order_details cod 
	LEFT JOIN gv_customer_order co ON co.customer_order_code = cod.customer_order_code
	LEFT JOIN gv_cpg cpg ON cpg.cpg_code = cod.cpg_code
	LEFT JOIN gv_article_mop article ON article.article_mop_code = cpg.article_mop_code
	WHERE co.release_time &gt;= #{releaseTimeStart} 
	  AND co.release_time &lt; #{releaseTimeEnd} 
<!--	 MER-1884
AND co.issuer_code = #{issuerCode}-->
	  AND co.outlet_code = #{outletCode}
	  AND cod.delete_status &lt;&gt; #{deleteStatus}
	GROUP BY article.article_code
  </select>
  
  <select id="sumCancelCustomerOrderGroupByArticle" parameterType="com.gtech.gvcore.dao.dto.CustomerOrderDto" resultType="com.gtech.gvcore.dto.salespostingxml.SumCustomerOrderGroupByArticle">
  	SELECT IFNULL(article.sap_article_code,'') articleCode, IFNULL(SUM(cod.voucher_amount), 0) voucherAmount, IFNULL(SUM(cod.voucher_num), 0) voucherNum,
  		IFNULL(SUM(cod.amount), 0) amount, IFNULL(SUM(cod.discount), 0) discount 
  	FROM gv_customer_order_details cod 
	LEFT JOIN gv_customer_order co ON co.customer_order_code = cod.customer_order_code
	LEFT JOIN gv_cpg cpg ON cpg.cpg_code = cod.cpg_code
	LEFT JOIN gv_article_mop article ON article.article_mop_code = cpg.article_mop_code
      <where> co.release_time &gt;= #{dto.releaseTimeStart}
	  AND co.release_time &lt; #{dto.releaseTimeEnd}
	  <if test="dto.issuerCode !=null and dto.issuerCode.trim().length() != 0">
          AND co.issuer_code = #{dto.issuerCode}
      </if>
	  AND co.outlet_code = #{dto.outletCode}
	  AND co.status = #{dto.status}
	  AND co.update_time &gt;= #{dto.updateTimeStart}
	  AND co.update_time &lt; #{dto.updateTimeEnd}
	  AND cod.delete_status &lt;&gt; #{dto.deleteStatus}
	GROUP BY article.article_code
      </where>
  </select>
  
  <!-- // 真确的应该是means_of_payment_code, IFNULL(SUM(amount), 0) amount
        // 目前这样计算是因为保存的数据是错的，其他人不想修改，只能配合错误的数据修改 , 会产生负数需要再程序内修改-->
  <select id="sumGroupByMeansOfPaymentCode" parameterType="com.gtech.gvcore.dao.dto.CustomerOrderDto" resultMap="BaseResultMap">
  	SELECT means_of_payment_code, (IFNULL(SUM(voucher_amount), 0) - IFNULL(SUM(amount), 0)) amount 
  	FROM gv_customer_order 
	WHERE release_time &gt;= #{releaseTimeStart} 
	  AND release_time &lt; #{releaseTimeEnd} 
<!--	  MER-1884
AND issuer_code = #{issuerCode} -->
	  AND outlet_code = #{outletCode} 
	GROUP BY means_of_payment_code 
  </select>
  
  <select id="sumCancelGroupByMeansOfPaymentCode" parameterType="com.gtech.gvcore.dao.dto.CustomerOrderDto" resultMap="BaseResultMap">
  	SELECT means_of_payment_code, (IFNULL(SUM(voucher_amount), 0) - IFNULL(SUM(amount), 0)) amount 
  	FROM gv_customer_order 
	WHERE release_time &gt;= #{releaseTimeStart} 
	  AND release_time &lt; #{releaseTimeEnd} 
<!--	  MER-1884
AND issuer_code = #{issuerCode} -->
	  AND outlet_code = #{outletCode} 
	  AND status = #{status} 
	  AND update_time &gt;= #{updateTimeStart} 
	  AND update_time &lt; #{updateTimeEnd} 
	GROUP BY means_of_payment_code 
  </select>

</mapper>