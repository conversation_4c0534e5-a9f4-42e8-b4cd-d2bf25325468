package com.gtech.gvcore.giftcard.domain.service;

import com.gtech.gvcore.giftcard.application.dto.ExtensionDTO;
import com.gtech.gvcore.giftcard.domain.model.GcActivationExtensionRecord;
import com.gtech.gvcore.giftcard.domain.model.GiftCard;
import com.gtech.gvcore.giftcard.domain.repository.GcActivationExtensionRepository;
import com.gtech.gvcore.giftcard.domain.repository.GiftCardRepository;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.helper.GvCodeHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GcExtensionDomainServiceTest {

    @Mock
    private GiftCardRepository giftCardRepository;

    @Mock
    private GcActivationExtensionRepository extensionRepository;

    @Mock
    private MasterDataCache masterDataCache;

    @Mock
    private GvCodeHelper gvCodeHelper;

    @InjectMocks
    private GcExtensionDomainService gcExtensionDomainService;

    private GiftCard testGiftCard;
    private ExtensionDTO testExtensionDTO;
    private Outlet testOutlet;

    @BeforeEach
    void setUp() {
        // 创建测试礼品卡
        testGiftCard = new GiftCard();
        testGiftCard.setCardNumber("1234567890123456");
        testGiftCard.setIssuerCode("IS001");
        testGiftCard.setActivationDeadline(new Date(System.currentTimeMillis() - 86400000)); // 昨天过期
        testGiftCard.setExpiryTime(new Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000)); // 1年后过期
        testGiftCard.setActivationExtensionCount(0);
        testGiftCard.setActivationGracePeriod("00010000"); // 1天宽限期

        // 创建测试延长DTO
        testExtensionDTO = new ExtensionDTO();
        testExtensionDTO.setCardNumber("1234567890123456");
        testExtensionDTO.setOutletCode("OUT001");
        testExtensionDTO.setNotes("Test extension");
        testExtensionDTO.setBatchNumber("BATCH001");
        testExtensionDTO.setSource("API");

        // 创建测试网点
        testOutlet = new Outlet();
        testOutlet.setOutletCode("OUT001");
        testOutlet.setMerchantCode("MER001");
    }

    @Test
    void testExtendActivationPeriod_Success() {
        // Arrange
        when(giftCardRepository.findByCardNumber(anyString())).thenReturn(Optional.of(testGiftCard));
        when(masterDataCache.getOutlet(anyString())).thenReturn(testOutlet);
        when(gvCodeHelper.generateExtensionCode()).thenReturn("EX001");
        when(extensionRepository.save(any(GcActivationExtensionRecord.class))).thenReturn(null);
        when(giftCardRepository.updateCard(any(GiftCard.class))).thenReturn(1);

        // Act
        GiftCard result = gcExtensionDomainService.extendActivationPeriod(testExtensionDTO);

        // Assert
        assertNotNull(result);
        assertEquals("1234567890123456", result.getCardNumber());
        assertEquals(1, result.getActivationExtensionCount());
        
        // 验证保存延长记录被调用
        verify(extensionRepository, times(1)).save(any(GcActivationExtensionRecord.class));
        verify(giftCardRepository, times(1)).updateCard(any(GiftCard.class));
    }

    @Test
    void testExtendActivationPeriod_CardNotFound() {
        // Arrange
        when(giftCardRepository.findByCardNumber(anyString())).thenReturn(Optional.empty());

        // Act & Assert
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> gcExtensionDomainService.extendActivationPeriod(testExtensionDTO)
        );
        
        assertEquals("礼品卡不存在", exception.getMessage());
        verify(extensionRepository, never()).save(any());
        verify(giftCardRepository, never()).updateCard(any());
    }

    @Test
    void testExtendActivationPeriod_WithoutOutlet() {
        // Arrange
        testExtensionDTO.setOutletCode(null);
        when(giftCardRepository.findByCardNumber(anyString())).thenReturn(Optional.of(testGiftCard));
        when(gvCodeHelper.generateExtensionCode()).thenReturn("EX001");
        when(extensionRepository.save(any(GcActivationExtensionRecord.class))).thenReturn(null);
        when(giftCardRepository.updateCard(any(GiftCard.class))).thenReturn(1);

        // Act
        GiftCard result = gcExtensionDomainService.extendActivationPeriod(testExtensionDTO);

        // Assert
        assertNotNull(result);
        verify(masterDataCache, never()).getOutlet(anyString());
        verify(extensionRepository, times(1)).save(any(GcActivationExtensionRecord.class));
    }
}
