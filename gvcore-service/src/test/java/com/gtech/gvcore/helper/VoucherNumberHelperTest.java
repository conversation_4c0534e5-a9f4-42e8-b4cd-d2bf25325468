package com.gtech.gvcore.helper;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

/**
 * <AUTHOR>
 * @Date 2022/2/25 14:30
 */
@RunWith(MockitoJUnitRunner.class)
public class VoucherNumberHelperTest {

    @InjectMocks
    private VoucherNumberHelper voucherNumberHelper;

    @Mock
    private RedisTemplate redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Before
    public void before(){

        Mockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);


    }


    @Test
    public void voucherCodePhysical() {


        voucherNumberHelper.voucherCodePhysical("123");
    }




}
