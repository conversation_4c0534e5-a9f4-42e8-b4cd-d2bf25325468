package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.voucher.GetStartAndEndVoucherRequest;
import com.gtech.gvcore.common.request.voucher.UpdateVoucherStatusRequest;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import com.gtech.gvcore.components.CheckReceiveAndIssuanceComponent;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherBooklet;
import com.gtech.gvcore.service.impl.VoucherServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.util.Arrays;

@RunWith(MockitoJUnitRunner.class)
public class VoucherServiceTest {

    @InjectMocks
    private VoucherServiceImpl voucherService;

    @Mock
    private VoucherBookletService voucherBookletService;
    
    @Mock
    private VoucherBatchService voucherBatchService;
    
    @Mock
    private VoucherMapper voucherMapper;

	@Mock
	private CheckReceiveAndIssuanceComponent checkReceiveAndIssuanceComponent;

	@Before
	public void before() {
		EntityHelper.initEntityNameMap(Voucher.class, new MapperHelper().getConfig());
	}

    
	@Test
	public void queryNotReceiveVoucherTest() {
		voucherService.queryNotReceiveVoucher(null, null, "123");
		voucherService.queryNotReceiveVoucher("123", "124", "123");
		Assert.assertTrue(true);
	}

	@Test
	public void updateStatusTest() {
		UpdateVoucherStatusRequest request = new UpdateVoucherStatusRequest();

		Mockito.when(voucherMapper.updateByConditionSelective(Mockito.any(), Mockito.any())).thenReturn(1);
		voucherService.updateVoucherStatus(request);
		request.setStatus(1);
		request.setOldStatus(0);
		request.setVoucherStartNo("21");
		request.setVoucherEndNo("123");
		request.setCirculationStatus(1);
		request.setOldCirculationStatus(2);
		request.setVoucherCode("123");
		voucherService.updateVoucherStatus(request);
		Assert.assertTrue(true);
	}

    @Test
    public void getStartAndEndVoucherTest() {
    	GetStartAndEndVoucherRequest request = new GetStartAndEndVoucherRequest();
    	request.setBookletStartNo("123");
    	request.setBookletEndNo("123");
		request.setCheckType("receive");
    	
    	VoucherBooklet voucherBooklet = new VoucherBooklet();
    	voucherBooklet.setVoucherBatchCode("111");
    	voucherBooklet.setVoucherStartNo("111");
    	voucherBooklet.setVoucherEndNo("123");
    	voucherBooklet.setBookletCode("123");
    	voucherBooklet.setBookletPerNum(20);
    	Mockito.when(voucherBookletService.queryBookletByCodeList(Mockito.any())).thenReturn(Arrays.asList(voucherBooklet));
    	VoucherBatchResponse voucherBatchResponse = new VoucherBatchResponse();
    	Mockito.when(voucherBatchService.getVoucherBatch(Mockito.any())).thenReturn(voucherBatchResponse);
    	voucherService.getStartAndEndVoucher(request);
    	
    	request = new GetStartAndEndVoucherRequest();
    	request.setVoucherStartNo("123");
    	request.setVoucherEndNo("123");
    	
    	Voucher voucher = new  Voucher();
    	voucher.setVoucherCode("123");
    	Mockito.when(voucherMapper.queryByVoucherCodeList(Mockito.any())).thenReturn(Arrays.asList(voucher));
    	voucherService.getStartAndEndVoucher(request);
    	
    	request = new GetStartAndEndVoucherRequest();
    	request.setVoucherStartNo("123");
    	request.setVoucherEndNo("123");
    	voucherService.getStartAndEndVoucher(request);
    	
    	request = new GetStartAndEndVoucherRequest();
    	request.setVoucherCodeList(Arrays.asList("123"));
    	voucherService.getStartAndEndVoucher(request);
		Assert.assertTrue(true);
		
		request = new GetStartAndEndVoucherRequest();
    	request.setBookletCodeList(Arrays.asList("123"));
    	voucherService.getStartAndEndVoucher(request);
		Assert.assertTrue(true);
    	
    }
}
