package com.gtech.gvcore.service;

import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.model.TransactionData;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.TransactionDataServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertTrue;

/**
 * 
 * <AUTHOR>
 * @date 2022年5月5日
 */
@RunWith(MockitoJUnitRunner.class)
public class TransactionDataServiceTests {

    @InjectMocks
    private TransactionDataServiceImpl transactionDataServiceImpl;

    @Mock
    private GvCodeHelper gvCodeHelper;

    @Mock
    private TransactionDataMapper transactionDataMapper;

    @Test
    public void insertList() {

        List<TransactionData> transactionDataList = new ArrayList<>();
        transactionDataList.add(new TransactionData());
        transactionDataServiceImpl.insertList(transactionDataList);

        assertTrue(true);
    }

}


