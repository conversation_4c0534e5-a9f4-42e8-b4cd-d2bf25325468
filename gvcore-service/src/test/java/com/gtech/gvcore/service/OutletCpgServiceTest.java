package com.gtech.gvcore.service;

import com.gtech.gvcore.common.request.outletcpg.*;
import com.gtech.gvcore.dao.mapper.OutletCpgMapper;
import com.gtech.gvcore.dao.model.OutletCpg;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.impl.OutletCpgServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

/**
 * <AUTHOR>
 * @Date 2022/3/23 15:35
 */
@RunWith(MockitoJUnitRunner.class)
public class OutletCpgServiceTest {

    @InjectMocks
    private OutletCpgServiceImpl outletCpgServiceImpl;

    @Mock
    private OutletCpgMapper outletCpgMapper;

    @Mock
    private GvCodeHelper codeHelper;
    @Before
    public void before() {
        EntityHelper.initEntityNameMap(OutletCpg.class, new MapperHelper().getConfig());
    }


    @Test
    public void createOutletCpg() {

        CreateOutletCpgRequest build = CreateOutletCpgRequest.builder()
                .cpgCode("123")
                .outletCode("123")
                .build();

        outletCpgServiceImpl.createOutletCpg(build);


    }


    @Test
    public void updateOutletCpg(){
        UpdateOutletCpgRequest request = UpdateOutletCpgRequest.builder()
                .cpgCode("123")
                .build();

        outletCpgServiceImpl.updateOutletCpg(request);

    }


    @Test
    public void deleteOutletCpg(){
        DeleteOutletCpgRequest request = DeleteOutletCpgRequest.builder()
                .outletCpgCode("123")
                .build();

        outletCpgServiceImpl.deleteOutletCpg(request);

    }


    @Test
    public void deleteOutletCpgByOutletCode(){

        outletCpgServiceImpl.deleteOutletCpgByOutletCode(DeleteOutletCpgByOutletCodeRequest.builder().outletCode("123").build());
    }

    @Test
    public void queryOutletCpgList(){


        outletCpgServiceImpl.queryOutletCpgList(QueryOutletCpgRequest.builder().outletCode("123").cpgCode("123").status(1).build());

    }



    @Test
    public void getOutletCpg(){


        outletCpgServiceImpl.getOutletCpg(GetOutletCpgRequest.builder().outletCpgCode("123").build());

    }


    @Test
    public void queryOutletCpgListByOutlet(){

        outletCpgServiceImpl.queryOutletCpgListByOutlet("123");

    }


}
