
package com.gtech.gvcore.service;

import java.util.Arrays;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.gvcore.common.request.receive.VoucherReceiveRecordRequest;
import com.gtech.gvcore.dao.mapper.VoucherReceiveRecordMapper;
import com.gtech.gvcore.dao.model.VoucherReceiveRecord;
import com.gtech.gvcore.service.impl.VoucherReceiveRecordServiceImpl;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class VoucherReceiveRecordServiceTest {

	@InjectMocks
	VoucherReceiveRecordServiceImpl voucherReceiveRecordService;


	@Mock
	private VoucherReceiveRecordMapper voucherReceiveRecordMapper;

	@Before
	public void before() {
		EntityHelper.initEntityNameMap(VoucherReceiveRecord.class, new MapperHelper().getConfig());
	}

	@Test
	public void saveVoucherReceiveRecordTest() {
		voucherReceiveRecordService.saveVoucherReceiveRecord(null);
		VoucherReceiveRecordRequest voucherReceiveRecordRequest = new VoucherReceiveRecordRequest();
		voucherReceiveRecordService.saveVoucherReceiveRecord(Arrays.asList(voucherReceiveRecordRequest));
		Assert.assertTrue(true);
	}

}
