package com.gtech.gvcore.service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

import org.apache.commons.lang.time.DateUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.gtech.basic.masterdata.web.service.MasterDataValueService;
import com.gtech.commons.result.PageResult;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.vouchermerge.CancelMergeVoucherRequest;
import com.gtech.gvcore.common.request.vouchermerge.MergeVoucherRequest;
import com.gtech.gvcore.common.response.cpg.QueryCpgsByPageResponse;
import com.gtech.gvcore.dao.mapper.VoucherMergeMapper;
import com.gtech.gvcore.dao.model.CpgType;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.dao.model.VoucherMerge;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.impl.VoucherMergeServiceImpl;

import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

@RunWith(MockitoJUnitRunner.class)
public class VoucherMergeServiceTest {

    @InjectMocks
	private VoucherMergeServiceImpl voucherMergeService;

    @Mock
	private VoucherService voucherService;

	@Mock
	private CpgTypeService cpgTypeService;

	@Mock
	private CpgService cpgService;

	@Mock
	private MasterDataValueService masterDataValueService;

	@Mock
	private VoucherNumberHelper voucherNumberHelper;

	@Mock
	private VoucherMergeMapper voucherMergeMapper;

	@Before
	public void before() {
		EntityHelper.initEntityNameMap(VoucherMerge.class, new MapperHelper().getConfig());
	}

    
	@Test
	public void mergeTest() {
		voucherMergeService.merge(null);
		MergeVoucherRequest request = new MergeVoucherRequest();
		voucherMergeService.merge(request);
		request.setVoucherCodeList(Arrays.asList("123"));
		Voucher voucher = new Voucher();
		voucher.setStatus(1);
		voucher.setVoucherStatus(1);
		voucher.setDenomination(BigDecimal.TEN);
		voucher.setVoucherEffectiveDate(DateUtils.addHours(new Date(), 1));
		Voucher voucher1 = new Voucher();
		voucher1.setStatus(0);
		voucher1.setVoucherStatus(1);
		voucher1.setDenomination(BigDecimal.TEN);
		voucher1.setVoucherEffectiveDate(DateUtils.addHours(new Date(), 1));
		Mockito.when(voucherService.queryByVoucherCodeList(Mockito.any(), Mockito.any())).thenReturn(Arrays.asList(voucher, voucher1));
		CpgType cpgType = new CpgType();
		Mockito.when(cpgTypeService.getCpgTypeByPrefix(Mockito.any())).thenReturn(cpgType);
		QueryCpgsByPageResponse queryCpgsByPageResponse = new QueryCpgsByPageResponse();
		PageResult<QueryCpgsByPageResponse> pageResult = new PageResult<>(Arrays.asList(queryCpgsByPageResponse), 1l);
		Mockito.when(cpgService.queryCpgsByPage(Mockito.any())).thenReturn(pageResult);
		voucherMergeService.merge(request);
		Assert.assertTrue(true);
	}

	@Test
	public void cancelmergeTest() {
		voucherMergeService.cancelMerge(null);
		CancelMergeVoucherRequest request = new CancelMergeVoucherRequest();
		voucherMergeService.cancelMerge(request);
		request.setVoucherCode("123");
		Voucher voucher = new Voucher();
		voucher.setStatus(1);
		voucher.setVoucherStatus(1);
		voucher.setDenomination(BigDecimal.TEN);
		voucher.setVoucherEffectiveDate(DateUtils.addHours(new Date(), 1));
		Mockito.when(voucherService.getVoucherByCode(Mockito.any())).thenReturn(voucher);

		VoucherMerge mergeRecord = new VoucherMerge();
		mergeRecord.setVoucherMainCode(request.getVoucherCode());
		mergeRecord.setStatus(VoucherStatusEnum.VOUCHER_ACTIVATED.getCode());
		Mockito.when(voucherMergeMapper.select(Mockito.any())).thenReturn(Arrays.asList(mergeRecord));
		voucherMergeService.cancelMerge(request);
		Assert.assertTrue(true);
	}

	@Test
	public void usemergeTest() {
		voucherMergeService.useMergeVoucher("123");
		Assert.assertTrue(true);
	}
}
