package com.gtech.gvcore.service;

import com.gtech.gvcore.common.enums.DisEmailTemplateTypeEnum;
import com.gtech.gvcore.common.exception.GvcoreParamValidateException;
import com.gtech.gvcore.common.request.distribution.ChangeEmailTemplateStatusRequest;
import com.gtech.gvcore.common.request.distribution.CreateDistributionEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.PreviewSendEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.QueryEmailTemplateRequest;
import com.gtech.gvcore.common.request.distribution.UpdateDistributionEmailTemplateRequest;
import com.gtech.gvcore.common.response.distribution.DistributionEmailTemplateResponse;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.mapper.DistributionEmailTemplateMapper;
import com.gtech.gvcore.dao.model.DistributionEmailTemplate;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.distribution.impl.DistributionEmailTemplateServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName DistributionEmailTemplateServiceTest
 * @Description DistributionEmailTemplateServiceTest
 * <AUTHOR>
 * @Date 2022/7/5 18:42
 * @Version V1.0
 **/
@RunWith(MockitoJUnitRunner.class)
public class DistributionEmailTemplateServiceTest {

    @InjectMocks
    private DistributionEmailTemplateServiceImpl distributionEmailTemplateService;

    @Mock
    private DistributionEmailTemplateMapper distributionEmailTemplateMapper;

    @Mock
    private GvCodeHelper gvCodeHelper;

    @Before
    public void before() {
        EntityHelper.initEntityNameMap(DistributionEmailTemplate.class, new MapperHelper().getConfig());
    }

    @Test
    public void createEmailTemplate() {

        try {
            final Field domainNameWhitelist = this.distributionEmailTemplateService.getClass().getDeclaredField("domainNameWhitelist");
            domainNameWhitelist.setAccessible(true);
            domainNameWhitelist.set(this.distributionEmailTemplateService, Collections.singleton("mapgiftvoucher-dev.gtech.asia"));
        } catch (Exception e) {
            // no codes
        }

        final CreateDistributionEmailTemplateRequest request = new CreateDistributionEmailTemplateRequest();
        request.setTemplateType(DisEmailTemplateTypeEnum.INDIVIDUAL.code());
        request.setRichText("http://mapgiftvoucher-dev.gtech.asia/abc");

        Mockito.when(this.gvCodeHelper.generateDistributionEmailTemplateCode()).thenReturn(UUIDUtils.generateCode());

        final String emailTemplateCode = this.distributionEmailTemplateService.createEmailTemplate(request);

        Assert.assertNotNull(emailTemplateCode);

        try {
            request.setRichText("http://mapgiftvoucher-dev-dddd.gtech.asia");
            this.distributionEmailTemplateService.createEmailTemplate(request);
        } catch (GvcoreParamValidateException e) {
            // no codes
        }

        try {
            request.setRichText("any");
            request.setTemplateType(3);
            this.distributionEmailTemplateService.createEmailTemplate(request);
        } catch (Exception e) {
            // no codes
        }

        try {
            Mockito.when(this.distributionEmailTemplateMapper.selectCountByCondition(Mockito.any())).thenReturn(0);
            request.setRichText("any");
            this.distributionEmailTemplateService.createEmailTemplate(request);
        } catch (Exception e) {
            // no codes
        }

        try {
            Mockito.when(this.distributionEmailTemplateMapper.selectCountByCondition(Mockito.any())).thenReturn(1);
            request.setRichText("any");
            request.setTemplateType(DisEmailTemplateTypeEnum.INDIVIDUAL.code());
            this.distributionEmailTemplateService.createEmailTemplate(request);
        } catch (Exception e) {
            // no codes
        }
    }

    @Test
    public void updateEmailTemplate() {

        final UpdateDistributionEmailTemplateRequest request = new UpdateDistributionEmailTemplateRequest();
        request.setTemplateType(DisEmailTemplateTypeEnum.INDIVIDUAL.code());
        request.setTemplateCode(UUIDUtils.generateCode());

        final String emailTemplateCode = this.distributionEmailTemplateService.updateEmailTemplate(request);

        Assert.assertNotNull(emailTemplateCode);
    }

    @Test
    public void changeStatus() {

        final ChangeEmailTemplateStatusRequest request = new ChangeEmailTemplateStatusRequest();
        request.setTemplateCode(UUIDUtils.generateCode());

        final String emailTemplateCode = this.distributionEmailTemplateService.changeStatus(request);

        Assert.assertNotNull(emailTemplateCode);
    }

    @Test
    public void queryAllEmailTemplate() {

        final QueryEmailTemplateRequest request = new QueryEmailTemplateRequest();

        Mockito.when(this.distributionEmailTemplateMapper.selectByCondition(Mockito.any())).thenReturn(Collections.singletonList(new DistributionEmailTemplate()));

        final List<DistributionEmailTemplateResponse> responseList = this.distributionEmailTemplateService.queryEmailTemplates(request);

        Assert.assertNotNull(responseList);
    }

    @Test
    public void previewSend() {

        final PreviewSendEmailTemplateRequest request = new PreviewSendEmailTemplateRequest();

        this.distributionEmailTemplateService.previewSend(request);
    }

}
